'use strict'

const { graphql } = require('graphql')
const jwt = require('jsonwebtoken')

const util = require('./src/util/util')
const schema = require('./src/schema/file_upload/index')
const common = require('./src/util/common')
const middleware = require('./src/util/middleware')
const { serverlessErrorHandler } = require('./src/util/errorHandler')
const { processRequest } = require('graphql-upload-minimal')
const log = require('./src/util/log')

module.exports.fileupload = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)
  }

  const downtimeresp = await common.downTime()
  if (downtimeresp[0] != 'undefined' && downtimeresp[0] != null && downtimeresp[0].notice_flag == 'Y') {
    const defaultDownResp = JSON.stringify({ status: 301, message: downtimeresp[0].message })
    // if (util.encryptionFlag) return await common.encrypt(defaultDownResp)
    return defaultDownResp
  }

  let payload = {}
  let token = ''
  try {
    if (util.isProduction() || util.isStaging()) {
      console.log('Production123')
      token = event.headers.Authorization
      payload = jwt.verify(token, util.jwtKey)
      console.log('payload data++++++++', payload)
    }
    if ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey) || process.env.NODE_ENV == 'development') {
      // console.log('before decrypt data', event.data.query)
      if (util.isProduction() || util.isStaging()) {
        const tokenresp = await common.tokenValidation(token, payload.publickey)
        if (tokenresp.status === 400) {
          const defaultTokenResp = JSON.stringify({ status: 401, message: tokenresp.message })
          // if (util.encryptionFlag) return await common.encrypt(defaultTokenResp)
          return defaultTokenResp
        }
      }

      if (util.encryptionFlag) {
        // event.data.query = await common.decrypt(event.data.query, util.encryptpassword)
        // console.log('after decrypt data', event.data.query)
      }

      event.headers['content-type'] = event.headers['Content-Type'] // content-type issue
      const request = await processRequest(event, null, { environment: 'lambda' })

      let response = await graphql(schema, request.query, { ip_address: event.headers['X-Forwarded-For'] || '' }, (err, result) => {
        if (err) {
          console.log('Error in graphql', err)
          callback(err)
        }
        console.log('handler result', result)
      }, request.variables)

      // let response = await graphql(schema, event.data.query, { ip_address: event.headers['X-Forwarded-For'] || '' }, (err, result) => {
      //   if (err) {
      //     console.log('Error in graphql', err)
      //     callback(err)
      //   }
      //   console.log('handler result', result)
      // })

      response = await serverlessErrorHandler(event.data, response)

      if (util.encryptionFlag) {
        // console.log('res1', await common.encrypt(JSON.stringify(response)))
        // return await common.encrypt(JSON.stringify(response))
        return JSON.stringify(response)
      } else {
        console.log('res1', response)
        if (process.env.IS_OFFLINE === 'true') {
          callback(null, { statusCode: 200, body: JSON.stringify(response) })
        }
        return response
      }
      // console.log('=========Graphql Res==========',res)
    } else {
      console.log('jwt failed')
    }
  } catch (error) {
    console.log('JWT FAILED IN VERIFICATION', error)
    let errResp = { status: 400, message: 'Request Failed' }
    if (error instanceof jwt.JsonWebTokenError) {
      errResp = { status: 401, message: error.message }
    }

    if (util.encryptionFlag) {
      // console.log('err1', await common.encrypt(JSON.stringify(errResp)))
      // return await common.encrypt(JSON.stringify(errResp))
      return JSON.stringify(errResp)
    }
    console.log('res1', errResp)
    return errResp
  }
}
