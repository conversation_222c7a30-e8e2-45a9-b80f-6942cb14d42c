'use strict'

const { graphql } = require('graphql')
const schema = require('./src/schema/ma_transaction_master/index')

const { serverlessErrorHandler } = require('./src/util/errorHandler')
const log = require('./src/util/log')

module.exports.receiptpost = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)
  }

  // middleware.logs(event)
  var payload = {}
  try {
    event.data = {
      query: 'mutation{receiptpost{status,message,respcode}}'
    }
    console.log('before decrypt data', event.data.query)

    let response = await graphql(schema, event.data.query, (err, result) => {
      if (err) {
        console.log('Error in graphql', err)
        callback(err)
      }
      console.log('handler result', result)
      // callback(null, { statusCode: 200, body: JSON.stringify(err) })
    })

    response = await serverlessErrorHandler(event, response)

    console.log('res1', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('AEPS SETTLEMENT CRON ERROR', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
