const primaryServicesRestHandler = require('./src/controller/externalApi/handlers/primaryServices/primaryServicesRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.affiliatetransactionstatus = async (event, context, callback) => {
  // const originalLogger = log.logger
  // log.logger = (logData) => {
  //   originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  // }
  log.setAwsRequestId(context.awsRequestId)
  const env = process.env.NODE_ENV || 'development'
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id', 'token'],
    isTokenRequired: true, // token verification in header,
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.TRANSACTION_STATUS.name,
    request_url: util.externalServiceList.TRANSACTION_STATUS.restEndpoint,
    callback: primaryServicesRestHandler.transactionStatus
  })
}
