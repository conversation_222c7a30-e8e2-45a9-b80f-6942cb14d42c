const primaryServicesRestHandler = require('./src/controller/externalApi/handlers/primaryServices/primaryServicesRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.merchants = async (event, context, callback) => {
  const env = process.env.NODE_ENV || 'development'
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id', 'token'],
    isTokenRequired: true, // token verification in header
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.MERCHANT_LIST.name,
    request_url: util.externalServiceList.MERCHANT_LIST.restEndpoint,
    callback: primaryServicesRestHandler.getMerchantList
  })
}
