'use strict'

const { serverlessErrorHandler } = require('./src/util/errorHandler')
var lombardInsuranceController = require('./src/controller/insurance/lombardInsuranceController')
const util = require('./src/util/util')
const log = require('./src/util/log')

module.exports.lombardpolicyexpirycron = async (event, context, callback) => {
  console.log('----RUNNING ICICI LOMBARD SACHET CRON----')
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  var payload = {}
  try {
    var response = ''
    const env = process.env.NODE_ENV || 'development'
    // const fields = util[env].cron_details
    // fields.ma_user_id = fields.mercid
    // fields.userid = fields.USER_ID
    response = await lombardInsuranceController.iciciLombardPolicyExpiryCron()
    console.log(response)
    response = await server<PERSON><PERSON>rror<PERSON>and<PERSON>(event, response)

    console.log('---<PERSON>CICI LOMBARD SACHET CRON FINAL RESPONSE---', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('---ICICI LOMBARD SACHET CRON CATCH ERROR---', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
