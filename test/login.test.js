const chai = require('chai')
const express = require('express')
const should = require('should')
const bodyParser = require('body-parser')

const expressApp = express()
const expect = chai.expect
const login = require('../src/routes/login')

const {
    runCSVTestCases,
    queryBuilder
} = require('./commonTestFunction')

const schema = require('../src/schema/ma_user_master/index')

expressApp.use(bodyParser.json())
expressApp.use('/login', login)


describe(`
============================================================================
            Login 
============================================================================
`, () => {

    describe('Running Test Cases From CSV', function () {

        const testCases = require('./testCases.json')

        const query = queryBuilder({
            schema: schema,
            params: testCase
        })

        testCases.forEach(testCase => {
            try {

                runCSVTestCases({
                    expressApp: expressApp,
                    describe: testCase.expectBehaviour,
                    query: query.query,
                    endpoint: '/login',
                    functionName: testCase.functionName,
                    fields: query.responseParams,
                })
            } catch (error) {
                console.log(error);
            }
        })
    })

})