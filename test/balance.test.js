const chai = require('chai')
// eslint-disable-next-line no-unused-vars
const should = require('should')
const express = require('express')
const user = require('../src/routes/user')
const bodyParser = require('body-parser')
const expressApp = express()
// eslint-disable-next-line no-unused-vars
const expect = chai.expect

const request = require('supertest')

expressApp.use(bodyParser.json())
expressApp.use('/user', user)
// eslint-disable-next-line no-undef
describe('GraphQL', () => {
  // eslint-disable-next-line no-undef
  it('Returns balance with id = 18999', (done) => {
    request(expressApp)
      .post('/user')
      .send({ query: '{distributers{firstname,profileid}}' })
      .expect(200)
      .end((err, res) => {
        // res will contain array with one user
        if (err) return done(err)
        res.body.data.distributers.should.have.lengthOf(15)
        done()
      })
  })
})
