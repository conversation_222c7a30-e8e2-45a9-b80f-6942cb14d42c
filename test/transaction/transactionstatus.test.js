const chai = require('chai')
const request = require('supertest')
const express = require('express')
const should = require('should')
const bodyParser = require('body-parser')

const expressApp = express()
const expect = chai.expect
const transactionStatus = require('../../src/routes/transactionstatus')
const schema = require('../../src/schema/ma_transaction_status/index')

const {
    runCSVTestCases,
    queryBuilder
} = require('../commonTestFunction')


expressApp.use(bodyParser.json())
expressApp.use('/transactionstatus', transactionStatus)

const transactionStatusTestCases = () => {
    const invalidQueries = [{
            query: ''
        },
        {
            query: 'mutation()'
        }
    ]

    invalidQueries.forEach(query => {
        it(`invalid : ${query.query}`, async () => {
            const mutationResult = await request(expressApp)
                .post('/transactionstatus')
                .send(query)
                .expect(400)
        })
    })
}

describe(`
============================================================================
            TRANSACTION STATUS 
============================================================================
`, () => {

    describe('Invalid Query', transactionStatusTestCases)


    describe('Running Test Cases From CSV', function () {

        const testCases = require('../testCases.json')

        console.log(testCases);


        testCases.forEach(testCase => {
            try {

                const query = queryBuilder({
                    schema: schema,
                    params: testCase,
                    endpoint: '/transactionstatus',
                })

                runCSVTestCases({
                    expressApp: expressApp,
                    describe: testCase,
                    query: query.query,
                    endpoint: '/transactionstatus',
                    functionName: testCase.functionName,
                    fields: query.responseParams,
                })

            } catch (error) {
                console.log(error.toString());
            }
        })
    })


})