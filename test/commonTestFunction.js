const request = require('supertest')
const should = require('should')

/**
 * 
 * @param {Object} param
 */
const runCSVTestCases = async ({
    expressApp,
    describe,
    query,
    endpoint,
    fields,
    functionName,
    successCallback,
    failCallback
}) => {
    // console.log(describe);
    it(describe.expectBehaviour, async () => {
        const mutationResult = await request(expressApp)
            .post(endpoint)
            .send(query)
            .expect(parseInt(describe.expectStatus))

        /* basic field validation */

        mutationResult.body.data.should.property(functionName)
        fields.forEach(params => {
            mutationResult.body.data[functionName].should.property(params)
        })

        if (successCallback != undefined && describe.expectStatus == '200') {
            successCallback({
                mutationResult,
                fields,
                functionName
            })

        }

        if (failCallback != undefined &&  describe.expectStatus == '400') {
            failCallback({
                mutationResult,
                fields,
                functionName
            })
        }


    })

}

/**
 * 
 * @param {*} param
 * @returns Object
 */
const queryBuilder = ({
    schema,
    params,
    endpoint
}) => {

    const querySchema = params.format == 'mutation' ?
        schema.getMutationType().getFields()[params.functionName] : schema.getQueryType().getFields()[params.functionName]

    if (!querySchema) {
        throw new Error(`Query ${params.functionName} of type "${params.format}"  Not Found in "${endpoint}"!`)
    }
    const queryParams = []

    querySchema.args.forEach(arg => {
        const argType = arg.type.toString();
        let queryParam = `${arg.name}:${params[arg.name] ?? ""}`

        if (argType.match(/string/ig)) {
            queryParam = `${arg.name}:"${params[arg.name] ?? ""}"`
        }

        if (argType.match(/boolean/ig)) {
            queryParam = `${arg.name}:"${params[arg.name] ? true : false}"`
        }
        queryParams.push(queryParam)
    })

    const requestParams =  params.responseParams ? params.responseParams :   Object.keys(querySchema.type.getFields()).join(',');

    return {
        query: {
            query: `${params.format == 'mutation' ? 'mutation' : ''}{${params.functionName}(${queryParams.join(',')}){${requestParams}}}`
        },
        responseParams: requestParams.split(',')
    }
}

module.exports = {
    runCSVTestCases,
    queryBuilder
}