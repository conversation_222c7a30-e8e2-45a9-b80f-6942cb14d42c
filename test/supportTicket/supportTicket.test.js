const chai = require('chai')
const request = require('supertest')
const express = require('express')
const should = require('should')
const expressApp = express()
const bodyParser = require('body-parser')
const expect = chai.expect
const supportTicket = require('../../src/routes/supportTicket')
const schema = require('../../src/schema/ma_support_ticket/index')
const {
    runCSVTestCases
} = require('../commonTestFunction')
const mutationName = process.env.npm_config_mutationName


expressApp.use(bodyParser.json())
expressApp.use('/supportticket', supportTicket)

describe(`
============================================================================
                  Support Ticket 
============================================================================
`, () => {

    describe('InvalidQueries Validation', () => {
        const invalidQueries = [{
                query: ''
            },
            {
                query: 'mutation()'
            },
            {
                query: 'mutation {addSupportTicket(subject:"test1",description: "test1",support_type:"Settlement",support_status: "Migrate Number",userid:1251,order_id:"90990"){status,respcode,message}}'
            }
        ]

        invalidQueries.forEach(query => {
            it(`invalid query validation check : ${query.query}`, async () => {

                const mutationResult = await request(expressApp)
                    .post('/supportticket')
                    .send(query)
                    .expect(400)
            })
        })
    })

    describe('Mutation Queries Validation', () => {
        const mutationQueries = [{
            query: 'mutation {addSupportTicket(subject:"test",description: "test",support_type:"Settlement",support_status: RAISED,userid:1251,order_id:""){status,respcode,message}}'
        }, {
            query: 'mutation {addSupportTicket(subject:"test1",description: "test1",support_type:"Settlement",support_status: RAISED,userid:1251,order_id:"90990"){status,respcode,message}}'
        }]





        mutationQueries.forEach(query => {

            runCSVTestCases({
                describe: `mutation query validation check : ${query.query}`,
                query: query,
                endpoint: '/supportticket',
                successCallback(mutationResult) {
                    mutationResult.body.data.should.property('addSupportTicket')
                    mutationResult.body.data.addSupportTicket.should.property('status')
                    mutationResult.body.data.addSupportTicket.should.property('respcode')
                    mutationResult.body.data.addSupportTicket.should.property('message')
                    mutationResult.body.data.addSupportTicket.status.should.equal(200)
                    mutationResult.body.data.addSupportTicket.respcode.should.equal(1000)
                    mutationResult.body.data.addSupportTicket.message.should.equal('Success')
                }
            })

        })
    })

    describe('Running Test Cases From CSV', async function () {

        const testCases = require('../testCases.json')

        const query = queryBuilder({
            schema: schema,
            params: testCase
        })

        testCases.forEach(testCase => {
            try {
                runCSVTestCases({
                    expressApp: expressApp,
                    describe: testCase.expectBehaviour,
                    query: query.query,
                    endpoint: '/supportticket',
                    functionName: testCase.functionName,
                    fields: query.responseParams,
                })

            } catch (error) {
                console.log(error);
            }
        })
    })

})