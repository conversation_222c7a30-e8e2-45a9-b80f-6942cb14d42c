const chai = require('chai')
const request = require('supertest')
const express = require('express')
const should = require('should')
const bodyParser = require('body-parser')

const expressApp = express()
const expect = chai.expect
const beneficiary = require('../../src/routes/beneficiary')
const schema = require('../../src/schema/ma_beneficiaries/index')

const {
    runCSVTestCases,
    queryBuilder
} = require('../commonTestFunction')


expressApp.use(bodyParser.json())
expressApp.use('/beneficiary', beneficiary)

describe(`
============================================================================
            DMT : BENEFICIARY
============================================================================
`, () => {


    describe('Running Test Cases From CSV', function () {

        const testCases = require('../testCases.json')


        testCases.forEach(testCase => {
            try {

                const query = queryBuilder({
                    schema: schema,
                    params: testCase,
                    endpoint: '/beneficiary'
                })

                runCSVTestCases({
                    expressApp: expressApp,
                    describe: testCase,
                    query: query.query,
                    endpoint: '/beneficiary',
                    functionName: testCase.functionName,
                    fields: query.responseParams,
                })

            } catch (error) {
                console.log(error.toString());
            }
        })
    })


})