const chai = require('chai')
const express = require('express')
const should = require('should')
const bodyParser = require('body-parser')

const expressApp = express()
const custlogin = require('../../src/routes/custlogin')
const schema = require('../../src/schema/ma_customer_details/index')

const {
    runCSVTestCases,
    queryBuilder
} = require('../commonTestFunction')


expressApp.use(bodyParser.json())
expressApp.use('/custlogin', custlogin)


describe(`
============================================================================
            DMT : REMITTER 
============================================================================
`, () => {

    describe('Running Test Cases From CSV', function () {

        const testCases = require('../testCases.json')


        testCases.forEach(testCase => {
            try {

                const query = queryBuilder({
                    schema: schema,
                    params: testCase,
                    endpoint: '/custlogin',
                })

                runCSVTestCases({
                    expressApp: expressApp,
                    describe: testCase,
                    query: query.query,
                    endpoint: '/custlogin',
                    functionName: testCase.functionName,
                    fields: query.responseParams,
                })

            } catch (error) {
                console.log(error.toString());
            }
        })
    })


})