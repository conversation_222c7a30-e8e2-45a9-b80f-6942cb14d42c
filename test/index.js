/* 
 command to run module level unit testing :  npm run test-module --moduleName='<folder name >' 

*/
const npm = require('npm')
const moduleName = process.env.npm_config_moduleName
const fs = require('fs')
const csvResult = [];


if (fs.existsSync(`${__dirname}\\..\\test.csv`)) {

    const csvsync = require('csvsync')

    const csv = fs.readFileSync(`${__dirname}\\..\\test.csv`)
    const csvResult = csvsync.parse(csv, {
        returnObject: true,
    })
    // console.log(csvResult)
    fs.writeFileSync(`${__dirname}\\testCases.json`, JSON.stringify(csvResult));
}

try {

    if (moduleName == undefined) {
        npm.load(() => npm.run('test'))
    }

    if (fs.existsSync(`${__dirname}\\${moduleName}`)) {
        require(`./${moduleName}/index.test`)
    }

} catch (error) {
    console.error(error);
}