const chai = require('chai')
const should = require('should')
const express = require('express')
const bodyParser = require('body-parser')
const expressApp = express()
const expect = chai.expect
const request = require('supertest')
const args = require('yargs').argv
const authentication = require('../src/routes/authentication')
const transaction = require('../src/routes/transaction')
const pointsbalance = require('../src/routes/pointsbalance')
const incentive = require('../src/routes/incentive')

expressApp.use(bodyParser.json())
expressApp.use('/authentication', authentication)
expressApp.use('/transaction', transaction)
expressApp.use('/pointsbalance', pointsbalance)
expressApp.use('/incentive', incentive)

const mobile = '9766685476'
const aggregatorTxnId = '10452365'
const aggregatorOrderId = '454fdfd405695'
const amount = 10
const checksum = 'dsds454'

let otp = 0

let merchantId = 0
const receiverId = 99999

let intSenderBalance = 0
let intReceiverBalance = 0
let txnSenderBalance = 0
let txnRecevierBalance = 0

const incentiveAmount = 4
let totalIncentivePercentage = 0
let incentiveList = []
const merchantBalancePlusIncentive = {}

describe('BBPS TEST CASES', () => {
  try {
    it('Sent OTP To Merchant', async () => {
      // console.log('Sent OTP {sentOtp(mobile:"' + mobile + '",aggregator_txn_id:"' + aggregatorTxnId + '",aggregator_order_id:"' + aggregatorOrderId + '",amount:' + amount + ',checksum:"' + checksum + '"){status,message,respcode,token,transaction{ma_user_id,aggregator_txn_id,aggregator_order_id,transaction_id,amount,bank_rrn}}}')
      const sentOtpReq = await request(expressApp)
        .post('/authentication')
        .send({ query: '{sentOtp(mobile:"' + mobile + '",aggregator_txn_id:"' + aggregatorTxnId + '",aggregator_order_id:"' + aggregatorOrderId + '",amount:' + amount + ',checksum:"' + checksum + '"){status,message,respcode,token,transaction{ma_user_id,aggregator_txn_id,aggregator_order_id,transaction_id,amount,bank_rrn}}}' })
        .expect(200)
      sentOtpReq.body.data.sentOtp.should.have.property('status').which.is.equal(200)
      sentOtpReq.body.data.sentOtp.should.have.property('message').which.is.equal('success')
      sentOtpReq.body.data.sentOtp.should.have.property('respcode')
      sentOtpReq.body.data.sentOtp.should.have.property('token')
      sentOtpReq.body.data.sentOtp.should.have.property('transaction')
      sentOtpReq.body.data.sentOtp.transaction.should.have.property('ma_user_id').which.is.a.Number()
      sentOtpReq.body.data.sentOtp.transaction.should.have.property('aggregator_txn_id')
      sentOtpReq.body.data.sentOtp.transaction.should.have.property('aggregator_order_id')
      sentOtpReq.body.data.sentOtp.transaction.should.have.property('transaction_id')
      sentOtpReq.body.data.sentOtp.transaction.should.have.property('amount').which.is.a.Number()
      sentOtpReq.body.data.sentOtp.transaction.should.have.property('bank_rrn')
      merchantId = sentOtpReq.body.data.sentOtp.transaction.ma_user_id
    })
    //  if (merchantId > 0) {
    it('Initial Merchant Balance Point ', async () => {
      const intSReq = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + merchantId + '){amount}}' })
        .expect(200)
      intSReq.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      intSenderBalance = intSReq.body.data.getBalance.amount
      console.log('Initial Merchant Balance Point :' + intSenderBalance)
    })

    it('Initial Receiver Merchant Balance Point ', async () => {
      const intRReq = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + receiverId + '){amount}}' })
        .expect(200)
      intRReq.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      intReceiverBalance = intRReq.body.data.getBalance.amount
      console.log('Initial Receiver Merchant Balance Point :' + intReceiverBalance)
    })
    it('Get Otp To Run Test Case ', async () => {
      const getOtpReq = await request(expressApp)
        .post('/authentication')
        .send({ query: '{getOtp(aggregator_order_id:"' + aggregatorOrderId + '"){status,message}}' })
        .expect(200)
      getOtpReq.body.data.getOtp.should.have.property('status').which.is.equal(200)
      getOtpReq.body.data.getOtp.should.have.property('message')
      otp = getOtpReq.body.data.getOtp.message
      console.log('Get Otp To Run Test Case ' + otp)
    })
    it('Verify OTP And Do Transaction ', async () => {
      // console.log('Verify OTP {verifyOtp(mobile:"' + mobile + '",otp:"' + otp + '",transaction_id:"' + aggregatorTxnId + '",amount:' + amount + ',aggregator_order_id:"' + aggregatorOrderId + '",checksum:"' + checksum + '"){status,message,respcode,transaction{ma_user_id,aggregator_txn_id,aggregator_order_id,transaction_id,amount,bank_rrn}}}')
      const verifyOtpReq = await request(expressApp)
        .post('/authentication')
        .send({ query: '{verifyOtp(mobile:"' + mobile + '",otp:"' + otp + '",transaction_id:"' + aggregatorTxnId + '",amount:' + amount + ',aggregator_order_id:"' + aggregatorOrderId + '",checksum:"' + checksum + '"){status,message,respcode,transaction{ma_user_id,aggregator_txn_id,aggregator_order_id,transaction_id,amount,bank_rrn}}}' })
        .expect(200)
      verifyOtpReq.body.data.verifyOtp.should.have.property('status').which.is.equal(200)
      verifyOtpReq.body.data.verifyOtp.should.have.property('message').which.is.equal('success')
      verifyOtpReq.body.data.verifyOtp.should.have.property('respcode')
      verifyOtpReq.body.data.verifyOtp.should.have.property('transaction')
      verifyOtpReq.body.data.verifyOtp.transaction.should.have.property('ma_user_id').which.is.a.Number()
      verifyOtpReq.body.data.verifyOtp.transaction.should.have.property('aggregator_txn_id')
      verifyOtpReq.body.data.verifyOtp.transaction.should.have.property('aggregator_order_id')
      verifyOtpReq.body.data.verifyOtp.transaction.should.have.property('transaction_id')
      verifyOtpReq.body.data.verifyOtp.transaction.should.have.property('amount').which.is.a.Number()
      verifyOtpReq.body.data.verifyOtp.transaction.should.have.property('bank_rrn')
      console.log('Varify Otp And Do Transaction')
    })
    it('Merchant Balance Point After Transaction ( Initial Balance should be deducted by value ' + amount + ') ', async () => {
      const txnSReq = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + merchantId + '){amount}}' })
        .expect(200)
      txnSReq.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      txnSenderBalance = txnSReq.body.data.getBalance.amount
      expect(txnSenderBalance).to.equal(intSenderBalance - amount)
      console.log('Merchant Balance Point After Transaction :' + txnSenderBalance)
    })
    it('Receiver Merchant Balance Point After Transaction ( Initial Balance should be added by value ' + amount + ') ', async () => {
      const txnRReq = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + receiverId + '){amount}}' })
        .expect(200)
      txnRReq.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      txnRecevierBalance = txnRReq.body.data.getBalance.amount
      expect(txnRecevierBalance).to.equal(intReceiverBalance + amount)
      console.log('Receiver Merchant Balance Point After Transaction :' + txnRecevierBalance)
    })
    it('Get Incentive Details ', async () => {
      const incentiveReq = await request(expressApp)
        .post('/incentive')
        .send({ query: '{getIncentive(ma_user_id:' + merchantId + ',transaction_type:BBPS){user_type,status,message,details{ma_user_id,percentage}}}' })
        .expect(200)
      incentiveReq.body.data.getIncentive.should.have.property('message').which.is.equal('Success')
      incentiveReq.body.data.getIncentive.should.have.property('status').which.is.equal(200)
      incentiveReq.body.data.getIncentive.should.have.property('details')
      expect(incentiveReq.body.data.getIncentive.details).to.be.an('array')
      incentiveList = incentiveReq.body.data.getIncentive.details
      incentiveReq.body.data.getIncentive.details.forEach((value) => {
        value.should.have.property('ma_user_id')
        value.should.have.property('percentage')
        totalIncentivePercentage += value.percentage
      })
      expect(totalIncentivePercentage).which.is.equal(100)
    })
    it('Calculate Merchant Balance Point Before Incentive ', async () => {
      incentiveList.forEach(async (value) => {
        const insBReq = await request(expressApp)
          .post('/pointsbalance')
          .send({ query: '{getBalance(ma_user_id:' + value.ma_user_id + '){amount}}' })
          .expect(200)
        insBReq.body.data.getBalance.should.have.property('amount').which.is.a.Number()
        const merchantBalanceBInc = insBReq.body.data.getBalance.amount
        const calculateAmt = (merchantBalanceBInc + (incentiveAmount * value.percentage / 100))
        merchantBalancePlusIncentive[value.ma_user_id] = calculateAmt
        console.log('Calculate Merchant Balance Point Before Incentive  ' + value.ma_user_id + ' :' + merchantBalanceBInc)
      })
    })
    it('Recon ', async () => {
      console.log('mutation{recons(input:[{ma_user_id:' + merchantId + ',amount:' + incentiveAmount + ',orderid:"' + aggregatorOrderId + '",airpayid:' + aggregatorTxnId + ',txnStatus:SUCCESS}]){status,message}}')
      const reconReq = await request(expressApp)
        .post('/recon')
        .send({ query: 'mutation{recons(input:[{ma_user_id:' + merchantId + ',amount:' + incentiveAmount + ',orderid:"' + aggregatorOrderId + '",airpayid:' + aggregatorTxnId + ',txnStatus:SUCCESS}]){status,message}}' })
        .expect(200)
      reconReq.body.data.recons.should.have.property('message').which.is.equal('Success')
      reconReq.body.data.recons.should.have.property('status').which.is.equal(200)
    })
    it('Calculate Merchant Balance Point After Incentive Added ', async () => {
      try {
        incentiveList.forEach(async (value) => {
          const incAReq = await request(expressApp)
            .post('/pointsbalance')
            .send({ query: '{getBalance(ma_user_id:' + value.ma_user_id + '){amount}}' })
            .expect(200)
          incAReq.body.data.getBalance.should.have.property('amount').which.is.a.Number()
          const merchantBalanceAInc = incAReq.body.data.getBalance.amount
          const merId = value.ma_user_id
          const merIncBal = merchantBalancePlusIncentive[merId]
          // expect(merchantBalanceAInc).to.equal(merIncBal)
          console.log('Calculate Merchant Balance Point After Incentive Added ' + value.ma_user_id + ' :' + merchantBalanceAInc)
        })
      } catch (err) {
        console.log(err)
      }
    })

//  } else {
//    console.log('MerchantId not found. Fail to run futher test cases')
//  }

    /*
    it('Resend OTP ', async () => {
      console.log('Resend OTP {resentOtp(mobile:"' + mobile + '",aggregator_order_id:"' + aggregatorOrderId + '",checksum:"' + checksum + '"){status,message,respcode}}')
      const sentOtpReq = await request(expressApp)
        .post('/authentication')
        .send({ query: '{resentOtp(mobile:"' + mobile + '",aggregator_order_id:"' + aggregatorOrderId + '",checksum:"' + checksum + '"){status,message,respcode}}' })
        .expect(200)
      sentOtpReq.body.data.resentOtp.should.have.property('status').which.is.equal(200)
      sentOtpReq.body.data.resentOtp.should.have.property('message').which.is.equal('success')
      sentOtpReq.body.data.resentOtp.should.have.property('respcode')
    })

    it('Revalidation ', async () => {
      console.log('Revalidation {transactionDetails(aggregator_order_id:"' + aggregatorOrderId + '"){transaction_status}}')
      const sentOtpReq = await request(expressApp)
        .post('/transaction')
        .send({ query: '{transactionDetails(aggregator_order_id:"' + aggregatorOrderId + '"){transaction_status}}' })
        .expect(200)
      sentOtpReq.body.data.transactionDetails.should.have.property('status').which.is.equal(200)
      sentOtpReq.body.data.transactionDetails.should.have.property('message').which.is.equal('success')
      sentOtpReq.body.data.transactionDetails.should.have.property('respcode')
    })
  */
  } catch (err) {
    console.log('Error :-' + err)
  }
})
