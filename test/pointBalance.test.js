const chai = require('chai')
const should = require('should')
const express = require('express')
const pointsbalance = require('../src/routes/pointsbalance')
const topup = require('../src/routes/topup')
const refund = require('../src/routes/refund')
const recon = require('../src/routes/recon')
const bodyParser = require('body-parser')
const expressApp = express()
const expect = chai.expect
const request = require('supertest')
const args = require('yargs').argv

expressApp.use(bodyParser.json())

expressApp.use('/pointsbalance', pointsbalance)
expressApp.use('/topup', topup)
expressApp.use('/refund', refund)
expressApp.use('/recon', recon)

/*
let lastBalance = newBalance = 0
let topUpAmount = 1
let merchantId = 18977
let orderid = 111118
let txnid = 987654321
let rrn = 1212121212
*/

/*
*
argument example
mocha test/pointBalance.test.js --topupamount=1 --merchantid=18977 --orderid=111142 --txnid=987654324 --rrn=999888777666
*
*/
let lastBalance = 0
let newBalance = 0
let finalbalance = 0
const topUpAmount = args.topupamount
const merchantId = args.merchantid
const orderid = args.orderid
const txnid = args.txnid
const rrn = args.rrn

describe('GraphQL', () => {
  try {
    it('Actual Balance Point ', async () => {
      const req1 = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + merchantId + '){amount}}' })
        .expect(200)
      req1.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      lastBalance = req1.body.data.getBalance.amount
      console.log('Actual Balance Point :' + lastBalance)
    })

    it('Top Up Amount ', async () => {
      const req2 = await request(expressApp)
        .post('/topup')
        .send({ query: 'mutation{topup(ma_user_id:' + merchantId + ',amount:' + topUpAmount + ',orderid:"' + orderid + '",airpayid:' + txnid + ',rrn:"' + rrn + '"){message,status}}' })
        .expect(200)

      req2.body.data.topup.should.have.property('message').which.is.equal('Success')
      req2.body.data.topup.should.have.property('status').which.is.equal(200)
    })

    it('Top Up Settlement ', async () => {
      const req6 = await request(expressApp)
        .post('/recon')
        .send({ query: 'mutation{recons(input:[{ma_user_id:' + merchantId + ',amount:' + topUpAmount + ',orderid:"' + orderid + '",airpayid:' + txnid + ',txnStatus:SUCCESS}]){status,message}}' })
        .expect(200)
      // console.log('RESPONSE ++++++++++++++++' + JSON.stringify(req6))
      req6.body.data.recons.should.have.property('message').which.is.equal('Success')
      req6.body.data.recons.should.have.property('status').which.is.equal(200)
    })

    it('Balance Point After Top Up ', async () => {
      const req3 = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + merchantId + '){amount}}' })
        .expect(200)

      req3.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      newBalance = req3.body.data.getBalance.amount
      expect(newBalance).to.equal(lastBalance + topUpAmount)
      console.log('Balance Point After Top Up  :' + newBalance)
    })

    it('Refund Top Up Points ', async () => {
      console.log('Refund Top Up Points mutation{refund(ma_user_id:' + merchantId + ',amount:' + topUpAmount + ',orderid:"' + orderid + '",txnid:"' + txnid + '"){message,status}}')
      const req4 = await request(expressApp)
        .post('/refund')
        .send({ query: 'mutation{refund(input:[{ma_user_id:' + merchantId + ',amount:' + topUpAmount + ',orderid:"' + orderid + '",airpayid:' + txnid + ',txnStatus:SUCCESS}]){status,message}}' })
        .expect(200)
      req4.body.data.refund.should.have.property('message').which.is.equal('Success')
      req4.body.data.refund.should.have.property('status').which.is.equal(200)
    })

    it('Balance Point After Refund ', async () => {
      const req5 = await request(expressApp)
        .post('/pointsbalance')
        .send({ query: '{getBalance(ma_user_id:' + merchantId + '){amount}}' })
        .expect(200)
      req5.body.data.getBalance.should.have.property('amount').which.is.a.Number()
      finalbalance = req5.body.data.getBalance.amount
      expect(finalbalance).to.equal(newBalance - topUpAmount)
      console.log('Balance Point After Refund :' + finalbalance)
    })
  } catch (error) {
    console.log('Error ' + error)
  }
})
