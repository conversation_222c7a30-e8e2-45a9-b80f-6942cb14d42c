const beneficiaryRestHandler = require('./src/controller/externalApi/handlers/channels/DMT/beneficiaryRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.dmtAddBeneficiaryVerifyOTP = async (event, context, callback) => {
  const env = process.env.NODE_ENV || 'development'
  // const originalLogger = log.logger
  // log.logger = (logData) => {
  //   originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  // }
  log.setAwsRequestId(context.awsRequestId)
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id', 'session_token'],
    isSessionTokenRequired: true,
    requiredChannel: ['DMT'],
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.DMT_VERIFY_ADDBENEFICIARY_OTP.name,
    request_url: util.externalServiceList.DMT_VERIFY_ADDBENEFICIARY_OTP.restEndpoint,
    callback: beneficiaryRestHandler.addBeneficiaryVerifyOTP
  })
}
