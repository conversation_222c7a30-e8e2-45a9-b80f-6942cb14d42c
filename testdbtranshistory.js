'use strict'
global.G_MYSQL_CONNECTED = require('./src/lib/db')
global.G_MYSQL_SINGLE = 'true'
const { graphql } = require('graphql')
const schema = require('./src/schema/ma_transaction_master/index')
const mysql = require('mysql')

const { serverlessErrorHandler } = require('./src/util/errorHandler')
const log = require('./src/util/log')

module.exports.testdbtranshistory = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)
  }

  // middleware.logs(event)
  var payload = {}
  try {
    let ma_user_id = '28479'
    let userid = '7556'
    if (process.env && process.env.NODE_ENV == 'staging') {
      ma_user_id = '28641'
      userid = '1249'
    }
    event.data = {
      query: `{transactionDetails(userid:${userid},ma_user_id:${ma_user_id},datefrom:"2021-02-01",dateto:"2021-02-28",limit:10,offset:0){status,respcode,message,nextFlag,transactionList{ma_user_id,aggregator_txn_id,aggregator_order_id,transaction_status,amount,addedon,transaction_type,ma_transaction_master_id,mobile_number,lts_flag,receipt_flag,bank_name}}}`
    }
    console.log('before decrypt data', event.data.query)

    let response = await graphql(schema, event.data.query, (err, result) => {
      if (err) {
        console.log('Error in graphql', err)
        callback(err)
      }
      console.log('handler result', result)
      // callback(null, { statusCode: 200, body: JSON.stringify(err) })
    })

    response = await serverlessErrorHandler(event, response)

    console.log('res1', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('TRANSFER CRON ERROR', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
