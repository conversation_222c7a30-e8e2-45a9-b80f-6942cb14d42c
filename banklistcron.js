'use strict'

const { serverlessErrorHandler } = require('./src/util/errorHandler')
var banklistController = require('./src/controller/banklist/banklistController')
const log = require('./src/util/log')

module.exports.banklistcron = async (event, context, callback) => {
  console.log('---RUNNING CRON TO CHECK BANK SERVER STATUS---')
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  try {
    var response = ''
    response = await banklistController.getBanklist()
    console.log(response)
    // response = await serverlessErrorHandler(event, response)

    console.log('---WEB TOKEN FINAL RESPONSE---', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('---WEB TOKEN CRON CATCH ERROR---', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
