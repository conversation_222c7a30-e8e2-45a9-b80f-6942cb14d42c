const depthLimit = require('graphql-depth-limit')
const graphqlHTTP = require('express-graphql')
const router = require('express').Router()
const util = require('../util/util')
const schema = require('../schema/externalApiHandlers/index')
const { graphqlUploadExpress } = require('graphql-upload-minimal')

router.get('/', graphqlHTTP({
  schema,
  graphiql: !util.isProduction(),
  validationRules: [depthLimit(10)]
}))

router.post('/',
  graphqlUploadExpress({ maxFileSize: 10000000, maxFiles: 10 }),
  graphqlHTTP({
    schema,
    graphiql: false,
    validationRules: [depthLimit(10)]
  }))

module.exports = router
