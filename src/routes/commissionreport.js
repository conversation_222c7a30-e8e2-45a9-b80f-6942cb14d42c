const depthLimit = require('graphql-depth-limit')
const graphqlHTTP = require('express-graphql')
const router = require('express').Router()
const util = require('../util/util')
const schema = require('../schema/commissionReport/index')

router.get('/', graphqlHTTP({
  schema,
  graphiql: !util.isProduction(),
  validationRules: [depthLimit(10)]
}))

router.post('/', graphqlHTTP({
  schema,
  graphiql: false,
  validationRules: [depthLimit(10)]
}))

module.exports = router
