const {
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull
} = require('graphql')
const { getrekycData, saveReKyc } = require('./type')
const { GraphQLJSON } = require('graphql-type-json')
const reKycController = require('../../controller/reKyc/reKycController')

// Defines the mutations
module.exports = {
  saveRekycData: {
    type: saveReKyc,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      campaign_id: {
        type: GraphQLInt
      },
      form_data: {
        type: GraphQLJSON
      },
      action_type: {
        type: GraphQLString
      },
      merch_action: {
        type: GraphQLString
      }
      // digilocker_param: {
      //   type: GraphQLJSON
      // }
    },
    resolve: reKycController.saveReKycData.bind(reKyc<PERSON>ontroller)
  },
  digilockerApiCall: {
    type: saveReKyc,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_no: {
        type: GraphQLString
      },
      stage: {
        type: GraphQLString
      }
    },
    resolve: reKycController.digilockerApiCall.bind(reKycController)
  }
}
