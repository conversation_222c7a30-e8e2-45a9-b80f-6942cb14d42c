const {

  GraphQLString,
  GraphQLObjectType,
  GraphQLList,
  GraphQLInt,
  GraphQLScalarType,
  GraphQLBoolean
} = require('graphql')
const scalarType = require('../commonScalar')
const { GraphQLJSON } = require('graphql-type-json')

const campaignDataList = new GraphQLObjectType({
  name: 'campaignDataList',
  description: 'Campaign Data List',
  fields: {
    ma_campaign_id: {
      type: GraphQLInt
    },
    campaign_name: {
      type: GraphQLString
    },
    form_data: {
      type: GraphQLJSON
    }
  }
})

const profileDataList = new GraphQLObjectType({
  name: 'profileDataList',
  description: 'profile Data List',
  fields: {
    first_name: {
      type: GraphQLString
    },
    last_name: {
      type: GraphQLString
    },
    address: {
      type: GraphQLString
    },
    city: {
      type: GraphQLString
    },
    state: {
      type: GraphQLString
    },
    country: {
      type: GraphQLString
    },
    email: {
      type: Graph<PERSON>String
    },
    mobile: {
      type: GraphQLString
    },
    pincode: {
      type: GraphQLInt
    },
    pan: {
      type: GraphQLString
    },
    aadhar: {
      type: GraphQLString
    }

  }
})

const getrekycData = new GraphQLObjectType({

  name: 'getrekycData',
  description: 'rekyc Data',
  fields: {
    status: {
      type: GraphQLString
    },
    respcode: {
      type: GraphQLString
    },
    action_code: {
      type: GraphQLString
    },
    message: {
      type: GraphQLString
    },
    skipCount: {
      type: GraphQLInt
    },
    priority: {
      type: GraphQLString
    },
    campaign_id: {
      type: GraphQLInt
    },
    show_skip_button: {
      type: GraphQLBoolean
    },
    profile_data: {
      type: GraphQLList(profileDataList)
    },
    campaignDataList: {
      type: GraphQLList(campaignDataList)
    }
  }

})

const saveReKyc = new GraphQLObjectType({

  name: 'saveRekyc',
  description: 'save rekyc Data',
  fields: {
    status: {
      type: GraphQLString
    },
    respcode: {
      type: GraphQLString
    },
    action_code: {
      type: GraphQLString
    },
    message: {
      type: GraphQLString
    },
    data: {
      type: GraphQLJSON
    }
  }

})

// const saveRekycData = new GraphQLObjectType({

//   name: 'saveRekycData',
//   description: 'save rekyc data',
//   fields: {
//     status: {
//       type: GraphQLString
//     },
//     respcode: {
//       type: GraphQLString
//     },
//     message: {
//       type: GraphQLString
//     }
//   }
// })

module.exports = { getrekycData, saveReKyc }
