const {
  GraphQLString,
  GraphQLNonNull,
  GraphQLInt
} = require('graphql')
const { getrekycData, saveReKyc} = require('./type')
const reKycController = require('../../controller/reKyc/reKycController')
const commonEnum = require('../commonEnum')

module.exports = {
  getrekycData: {
    type: getrekycData,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: reKycController.getrekycData.bind(reKycController)
  }
}
