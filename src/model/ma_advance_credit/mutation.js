const { GraphQLNonNull, GraphQLString, GraphQLInt, GraphQLBoolean, GraphQLFloat } = require('graphql')
const type = require('./type')
const advancecreditController = require('../../controller/advancecredit/advancecreditController')

module.exports = {

  requestGeneration: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      request_id: {
        type: GraphQLInt
      }
    },
    resolve: advancecreditController.requestGeneration.bind(advancecreditController)
  },
  getAdvanceCreditForm: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      request_id: {
        type: GraphQLInt
      }
    },
    resolve: advancecreditController.getAdvanceCreditForm.bind(advancecreditController)
  },
  submitMerchantDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      request_id: {
        type: GraphQLInt
      },
      firstname: {
        type: GraphQLString
      },
      lastname: {
        type: GraphQLString
      },
      email_id: {
        type: GraphQLString
      },
      mobile_number: {
        type: GraphQLString
      },
      merchant_type: {
        type: GraphQLString
      },
      address: {
        type: GraphQLString
      },
      personal_address: {
        type: GraphQLString
      },
      pincode: {
        type: GraphQLString
      },
      pan: {
        type: GraphQLString
      },
      pan_name: {
        type: GraphQLString
      },
      dob: {
        type: GraphQLString
      },
      fathername: {
        type: GraphQLString
      },
      gender: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.submitMerchantDetails.bind(advancecreditController)
  },
  resendOtp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: GraphQLString
      },
      aggregator_order_id: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.resendOtp.bind(advancecreditController)
  },
  verifyOtp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: GraphQLString
      },
      otp: {
        type: GraphQLString
      },
      request_id: {
        type: GraphQLInt
      }
    },
    resolve: advancecreditController.verifyOtp.bind(advancecreditController)
  },
  submitQuoteAmt: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      amount: {
        type: GraphQLFloat
      },
      request_id: {
        type: GraphQLInt
      }
    },
    resolve: advancecreditController.submitQuoteAmt.bind(advancecreditController)
  },
  generateEsignRequest: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      doc_link: {
        type: GraphQLString
      },
      request_id: {
        type: GraphQLInt
      },
      device_source: {
        type: GraphQLString
      },
      auth_mode: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.generateEsignRequest.bind(advancecreditController)
  },
  generateEsignResponse: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      xml_data: {
        type: GraphQLString
      },
      request_id: {
        type: GraphQLInt
      },
      device_source: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.generateEsignResponse.bind(advancecreditController)
  },
  getEsignStatus: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      txn_id: {
        type: GraphQLString
      },
      request_id: {
        type: GraphQLInt
      },
      device_source: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.getEsignStatus.bind(advancecreditController)
  },
  submitSignedAgreement: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      signed_doc_link: {
        type: GraphQLString
      },
      request_id: {
        type: GraphQLInt
      }
    },
    resolve: advancecreditController.submitSignedAgreement.bind(advancecreditController)
  }
}
