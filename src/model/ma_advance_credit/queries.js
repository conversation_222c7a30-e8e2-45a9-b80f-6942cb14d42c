const { GraphQLNonNull, GraphQLString, GraphQLInt } = require('graphql')
const type = require('./type')
const advancecreditController = require('../../controller/advancecredit/advancecreditController')

module.exports = {
  getAdvanceCreditForm: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      request_id: {
        type: GraphQLInt
      }
    },
    resolve: advancecreditController.getAdvanceCreditForm.bind(advancecreditController)
  },
  getMerchantDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      firstname: {
        type: GraphQLString
      },
      lastname: {
        type: GraphQLString
      },
      email_id: {
        type: GraphQLString
      },
      mobile_number: {
        type: GraphQLString
      },
      merchant_type: {
        type: GraphQLString
      },
      address: {
        type: GraphQLString
      },
      pincode: {
        type: GraphQLString
      },
      navigation: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.getMerchantDetails.bind(advancecreditController)
  },
  getRequestStatus: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      device_source: {
        type: GraphQLString
      }
    },
    resolve: advancecreditController.getRequestStatus.bind(advancecreditController)
  },
  getCreditLimit: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
    },
      request_id: {
        type: new GraphQLNonNull(GraphQLInt)
    }
  },
    resolve: advancecreditController.getCreditLimit.bind(advancecreditController)
  }
}
