const {
  GraphQLID,
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList,
  GraphQLBoolean,
  GraphQLFloat
} = require('graphql')
const commonEnum = require('../commonEnum')
const { GraphQLJSON } = require('graphql-type-json')

const advanceCreditModel = new GraphQLObjectType({
  name: 'advancecredit',
  description: 'advance credit Model',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    data: {
      type: GraphQLJSON
    },
    action_code: {
      type: GraphQLInt
    }
  }
})

module.exports = advanceCreditModel
