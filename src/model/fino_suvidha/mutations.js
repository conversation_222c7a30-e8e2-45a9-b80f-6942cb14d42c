
const {
  GraphQLInt,
  GraphQLNonNull,
  GraphQLBoolean,
  GraphQLString
} = require('graphql')
const type = require('./type')
const finoSuvidhaController = require('../../controller/finoSuvidha/finoSuvidhaController')

module.exports = {
  addBeneficiary: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
    },
    resolve: finoSuvidhaController.addBeneficiary.bind(finoSuvidhaController)
  },
}