const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLInt,
  GraphQLList
} = require('graphql')


// Beneficiary List
const BeneficiaryType = new GraphQLObjectType({
  name: 'Beneficiary',
  fields: {
    bene_name: { type: GraphQLString },
  }
});

module.exports = new GraphQLObjectType({
  name: 'fino_beneficiaries',
  description: 'Fino Beneficiaries Response',
  fields: {
    status: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    respcode: {
      type: GraphQLInt
    },
    action_code: {
      type: GraphQLInt
    },
    beneficiaries: { 
      type: new GraphQLList(BeneficiaryType) 
    }, 
  }
})
