const {
  GraphQLString,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const type = require('./type')
const finoSuvidhaController = require('../../controller/finoSuvidha/finoSuvidhaController')


module.exports = ({
    getBeneficiaryList: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
    },
    resolve: finoSuvidhaController.getBeneficiaryList.bind(finoSuvidhaController)
  }
})
