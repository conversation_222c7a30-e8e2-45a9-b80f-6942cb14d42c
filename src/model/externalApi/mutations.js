const {
  GraphQLInt, GraphQLString, GraphQLNonNull
} = require('graphql')
const type = require('./type')
const generateTokenGraphqlHandler = require('../../controller/externalApi/handlers/generateTokenGraphqlHandler')

module.exports = {
  getToken: {
    type: type,
    args: {
      affiliate_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      merchant_id: {
        type: GraphQLInt
      },
      api_key: {
        type: new GraphQLNonNull(GraphQLString)
      },
      checksum: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: generateTokenGraphqlHandler.getToken.bind(generateTokenGraphqlHandler)
  }
}
