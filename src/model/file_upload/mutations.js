const { GraphQLInt, GraphQLString, GraphQLNonNull } = require('graphql')
const { GraphQLUpload } = require('graphql-upload-minimal')
const type = require('./type')
const FileUploadController = require('../../controller/fileUpload/fileUploadController')

module.exports = {
  s3upload: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      file: {
        type: GraphQLUpload
      }
    },
    resolve: FileUploadController.s3upload.bind(FileUploadController)
  }
}
