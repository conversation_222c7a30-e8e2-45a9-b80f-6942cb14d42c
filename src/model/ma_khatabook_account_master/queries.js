const {
  GraphQLInt,
  GraphQLNonNull,
  GraphQLString
} = require('graphql')
const type = require('./type')
const khataBook = require('../../controller/khataBook/khataBookV2Controller')
const commonEnum = require('../commonEnum')

module.exports = ({
  getKhataList: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      filtertype: {
        type: commonEnum.khataFilterType
      },
      business_name: {
        type: GraphQLString
      }
    },
    resolve: khataBook.getKhataList.bind(khataBook)
  }
})
