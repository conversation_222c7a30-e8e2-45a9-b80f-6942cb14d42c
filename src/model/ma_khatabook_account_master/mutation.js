const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt
} = require('graphql')
const type = require('./type')
const khataBook = require('../../controller/khataBook/khataBookV2Controller')
const commonEnum = require('../commonEnum')

module.exports = ({
  addKhata: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      business_name: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: khataBook.addKhata.bind(khataBook)
  },
  editKhata: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      business_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: khataBook.editKhata.bind(khataBook)
  },
  deleteKhata: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: khataBook.deleteKhata.bind(khataBook)
  }
})
