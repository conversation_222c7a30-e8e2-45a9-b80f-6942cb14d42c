const { GraphQLInt, GraphQLString, GraphQLNonNull } = require('graphql')
const { GraphQLUpload } = require('graphql-upload-minimal')
const FastagPdfUploadController = require('../../controller/externalApiHandlers/fastagPdfUploadController')
const GoldHistoryController = require('../../controller/externalApiHandlers/goldHistoryController')
const loginApiController = require('../../controller/externalApiHandlers/loginApiController')
const enumType = require('../commonEnum')
const { dateScalar } = require('../commonScalar')
const type = require('./type')

module.exports = {
  forgotPassword: {
    type,
    args: {
      username: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile: {
        type: new GraphQLNonNull(GraphQLString)
      },
      source: {
        type: enumType.ExternalAPISource
      }
    },
    resolve: loginApiController.forgotPassword.bind(loginApiController)
  },
  changePassword: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      oldPassword: {
        type: new GraphQLNonNull(GraphQLString)
      },
      newPassword: {
        type: new GraphQLNonNull(GraphQLString)
      },
      confirmNewPassword: {
        type: new GraphQLNonNull(GraphQLString)
      },
      authstatus: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: loginApiController.changePassword.bind(loginApiController)
  },
  rCPdfUpload: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      source: {
        type: new GraphQLNonNull(enumType.ExternalAPISource)
      },
      customer_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      rc_front: {
        type: new GraphQLNonNull(GraphQLUpload)
      },
      rc_back: {
        type: new GraphQLNonNull(GraphQLUpload)
      }
    },
    resolve: FastagPdfUploadController.rCPdfUpload.bind(FastagPdfUploadController)
  },
  downloadGoldTransactionHistory: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      source: {
        type: new GraphQLNonNull(enumType.ExternalAPISource)
      },
      dateFrom: {
        type: new GraphQLNonNull(dateScalar)
      },
      dateTo: {
        type: new GraphQLNonNull(dateScalar)
      },
      extCustomerId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: GoldHistoryController.downloadGoldTransactionHistory.bind(GoldHistoryController)
  }
}
