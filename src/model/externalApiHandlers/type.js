const { GraphQLObjectType, GraphQLInt, GraphQLString, GraphQLNonNull } = require('graphql')
const { GraphQLJSON } = require('graphql-type-json')

module.exports = new GraphQLObjectType({
  name: 'ExternalAPI',
  description: 'External API Handlers Response Type',
  fields: {
    status: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    message: {
      type: new GraphQLNonNull(GraphQLString)
    },
    respcode: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    action_code: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    data: {
      type: GraphQLJSON
    }
  }
})
