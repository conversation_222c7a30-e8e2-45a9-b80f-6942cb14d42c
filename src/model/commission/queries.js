const {
  GraphQLNonNull,
  GraphQLInt,
  GraphQLString
} = require('graphql')
const type = require('./type')
const commissionReportController = require('../../controller/commissionReport/commissionReportController')
const { default: GraphQLJSON } = require('graphql-type-json')

// Defines the queries
module.exports = {
  getCommissionReport: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      start_date: {
        type: new GraphQLNonNull(GraphQLString)
      },
      end_date: {
        type: new GraphQLNonNull(GraphQLString)
      },
      orderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      transaction_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: commissionReportController.getCommissionReport.bind(commissionReportController)
  }

}
