const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')
const { commissionReport } = require('../../controller/listing/userController')

const commission_reports = new GraphQLObjectType({

  name: 'commissions',
  description: 'commission reports data',
  fields: {
    ma_user_id: {
      type: GraphQLString
    },
    company: {
      type: GraphQLString
    },
    user_type: {
      type: GraphQLString
    },
    merchant_name: {
      type: GraphQLString
    },
    transaction_type: {
      type: GraphQLString
    },
    order_id: {
      type: GraphQLString
    },
    transaction_amount: {
      type: GraphQLString
    },
    amount: {
      type: GraphQLString
    },
    tds_amount: {
      type: GraphQLString
    },
    tds_percent: {
      type: GraphQLString
    },
    specific_person: {
      type: GraphQLString
    },
    record_status: {
      type: GraphQLString
    },
    addedon: {
      type: GraphQLString
    }
  }
})

module.exports = new GraphQLObjectType({

  name: 'commission',
  description: 'commission response',
  fields: {
    status: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    message: {
      type: GraphQLString
    },
    respcode: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    action_code: {
      type: GraphQLInt
    },
    commission_report: {
      type: new GraphQLList(commission_reports)
    }
  }
})
