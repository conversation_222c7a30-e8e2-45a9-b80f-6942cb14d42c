const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLFloat,
  GraphQLList,
  GraphQLBoolean
} = require('graphql')
const enumType = require('../commonEnum')
const scalarType = require('../commonScalar')
const { GraphQLJSON, GraphQLJSONObject } = require('graphql-type-json')

const ondcsellerType = new GraphQLObjectType({
  name: 'ONDCSELLER',
  description: 'ondc seller details',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    action_code: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    form_data: {
      type: GraphQLJSON
    },
    data: {
      type: GraphQLJSON
    },
    url: {
      type: GraphQLString
    },
    api_params_json: {
      type: GraphQLJSON
    },
    nextFlag: {
      type: GraphQLBoolean
    },
    options: {
      type: GraphQLJSON
    }

  }
})

module.exports = ondcsellerType
