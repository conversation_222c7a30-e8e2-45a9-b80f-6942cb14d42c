const {
  GraphQLID,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLList,
  GraphQLInputObjectType
} = require('graphql')
const type = require('./type')
const enumType = require('../commonEnum')
const scalarType = require('../commonScalar')
const ondcSellerController = require('../../controller/seller/ondcSellerController')
const { GraphQLJSON } = require('graphql-type-json')

// Defines the mutations
module.exports = {

  ondcsellerRegistration: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      form_data: {
        type: new GraphQLNonNull(GraphQLJSON)
      }

    },
    resolve: ondcSellerController.ondcsellerRegistration.bind(ondcSellerController)
  },
  ondcSellerAuthentication: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: ondcSellerController.ondcSellerAuthentication.bind(ondcSellerController)
  }
}
