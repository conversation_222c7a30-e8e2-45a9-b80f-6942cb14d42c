const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean
} = require('graphql')
const type = require('./type')
const ondcSellerController = require('../../controller/seller/ondcSellerController')
const { GraphQLJSON } = require('graphql-type-json')

// Defines the queries
module.exports = {

  getSellerDynamicForm: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      seller_type: {
        type: new GraphQLNonNull(GraphQLString)
      }

    },
    resolve: ondcSellerController.getSellerDynamicForm.bind(ondcSellerController)
  },
  getSellerList: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      }

    },
    resolve: ondcSellerController.getSellerList.bind(ondcSellerController)
  },
  showOptions: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: ondcSellerController.showOptions.bind(ondcSellerController)
  }
}
