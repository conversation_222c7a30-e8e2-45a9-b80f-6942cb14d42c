const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')

const prepaidCard = new GraphQLObjectType({
  name: 'prepaidCard',
  description: 'prepaidCard response type',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    dynamicPrepaidForm: {
      type: GraphQLJSON
    },
    uniqueCustId: {
      type: GraphQLString
    },
    customerDetails: {
      type: GraphQLJSON
    },
    uploadedCustomerDocs: {
      type: GraphQLJSON
    },
    uploadFileTypes: {
      type: GraphQLJSON
    },
    data: {
      type: GraphQLJSON
    },
    orderid: {
      type: GraphQLString
    },
    registerMsg: {
      type: GraphQLString
    },
    registerTime: {
      type: GraphQLString
    },
    card_issuance_fee: {
      type: GraphQLString
    },
    total_amount: {
      type: GraphQLString
    },
    gst: {
      type: GraphQLString
    },
    aggregator_order_id: {
      type: GraphQLString
    },
    topup_amount: {
      type: GraphQLString
    },
    auth_action: {
      type: GraphQLString
    },
    airpayTransactionId: {
      type: GraphQLInt
    },
    requestId: {
      type: GraphQLString
    },
    otp: {
      type: GraphQLString
    },
    programDetails: {
      type: GraphQLJSON
    },
    airpay_charges: {
      type: GraphQLString
    },
    kyc_source: {
      type: GraphQLString
    }
  }
})

module.exports = prepaidCard
