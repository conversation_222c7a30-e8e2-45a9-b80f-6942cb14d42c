const {
  GraphQLNonNull,
  GraphQLInt,
  GraphQLString
} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const prepaidCardController = require('../../controller/prepaidCard/prepaidCardController')
const { Mobile } = require('aws-sdk')

// Defines the queries
module.exports = {
  getPrepaidCardForm: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      resident_type: {
        type: new GraphQLNonNull(commonEnum.prepaidResidentType)
      }
    },
    resolve: prepaidCardController.getPrepaidCardForm.bind(prepaidCardController)
  },
  getPrepaidCardSplitForm: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      resident_type: {
        type: new GraphQLNonNull(commonEnum.prepaidResidentType)
      },
      program_id: {
        type: GraphQLInt
      },
      mobile_code: {
        type: GraphQLString
      },
      mobile_no: {
        type: GraphQLString
      }
    },
    resolve: prepaidCardController.getPrepaidCardSplitForm.bind(prepaidCardController)
  },
  getCustomerInfo: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.getCustomerInfo.bind(prepaidCardController)
  },
  fetchUploadedDocs: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      },
      doc_type: {
        type: new GraphQLNonNull(commonEnum.prepaidUploadDocumentType)
      }
    },
    resolve: prepaidCardController.fetchUploadedDocs.bind(prepaidCardController)
  },
  getKycFileUploadType: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      resident_type: {
        type: new GraphQLNonNull(commonEnum.prepaidResidentType)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.getKycFileUploadType.bind(prepaidCardController)
  },
  generateCafPdf: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.generateCafPdf.bind(prepaidCardController)
  },
  digilockerResidentApi: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.digilockerResidentApi.bind(prepaidCardController)
  },
  calculateCharges: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      customer_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(GraphQLString)
      },
      remarks: {
        type: GraphQLString
      }
    },
    resolve: prepaidCardController.calculateCharges.bind(prepaidCardController)
  },
  getResidentKycType: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.getResidentKycType.bind(prepaidCardController)
  },
  personalDetailsApi: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      },
      kycType: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.personalDetailsApi.bind(prepaidCardController)
  },
  getProgramDropdown: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: prepaidCardController.getProgramDropdown.bind(prepaidCardController)
  },
  checkCustomerInfo: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      program_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_no: {
        type: new GraphQLNonNull(GraphQLString)
      },
      resident_type: {
        type: commonEnum.prepaidResidentType
      },
      mobile_code: {
        type: GraphQLString
      }
    },
    resolve: prepaidCardController.checkCustomerInfo.bind(prepaidCardController)
  },
  kycSuccessPage: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.kycSuccessPage.bind(prepaidCardController)
  }
}
