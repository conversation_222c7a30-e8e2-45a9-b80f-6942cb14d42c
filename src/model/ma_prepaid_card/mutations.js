const {
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull,
  DateTime,
  GraphQLBoolean,
  // GraphQLID,
  GraphQLFloat
  // GraphQLList

} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const prepaidCardController = require('../../controller/prepaidCard/prepaidCardController')

// Defines the mutations
module.exports = {
  sendOtpToCustomer: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.sendOtpToCustomer.bind(prepaidCardController)
  },
  resendOtpToCustomer: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.resendOtpToCustomer.bind(prepaidCardController)
  },
  verifyOtpFromCustomer: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.verifyOtpFromCustomer.bind(prepaidCardController)
  },
  sendOtpToMerchant: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: prepaidCardController.sendOtpToMerchant.bind(prepaidCardController)
  },
  resendOtpToMerchant: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.resendOtpToMerchant.bind(prepaidCardController)
  },
  verifyOtpFromMerchant: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.verifyOtpFromMerchant.bind(prepaidCardController)
  },
  savePrepaidCardForm: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      form_data: {
        type: new GraphQLNonNull(GraphQLString)
      },
      resident_type: {
        type: new GraphQLNonNull(commonEnum.prepaidResidentType)
      }
    },
    resolve: prepaidCardController.savePrepaidCardForm.bind(prepaidCardController)
  },
  uploadCustomerDoc: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      },
      document_expiry_date: {
        type: GraphQLString
      },
      document_no: {
        type: GraphQLString
      },
      doc_front_link: {
        type: new GraphQLNonNull(GraphQLString)
      },
      doc_back_link: {
        type: GraphQLString
      },
      uploadFileType: {
        type: new GraphQLNonNull(commonEnum.prepaidUploadDocumentType)
      },
      device_id: {
        type: GraphQLString
      }
      /* ip_address: {
        type: GraphQLString
      } */
    },
    resolve: prepaidCardController.uploadCustomerDoc.bind(prepaidCardController)
  },
  issueCardKit: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      },
      kitNo: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.issueCardKit.bind(prepaidCardController)
  },
  verifyPin: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      amount: {
        type: new GraphQLNonNull(GraphQLString)
      },
      pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.verifyPin.bind(prepaidCardController)
  },
  sendEmailOtpToCustomer: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.sendEmailOtpToCustomer.bind(prepaidCardController)
  },
  verifyEmailOtpFromCustomer: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.verifyEmailOtpFromCustomer.bind(prepaidCardController)
  },
  resendEmailOtpToCustomer: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.resendEmailOtpToCustomer.bind(prepaidCardController)
  },
  validatePanCardNo: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      pan_card_no: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.validatePanCardNo.bind(prepaidCardController)
  },
  validateAadhaarEkyc: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aadhaar_card_no: {
        type: GraphQLString
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      },
      kycOption: {
        type: new GraphQLNonNull(GraphQLString)
      },
      requestId: {
        type: GraphQLString
      },
      airpayTransactionId: {
        type: GraphQLInt
      },
      otp: {
        type: GraphQLString
      }
    },
    resolve: prepaidCardController.validateAadhaarEkyc.bind(prepaidCardController)
  },
  digilockerXmlApi: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uniqueCustId: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: prepaidCardController.digilockerXmlApi.bind(prepaidCardController)
  }
}
