const {
  GraphQLString,
  GraphQLInt,
  GraphQLNonNull,
  GraphQLList,
  GraphQLInputObjectType
} = require('graphql')
const type = require('./type')
const scalarType = require('../commonScalar')
const ondcController = require('../../controller/ondc/ondcController')
const ondcPreOrderController = require('../../controller/ondcv2/ondc.preorder.controller')
const transactionController = require('../../controller/ondcv2/ondc.transaction.controller')
const { GraphQLJSON } = require('graphql-type-json')

const productList = new GraphQLInputObjectType({
  name: 'ProductRefundList',
  description: 'Product Refund Details',
  fields: {
    product_id: {
      type: new GraphQLNonNull(GraphQLString)
    },
    reason_id: {
      type: new GraphQLNonNull(GraphQLString)
    },
    quantity: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    refund_type: {
      type: new GraphQLNonNull(GraphQLString)
    }
  }
})

const cartProductList = new GraphQLInputObjectType({
  name: 'CartProductList',
  description: 'Cart Product List',
  fields: {
    product_id: {
      type: new GraphQLNonNull(GraphQLString)
    },
    provider_id: {
      type: new GraphQLNonNull(GraphQLString)
    },
    ondc_transaction_id: {
      type: new GraphQLNonNull(GraphQLString)
    },
    quantity: {
      type: new GraphQLNonNull(GraphQLInt)
    }
  }
})

// Defines the mutations
module.exports = {
  ondcSearchItems: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      search_string: {
        type: new GraphQLNonNull(GraphQLString)
      },
      delivery_location: {
        type: new GraphQLNonNull(GraphQLString)
      },
      storeLimit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      storeOffset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ondc_transaction_id: {
        type: GraphQLString
      },
      itemsLimit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      itemsOffset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      search_type: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sort_object: {
        type: GraphQLJSON
      }
    },
    resolve: ondcPreOrderController.ondcSearchItems.bind(ondcPreOrderController)
  },
  ondcSendOtp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ondc_transaction_id: {
        type: GraphQLString
      },
      otp_for: {
        type: GraphQLString
      }
    },
    resolve: ondcController.ondcSendOtp.bind(ondcController)
  },
  ondcResendOtp: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otpOrderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ondc_transaction_id: {
        type: GraphQLString
      },
      otp_for: {
        type: GraphQLString
      }
    },
    resolve: ondcController.ondcResendOtp.bind(ondcController)
  },
  ondcFetchMailingDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp: {
        type: GraphQLString
      },
      aggregator_order_id: {
        type: GraphQLString
      },
      address_id: {
        type: GraphQLInt
      },
      ondc_transaction_id: {
        type: GraphQLString
      }
    },
    resolve: ondcController.ondcFetchMailingDetails.bind(ondcController)
  },
  ondcAddCustomerMailingAddress: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ondc_transaction_id: {
        type: GraphQLString
      },
      mobile_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      billing_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      email_id: {
        type: GraphQLString
      },
      address1: {
        type: new GraphQLNonNull(GraphQLString)
      },
      address2: {
        type: new GraphQLNonNull(GraphQLString)
      },
      country: {
        type: new GraphQLNonNull(GraphQLString)
      },
      city: {
        type: new GraphQLNonNull(GraphQLString)
      },
      state: {
        type: new GraphQLNonNull(GraphQLString)
      },
      pincode: {
        type: new GraphQLNonNull(GraphQLString)
      },
      address_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcAddCustomerMailingAddress.bind(ondcController)
  },
  ondcVerifyPin: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mailing_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mailing_type: {
        type: new GraphQLNonNull(GraphQLString)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      customer_delivery_location: {
        type: new GraphQLNonNull(GraphQLString)
      },
      cart_session_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: transactionController.ondcVerifyPin.bind(transactionController)
  },
  ondcSelectItems: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ondc_transaction_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      prov_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      product_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      product_quantity: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: ondcController.ondcSelectItems.bind(ondcController)
  },
  ondcViewCartItems: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      cart_session_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_no: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: transactionController.ondcViewCartItems.bind(transactionController)
  },
  ondcOrderHistoryVerifyOTP: {
    type,
    args: {
      otp: {
        type: new GraphQLNonNull(GraphQLString)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: ondcController.ondcOrderHistoryVerifyOTP.bind(ondcController)
  },
  ondcRefundOrder: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      product_list: {
        type: GraphQLList(productList)
      },
      otpOrderid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp: {
        type: new GraphQLNonNull(GraphQLString)
      },
      refund_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcRefundOrder.bind(ondcController)
  },
  ondcAddToCart: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ondc_transaction_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      store_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      product_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      product_quantity: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcPreOrderController.ondcCartCheckout.bind(ondcPreOrderController)
  },
  ondcCartCheckout: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      cart_product_list: {
        type: GraphQLList(cartProductList)
      },
      cart_session_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcPreOrderController.ondcCartCheckout.bind(ondcPreOrderController)
  }
}
