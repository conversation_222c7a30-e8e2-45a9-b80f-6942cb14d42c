const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLFloat,
  GraphQLList,
  GraphQLBoolean
} = require('graphql')
const enumType = require('../commonEnum')
const scalarType = require('../commonScalar')
const { GraphQLJSON, GraphQLJSONObject } = require('graphql-type-json')

const mailingDetails = new GraphQLObjectType({
  name: 'MailingDetails',
  description: 'Complete Address Information For Order Delivery',
  fields: {
    address_id: {
      type: GraphQLInt
    },
    mobile_id: {
      type: GraphQLString
    },
    billing_name: {
      type: GraphQLString
    },
    email_id: {
      type: GraphQLString
    },
    address1: {
      type: GraphQLString
    },
    address2: {
      type: GraphQLString
    },
    country: {
      type: GraphQLString
    },
    city: {
      type: GraphQLString
    },
    state: {
      type: GraphQLString
    },
    pincode: {
      type: GraphQLString
    },
    address_type: {
      type: GraphQLString
    }
  }
})

const refundItem = new GraphQLObjectType({
  name: 'RefundItem',
  description: 'Refund Item Detail',
  fields: {
    product_id: {
      type: GraphQLString
    },
    reason_id: {
      type: GraphQLString
    },
    ma_ondc_product_details_id: {
      type: GraphQLInt
    },
    quantity: {
      type: GraphQLInt
    },
    price: {
      type: GraphQLString
    },
    total_price: {
      type: GraphQLString
    }
  }
})

const refundDetails = new GraphQLObjectType({
  name: 'RefundDetails',
  description: 'Refund Details of cancelled',
  fields: {
    amount: {
      type: GraphQLString
    },
    items: {
      type: GraphQLList(refundItem)
    }
  }
})

const refundReason = new GraphQLObjectType({
  name: 'RefundReasons',
  description: 'Refund Reasons and code',
  fields: {
    code: {
      type: GraphQLString
    },
    reason: {
      type: GraphQLString
    }
  }
})

const refundableProduct = new GraphQLObjectType({
  name: 'RefundableProduct',
  description: 'Product Details of refundableProduct',
  fields: {
    product_id: {
      type: GraphQLString
    },
    product_name: {
      type: GraphQLString
    },
    product_image: {
      type: GraphQLString
    },
    product_quantity: {
      type: GraphQLInt
    },
    product_price: {
      type: GraphQLString
    },
    product_total_price: {
      type: GraphQLString
    },
    reasons: {
      type: GraphQLList(refundReason)
    }
  }
})

const ondcType = new GraphQLObjectType({
  name: 'ONDC',
  description: 'ondc details',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    search_data: {
      type: GraphQLJSON
    },
    items_data: {
      type: GraphQLJSON
    },
    ondc_transaction_id: {
      type: GraphQLString
    },
    store_next_flag: {
      type: GraphQLBoolean
    },
    items_next_flag: {
      type: GraphQLBoolean
    },
    action_code: {
      type: GraphQLInt
    },
    otpOrderid: {
      type: GraphQLString
    },
    agentMailingDetails: {
      type: GraphQLList(mailingDetails)
    },
    customerMailingDetails: {
      type: GraphQLList(mailingDetails)
    },
    order_history_data: {
      type: GraphQLJSON
    },
    item_data: {
      type: GraphQLJSON
    },
    history_next_flag: {
      type: GraphQLBoolean
    },
    contact_name: {
      type: GraphQLString
    },
    mobile: {
      type: GraphQLString
    },
    merchant_name: {
      type: GraphQLString
    },
    amount: {
      type: GraphQLString
    },
    transaction_charge: {
      type: GraphQLString
    },
    transaction_time: {
      type: GraphQLString
    },
    transaction_status: {
      type: enumType.maStatus
    },
    aggregator_order_id: {
      type: GraphQLString
    },
    cart_session_id: {
      type: GraphQLString
    },
    api_params_json: {
      type: GraphQLJSON
    },
    selectData: {
      type: GraphQLJSON
    },
    viewCartData: {
      type: GraphQLJSON
    },
    refundable_products: {
      type: GraphQLList(refundableProduct)
    },
    refund_details: {
      type: refundDetails
    }
  }
})

module.exports = ondcType
