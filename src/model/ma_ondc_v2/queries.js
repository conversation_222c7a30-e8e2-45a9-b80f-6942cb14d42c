const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean
} = require('graphql')
const type = require('./type')
const ondcController = require('../../controller/ondc/ondcController')

// Defines the queries
module.exports = {
  ondcStoreItemsViewAll: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      store_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      itemsLimit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      itemsOffset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ondc_transaction_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcStoreItemsViewAll.bind(ondcController)
  },
  ondcFetchItemsDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      store_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      product_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ondc_transaction_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcFetchItemsDetails.bind(ondcController)
  },
  ondcOrderHistory: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      historyLimit: {
        type: new GraphQLNonNull(GraphQLString)
      },
      historyOffset: {
        type: new GraphQLNonNull(GraphQLString)
      },
      datefrom: {
        type: new GraphQLNonNull(GraphQLString)
      },
      dateto: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcOrderHistory.bind(ondcController)
  },
  ondcOrderHistoryItemDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcOrderHistoryItemDetails.bind(ondcController)
  },
  ondcRefundInitiation: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      refund_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: ondcController.ondcRefundInitiation.bind(ondcController)
  }
}
