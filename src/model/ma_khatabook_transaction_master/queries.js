const {
  GraphQLInt,
  GraphQLNonNull,
  GraphQLString
} = require('graphql')
const type = require('./type')
const khataBook = require('../../controller/khataBook/khataBookV2Controller')
const commonEnum = require('../commonEnum')
const commonScalar = require('../commonScalar')

module.exports = ({
  getTransactionList: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: GraphQLString
      },
      amount: {
        type: commonScalar.amountScalar
      },
      datefrom: {
        type: GraphQLString
      },
      dateto: {
        type: GraphQLString
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }

    },
    resolve: khataBook.getTransactionList.bind(khataBook)
  },
  getCustomerWiseBalance: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      customer_name: {
        type: GraphQLString
      },
      mobile: {
        type: GraphQLString
      }
    },
    resolve: khataBook.getCustomerWiseBalance.bind(khataBook)
  },
  getAccountWiseBalance: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: khataBook.getAccountWiseBalance.bind(khataBook)
  }
})
