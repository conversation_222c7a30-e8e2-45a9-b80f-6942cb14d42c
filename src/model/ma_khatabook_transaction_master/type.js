const {
  GraphQLID,
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList
} = require('graphql')
const commonEnum = require('../commonEnum')
const scalarType = require('../commonScalar')

const Transactiondata = new GraphQLObjectType({
  name: 'KhataBookTransactionData',
  description: 'Khata Book Transaction Master Data',
  fields: {
    ma_kb_transaction_master_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    ma_kb_account_master_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    ma_user_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    userid: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    transaction_date: {
      type: new GraphQLNonNull(GraphQLString)
    },
    due_date: {
      type: new GraphQLNonNull(GraphQLString)
    },
    attachment: {
      type: GraphQLString
    },
    bill_no: { 
      type: GraphQLString
    },
    description: {
      type: GraphQLString
    },
    uuid: {
      type: new GraphQLNonNull(GraphQLString)
    },
    amount: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    closing_balance: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    credit_type: {
      type: commonEnum.khataCreditType
    },
    addedon: {
      type: new GraphQLNonNull(scalarType.dateScalar)
    },
    uploadid: {
      type: GraphQLInt
    }
  }
})

const CustomerList = new GraphQLObjectType({
  name: 'CustomerList',
  description: 'Khata Book Customer List Data',
  fields: {
    ma_user_id: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    userid: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    uuid: {
      type: new GraphQLNonNull(GraphQLString)
    },
    total_amount_given: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    total_amount_received: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    final_bal: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    email: {
      type: GraphQLString
    },
    mobile: {
      type: new GraphQLNonNull(GraphQLString)
    },
    attachment: {
      type: GraphQLString
    },
    customer_name: {
      type: new GraphQLNonNull(GraphQLString)
    },
    flag: {
      type: commonEnum.khataCreditType
    },
    updatedon: {
      type: new GraphQLNonNull(scalarType.dateScalar)
    }
  }
})

// Defines the type
module.exports = new GraphQLObjectType({
  name: 'KhataBookTransaction',
  description: 'Khata Book Transaction Master',
  fields: {
    message: {
      type: GraphQLString
    },
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    nextFlag: {
      type: GraphQLBoolean
    },
    credit_given: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    credit_received: {
      type: new GraphQLNonNull(scalarType.amountScalar)
    },
    Transactiondata: {
      type: new GraphQLList(Transactiondata)
    },
    CustomerList: {
      type: new GraphQLList(CustomerList)
    },
    action_code: {
      type: GraphQLInt
    }
  }
})
