const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt
} = require('graphql')
const type = require('./type')
const khataBook = require('../../controller/khataBook/khataBookV2Controller')
const commonEnum = require('../commonEnum')

module.exports = ({
  addCustomer: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      business_name: {
        type: GraphQLString
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile: {
        type: new GraphQLNonNull(GraphQLString)
      },
      email: {
        type: GraphQLString
      },
      address1: {
        type: GraphQLString
      },
      address2: {
        type: GraphQLString
      },
      pincode: {
        type: GraphQLInt
      },
      state: {
        type: GraphQLInt
      },
      attachment: {
        type: GraphQLString
      },
      uploadid: {
        type: GraphQLInt
      },
      city: {
        type: GraphQLInt
      }
    },
    resolve: khataBook.addCustomer.bind(khataBook)
  },
  editCustomer: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      business_name: {
        type: GraphQLString
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile: {
        type: new GraphQLNonNull(GraphQLString)
      },
      email: {
        type: GraphQLString
      },
      address1: {
        type: GraphQLString
      },
      address2: {
        type: GraphQLString
      },
      pincode: {
        type: GraphQLInt
      },
      state: {
        type: GraphQLInt
      },
      attachment: {
        type: GraphQLString
      },
      city: {
        type: GraphQLInt
      },
      uploadid: {
        type: GraphQLInt
      }
    },
    resolve: khataBook.editCustomer.bind(khataBook)
  },
  setDueDate: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      due_date: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      }

    },
    resolve: khataBook.setDueDate.bind(khataBook)
  },
  sentPaymentReminder: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      }

    },
    resolve: khataBook.sentPaymentReminder.bind(khataBook)
  },
  deleteCustomer: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: khataBook.deleteCustomer.bind(khataBook)
  }
})
