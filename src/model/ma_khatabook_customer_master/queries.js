const {
  GraphQLInt,
  GraphQLNonNull,
  GraphQLString
} = require('graphql')
const type = require('./type')
const khataBook = require('../../controller/khataBook/khataBookV2Controller')
const commonEnum = require('../commonEnum')

module.exports = ({
  getCustomerList: {
    // type: new GraphQLList(type),
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_kb_account_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile: {
        type: GraphQLString
      },
      name: {
        type: GraphQLString
      },
      business_name: {
        type: GraphQLString
      }
    },
    resolve: khataBook.getCustomerList.bind(khataBook)
  },
  reminderCron: {
    type: type,
    args: {
      limit: {
        type: GraphQLInt
      }
    },
    resolve: khataBook.reminderCron.bind(khataBook)
  },

  getCustomerBalance: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uuid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: khataBook.getCustomerBalance.bind(khataBook)
  }
})
