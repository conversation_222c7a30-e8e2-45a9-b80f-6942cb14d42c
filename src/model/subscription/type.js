const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLList
} = require('graphql')
const { default: GraphQLJSON } = require('graphql-type-json')

const merchant_subscription_data = new GraphQLObjectType({

  name: 'merchant_subscription_data',
  description: 'Subscriptions plans data',
  fields: {
    ma_subscription_plan_id: {
      type: GraphQLString
    },
    plan_duration: {
      type: GraphQLString
    },
    short_description: {
      type: GraphQLString
    },
    long_description: {
      type: GraphQLString
    },
    price: {
      type: GraphQLString
    },
    plan_status: {
      type: GraphQLString
    },
    module_type: {
      type: GraphQLString
    },
    cap_amount: {
      type: GraphQLString
    },
    incentive_share: {
      type: GraphQLString
    },
    incentive_share_applied_type: {
      type: GraphQLString
    }
  }
})

module.exports = new GraphQLObjectType({
  name: 'subscription',
  description: 'subscription response',
  fields: {
    status: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    message: {
      type: GraphQLString
    },
    respcode: {
      type: new GraphQLNonNull(GraphQLInt)
    },
    action_code: {
      type: GraphQLInt
    },
    planList: {
      type: GraphQLJSON
    },
    activePlans: {
      type: GraphQLJSON
    },
    renewedPlans: {
      type: GraphQLJSON
    },
    showRenewButton: {
      type: GraphQLString
    },
    merchant_subscription_data: {
      type: new GraphQLList(merchant_subscription_data)
    },
    aggregator_order_id: {
      type: GraphQLString
    },
    data: {
      type: GraphQLJSON
    }
  }
})

