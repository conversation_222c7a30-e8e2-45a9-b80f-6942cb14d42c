const { GraphQLNonNull, GraphQLString, GraphQLInt, GraphQLBoolean } = require('graphql')
const type = require('./type')
const SubscriptionController = require('../../controller/subscription/subscriptionController')

module.exports = {
  confirmSubscription: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: GraphQLString
      },
      pin: {
        type: GraphQLString
      },
      module_type: {
        type: GraphQLString
      },
      ma_subscription_plan_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: SubscriptionController.confirmSubscription.bind(SubscriptionController)
  }
}
