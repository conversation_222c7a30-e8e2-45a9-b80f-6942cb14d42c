const {
  GraphQLNonNull,
  GraphQLInt,
  GraphQLString
} = require('graphql')
const type = require('./type')
const subscriptionController = require('../../controller/subscription/subscriptionController.js')
const { default: GraphQLJSON } = require('graphql-type-json')

// Defines the queries
module.exports = {
  getPlansList: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      module_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: subscriptionController.getPlansList.bind(subscriptionController)
  },
  getMerchantSubscriptionStatus: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      module_type: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: subscriptionController.getMerchantSubscriptionStatus.bind(subscriptionController)
  },
  subscriptionPlan: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: subscriptionController.subscriptionPlan.bind(subscriptionController)
  }

}
