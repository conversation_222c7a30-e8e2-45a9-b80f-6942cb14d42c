const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat
} = require('graphql')
const type = require('./type')
const pobqrActivationController = require('../../controller/pobqrActivation/pobqrActivationController')

// Defines the queries
module.exports = {
  test: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
    },
    resolve: pobqrActivationController.test.bind(pobqrActivationController)
  }
}
