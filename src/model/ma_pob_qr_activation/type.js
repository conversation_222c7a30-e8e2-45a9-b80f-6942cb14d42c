const {
  GraphQLString,
  GraphQLObjectType,
  GraphQLInt,
  GraphQLJSON
} = require('graphql')

const qrDetail = new GraphQLObjectType({
  name: 'POBQR',
  description: 'POB QR ACTIVATION',
  fields: {
    status: {
      type: GraphQLInt
    },
    respcode: {
      type: GraphQLInt
    },
    message: {
      type: GraphQLString
    },
    data: {
      type: GraphQLJSON
    },
    action_code: {
      type: GraphQLInt
    }
  }
})

module.exports = qrDetail
