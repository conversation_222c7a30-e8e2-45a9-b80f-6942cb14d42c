// -------------- GET eKYC Status -----------------
/**
* @api {post} /ekyc 1) GET eKYC Status
* @apiName getEKYCStatus
* @apiGroup ekyc

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query {
    getEKycStatus(ma_user_id: 18962, userid: 111) {
      status,
      message,
      respcode,
      pendingEKyc {
        ekyc_id,
        bank_name,
        bank_logo,
        bank_status,
        biometric_kyc,
        ekyc_biometric_status,
        ekyc_otp_status
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
  <pre><code>{"query": "{getEKycStatus(ma_user_id: 18962, userid: 111){status, message, respcode, pendingEKyc{ekyc_id, bank_name, bank_logo, bank_status, biometric_kyc, ekyc_biometric_status, ekyc_otp_status}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} pendingEKyc  Array of merchant list.

* @apiSuccessExample Success-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "getEKycStatus": {
        "status": 200,
        "message": "Success",
        "respcode": 1000,
        "pendingEKyc": [
          {
              "ekyc_id": 1,
              "bank_name": "Fingpay",
              "bank_logo": "https://vyaapaar.airpay.ninja/images/favicon.ico",
              "bank_status": "A",
              "biometric_kyc": "YES",
              "ekyc_biometric_status": "PENDING",
              "ekyc_otp_status": "COMPLETED
          }
        ]
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} message  API message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
  HTTP/1.1 200 OK
  {
    "errors": [
      {
        "message": "connect ETIMEDOUT",
        "locations": [
          {
            "line": 2,
            "column": 3
          }
        ],
        "path": [
          "getCmsMerchantList"
        ]
      }
    ],
    "data": {
      "getCmsMerchantList": null
    }
  }
*/

// -------------- Get eKyc Form Data -----------------
/**
* @api {post} /ekyc 2) Get eKyc Form Data
* @apiName getEKycFormData
* @apiGroup ekyc

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getEKycFormData(
      ma_user_id: 28479,
      userid: 7556
    ){
      status,
      message,
      respcode,
      formData{
        pan,
        phone
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
  <pre><code>{"query": "{getEKycFormData(ma_user_id: 28479, userid: 7556){status, message, respcode, formData{aadhar_number, pan, phone}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} formData

* @apiSuccessExample Success-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "getEKycFormData": {
        "status": 200,
        "respcode": 1000,
        "message": "Success",
        "formData": [
          {
            "pan": "AYFOJ543H",
            "phone": "9664332419"
          }
        ]
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API message.

* @apiErrorExample Error-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "getEKycFormData": {
        "status": 400,
        "respcode": 1002,
        "message": "Fail: No Records",
      }
    }
  }
*/

// -------------- eKyc Send OTP -----------------
/**
* @api {post} /ekyc 3) eKyc Send OTP
* @apiName eKycSendOtp
* @apiGroup ekyc

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    eKycSendOtp(
      ma_user_id: 28479,
      userid: 7556,
      aadhar_number: \"************\",
      pan: \"**********\",
      phone: \"**********\",
      ekyc_id: 1,
      longitude: \"79.4808912\",
      latitude: \"17.4442488\",
      deviceIMEI: \"J9:H5:4D:9D:0Q\",
      matmSerialNumber: \"\"
    ){
      status,
      message,
      respcode,
      orderid
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
  <pre><code>{"query":"mutation{eKycSendOtp(ma_user_id: 28479, userid: 7556, aadhar_number: \"************\", pan: \"**********\", phone: \"**********\", ekyc_id: 1, longitude: \"79.4808912\", latitude: \"17.4442488\", deviceIMEI: \"J9:H5:4D:9D:0Q\", matmSerialNumber: \"\" ){status, message, respcode, orderid}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} orderid
* @apiSuccessExample Success-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "eKycSendOtp": {
        "status": 200,
        "message": "OTP sent successfully",
        "respcode": 1028,
        "orderid": "MAEKYC95421621944804"
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "getCmsAmount": {
        "status": 400,
        "respcode": 1201,
        "message": "Error: eKYC already done",
      }
    }
  }
*/

// -------------- eKyc Resend OTP -----------------
/**
* @api {post} /ekyc 4) eKyc Resend OTP
* @apiName eKycResendOtp
* @apiGroup ekyc

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    eKycResendOtp(
      ma_user_id: 28479,
      userid: 7556,
      phone: \"**********\",
      ekyc_id: 1,
      orderid: \"MAEKYC95421621944804\"
    ){
      status,
      message,
      respcode
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
  <pre><code>{"query":"mutation{eKycResendOtp(ma_user_id: 28479, userid: 7556, phone: \"**********\", ekyc_id: 1, orderid: \"MAEKYC95421621944804\"){status, message, respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
  HTTP/1.1 200 OK
   {
    "data": {
      "eKycResendOtp": {
        "status": 200,
        "message": "OTP re-sent successfully",
        "respcode": 1028
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "eKycResendOtp": {
      "status": 400,
      "respcode": 1199,
      "message": "Fail: Orderid Not Found"
    }
  }
}
*/

// -------------- eKyc Validate OTP -----------------
/**
* @api {post} /ekyc 5) eKyc Validate OTP
* @apiName eKycValidateOtp
* @apiGroup ekyc

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    eKycValidateOtp(
      ma_user_id: 28479,
      userid: 7556,
      ekyc_id: 1,
      phone: \"**********\",
      otp: \"0654082\",
      orderid: \"MAEKYC95421621944804\"
    ){
      status,
      message,
      respcode
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
  <pre><code>{"query":"mutation{eKycValidateOtp(ma_user_id: 28479, userid: 7556, ekyc_id: 1, phone: \"**********\", otp: \"0654082\", orderid: \"MAEKYC95421621944804\"){status, message, respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} respcode  API response code.
* @apiSuccessExample Success-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "eKycValidateOtp": {
        "status": 200,
        "message": "OTP validated successfully",
        "respcode": 1028
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "resendOtp": {
        "status": 400,
        "message": "Error: OTP validation failed",
        "respcode": 1028,
      }
    }
  }
*/

// -------------- eKyc Biometric Validation -----------------
/**
* @api {post} /ekyc 6) eKyc Biometric Validation
* @apiName eKycBioMetricValidation
* @apiGroup ekyc

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation{
    eKycBioMetricValidation(
      ma_user_id: 28479,
      userid: 7556,
      ekyc_id: 1,
      orderid: \"MAEKYC18271622443312\",
      phone: \"**********\",
      aadhar_number: \"************\",
      cardnumberORUID: \"eyJuYXRpb25hbEJhbmtJZGVudGlmaWNhdGlvbk51bWJlciI6bnVsbCwiaW5kaWNhdG9yZm9yVUlEIjoiMCJ9\",
      captureResponse: \"eyJlcnJDb2RlIjoiMCIsImVyckluZm8iOiJDYXB0dXJlIFN1Y2Nlc3MiLCJmQ291bnQiOiIyIiwiZlR5cGUiOiIwIiwiaUNvdW50IjoiMCIsImlUeXBlIjpudWxsLCJwQ291bnQiOiIwIiwicFR5cGUiOiIwIiwibm1Qb2ludHMiOiIzMiwzMiIsInFTY29yZSI6IjY5LDY5IiwiZHBJRCI6Ik1BTlRSQS5NU0lQTCIsInJkc0lEIjoiTUFOVFJBLkFORC4wMDEiLCJyZHNWZXIiOiIxLjAuNCIsImRjIjoiODU1ZTAzMWEtMjA0Yy00NTQ0LWIyZWYtODgyMWI1M2VkMzliIiwibWkiOiJNRlMxMDAiLCJtYyI6Ik1JSUVHakNDQXdLZ0F3SUJBZ0lHQVhnQ1FmdGZNQTBHQ1NxR1NJYjNEUUVCQ3dVQU1JSHFNU293S0FZRFZRUURFeUZFVXlCTllXNTBjbUVnVTI5bWRHVmphQ0JKYm1ScFlTQlFkblFnVEhSa0lEY3hRekJCQmdOVkJETVRPa0lnTWpBeklGTm9ZWEJoZEdnZ1NHVjRZU0J2Y0hCdmMybDBaU0JIZFdwaGNtRjBJRWhwWjJnZ1EyOTFjblFnVXlCSElFaHBaMmgzWVhreEVqQVFCZ05WQkFrVENVRm9iV1ZrWVdKaFpERVFNQTRHQTFVRUNCTUhSM1ZxWVhKaGRERWRNQnNHQTFVRUN4TVVWR1ZqYUc1cFkyRnNJRVJsY0dGeWRHMWxiblF4SlRBakJnTlZCQW9USEUxaGJuUnlZU0JUYjJaMFpXTm9JRWx1WkdsaElGQjJkQ0JNZEdReEN6QUpCZ05WQkFZVEFrbE9NQjRYRFRJeE1ETXdOVEV4TkRRME4xb1hEVEl4TURRd05ERXhOVGswTUZvd2diQXhKREFpQmdrcWhraUc5dzBCQ1FFV0ZYTjFjSEJ2Y25SQWJXRnVkSEpoZEdWakxtTnZiVEVMTUFrR0ExVUVCaE1DU1U0eEVEQU9CZ05WQkFnVEIwZFZTa0ZTUVZReEVqQVFCZ05WQkFjVENVRklUVVZFUVVKQlJERU9NQXdHQTFVRUNoTUZUVk5KVUV3eEhqQWNCZ05WQkFzVEZVSnBiMjFsZEhKcFl5Qk5ZVzUxWm1GamRIVnlaVEVsTUNNR0ExVUVBeE1jVFdGdWRISmhJRk52Wm5SbFkyZ2dTVzVrYVdFZ1VIWjBJRXgwWkRDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTHR3U0wzYk9DQkQvNVFHZDl6ZDRzaHJycGI0NEtVRVUyMm9ONTJYR1dpbFJWcVBOb1k4SVJCRWJNQWphT1Q4M2xkZ2FycVlsTnAwMVRlTEd4b2VFUlFUclNpcldOWG43Tlhlall4TVZ0US9kMHlnanU1NkJRakVhcWQyOVpROHZUeU1uZThWS2FvbWRDa015WUEzcHNvL1YyMkdGZml3SHpHdGJ6Ry91SEhzcjYrT3NhcS9IT2EwNE9jRFlLWlpoYUdVR1ptREtGZmtoWlZkTlh1VGVBdDZScmhpczF6UnJMS2JocTFkc3YzdEVGeDNVb0NTTGZ2NEVMbm5DU3ZHOElRMFY1UTRaaWl5bUF4ZUVxOHhlL1U0TnQvZVEzS1A4QXprb251elBpbWRsUmxOOHlvUG1PNUZBbEtTUFZCOE50U2syN3FScThzZS9zOHB3aEdtSzRjQ0F3RUFBVEFOQmdrcWhraUc5dzBCQVFzRkFBT0NBUUVBb0pTR2ZVWnlYSkdOYkI3TEJqSUozRnNpUE51QUJISzhLSDhGTXZibHdHWmNPeW9uWHZGVW9IcUVxamd4bzV2VFhwVDQrOFhOWmJaRk90L255N1Z1SUJqNlRIUlNwUzJ6YnowamJxWDlhY0VMUnJGbjdVSVJ1RmlGV29NMll0b2JkT0Q2dWc5NitJb0M3R0dLaytUMjZGVmt4eXh5REdSZGZzR29XaXZhMTJmdzU5RTB2d1RzZndXN1ZYVUlDVC81RFlRSWpGZ2lwdTlZWDNhSmZKcmtIMWdSY2FvN0tCTlFDSFVwOTZ1RWJ3WUlKSFUrcnJmUjQyeXgzdytZeEJENVZOODV1YzZPMzNaajdYVXp4V3BvaE9YWVlLOVd0T090eXQwNlpXTnNncTA3U2duVzB3N1k5QytmRkJQY2d0L0h1SXNNNzV0Rmh6QmlqcThNdENDTzNBPT0iLCJjaSI6IjIwMjIxMDIxIiwic2Vzc2lvbktleSI6IkpUNUxCTGFEbzJDNW5lT2lYTWg3dHAzNWlwQkVYU2w4MmlId1N5blVPbjVSc1RGcWVIa3NNN0IzdjBtb2tDVG10OHVBWGRJeCtmZG5zNDR2Z1d0YitjaVowNjhrSnJBQ1NMWlVnM1Q5M2RCUCtvVGhpUkFEUWF0dDNQb0czMlE3bkRlMUloOFpEVUY5SUVOWXQrMUNYYTRoUitzVWRMN2ZUekNwRGNoQTNqTEhSNitLdy93N0hmMmxXOWM5WENkZ3ErNVhwQTIyWDhFUlhZN0RvVXBIVG5YYnNHUmk5MWpJZlBUVVZZMzVhLzBqUkpUaVQ5cnZOZkpEQWFFWXVERXFQcjJoQlpCSzFMci9xZDhiOWJaakNseGIrUnk1Y1FuRm41Z21iRm94UFY4dFZZL2h4WUpnL1hqcDhJaFYvRHdmQkhZWURMZjlVZzZuNGVxNDl5RkdlZz09IiwiaG1hYyI6IjFaZ21Ickt4cGRLb1JWMldjczFMNEJOTjRMMUFTTmhsaHRwRzJhR0ZNQlFCOFZ3U1MzS0VnS2lXYTkwQlJrRDAiLCJQaWREYXRhdHlwZSI6IlgiLCJQaWRkYXRhIjoiTWpBeU1TMHdNeTB4TWxReE1Eb3pPRG96TkkzY1U4ZVc3SmF3RWg4SnpId1Nwdll2Wm1FMmhKRjlaMHhjVVhPTnNJc3dPRVpFN3BDOXRVeWVBa2Mrb3ljQXQyTGlkbmkzVmZSTGJ3SmY1VDkwR2hRbHY5M0U3U2RzSkNTWGhFbGhHQTJBUUJZRWNhMEhncGJ5YVB3NmxtU0ZDS1ViSFA3RXVnRmx3SjBVM1ViQWJPVnJ1TzhYcDYyeUU1QitUTXpkVE00NktoaFN4dXIxczMreTNkSHBBUUJVQ0lTejFBTTh5aDU4dWwwdGJYbHN2MEJ3a3o1bmI1THUxMUY0eEw5OWFUTGY3V3F3cDEvVSs3TkprN1JKUHlKNUVrYUdQVHRod01QVGFwN1NzaXFNL2NidjBGZ3Ntb2xwbllGdnBmOXVSeENUcURvc2gzMnR2M0xCd3dsN2hZd3ptZTBaQktIRHdSS3YzQ2pYMzNqMm56V21wQmVuS212RlBEL05iNjhVa1ZGZ0JrL0VVSlEyRzI3cmNWNGY1RmFTY0NJYk5nNkR2cXJkTDJhZ21DTVpjbGtDcmFIcGd6WUxKUmpQVVdkSEhwNFI1WjJTL2lFL045bHUrZTBOVU9EdmtWNWtOQVB5QklEdkJ5ZW5lTDQ4Y2ZYa25zdFIwdzVZODF3YStBZzJ6eVUxWUxMR0pKYTlQbDJBSXM4SXhDaWFRaWNXMlBhVWtNekdlVmluQk1HRzMzWjNYNFlDMHM5K2xLUG5LV2d3a0ROT29HaFE0Ym1zTmFLcEczazhLeFplbUNTQUZta1M3TzdNd2FXTTZEL01mdWdjMnVVeDJpU0Nrdkw5WUh5aTNnUUlyajJXVmFVbnhYQ0UwNElmNnZZcTFGQ0c0MHhiVUMxVUhOaXlyZGJid2FDU2FkTkFlTDJBQWt2M05iWFpnNCtockI1UGRPUE1rb2NxZnU5VUNDNEFmalJud0ZRSWxkMjNsZ243UXhibEE0TUFmYkVBMUJ1QlJVYXdsZm5BTkpQYXVxSk9FK21xalZvTXZKMkRsbFlsTzdOenNTMnBEQmVlQ2dSUGNNNWRiWTEvNGF2aENrQWszU1A5YVY0RGEzNHZUZi9RSDc0Z3lRRWFtM04wTXZpVnEyTVlkSjcxdmw3REN6L21PMTJHZjBoZStKZm83clR1NFdlanYyeHhJRWhRbnVDVzd1TmQrV3Bob1gyNVZicnIvRCs5QmdvdWVGeVM5OHFYT28yNE9DQktvNCtaaEFWWDdkMXhNMXZxRzArdDI4b2ZqaVI2UXpxaEIyOFNaNGF6VjJrMFdJdzRjb0hWMFluRVR2K1NOTy9VN2pRVXRwSFppdzBuTTBJS0QwazR4TnptSDdRZnJSVTdST2NJRHdxU1BFOG9MNVRUWm51K2dkeTlyNThJalM2bzQ4NEp5aHdZQUFqUFZKNktSSHdQbDQvYTRYV0haRjM2YW9QRmFHVXd3aERvL1JMSjF6NnRVVFc0bUs1SitnOTNKRUxCc1ZNNi9pQUpaVENXQ3lyYm9OdVNEbmZNNWZwQ3FZeTNMOFBlVlp4M0kwakUxRXJkNHc9PSJ9\",
      longitude: \"79.4808912\",
      latitude: \"17.4442488\",
      deviceIMEI: \"1d69276c90fd926111a69417808544db\",
      matmSerialNumber: \"\"
    ){
      status,
      message,
      respcode
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
  <pre><code>{"query":"mutation{eKycBioMetricValidation(ma_user_id: 28479, userid: 7556, ekyc_id: 1, orderid: \"MAEKYC18271622443312\", phone: \"**********\", aadhar_number: \"************\", cardnumberORUID: \"eyJuYXRpb25hbEJhbmtJZGVudGlmaWNhdGlvbk51bWJlciI6bnVsbCwiaW5kaWNhdG9yZm9yVUlEIjoiMCJ9\", captureResponse: \"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\", longitude: \"79.4808912\", latitude: \"17.4442488\", deviceIMEI: \"1d69276c90fd926111a69417808544db\", matmSerialNumber: \"\"){status, message, respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
  HTTP/1.1 200 OK
  {
    "data": {
      "eKycBioMetricValidation": {
        "status": 200,
        "message": "Biometric EKYC done successfully",
        "respcode": 1028
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 400,
      "respcode": 1028,
      "message": "Error: Biometric EKYC request failed - WADHvaluevalidationfailed"
    }
  }
}
*/
