// -------------- Withdrawal requests listing API-----------------

/**
* @api {post} /withdrawalhistory 1) Show Withdrawal requests listing.
* @apiName withdrawalHistory
* @apiGroup Withdrawal requests listing API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.647zJB0m0q9g4Cjn4xqCj07N2hVWi3WnwBxShYKL3EA\"
  "app_version" : "1.65"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
"query": "{withdrawalHistory(ma_user_id:18924,userid:11,date_from:\"2021-12-24\",date_to:\"2021-12-24\",limit : 2, offset :0,){status,message,respcode,nextFlag,historydata{orderId,,bankName,ifscCode,accountNo,accountName,amount,withdrawalStatus,updatedon,utrNo,withdrawalMode, request_date_time}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to show Withdrawal requests listing.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderId API  Order id.
* @apiSuccess {String} nextFlag API  Next Flag.
* @apiSuccess {String} bankName API bank name.
* @apiSuccess {String} ifscCode  API IFSC code.
* @apiSuccess {String} accountNo  API Account no.
* @apiSuccess {String} accountName  API Account name
* @apiSuccess {String} bankName  API bank Name.
* @apiSuccess {String} amount API amount.
* @apiSuccess {String} withdrawalStatus  API withdrawal status.
* @apiSuccess {String} utrNo  API utr No.
* @apiSuccess {String} request_date_time  API added on.
* @apiSuccess {String} updatedon  API updated on.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "withdrawalHistory": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "nextFlag": false,
            "historydata": [
                {
                    "orderId": "MACMS84001626697334",
                    "bankName": "Hdfc Bank",
                    "ifscCode": "88888",
                    "accountNo": "**************",
                    "accountName": "Srushti J",
                    "amount": "10000.00",
                    "withdrawalStatus": "PENDING",
                    "updatedon": "24-12-2021 11:51:20",
                    "utrNo": 5555,
                    "withdrawalMode": "RTGS",
                    "request_date_time": "24-12-2021 11:51:17"
                }
            ]
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} nextFlag  API Next Flag.
* @apiError {String} historydata  API History data.
* @apiErrorExample Error-Response:
HTTP/1.1 400 OK
{"data": {"withdrawalHistory": {"status": 400,"message": "Fail: Invalid Date","respcode": 1024,"nextFlag": null,"historydata": null}}}
*/
