// ---------------------------------------------- GET MERCHANT DETAILS --------------------------------------------------
/**
 * @api {post} /intratransfers 1. Get Merchant Details
 * @apiName intraTransferMerchantDetails
 * @apiGroup Intra Transfers
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:
 {
    intraTransferMerchantDetails(ma_user_id: 29244, userid: 2265,mobile: "7710804049"){status,message,respcode,merchant_details{user_type, mobile, ma_user_id, name, company, pincode, city, state,userid}}
 }

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query":"{intraTransferMerchantDetails(ma_user_id: 29244, userid: 2265,mobile: "7710804049"){status,message,respcode,merchant_details{user_type, mobile, ma_user_id, name, company, pincode, city, state,userid}}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} merchant_details  merchant details object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferMerchantDetails": {
      "status": 200,
      "message": "Success",
      "respcode": 1000
      "merchant_details": {
        "user_type": "RT",
        "mobile": "7710804049",
        "ma_user_id": "28641",
        "name": "Backend3  retailer",
        "company": "Anshu Payment Services",
        "pincode": "400708",
        "city": "Navi Mumbai",
        "state": "Maharashtra",
        "userid": "1249"
      }
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferMerchantDetails": {
      "status": 400,
      "message": "Fail: User does not exist",
      "respcode":1003
      "merchant_details": null
    }
  }
}
 */

// ---------------------------------------------- GET Charges --------------------------------------------------
/**
 * @api {post} /intratransfers 2. Get Charges
 * @apiName intraTransferCharges
 * @apiGroup Intra Transfers
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:
  {
    intraTransferCharges(
      ma_user_id: 100030,
      userid: 1249,
      corresponding_id: 100029,
      amount: 100,
      source: BALANCE_TRANSFER
    ){
      status,
      message,
      respcode,
      sender_id,
      receiver_id,
      sender_type,
      receiver_type,
      network_type,
      charge_type,
      transaction_charges,
      max_amount,
      verificationType,
      action_code
    }
  }

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query":"{intraTransferCharges(ma_user_id: 100030, userid: 1249, corresponding_id: 100029, amount: 100, source: BALANCE_TRANSFER){status, message, respcode, sender_id, receiver_id, sender_type, receiver_type, network_type, charge_type, transaction_charges, max_amount, verificationType, action_code}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {Number} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Number} sender_id  senders ma_user_id.
* @apiSuccess {Number} receiver_id  receivers ma_user_id.
* @apiSuccess {String} senders_type  senders user_type.
* @apiSuccess {String} receiver_type  receivers user_type.
* @apiSuccess {String} network_type network type.
* @apiSuccess {String} charge_type charge type.
* @apiSuccess {String} transaction_charges calculated applicable charge.
* @apiSuccess {String} max_amount Max amount allowed, if max amount is null, no limit is set else allowed amount should be less than max amount.
* @apiSuccess {String} verificationType verification process.
* @apiSuccess {1000} action_code API status code.

* @apiSuccessExample Success without any max limit:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferCharges": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "sender_id": 100030,
      "receiver_id": 100029,
      "sender_type": "RT",
      "receiver_type": "RT",
      "network_type": "SAME",
      "charge_type": "NONE",
      "transaction_charges": "0.00",
      "max_amount": null,
      "verificationType": "PIN",
      "action_code": 1000
    }
  }
}

* @apiSuccessExample Success with max limit set to 100:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferCharges": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "sender_id": 100030,
      "receiver_id": 100029,
      "sender_type": "RT",
      "receiver_type": "RT",
      "network_type": "SAME",
      "charge_type": "FIXED",
      "transaction_charges": "10.00",
      "max_amount": "100.00",
      "verificationType": "OTP",
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {Number} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {Number} sender_id  senders ma_user_id.
* @apiError {Number} receiver_id  receivers ma_user_id.
* @apiError {String} senders_type  senders user_type.
* @apiError {String} receiver_type  receivers user_type.
* @apiError {String} network_type network type.
* @apiError {String} charge_type charge type.
* @apiError {String} transaction_charges calculated applicable charge.
* @apiError {String} max_amount Max amount allowed, if max amount is null, no limit is set else allowed amount should be less than max amount.
* @apiError {String} verificationType verification process.
* @apiError {1001} action_code API status code.

* @apiErrorExample Error-Response Invalid request:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferCharges": {
      "status": 400,
      "message": "Fail: Invalid request, <param> is missing",
      "respcode": 1019,
      "sender_id": null,
      "receiver_id": null,
      "sender_type": null,
      "receiver_type": null,
      "network_type": null,
      "charge_type": null,
      "transaction_charges": null,
      "max_amount": null,
      "verificationType": null,
      "action_code": 1001
    }
  }
}

* @apiErrorExample Error-Response Invalid Enum:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferCharges": {
      "status": 400,
      "message": "Error: Invalid Enum",
      "respcode": 14002,
      "sender_id": null,
      "receiver_id": null,
      "sender_type": null,
      "receiver_type": null,
      "network_type": null,
      "charge_type": null,
      "transaction_charges": null,
      "max_amount": null,
      "verificationType": null,
      "action_code": 1001
    }
  }
}

* @apiErrorExample Error-Response Invalid Merchant Id:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferCharges": {
      "status": 400,
      "message": "Fail: Invalid Merchant Id",
      "respcode": 1137,
      "sender_id": null,
      "receiver_id": null,
      "sender_type": null,
      "receiver_type": null,
      "network_type": null,
      "charge_type": null,
      "transaction_charges": null,
      "max_amount": null,
      "verificationType": null,
      "action_code": 1001
    }
  }
}

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
  "data": {
    "intraTransferCharges": {
      "status": 400,
      "message": "Fail: No Records",
      "respcode": 1002,
      "sender_id": null,
      "receiver_id": null,
      "sender_type": null,
      "receiver_type": null,
      "network_type": null,
      "charge_type": null,
      "transaction_charges": null,
      "max_amount": null,
      "verificationType": null,
      "action_code": 1001
    }
  }
}
 */

// ---------------------------------------------- SEND OTP --------------------------------------------------
/**
 * @api {post} /intratransfers 3. Send OTP
 * @apiName sendOtp
 * @apiGroup Intra Transfers
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:
 {
    mutation{sendOtp(ma_user_id: 28641, userid: 1249, amount: 10){status, respcode, message, otpOrderid}}
 }

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query": "mutation{sendOtp(ma_user_id: 28641, userid: 1249, amount: 10){status, respcode, message, otpOrderid}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} sendOtp object.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "sendOtp": {
            "status": 200,
            "respcode": 2007,
            "message": "Success: OTP sent successfully",
            "otpOrderid": "MACW18431656412608"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "sendOtp": {
            "status": 400,
            "respcode": 1001,
            "message": "Fail: Something Went wrong",
            "otpOrderid": null
        }
    }
}
*/

// ---------------------------------------------- RESEND OTP --------------------------------------------------
/**
 * @api {post} /intratransfers 4. Resend OTP
 * @apiName resendOtp
 * @apiGroup Intra Transfers
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:
 {
    mutation{resendOtp(ma_user_id: 29244, userid: 2265, otpOrderid: "MACW44081655787551"){status, respcode, message}}
 }

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query": "mutation{resendOtp(ma_user_id: 29244, userid: 2265, otpOrderid: "MACW44081655787551"){status, respcode, message}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} sendOtp object.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resendOtp": {
            "status": 200,
            "respcode": 2007,
            "message": "Success: OTP sent successfully"
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resendOtp": {
            "status": 400,
            "respcode": 1001,
            "message": "Fail: Something Went wrong"
        }
    }
}
*/
