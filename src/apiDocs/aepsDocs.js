// -------------- AEPS Transaction -----------------
/**
* @api {post} /aepstransaction 1) AEPS transaction
* @apiName doAepsTransaction
* @apiGroup AEPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
  doAepsTransaction(
    ma_user_id:28479,
    userid:7556,
    aggregator_order_id:"MAWEB91641602594665",
    remarks:"AEPS TRANSACTION",
    mobile_number:"**********",
    customer_name:"",
    customer_aadhaar:"************",
    captureResponse:"eyJQaWREYXRhdHlwZSI6Il",
    cardnumberORUID:"****************************************************************************************************************************************************************",
    languageCode: "en",
    latitude:"19.7514798",
    longitude:"75.7138884",
    mobileNumber:"**********",
    paymentType:"B",
    requestRemarks:"",
    timestamp:"**********",
    transactionType:"BE",
    merchantUserName: "",
    merchantPin: "",
    subMerchantId: "",
    call_type: "aeps",
    bank_name: "HDFC",
    email:"<EMAIL>",
    privatekey:"6bc2ceef179b4f0b003fa9f6f163f3869505a9cdb21d84f36ba2c80ad9f511c1",
    checksum:"8324294d36e48fc5e9779cc6f88348c7",
    mer_dom:"Imh0dHBzOi8vYXBtZXJjaGFudGFwcC5ub3dwYXkuY28uaW4i"
  ){status,message,data}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{doAepsTransaction(ma_user_id:28479,userid:7556,aggregator_order_id:"MAWEB91641602594665",remarks:"AEPS TRANSACTION",mobile_number:"**********",customer_name:"",customer_aadhaar:"************",captureResponse:"eyJQaWREYXRhdHlwZSI6Il", cardnumberORUID:"****************************************************************************************************************************************************************", languageCode: "en",latitude:"19.7514798",longitude:"75.7138884",mobileNumber:"**********",paymentType:"B",requestRemarks:"",timestamp:"**********",transactionType:"BE",merchantUserName: "",merchantPin: "", subMerchantId: "",call_type: "aeps",bank_name: "HDFC",email:"<EMAIL>",privatekey:"6bc2ceef179b4f0b003fa9f6f163f3869505a9cdb21d84f36ba2c80ad9f511c1",checksum:"8324294d36e48fc5e9779cc6f88348c7",mer_dom:"Imh0dHBzOi8vYXBtZXJjaGFudGFwcC5ub3dwYXkuY28uaW4i"){status,message,data}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
"data": {
  "doAepsTransaction": {
    "status": 200,
    "message": "Success",
    "data": "eyJzdGF0dXMiOmZhbHNlLCJtZXNzYWdlIjoiRHVwbGljYXRlIEJpb21ldHJpYyBkYXRhIiwiZGF0YSI6bnVsbCwic3RhdHVzQ29kZSI6MTAwMjd9"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
"data": {
  "doAepsTransaction": {
    "status": 400,
    "respcode": 1001,
    "message": "Something went wrong"
  }
}
}
*/

// -------------- Get Mini-Statement -----------------
/**
* @api {post} /aepstransaction 2) Get Mini-Statement
* @apiName getMiniStatement
* @apiGroup AEPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  getMiniStatement(
    ma_user_id: 2861, 
    userid: 1144, 
    remarks: "xhg",
    mobile_number: "**********",
    customer_name: "",
    customer_aadhaar: "************",
    captureResponse: "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", 
    cardnumberORUID: "****************************************************************************************************************************************************************************", 
    languageCode: "en",
    latitude: "24.2825309",
    longitude: "72.1582134",
    mobileNumber: "**********",
    paymentType: "B",
    requestRemarks: "xhg",
    timestamp: "01/06/202115:28:10",
    aggregator_order_id: "MAAPP87111622541490",
    transactionType: "MS",
    merchantUserName: "",
    merchantPin: "",
    subMerchantId: "",
    call_type: "aeps",
    email: "",
    bank_name: "607280",
    privatekey: "62ca906a8bb33cdf860e05ed8ccb4c613ced70c70fbf68197a5b446417788804",
    checksum: "c1269f8f7adcb77137515d9cecec1205", 
    mer_dom: "aHR0cHM6Ly9hcG1lcmNoYW50YXBwLm5vd3BheS5jby5pbg==") {
    status
    message
    respcode
    rrn
    uidaiAuthCode
    terminalId
    customer_name
    customer_mobile
    customer_aadhaar
    transaction_time
    bank_name
    bank_balance
    lastrecord {
      Date
      Mode
      Type
      Amount
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{getMiniStatement(ma_user_id: 28619, userid: 1144,remarks:"xhg",mobile_number:"**********",customer_name:"",customer_aadhaar:"************",captureResponse:"eyJQaWREYXRhdHlwZSI6IlgiLCJQaWRkYXRhIjoiTWpBeU1TMHdOaTB3TTFReE5Ub3lOVG8wTVc1bGU0aDhwSkZXcDlYZG82S1hwVnBaZU1rcUU2Mng3VE4wb2dGTGRNQjVkLzNudURqcVJGN3NCSEtFLzZwK0FIa0htcTFtUk01bExpOTB5VGZOcjBoc3g4TkhudUMxTXFROFBoSWVscEJ0RHRQaVVaOXNldHJOYlNXb2JnVlU0VjZqclRDaUZnS2JNZzJQcjkxTHZ6blIvOGx5UWxucjhzcHFpNFVYdGJVbG95WXR1SE9FenNieXR5Y0R1eEs5c3E3NFRZZTJtOHU2b1pEZldGSlFGc3RhdGFaKzhiSXVTSzJEWHNXVTVDZjNyLy9pOEpObXJsK1FTRlpMNGVpK2ZhZlRvbTFXWjQ0T0Y2dTVwSnpjN1kxWmZtUVJlQTNzWjJHUXNXdGt4MEZaeE1ZYitDYTNwazBhS1daN3Ara244WDFyUnhPM1hIelk0Slo3c1ZMOTF0cVRKWnpHdUNJYUpzWWJFUjNxWmVZWlNDZVIrb3hsWmNuUVNLTkJJNzUyV29oUXJXcDRSczNrL0k4R211YWZCZ1orOXc4Rm11QUgzTU8yeEFwSGJnWE9OZERWMzJtUy9wTEdrSUxXZ2dEbnVhbnVsWUlFRnZkSXpTNkZMU2cwU1F5VjNlTmo2UzJ2QXppVzl6andRNkRpUE5sRHVpRDJMTVNMYysrdDZFUmZoVEJGL0hSblV3UjBKcGxZMC9wRGpXTGtEVFYwV3dWNzFnbDUvYVduUU5GeUZFWDFNWkxHTzVJbjlLQUdISmlkM1JSdGR2cEtsT1hrc3RRMm9VVEgxd0hrSnJwNi8yK3JIcEZrajRkY1N6K0NOV0Jjc3lFTFUrczNpK3I2TW5oYXQvNm9MR2pZQ3R1WXRXdy9tL0ZFaGpkbnNKRzJGSmhqZW1hYmFuRTYyWGtYdWtqQWI1Uzg5YXUxTUJTeVZwWWUxL2N4UnErYWtrZzFuRHVYbjZZTWdKNjlQekRHcTZwT2R6ZklpZ0RnU2FpYXdIL0htVXU5L2N6M1NiOFJFeG9rc21obU45OTczM0lRYUY1WmJoUmtNUWQ5U0hlb3cvRW9MK0NDV2ZYbS93MXZaZUNaeVV4Mmt4SXROSStZR3grbnBlZ1V6YVU3YTRQbU5tNmdITGVUakg5NEZxRDNpNGNyODdxTWlyQk1rQUpGcG4xQjJDWWR4eGdGTHJXUXlhYytmREFtK21sdU41WndvRnFmd2lRcWozYzZIRmYvK0hRL0kvek1nODZxa0JqQU95bGxCb0Ztb3JXc2VOOXNsUGFaWXZDUkYvY05qNitWY2Jmd2RnRnJoSkdJRGRJbXUvTWd1a2NmcXpSZ0hHc0NGSGlHakNMRlI1OE5YOVRaVmZHbTcrS2kxaTN4K09iV3NCR2UxdGt6ZUZqbEFyQWNnRjgzT0VOMXdRTEJKSDNZQitCU0xVTEpsUmlqSEo2dE9HdUhFeUFtOE8vUFhmL2ZSWDYvTklrMGxEVW10dEM5VEhBaU1UaTZHNlNadTFsMmQ4ZjNpM0NjM0haczlQSjNybDRnUmhZZVdtY2Q1S1AzTnZDSWpyVkhVNllIQlBOcE9Jc1BTcUVQUHBScW4rSWozd0tmRDhmZWNlMTJJZDdSa1o0QmlwK3BpWDFHQnUwT2JETmcrNGJHekRYVUE3SjlCY2ZjeDc5eGRHeDhJbk5PbkFyWTNnPT0iLCJjaSI6IjIwMjIxMDIxIiwiZGMiOiJhMGU3Mzg5OS01MDBiLTRlOWItOWU5Yy1hYmY4ZTA3OWI1NWQiLCJkcElEIjoiTUFOVFJBLk1TSVBMIiwiZXJyQ29kZSI6IjAiLCJlcnJJbmZvIjoiU3VjY2VzcyIsImZDb3VudCI6IjEiLCJmVHlwZSI6IjAiLCJobWFjIjoiYnc2ZjlGeWgzQXlIZFNTM1Z5a05NemE5NFJJWVZzVC82WWZLQkJsam5ieFFBYzRLQnVPSVd4S0lQb2I2VEhqbiIsImlDb3VudCI6IjAiLCJtYyI6Ik1JSUVHRENDQXdDZ0F3SUJBZ0lFQVRFdEFEQU5CZ2txaGtpRzl3MEJBUXNGQURDQjZqRXFNQ2dHQTFVRUF4TWhSRk1nVFdGdWRISmhJRk52Wm5SbFkyZ2dTVzVrYVdFZ1VIWjBJRXgwWkNBM01VTXdRUVlEVlFRekV6cENJREl3TXlCVGFHRndZWFJvSUVobGVHRWdiM0J3YjNOcGRHVWdSM1ZxWVhKaGRDQklhV2RvSUVOdmRYSjBJRk1nUnlCSWFXZG9kMkY1TVJJd0VBWURWUVFKRXdsQmFHMWxaR0ZpWVdReEVEQU9CZ05WQkFnVEIwZDFhbUZ5WVhReEhUQWJCZ05WQkFzVEZGUmxZMmh1YVdOaGJDQkVaWEJoY25SdFpXNTBNU1V3SXdZRFZRUUtFeHhOWVc1MGNtRWdVMjltZEdWamFDQkpibVJwWVNCUWRuUWdUSFJrTVFzd0NRWURWUVFHRXdKSlRqQWVGdzB5TVRBMU1qZ3dPVFUwTWpkYUZ3MHlNVEEyTVRjd056TTVNakJhTUlHd01TVXdJd1lEVlFRREV4eE5ZVzUwY21FZ1UyOW1kR1ZqYUNCSmJtUnBZU0JRZG5RZ1RIUmtNUjR3SEFZRFZRUUxFeFZDYVc5dFpYUnlhV01nVFdGdWRXWmhZM1IxY21VeERqQU1CZ05WQkFvVEJVMVRTVkJNTVJJd0VBWURWUVFIRXdsQlNFMUZSRUZDUVVReEVEQU9CZ05WQkFnVEIwZFZTa0ZTUVZReEN6QUpCZ05WQkFZVEFrbE9NU1F3SWdZSktvWklodmNOQVFrQkZoVnpkWEJ3YjNKMFFHMWhiblJ5WVhSbFl5NWpiMjB3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREJwaEg5WHM4aVRGRkN1K0xvUDJQWHhqZVBRYzd4UWtkRElmSGo4WjB2UGhhU2NTQmsvbi8vR0xYQ1pzN0JmcW1HNEJ3cUJ0WDBLMFJKd2dOMHRpbm9Kc2s0S1BqbW91c1BITTNJRkFBR3AyOVB3VDRpclQ0andsV0dzTE5heXdmRkJXMHYwSUtDQWhFWktrcmdFZUtBL0JCdU5MNytYM0RkNXhQQXYyRzgwdDJOSS93UlZwcVdPY3hvV1ZscEkwL21ha0kwa1dkcXEzYytLUyttek5lN2JOa3VqOXl4M2xJODN2SzFVUllRV2VhT1Z2YWVsOEhrK0FWTTdPNWlPdmp1T0huM3UvOW5aNW5iM3JPWEd1SVMzVlBhbFQrVjRLZXQ1WFZMWXRsdmR3N2MxcmI4MDJnZVpWcEh3clNNNno3SXpiUzBlb2pJRkRyZE1ldGVsT203QWdNQkFBRXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRzZRU2t0cE96WFBOR2FhbVNKZklOajlUVjBLZUlxYUJNd0pza2VXamxqM01PSXBESTRJVUJqSDY3SnZhbktxNTZsQTBBcWRvMHdDcDZlZ1h2OEZsVTl2MlVVcytBcmRoMERlQjN6c01tTk5FeVVqY3ZqZDB1U0gxbE1vcnlaeFk4aFVUc3ZPNWRqcXc2UnVibUEveERBM3BRVnhyZnNrS0RpSlhRMExKb2xFZmxEQkxsRUdFSGFkTUVrdkhOMkF4cUduTmpIa0tPZ1VGbU1Ib3NSZ3dMWlVHUkJGbGp2SDZsdExsY1d5MTBUeW1GZ3lzS2JSL0JTVEFMendMVXBKR01CeTIza0o1cG9vRngzUCtQUC9Udk90TkFiN1kyNDZGQ3UzcSs1cCtFbitWWmRkakRlWm5iQmVuM1RaQ2ttVVJhUzBPRFhGalpzUFlUeUJSUVJDWWZRPSIsIm1pIjoiTUZTMTAwIiwibm1Qb2ludHMiOiI0NSIsInBDb3VudCI6MCwicFR5cGUiOjAsInFTY29yZSI6IjkwIiwicmRzSUQiOiJNQU5UUkEuV0lOLjAwMSIsInJkc1ZlciI6IjEuMC4yIiwic2Vzc2lvbktleSI6IldxVEt6VE5mK3k0SEpuZzlKODFYa0I1Qy9CNG9qR2w4dHdjUE53Y3ZGUEV2Q3JxTU9zR2ZHM1MzdHo3OGpabGxLa1hCMTVXNHlteUYxSHIvTG9ua3p1SzZLS0ZEbzFYQWVjK2JGZUJKeUlNSXZYTTZxUVZxYVV4RkNCL2p5UFVqb2xXbGFBcm0vU0NTSnlZQzZvLzZBRTl6Nzk2VWVtYi8rZWoxaURTR3YrSE9mZVhkYjRyNGF5b0gyakRqcWNLc1lvRnlZbW5LdDVVWWpGY09EdEFVQ2tCYnNNMWVHSjlJSmt3Ny9ySEJkUnJqenRqQThoZjFidU9pa2ZNMTgzRlU3TnRYWGJlOG81QzZMMlI5WVQwVjZ4MGhib0NCU3pjbHZrbW00Y051MUIrdXBGWjRTQlM1Qms5Rjl4S01ZeEM0S09QTkRMRFlLTUJoOENCQVp6cEdVUT09In0=",cardnumberORUID:"****************************************************************************************************************************************************************************",languageCode:"en",latitude:"24.2825309",longitude:"72.1582134",mobileNumber:"**********",paymentType:"B",requestRemarks:"xhg",timestamp:"01/06/202115:28:10",aggregator_order_id:"MAAPP87111622541490",transactionType:"MS",merchantUserName:"",merchantPin:"",subMerchantId:"",call_type:"aeps",email:"",bank_name:"607280",privatekey:"62ca906a8bb33cdf860e05ed8ccb4c613ced70c70fbf68197a5b446417788804",checksum:"c1269f8f7adcb77137515d9cecec1205",mer_dom:"aHR0cHM6Ly9hcG1lcmNoYW50YXBwLm5vd3BheS5jby5pbg=="){status,message,respcode,rrn,uidaiAuthCode,terminalId,customer_name,customer_mobile,customer_aadhaar,transaction_time,bank_name,bank_balance,lastrecord {Date,Mode,Type,Amount}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getMiniStatement": {
      "status": 200,
      "message": "Success",
      "respcode": 0,
      "rrn": "************",
      "uidaiAuthCode": "466537",
      "terminalId": "NSD00015",
      "customer_name": "",
      "customer_mobile": "**********",
      "customer_aadhaar": "********4856",
      "transaction_time": "01/06/202115:28:10",
      "bank_name": "Bank Of Maharashtra",
      "bank_balance": "     RS.19840.03",
      "lastrecord": [
        {
          "Date": "29/03/13",
          "Mode": "D",
          "Type": " NFS/CASH WD ",
          "Amount": "100.00"
        },
        {
          "Date": "15/03/13",
          "Mode": "D",
          "Type": " CMS/0000722 ",
          "Amount": "2000.00"
        },
        {
          "Date": "15/03/13",
          "Mode": "D",
          "Type": " CMS/0000721 ",
          "Amount": "2000.00"
        },
        {
          "Date": "14/03/13",
          "Mode": "D",
          "Type": " INWARD MICR ",
          "Amount": "2000.00"
        },
        {
          "Date": "01/03/13",
          "Mode": "C",
          "Type": " *********** ",
          "Amount": "496.00"
        },
        {
          "Date": "15/02/13",
          "Mode": "D",
          "Type": " CMS/0000699 ",
          "Amount": "2000.00"
        },
        {
          "Date": "15/02/13",
          "Mode": "D",
          "Type": " CMS/0000699 ",
          "Amount": "2000.00"
        },
        {
          "Date": "28/01/13",
          "Mode": "D",
          "Type": " NFS/CASH WD ",
          "Amount": "5000.00"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getMiniStatement": {
      "status": 400,
      "message": "bank not configured",
      "respcode": null,
      "rrn": null,
      "uidaiAuthCode": null,
      "terminalId": null,
      "lastrecord": null
    }
  }
}
*/
