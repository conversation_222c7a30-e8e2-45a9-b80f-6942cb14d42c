// --------------------- GET SHOPPING CATEGORIES ----------------------
/**
* @api {post} /shopping 01) GET SHOPPING CATEGORIES
* @apiName getShoppingCategories
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getShoppingCategories(
      ma_user_id: 28479,
      userid: 7556
    ){
      status,
      message,
      respcode,
      categories{
        category_id,
        bg_url,
        logo_url,
        name,
        title,
        sub_title,
        description,
        price,
        discounted_price
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>
{
  "query": "{getShoppingCategories(ma_user_id: 28479, userid: 7556){status, message, respcode, categories{category_id, bg_url, logo_url, name, title, sub_title, description, price, discounted_price}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} merchantList  Array of merchant list.
* @apiSuccess {String} merchantList.ma_cms_merchant_on_boarding_id id of ma_cms_merchant_on_boarding.
* @apiSuccess {String} merchantList.merchant_name  name of merchant.
* @apiSuccess {String} merchantList.merchant_legal_name legal name of merchant.
* @apiSuccess {String} merchantList.client_id id as per the parent merchant.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingCategories": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "categories": [
        {
          "category_id": 1,
          "bg_url": "https://upload.wikimedia.org/wikipedia/commons/d/d9/Fastag-logo.png",
          "logo_url": "https://upload.wikimedia.org/wikipedia/commons/d/d9/Fastag-logo.png",
          "name": "Fastag",
          "title": "Zoom past the toll plazas with FASTag",
          "sub_title": "Enjoy seamless journeys. Save time & fuel.",
          "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt quas, laudantium necessitatibus mollitia repellendus ab dolorum deserunt suscipit quasi est animi. Reprehenderit praesentium nemo dolores, vitae accusamus similique, natus eaque voluptatum ",
          "price": "450.00",
          "discounted_price": "250.00"
        },
        {
          "category_id": 2,
          "bg_url": "https://www.icicibank.com/managed-assets/images/offer-zone/brand-logos/erosnow.png",
          "logo_url": "https://www.icicibank.com/managed-assets/images/offer-zone/brand-logos/erosnow.png",
          "name": "EROSNOW",
          "title": "Get Eros Now Premium",
          "sub_title": "Watch anytime, anywhere. Personalised for you.",
          "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt quas, laudantium necessitatibus mollitia repellendus ab dolorum deserunt suscipit quasi est animi. Reprehenderit praesentium nemo dolores, vitae accusamus similique, natus eaque voluptatum ",
          "price": "399.00",
          "discounted_price": "349.00"
        },
        {
          "category_id": 3,
          "bg_url": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Assumenda ducimus facere ut sint odit ex! Doloribus porro molestiae vel dolorem!",
          "logo_url": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Assumenda ducimus facere ut sint odit ex! Doloribus porro molestiae vel dolorem!",
          "name": "GoGames",
          "title": "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ullam, ab?",
          "sub_title": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Consequuntur, quia?",
          "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Assumenda ducimus facere ut sint odit ex! Doloribus porro molestiae vel dolorem!",
          "price": "15.00",
          "discounted_price": "10.00"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingCategories": {
      "status": 400,
      "message": "Fail: Something went wrong",
      "respcode": 1001,
    }
  }
}
*/

// ---------------------- GET SHOPPING PRODUCTS -----------------------
/**
* @api {post} /shopping 02) GET SHOPPING PRODUCTS
* @apiName getShoppingProducts
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getShoppingProducts(
      ma_user_id: 28479,
      userid: 7556,
      category_id: 1
    ){
      status,
      message,
      respcode,
      products{
        category_id,
        product_id,
        title,
        description,
        virtual,
        price,
        discounted_price,
        min_quantity,
        max_quantity
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>
{
  "query": "{getShoppingProducts(ma_user_id: 28479, userid: 7556, category_id: 3){status, message, respcode, products{category_id, product_id, title, description, virtual, price, discounted_price, min_quantity, max_quantity}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} ma_cms_merchant_on_boarding_id State of approval.
* @apiSuccess {String} merchant_name name of merchant.
* @apiSuccess {String} merchant_legal_name legal name of merchant.
* @apiSuccess {String} pan pan card number of merchant.
* @apiSuccess {String} gst_number gst number of merchant.
* @apiSuccess {String} email email.
* @apiSuccess {String} api_params base64 of json containing input field details, validation and attributes.
* @apiSuccess {String} cms_ma_user_id merchant id.
* @apiSuccess {String} cms_userid userid of merchant.
* @apiSuccess {String} bank_name name of bank.
* @apiSuccess {String} account_name account name.
* @apiSuccess {String} account_no account number.
* @apiSuccess {String} ifsc_code ifsc code.
* @apiSuccess {String} company_reg_no company registration number.
* @apiSuccess {String} contact_name contact name.
* @apiSuccess {String} gst_state_name gst state name.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingProducts": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "products": [
        {
          "product_id": 1,
          "title": "Car/Jeep/Van Class 4 Vehicles",
          "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt quas, laudantium necessitatibus mollitia repellendus ab dolorum deserunt suscipit quasi est animi. Reprehenderit praesentium nemo dolores, vitae accusamus similique, natus eaque voluptatum ",
          "virtual": "N",
          "price": "450.00",
          "discounted_price": "250.00",
          "min_quantity": 10,
          "max_quantity": 30
        },
        {
          "product_id": 2,
          "title": "Light Commercial Vehcle 2-axle and Mini bus",
          "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt quas, laudantium necessitatibus mollitia repellendus ab dolorum deserunt suscipit quasi est animi. Reprehenderit praesentium nemo dolores, vitae accusamus similique, natus eaque voluptatum ",
          "virtual": "Y",
          "price": "450.00",
          "discounted_price": "400.00",
          "min_quantity": 1,
          "max_quantity": 50
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingProducts": {
      "status": 400,
      "respcode": 1002,
      "message": "Fail: No Records",
    }
  }
}
*/

// ------------------------ GET SHOPPING STATES -----------------------
/**
* @api {post} /shopping 03) GET SHOPPING STATES
* @apiName getShoppingStates
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getShoppingStates(
      ma_user_id: 28479,
      userid: 7556
    ){
      status,
      message,
      respcode,
      states{
        state_id,
        state
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>
{
  "query": "{getShoppingStates(ma_user_id: 28479, userid: 7556){status, message, respcode, states{state_id, state}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingStates": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "states": [
        {
          "state_id": 1,
          "state": "Andaman and Nicobar Islands"
        },
        {
          "state_id": 2,
          "state": "Andhra Pradesh"
        },
        {
          "state_id": 3,
          "state": "Arunachal Pradesh"
        },
        {
          "state_id": 4,
          "state": "Assam"
        },
        {
          "state_id": 5,
          "state": "Bihar"
        },
        {
          "state_id": 6,
          "state": "Chandigarh"
        },
        {
          "state_id": 7,
          "state": "Chhattisgarh"
        },
        {
          "state_id": 8,
          "state": "Dadra and Nagar Haveli"
        },
        {
          "state_id": 9,
          "state": "Daman and Diu"
        },
        {
          "state_id": 10,
          "state": "Delhi"
        },
        {
          "state_id": 11,
          "state": "Goa"
        },
        {
          "state_id": 12,
          "state": "Gujarat"
        },
        {
          "state_id": 13,
          "state": "Haryana"
        },
        {
          "state_id": 14,
          "state": "Himachal Pradesh"
        },
        {
          "state_id": 15,
          "state": "Jammu and Kashmir"
        },
        {
          "state_id": 16,
          "state": "Jharkhand"
        },
        {
          "state_id": 17,
          "state": "Karnataka"
        },
        {
          "state_id": 19,
          "state": "Kerala"
        },
        {
          "state_id": 20,
          "state": "Lakshadweep"
        },
        {
          "state_id": 21,
          "state": "Madhya Pradesh"
        },
        {
          "state_id": 22,
          "state": "Maharashtra"
        },
        {
          "state_id": 23,
          "state": "Manipur"
        },
        {
          "state_id": 24,
          "state": "Meghalaya"
        },
        {
          "state_id": 25,
          "state": "Mizoram"
        },
        {
          "state_id": 26,
          "state": "Nagaland"
        },
        {
          "state_id": 29,
          "state": "Odisha"
        },
        {
          "state_id": 31,
          "state": "Pondicherry"
        },
        {
          "state_id": 32,
          "state": "Punjab"
        },
        {
          "state_id": 33,
          "state": "Rajasthan"
        },
        {
          "state_id": 34,
          "state": "Sikkim"
        },
        {
          "state_id": 35,
          "state": "Tamil Nadu"
        },
        {
          "state_id": 36,
          "state": "Telangana"
        },
        {
          "state_id": 37,
          "state": "Tripura"
        },
        {
          "state_id": 38,
          "state": "Uttar Pradesh"
        },
        {
          "state_id": 39,
          "state": "Uttarakhand"
        },
        {
          "state_id": 41,
          "state": "West Bengal"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingStates": {
      "status": 400,
      "respcode": 1002,
      "message": "Fail: No Records",
    }
  }
}
*/

// ----------------------- GET SHOPPING ADDRESS -----------------------
/**
* @api {post} /shopping 04) GET SHOPPING ADDRESS
* @apiName getShoppingAddress
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getShoppingAddress(
      ma_user_id: 28479,
      userid: 7556
    ){
      status,
      message,
      respcode,
      addresses{
        address_id,
        name,
        email,
        phone,
        address,
        pincode,
        city,
        state,
        state_id
      }
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>
{
  "query": "{getShoppingAddress(ma_user_id: 28479, userid: 7556){status, message, respcode, addresses{address_id, name, email, phone, address, pincode, city, state, state_id}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingAddress": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "addresses": [
        {
          "address_id": 3,
          "name": "Elton Test 1",
          "email": "<EMAIL>",
          "phone": "7710804049",
          "address": "Kolar Gold Field",
          "pincode": "444444",
          "city": "Kolar",
          "state": "Karnataka"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingAddress": {
      "status": 400,
      "respcode": 1002,
      "message": "Fail: No Records"
    }
  }
}
*/

// ----------------------- ADD SHOPPING ADDRESS -----------------------
/**
* @api {post} /shopping 05) ADD SHOPPING ADDRESS
* @apiName addShoppingAddress
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    addShoppingAddress(
      ma_user_id: 28479,
      userid: 7556,
      name: "Elton Test 1",
      email: "<EMAIL>",
      phone: "7710804049",
      address: "Kolar Gold Field",
      pincode: "444444",
      state_id: 17,
      city: "Kolar"
    ){status,
      message,
      respcode
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>
{
  "query": "mutation{addShoppingAddress(ma_user_id: 28479, userid: 7556, name: \"Elton Test 1\", email: \"<EMAIL>\", phone: \"7710804049\", address: \"Kolar Gold Field\", pincode: \"444444\", state_id: 17, city: \"Kolar\"){status, message, respcode}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addShoppingAddress": {
      "status": 200,
      "message": "Success",
      "respcode": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addShoppingAddress": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
    }
  }
}
*/

// ---------------------- UPDATE SHOPPING ADDRESS ---------------------
/**
* @api {post} /shopping 06) UPDATE SHOPPING ADDRESS
* @apiName updateShoppingAddress
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation{
    updateShoppingAddress(
      ma_user_id: 28479,
      userid: 7556,
      address_id: 1,
      name: "Elton Test 1",
      email: "<EMAIL>",
      phone: "7710804049",
      address: "Kolar Gold Field",
      pincode: "444444",
      address_status: "I"
    ){
      status,
      message,
      respcode
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{updateShoppingAddress(ma_user_id: 28479, userid: 7556, address_id: 1, name: \"Elton Test 1\", email: \"<EMAIL>\", phone: \"7710804049\", address: \"Kolar Gold Field\", pincode: \"444444\", address_status: \"I\"){status, message, respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "updateShoppingAddress": {
      "status": 200,
      "message": "Success",
      "respcode": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "updateShoppingAddress": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// ---------------------- VALIDATE SHOPPING ORDER ---------------------
/**
* @api {post} /shopping 07) VALIDATE SHOPPING ORDER
* @apiName validateShoppingOrder
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    validateShoppingOrder(
      ma_user_id: 28479,
      userid: 7556,
      category_id: 1,
      product_id: 1,
      quantity: 1
    ){
      status,
      message,
      respcode,
      orderid,
      amount,
      delivery_charges,
      total_amount
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{validateShoppingOrder(ma_user_id: 28479, userid: 7556, category_id: 1, product_id: 1, quantity: 1){status, message, respcode, orderid, amount, delivery_charges, total_amount}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "validateShoppingOrder": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "orderid": "MASHP00671625826193",
      "amount": "550.00",
      "delivery_charges": "60.00",
      "total_amount": "610.00"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "validateShoppingOrder": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// ---------------------- PURCHASE SHOPPING ORDER ---------------------
/**
* @api {post} /shopping 08) PURCHASE SHOPPING ORDER -
* @apiName purchaseShoppingOrder
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    purchaseShoppingOrder(
      ma_user_id: 28479,
      userid: 7556,
      ip:"127.0.0.1",
      pin: "1234" orderid: "MASHP19491629207480",
      address_id: 3,
      name: "Elton",
      phone: "7710804049",
      email: "<EMAIL>"
  ){
    status,
    message,
    respcode,
    orderHistory{
      orderid,
      category,
      logo_url,
      product,
      quantity,
      amount,
      delivery_charges,
      current_status,
      virtual,
      name,
      email,
      phone,
      address,
      pincode,
      city,
      state,
      shipping_status,
      total_amount
    }
  }
}
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{purchaseShoppingOrder(ma_user_id: 28479, userid: 7556, ip:\"127.0.0.1\", pin: \"1234\" orderid: \"MASHP19491629207480\", address_id: 3, name: \"Elton\", phone: \"7710804049\", email: \"<EMAIL>\"){status, message, respcode, orderHistory{orderid, category, logo_url, product, quantity, amount, delivery_charges, current_status, virtual, name, email, phone, address, pincode, city, state, shipping_status, total_amount}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "purchaseShoppingOrder": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "orderHistory": [
        {
          "orderid": "MASHP54211627395489",
          "category": "Fastag",
          "logo_url": "https://upload.wikimedia.org/wikipedia/commons/d/d9/Fastag-logo.png",
          "product": "Car/Jeep/Van Class 4 Vehicles",
          "quantity": 2,
          "amount": "500.00",
          "delivery_charges": "60.00",
          "current_status": "PENDING",
          "virtual": "N",
          "name": "Elton Test 1",
          "email": "<EMAIL>",
          "phone": "7710804049",
          "address": "Kolar Gold Field",
          "pincode": "444444",
          "city": "Kolar",
          "state": "Karnataka",
          "shipping_status": "INITIATED",
          "total_amount": "610.00"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "purchaseShoppingOrder": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// -------------------- GET SHOPPING ORDER HISTORY --------------------
/**
* @api {post} /shopping 09) GET SHOPPING ORDER HISTORY
* @apiName getShoppingOrderHistory
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example Default:
  {
    getShoppingOrderHistory(
      ma_user_id:28479,
      userid:7556
    ){
      status,
      message,
      respcode,
      nextFlag,
      orderHistory{
        orderid,
        category,
        product,
        quantity,
        total_amount,
        transaction_date,
        transaction_status
      }
    }
  }

* @apiParamExample {json} Request-Example Orderid Filter:
  {
    getShoppingOrderHistory(
      ma_user_id:28479,
      userid:7556,
      orderid: "MASHP13471629978405"
    ){
      status,
      message,
      respcode,
      nextFlag,
      orderHistory{
        orderid,
        category,
        product,
        quantity,
        total_amount,
        transaction_date,
        transaction_status
      }
    }
  }

* @apiParamExample {json} Request-Example Date Filter:
  {
    getShoppingOrderHistory(
      ma_user_id:28479,
      userid:7556,
      fromDate: "29-07-2021",
      toDate: "29-08-2021"
    ){
      status,
      message,
      respcode,
      nextFlag,
      orderHistory{
        orderid,
        category,
        product,
        quantity,
        total_amount,
        transaction_date,
        transaction_status
      }
    }
  }

* @apiParamExample {json} Request-Example Date Filter:
  {
    getShoppingOrderHistory(
      ma_user_id:28479,
      userid:7556,
      fromDate: "29-07-2021",
      toDate: "29-08-2021"
    ){
      status,
      message,
      respcode,
      nextFlag,
      orderHistory{
        orderid,
        category,
        product,
        quantity,
        total_amount,
        transaction_date,
        transaction_status
      }
    }
  }

* @apiParamExample {json} Request-Example Pagination:
  {
    getShoppingOrderHistory(
      ma_user_id:28479,
      userid:7556,
      limit: 10,
      offset: 0
    ){
      status,
      message,
      respcode,
      nextFlag,
      orderHistory{
        orderid,
        category,
        product,
        quantity,
        total_amount,
        transaction_date,
        transaction_status
      }
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query": "{getShoppingOrderHistory(ma_user_id:28479, userid:7556, limit: 1, offset: 0, orderid: \"  \"){status, message, respcode, nextFlag, orderHistory{orderid, category, product, quantity, total_amount, transaction_date, transaction_status}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingOrderHistory": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "nextFlag": true,
      "orderHistory": [
        {
          "orderid": "MASHP13471629978405",
          "category": "GoGames",
          "product": "Game Pass",
          "quantity": 1,
          "total_amount": "10.00",
          "transaction_date": "26-08-2021 05:25:23 PM",
          "transaction_status": "SUCCESS"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingOrderHistory": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// -------------------- GET SHOPPING ORDER HISTORY RECEIPT --------------------
/**
* @api {post} /shopping 09) GET SHOPPING ORDER HISTORY RECEIPT
* @apiName getShoppingOrderHistory
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getShoppingOrderHistory(
      ma_user_id:28479,
      userid:7556,
      limit: 10,
      offset: 0
    ){
      status,
      message,
      respcode,
      nextFlag,
      orderHistory{
        orderid,
        category,
        logo_url,
        product,
        quantity,
        amount,
        delivery_charges,
        current_status,
        virtual,
        name,
        email,
        phone,
        address,
        pincode,
        city,
        state,
        shipping_status,
        total_amount,
        transaction_date,
        partner_unique_id
      }
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getShoppingOrderHistory(ma_user_id:28479, userid:7556, limit: 10, offset: 0, orderid: \"  \"){status, message, respcode, nextFlag, orderHistory{orderid, category, logo_url, product, quantity, amount, delivery_charges, current_status, virtual, name, email, phone, address, pincode, city, state, shipping_status, total_amount, transaction_date, partner_unique_id}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getShoppingOrderHistory": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderHistory": [
                {
                    "orderid": "MASHP41171627392268",
                    "category": "Fastag",
                    "logo_url": "https://upload.wikimedia.org/wikipedia/commons/d/d9/Fastag-logo.png",
                    "product": "Car/Jeep/Van Class 4 Vehicles",
                    "quantity": 2,
                    "amount": "500.00",
                    "delivery_charges": "60.00",
                    "current_status": "INITIATED",
                    "virtual": "N",
                    "name": "Elton Test 1",
                    "email": "<EMAIL>",
                    "phone": "7710804049",
                    "address": "Kolar Gold Field",
                    "pincode": "444444",
                    "city": "Kolar",
                    "state": "Karnataka",
                    "shipping_status": "INITIATED",
                    "total_amount": "560.00"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingOrderHistory": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// -------------- GET SHOPPING TRANSACTION ORDER RECEIPT --------------
/**
* @api {post} /shopping 10) GET SHOPPING TRANSACTION ORDER RECEIPT
* @apiName getShoppingTransactionOrderReceipt
* @apiGroup SHOPPING

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getShoppingTransactionOrderReceipt(
      ma_user_id:28479,
      userid:7556,
      aggregator_order_id: "MASHP76181627885513"
    ){
      status,
      message,
      respcode,
      mobile_number,
      amount,
      aggregator_order_id,
      transaction_charge,
      transaction_time,
      transaction_status,
      total_amount,
      delivery_charges,
      delivery_orderid,
      product_name
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getShoppingTransactionOrderReceipt(ma_user_id:28479, userid:7556, aggregator_order_id: \"MASHP76181627885513\"){status, message, respcode, mobile_number, amount, aggregator_order_id, transaction_charge, transaction_time, transaction_status, total_amount, delivery_charges, delivery_orderid}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingTransactionOrderReceipt": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "mobile_number": "7710804049",
      "amount": "250.00",
      "aggregator_order_id": "MASHP76181627885513",
      "transaction_charge": "0",
      "transaction_time": "02-08-2021 12:04:38",
      "transaction_status": "SUCCESS",
      "total_amount": "310.00",
      "delivery_charges": "60.00",
      "delivery_orderid": "D-MASHP76181627885513",
      "product_name": "Car/Jeep/Van Class 4 Vehicles"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getShoppingTransactionOrderReceipt": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/
