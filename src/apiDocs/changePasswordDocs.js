// -------------- Transaction STATUS -----------------
/**
* @api {post} /changepassword Change Password
* @apiName changePassword
* @apiGroup CHANGEPASSWORD

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{changePassword(ma_user_id:28479,userid:2,current_password:\"123456789\",new_password:\"Test123488882222\",new_confirm_password:\"Test123488882222\"){status,message}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>Change password API</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success--Response:
HTTP/1.1 200 OK
{
    "data": {
        "changePassword": {
            "status": 200,
            "message": "Success: Password change successfully"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "changePassword": {
            "status": 400,
            "message": "Fail: Current password not match!"
        }
    }
}
*/
