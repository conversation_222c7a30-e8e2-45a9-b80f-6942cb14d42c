// -------------- QR ACTIVATION API-----------------

/**
* @api {post} /qractivation 1) Check vpa.
* @apiName checkvpa
* @apiGroup QR ACTIVATION API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwdWJsaWNrZXkiOjEyNDksInByb2ZpbGVpZCI6Mjg2NDEsImlhdCI6MTYzODM2MjE2NiwiZXhwIjoxNjM4NDQ4NTY2fQ.Y-Vv2KuVEnd0C5GQaIpcfuxBEbuRvA2_0M9Sl39Z-1s\"
  "app_version" : "1.65"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{checkvpa(ma_user_id:28641,userid:7556,vpa:\"srushti@hdfc\"){status,respcode,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used check vpa for qr activation.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"checkvpa":{"status":200,"respcode":1000,"message":"Merchant is registered successfully"}}}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 400 OK
{"data":{"checkvpa":{"status":400,"respcode":12357,"message":"VPA activation already done for this user"}}}
*/
