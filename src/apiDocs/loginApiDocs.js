// -------------- LOGIN LAMBDA API-----------------

/**
* @api {post} /login 1) Login API Send
* @apiName checkUserLogin
* @apiGroup LOGIN LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{checkUserLogin(username:"<PERSON>2847<PERSON>",password:"Test1234",gcm_id:"hjdgfhgdfhg1hjhhhhg",checksum:"e5382e6cb84767720b353abda5b153f6",deviceos:"Windows",deviceimei:"RRNnpqew94535",action:SEND){status,respcode,message,orderid,deviceid,data}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to check User Login</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} data  User Details array.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  API order id, if deviceimei is not verified.
* @apiSuccess {String} deviceid  API device id, if deviceimei is not verified.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"checkUserLogin":{"status":200,"respcode":1000,"message":"success","orderid":null,"deviceid":null,"data":"{"secret":"test","username":"test","password":"test","user_id":1248,"mid":100000042,"gcm_key":"","mer_name":"sinalkar pvt ltd","gst":"27ABXPT6949B1ZV","pan":{"type":"Buffer","data":[65,72,72,80,88,54,50,53,52,71]},"address":"Sector 10","city":"Navi Mumbai","state":"Maharashtra","country":"India","email":"<EMAIL>","mobile":"7433156906","pincode":400708,"viracc":"","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.cYqqyPDx-OERYoLn0XPVHUevXrYCIui8OdtvKArwM-Y","dist_username":"2374952","dist_password":"EpSl3xNJ","dist_apikey":"RbVqe3xTjiwvlYCB","dist_mid":18999,"dist_name":"merchant retailer","dist_company":null,"dist_mobile":"**********","dt_username":"test","dt_password":"test","dt_apikey":"test","dt_mid":*********,"dt_name":"Backend1 superdistributor","dt_company":"sinalkar pvt ltd","dt_mobile":"**********","user_type":"DISTRIBUTOR","gcm_id":29293,"logo":"","esign_link":"","created":"2020-06-26","valid_upto":"2021-06-26","channels":["billpayment","cashinevalue","cms","moneytransfer","insurance","khata","withdrawal","trnshistory","collectmoney","upi","upiqr"],"insuranceList":[{"category_code":"HL","category_name":"Health Insurance"},{"category_code":"TWC","category_name":"Tw Wheeler Comprehensive"}],"tsmname":"QASalesTerritoryNew","tsmmobile":**********,"tsmemail":"<EMAIL>","asmname":"QASalesStateHeadNew","asmmobile":**********,"asmemail":"<EMAIL>","supportnumber":"02268870500","supportemail":"<EMAIL>","supporttime":"9am to 9pm","message":"success","respcode":1000,"checksum":"5258a3fca1238558cf6d1bb45fcb2ebe","status":200,"allow_new_pin":true,"pinExpired":true}"}}}
{"data": {"checkUserLogin": {"status": 200,"respcode": 2007,"message": "Success: OTP sent successfully","orderid": "MADR04021610724960","deviceid": 32,"data": null}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} data  User Details array.
* @apiError {String} message  API response message.
* @apiError {String} orderid  API order id, if deviceimei is not verified.
* @apiError {String} deviceid  API device id, if deviceimei is not verified.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"checkUserLogin":{"status":400,"respcode": 1001,"message":"Invalid Credentials","orderid":null,"deviceid":null,"data":null}}}
*/

/**
* @api {post} /login 2) Verify User Login
* @apiName verifyUserLogin
* @apiGroup LOGIN LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{verifyUserLogin(otp:"123456",deviceid:10711, orderid:"MADR23891611040352",action:VERIFY){status,respcode,message,data}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to verify OTP for User Login</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data API User details array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"verifyUserLogin":{"status":200,"respcode":1000,"message":"success","data":"{"secret":"4dBE183R725a9","username":"1919255","password":"698kL4l7","user_id":1155,"mid":245,"gcm_key":"","mer_name":null,"gst":null,"pan":null,"address":"test company address parel","city":"Mumbai","state":"Andaman and Nicobar Islands","country":"ind","email":"<EMAIL>","mobile":"9821191280","pincode":400018,"viracc":"","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwdWJsaWNrZXkiOjExNTUsInByb2ZpbGVpZCI6MjQ1LCJpYXQiOjE2MTA5ODExNjgsImV4cCI6MTYxMTA2NzU2OH0.rvcl9DI_w1xSIzfERnfVtqi68D0c8kAoAErHay64YhY","dist_username":"2374952","dist_password":"EpSl3xNJ","dist_apikey":"RbVqe3xTjiwvlYCB","dist_mid":18999,"dist_name":"Ahindra Sinha","dist_company":null,"dist_mobile":"**********","dt_username":"5108563","dt_password":"dY7ATpC8","dt_apikey":"66e9PhrS7KuWBKYh","dt_mid":28478,"dt_name":"AirpayDT6 Payment","dt_company":"DemoLiveSouthern","dt_mobile":"**********","user_type":"RETAILER","gcm_id":2363,"logo":"","esign_link":"","created":"2020-06-26","valid_upto":"2021-06-26","channels":["billpayment","moneytransfer","insurance","trnshistory","collectmoney","upi","upiqr"],"insuranceList":[{"category_code":"HL","category_name":"Health Insurance"},{"category_code":"TWC","category_name":"Tw Wheeler Comprehensive"}],"tsmname":"","tsmmobile":"","tsmemail":"","asmname":"","asmmobile":"","asmemail":"","supportnumber":"02268870500","supportemail":"<EMAIL>","supporttime":"9am to 9pm","message":"success","respcode":1000,"checksum":"5258a3fca1238558cf6d1bb45fcb2ebe","status":200,"allow_new_pin":true,"pinExpired":true}"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} data API User details array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"verifyUserLogin":{"status":400,"respcode": 1001,"message":"Invalid Otp","data":null}}}
*/

// -------------- Login Lambda RE SEND OTP -----------------
/**
* @api {post} /login 3) ReSend Login OTP
* @apiName resendLoginOtp
* @apiGroup LOGIN LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{resendLoginOtp(orderid:"MACW59901606823564", action:RESEND){status,respcode,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Re-send OTP for User Login</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"resendLoginOtp": {"status": 200,"respcode": 1000,"message": "Success"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"resendLoginOtp":{"status":400,"respcode":1001,"message":"Something went wrong"}}}
*/
