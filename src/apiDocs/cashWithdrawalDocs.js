// -------------- <PERSON>S<PERSON> WITHDRAWAL SEND OTP -----------------
/**
* @api {post} /cashwithdrawal 1) Send OTP
* @apiName cashWithdrawalSendOtp
* @apiGroup CASH WITHDRAWAL POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawal(ma_user_id:28552,userid:7638,amount:100,orderid:"MAWEB54031594806719",bankid:67,handler:SEND){status,message,otpOrderid}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to send OTP for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} otpOrderid  OTP order Id.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Success: OTP Sent to customer","otpOrderid":"MACW73111594806728"}}}

* @apiError {String} status API status code.
* @apiError {String} otpOrderid  OTP order Id.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":400,"message":"Something went wrong","otpOrderid":null}}}
*/

// -------------- CASH WITHDRAWAL RE SEND OTP -----------------
/**
* @api {post} /cashwithdrawal 2) Re Send OTP
* @apiName cashWithdrawalReSendOtp
* @apiGroup CASH WITHDRAWAL POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawal(ma_user_id:28552,userid:7638,amount:100,orderid:"MAWEB54031594806719",bankid:67,handler:RESEND,otpOrderid:"MACW73111594806728",otp:""){status,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Re-send OTP for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Success"}}}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Something went wrong"}}}
*/

// -------------- Verfiy OTP & CashWithdrawal POINTS TO CASH -----------------
/**
* @api {post} /cashwithdrawal 3) Verfiy OTP & CashWithdrawal POINTS TO CASH
* @apiName cashWithdrawalVerifyOtp
* @apiGroup CASH WITHDRAWAL POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawal(ma_user_id:28552,userid:7638,amount:100,orderid:"MAWEB54031594806719",bankid:67,handler:VERIFY,otpOrderid:"MACW73111594806728",otp:"123456"){status,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Verify the OTP and process points to cash for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Success"}}}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Something went wrong"}}}
*/
