// -------------- Transaction STATUS -----------------
/**
* @api {post} /transactionstatus Get transaction Status
* @apiName getTransactionStatus
* @apiGroup TRANSACATIONSTATUS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{getTransactionStatus(ma_user_id:245,userid:1155,aggregator_order_id:\"MABBPS001\"){status,message,transaction_status,addedon,updatedon}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>Get Transaciton Status again given order id</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success--Response:
HTTP/1.1 200 OK
{
    "data": {
        "getTransactionStatus": {
            "status": 200,
            "message": "success",
            "transaction_status": "REFUNDED",
            "addedon": "16-01-2020 18:14:18",
            "updatedon": "24-12-2020 10:24:12"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getTransactionStatus": {
            "status": 400,
            "message": "Fail: No Records",
            "transaction_status": null,
            "addedon": null,
            "updatedon": null
        }
    }
}
*/
