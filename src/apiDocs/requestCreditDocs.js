// -------------- Request Credit API-----------------

/**
* @api {post} /requestcredit 1) Request for Credit Balance
* @apiName requestForCredit
* @apiGroup REQUEST CREDIT API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{requestForCredit(ma_user_id:29138,user_id:2079,amount_requested:500,user_type:RETAILER,credit_request_status:PENDING,request_to:1234){status,message,respcode,transaction_id}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to request credit balance from Distributor.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"requestForCredit": {"status": 200,"message": "Request Added Successfully","respcode": 1000,"transaction_id": "MATOPUP04761627622962"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"requestForCredit": {"status": 400,"message": "Fail: Something Went Wrong.","respcode": 1028}}}
*/

/**
* @api {post} /requestcredit 2) Approve Reject Credit Request
* @apiName approveRejectCreditRequest
* @apiGroup REQUEST CREDIT API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation{approveRejectCreditRequest(ma_user_id:1234,userid:1298,amount_approved:\"2000\",ma_request_credit_id:3,credit_request_status:APPROVED,security_pin:\"1234\"){status,message,respcode}}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to approve or reject the request</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"approveRejectCreditRequest": {"status": 200,"message": "Request Approved Successfully","respcode": 1000}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"approveRejectCreditRequest": {"status": 400,"message": "Fail: Something Went Wrong.","respcode": 1028}}}
*/

/**
* @api {post} /requestcredit 3) get Credit Request List
* @apiName getCreditRequestList
* @apiGroup REQUEST CREDIT API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getCreditRequestList(userid:2079, ma_user_id:1234,offset:0,limit:5, date_from:\"2021-07-13\",date_to:\"2021-07-18\", request_status_val:PENDING){respcode,status,message,nextFlag,requestList{ma_request_credit_id,ma_user_id,mapped_reference_id,amount_requested, amount_approved, request_status,addedon,updatedon,requested_to}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Request credit list</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} ma_request_credit_id  API request id.
* @apiSuccess {String} ma_user_id  API requested by.
* @apiSuccess {String} mapped_reference_id  API reference id.
* @apiSuccess {String} amount_requested  API amount requested.
* @apiSuccess {String} amount_approved  API amount approved.
* @apiSuccess {String} request_status  API request status.
* @apiSuccess {String} addedon  API added on.
* @apiSuccess {String} updatedon  API updated on.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"getCreditRequestList": {"respcode": 1000,"status": 200,"message": "Success","nextFlag": true,"requestList": [{"ma_request_credit_id": 13,"ma_user_id": 29138,"mapped_reference_id": null,"amount_requested": "2079","amount_approved": null,"request_status": "PENDING","addedon": "14-07-2021 08:41:00","updatedon": null,"requested_to": "Sanjay Sinalkar"},{"ma_request_credit_id": 12,"ma_user_id": 29138,"mapped_reference_id": "MA13434134","amount_requested": "2079","amount_approved": "0","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": "15-07-2021 07:35:17","requested_to": "Sanjay Sinalkar"},{"ma_request_credit_id": 11,"ma_user_id": 29138,"mapped_reference_id": "MA13434135","amount_requested": "2079","amount_approved": "2079","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": "14-07-2021 06:31:57","requested_to": "Sanjay Sinalkar"},{"ma_request_credit_id": 10,"ma_user_id": 29138,"mapped_reference_id": "MA13434134","amount_requested": "2079","amount_approved": "2079","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": null,"requested_to": "Sanjay Sinalkar"},{"ma_request_credit_id": 9,"ma_user_id": 29138,"mapped_reference_id": "MA13434135","amount_requested": "2079","amount_approved": "2079","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": "14-07-2021 06:31:57","requested_to": "Sanjay Sinalkar"}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getCreditRequestList": {"status": 400,"message": "Fail: Something Went Wrong","respcode": 1001,"requestList": null}}}
*/

/**
* @api {post} /requestcredit 4) get Distributor Credit Request List
* @apiName getDistributorCreditRequestList
* @apiGroup REQUEST CREDIT API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getDistributorCreditRequestList(userid:2079, ma_user_id:1234,offset:0,limit:5, date_from:\"2021-07-13\",date_to:\"2021-07-18\", request_status_val:PENDING){respcode,status,message,nextFlag,requestList{ma_request_credit_id,ma_user_id,mapped_reference_id,amount_requested, amount_approved, request_status,addedon,updatedon,requested_by}}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Distributor Request credit list</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} ma_request_credit_id  API request id.
* @apiSuccess {String} ma_user_id  API requested by.
* @apiSuccess {String} mapped_reference_id  API reference id.
* @apiSuccess {String} amount_requested  API amount requested.
* @apiSuccess {String} amount_approved  API amount approved.
* @apiSuccess {String} request_status  API request status.
* @apiSuccess {String} addedon  API added on.
* @apiSuccess {String} updatedon  API updated on.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"getDistributorCreditRequestList": {"respcode": 1000,"status": 200,"message": "Success","nextFlag": true,"requestList": [{"ma_request_credit_id": 13,"ma_user_id": 29138,"mapped_reference_id": null,"amount_requested": "2079","amount_approved": null,"request_status": "PENDING","addedon": "14-07-2021 08:41:00","updatedon": null,"requested_by": "Sanjay Sinalkar"},{"ma_request_credit_id": 12,"ma_user_id": 29138,"mapped_reference_id": "MA13434134","amount_requested": "2079","amount_approved": "0","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": "15-07-2021 07:35:17","requested_by": "Sanjay Sinalkar"},{"ma_request_credit_id": 11,"ma_user_id": 29138,"mapped_reference_id": "MA13434135","amount_requested": "2079","amount_approved": "2079","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": "14-07-2021 06:31:57","requested_by": "Sanjay Sinalkar"},{"ma_request_credit_id": 10,"ma_user_id": 29138,"mapped_reference_id": "MA13434134","amount_requested": "2079","amount_approved": "2079","request_status": "PENDING","addedon": "13-07-2021 07:46:33","updatedon": null,"requested_by": "Sanjay Sinalkar"}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getDistributorCreditRequestList": {"status": 400,"message": "Fail: Something Went Wrong","respcode": 1001,"requestList": null}}}
*/
