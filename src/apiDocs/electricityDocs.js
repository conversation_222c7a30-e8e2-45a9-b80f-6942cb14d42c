// ----------------------------------------------Get States ID and Name----------------------------------------------------------
/**
 * @api {post} /electricity 1. Get States
 * @apiName getStates
 * @apiGroup Electricity
 *
 * @apiHeader {String}  Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:
 {
    getStates(ma_user_id:18999,userid:5912){status, message,respcode, states{state_id, state_name}}
 }

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query":"{getStates(ma_user_id:18999,userid:5912){status, message,respcode, states{state_id, state_name}}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} states  state_id state_name array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getStates": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "states": [
                {
                    "state_id": 10,
                    "state_name": "Delhi"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getStates": {
            "status": 400,
            "message": "Fail: No records found",
            "respcode": 1002,
            "states": null
        }
    }
}
 */

// -----------------------GET ELECTRICITY PROVIDERS-----------------------------
/**
 * @api {post} /electricity 2) Get Electricity Providers
 * @apiName getElectricityProviders
 * @apiGroup Electricity

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    getElectricityProviders(ma_user_id:29244,userid:2265, state_id:10){status, message,respcode, providers{provider_id, provider_name, logo_url, fields{label, value, type,isEditable, isVisible, validation{min, max, regex}, postKey, isRequired, subLabel}}}
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query":"{getElectricityProviders(ma_user_id:29244,userid:2265, state_id:10){status, message,respcode, providers{provider_id, provider_name, logo_url, fields{label, value, type,isEditable, isVisible, validation{min, max, regex}, postKey, isRequired, subLabel}}}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} getElectricityProviders API providers array

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getElectricityProviders": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "providers": [
        {
          "provider_id": 34,
          "provider_name": "BSES Yamuna Power Limited",
          "logo_url": null,
          "fields": [
            {
              "label": "Mobile Number",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": null,
                "max": null,
                "regex": "^[0-9]{1,10}$"
              },
              "postKey": "mobile_number",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "CA",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": 10,
                "max": 20,
                "regex": "^[0-9]{9}$"
              },
              "postKey": "customer_id",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "Email",
              "value": "",
              "type": "String",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": 10,
                "max": 50,
                "regex": "^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$"
              },
              "postKey": "email",
              "isRequired": true,
              "subLabel": ""
            }
          ]
        },
        {
          "provider_id": 35,
          "provider_name": "BSES Rajdhani Power Limited",
          "logo_url": null,
          "fields": [
            {
              "label": "Mobile Number",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": null,
                "max": null,
                "regex": "^[0-9]{1,10}$"
              },
              "postKey": "mobile_number",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "CA",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": 10,
                "max": 20,
                "regex": "^[0-9]{9}$"
              },
              "postKey": "customer_id",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "Email",
              "value": "",
              "type": "String",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": 10,
                "max": 50,
                "regex": "^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$"
              },
              "postKey": "email",
              "isRequired": true,
              "subLabel": ""
            }
          ]
        },
        {
          "provider_id": 63,
          "provider_name": "TATA Power North Delhi Power Limited",
          "logo_url": null,
          "fields": [
            {
              "label": "Mobile Number",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": null,
                "max": null,
                "regex": "^[0-9]{1,10}$"
              },
              "postKey": "mobile_number",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "Contract Account Number",
              "value": "",
              "type": "String",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": null,
                "max": null,
                "regex": "^[0-9]{11,12}$"
              },
              "postKey": "customer_id",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "Email",
              "value": "",
              "type": "String",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": 10,
                "max": 50,
                "regex": "^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$"
              },
              "postKey": "email",
              "isRequired": true,
              "subLabel": ""
            }
          ]
        },
        {
          "provider_id": 161,
          "provider_name": "New Delhi Municipal Council",
          "logo_url": null,
          "fields": [
            {
              "label": "Mobile Number",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": null,
                "max": null,
                "regex": "^[0-9]{1,10}$"
              },
              "postKey": "mobile_number",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "Consumer Number",
              "value": "",
              "type": "Number",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": null,
                "max": null,
                "regex": "^[0-9]{7,10}$"
              },
              "postKey": "customer_id",
              "isRequired": true,
              "subLabel": ""
            },
            {
              "label": "Email",
              "value": "",
              "type": "String",
              "isEditable": true,
              "isVisible": true,
              "validation": {
                "min": 10,
                "max": 50,
                "regex": "^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$"
              },
              "postKey": "email",
              "isRequired": true,
              "subLabel": ""
            }
          ]
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} getElectricityProviders API providers array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getElectricityProviders": {
            "status": 400,
            "message": "Fail: Something Went wrong",
            "respcode": 1001,
            "providers": null
        }
    }
}
 */

// ------------------- GET BILL ---------------------------------
/**
 * @api {post} /electricity 3) Get Bill
 * @apiName getBill
 * @apiGroup Electricity

 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:
 {
   "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
   "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:
 {
     getBill(ma_user_id:29244,userid:2265, provider_id:127, form_data:"eyJjdXN0b21lcl9pZCI6ICI5MDAwMDAzMjgyMzIifQ==", order_id:"MAWEB97421640065506"){status, message, respcode, billDetails{label, value, type,isEditable, isVisible, validation{min, max, regex}, postKey, isRequired, subLabel}}
 }
 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query":"{getBill(ma_user_id:29244,userid:2265, provider_id:127, form_data:"eyJjdXN0b21lcl9pZCI6ICI5MDAwMDAzMjgyMzIifQ==", order_id:"MAWEB97421640065506"){status, message, respcode, billDetails{label, value, type,isEditable, isVisible, validation{min, max, regex}, postKey, isRequired, subLabel}}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} getBill API billDetails array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBill": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "billDetails": [
        {
          "label": "Customer Name",
          "value": "BHARAT S KANSARA & MR PRASHANT B KANSARA",
          "type": "String",
          "isEditable": false,
          "isVisible": true,
          "validation": {
            "min": 1,
            "max": 30,
            "regex": "^[a-zA-Z &;.']{3,60}$"
          },
          "postKey": "customer_name",
          "isRequired": true,
          "subLabel": ""
        },
        {
          "label": "Bill Amount",
          "value": "1181.00",
          "type": "Number",
          "isEditable": false,
          "isVisible": true,
          "validation": {
            "min": 1,
            "max": 10,
            "regex": "[0-9.]{1,10}$"
          },
          "postKey": "amount",
          "isRequired": true,
          "subLabel": ""
        },
        {
          "label": "Due Date",
          "value": "10/01/2022",
          "type": "String",
          "isEditable": false,
          "isVisible": true,
          "validation": {
            "min": 1,
            "max": 30,
            "regex": "^([0-2][0-9]|(3)[0-1])(\\/)(((0)[0-9])|((1)[0-2]))(\\/)[0-9]{4}$"
          },
          "postKey": "due_date",
          "isRequired": true,
          "subLabel": ""
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} getBill API billDetails array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBill": {
            "status": 400,
            "message": "Error::: No data found",
            "respcode": 1028,
            "billDetails": null
        }
    }
}
 */

// -------------------- DO ELECTRICITY PAYMENT ---------------------------
/**
* @api {post} /electricity 4) Do Electricity Payment
* @apiName doElectricityPayment
* @apiGroup Electricity

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
"Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
"app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation{
  doElectricityPayment
  (
    ma_user_id:29244,
    userid:2265,
    pin:"0101",
    aggregator_order_id:"MAWEB97421640065506",
    provider_id:127,
    form_data:"********************************************************************************************************************************************************************************************************************************************************************",
    provider_name:"The Tata Power Company Ltd.-Mumbai",
    state_name:"Maharashtra"
    )
    {
      status,
      message,
      respcode,
      customer_mobile,
      customer_name,
      amount,
      aggregator_order_id,
      merchant_name,
      merchant_mobile,
      transaction_time,
      transaction_status,
      provider_name,
      utility_name,
      transaction_charges,
      transaction_amount,
      bill_response,
      transaction_id,
      state_name,
      logo_url
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{"query":"mutation{doElectricityPayment(ma_user_id:29244,userid:2265,pin:"0101",aggregator_order_id:"MAWEB97421640065506",provider_id:127, form_data:"********************************************************************************************************************************************************************************************************************************************************************",provider_name:"The Tata Power Company Ltd.-Mumbai", state_name:"Maharashtra"){status, message, respcode, customer_mobile, customer_name, amount, aggregator_order_id,merchant_name, merchant_mobile, transaction_time, transaction_status, provider_name,utility_name, transaction_charges, transaction_amount, bill_response, transaction_id}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "doElectricityPayment": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "customer_mobile": "**********",
      "customer_name": "BHARAT S KANSARA & MR PRASHANT B KANSARA",
      "amount": "1181",
      "aggregator_order_id": "MAWEB97421640065506",
      "merchant_name": "acquiring external",
      "merchant_mobile": "**********",
      "transaction_time": "21-12-2021 03:13:28",
      "transaction_status": "SUCCESS",
      "provider_name": "The Tata Power Company Ltd.-Mumbai",
      "utility_name": "Electricity",
      "transaction_charges": "0",
      "transaction_amount": "1181",
      "bill_response": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
      "transaction_id": "MAWEB97421640065506",
      "state_name": "Maharashtra",
      "logo_url": "https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png"
    }
  }
}
* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.
* @apiErrorExample Error-Response: Transaction details not available
HTTP/1.1 200 OK
{
    "data": {
        "doElectricityPayment": {
            "status": 200,
            "message": "Fail : Transaction details not available",
            "respcode": 1028,
            "customer_mobile": "**********",
            "customer_name": "FRANICS ANDREW",
            "amount": "1020",
            "aggregator_order_id": "MAWEB99581636969250",
            "merchant_name": "AirpayRT6  ",
            "merchant_mobile": "**********",
            "transaction_time": "25-11-2021 03:24:05",
            "transaction_status": "INITIATED",
            "provider_name": "BEST Undertaking",
            "utility_name": "Electricity",
            "transaction_charges": "0",
            "transaction_amount": "1020",
            "bill_response": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            "transaction_id": "MAWEB99581636969250",
            "state_name": "Maharashtra",
            "logo_url": "https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png"
        }
    }
}
* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.
* @apiErrorExample Error-Response: Previous transaction already in process
HTTP/1.1 200 OK
{
    "data": {
        "doElectricityPayment": {
            "status": 400,
            "message": "Fail: Previous transaction already in process",
            "respcode": 1133,
            "customer_name": null,
            "customer_mobile": null,
            "amount": null,
            "aggregator_order_id": null,
            "merchant_name": null,
            "merchant_mobile": null,
            "transaction_time": null,
            "transaction_status": null,
            "provider_name": null,
            "utility_name": null,
            "transaction_charges": null,
            "transaction_amount": null,
            "bill_response": null,
            "transaction_id": null,
            "state_name": null,
            "logo_url": null
        }
    }
}
*/
