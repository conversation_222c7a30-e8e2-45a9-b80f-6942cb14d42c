// -------------- GET CMS Merchant List -----------------
/**
* @api {post} /cms 1) Get CMS Merchant List
* @apiName getCmsMerchantList
* @apiGroup CMS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getCmsMerchantList(ma_user_id: 18989, userid: 1) {
      status
      respcode
      message
      merchantList{
        ma_cms_merchant_on_boarding_id
        merchant_name
        merchant_legal_name
        client_id
        cms_ma_user_id
        cms_userid
        merchant_type
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getCmsMerchantList(ma_user_id: 18989, userid: 1) {status,respcode,message,merchantList{ma_cms_merchant_on_boarding_id,merchant_name,merchant_legal_name,client_id,cms_ma_user_id,cms_userid,merchant_type}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} merchantList  Array of merchant list.
* @apiSuccess {String} merchantList.ma_cms_merchant_on_boarding_id id of ma_cms_merchant_on_boarding.
* @apiSuccess {String} merchantList.merchant_name  name of merchant.
* @apiSuccess {String} merchantList.merchant_legal_name legal name of merchant.
* @apiSuccess {String} merchantList.client_id id as per the parent merchant.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCmsMerchantList": {
      "status": 200,
      "respcode": 1000,
      "message": "Success",
      "merchantList": [
        {
          "ma_cms_merchant_on_boarding_id": 1,
          "merchant_name": "Eureka forbes",
          "merchant_type": "DIRECT",
          "merchant_legal_name": "Eureka forbes",
          "client_id": "0",
          "cms_ma_user_id": 18962,
          "cms_userid": 11
        },
        {
          "ma_cms_merchant_on_boarding_id": 3,
          "merchant_name": "Margdarshak",
          "merchant_type": "SUB",
          "merchant_legal_name": "Margdarshak",
          "client_id": "14",
          "cms_ma_user_id": 18960,
          "cms_userid": 12
        },
        {
          "ma_cms_merchant_on_boarding_id": 3,
          "merchant_name": "Swiggy",
          "merchant_type": "SUB",
          "merchant_legal_name": "Swiggy",
          "client_id": "2",
          "cms_ma_user_id": 18960,
          "cms_userid": 12
        },
        {
          "ma_cms_merchant_on_boarding_id": 3,
          "merchant_name": "Zomato",
          "merchant_type": "SUB",
          "merchant_legal_name": "Zomato",
          "client_id": "6",
          "cms_ma_user_id": 18960,
          "cms_userid": 12
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "errors": [
    {
      "message": "connect ETIMEDOUT",
      "locations": [
        {
          "line": 2,
          "column": 3
        }
      ],
      "path": [
        "getCmsMerchantList"
      ]
    }
  ],
  "data": {
    "getCmsMerchantList": null
  }
}
*/

// -------------- GET Merchant Details -----------------
/**
* @api {post} /cms 2) Get Merchant Details
* @apiName getCmsMerchantDetails
* @apiGroup CMS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getCmsMerchantDetails(ma_user_id: 18989, userid: 1, cms_ma_user_id: 18960, ma_cms_merchant_on_boarding_id: 3, client_id: "2") {
      status
      respcode
      message
      ma_cms_merchant_on_boarding_id
      merchant_name
      merchant_legal_name
      pan
      gst_number
      email
      mobile
      api_params
      cms_ma_user_id
      cms_userid
      bank_name
      account_name
      account_no
      ifsc_code
      company_reg_no
      contact_name
      gst_state_name
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getCmsMerchantDetails(ma_user_id: 18989, userid: 1, cms_ma_user_id: 18960, ma_cms_merchant_on_boarding_id: 3, client_id: \"2\") {status,respcode,message,ma_cms_merchant_on_boarding_id,merchant_name,merchant_legal_name,pan,gst_number,email,mobile,api_params,cms_ma_user_id,cms_userid,bank_name,account_name,account_no,ifsc_code,company_reg_no,contact_name,gst_state_name}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} ma_cms_merchant_on_boarding_id State of approval.
* @apiSuccess {String} merchant_name name of merchant.
* @apiSuccess {String} merchant_legal_name legal name of merchant.
* @apiSuccess {String} pan pan card number of merchant.
* @apiSuccess {String} gst_number gst number of merchant.
* @apiSuccess {String} email email.
* @apiSuccess {String} api_params base64 of json containing input field details, validation and attributes.
* @apiSuccess {String} cms_ma_user_id merchant id.
* @apiSuccess {String} cms_userid userid of merchant.
* @apiSuccess {String} bank_name name of bank.
* @apiSuccess {String} account_name account name.
* @apiSuccess {String} account_no account number.
* @apiSuccess {String} ifsc_code ifsc code.
* @apiSuccess {String} company_reg_no company registration number.
* @apiSuccess {String} contact_name contact name.
* @apiSuccess {String} gst_state_name gst state name.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCmsMerchantDetails": {
      "status": 200,
      "respcode": 1000,
      "message": "Success",
      "ma_cms_merchant_on_boarding_id": 3,
      "merchant_name": "SimpleICash",
      "merchant_legal_name": "SimpleICash",
      "pan": "BBHHPP512",
      "gst_number": "AB454EFF66465",
      "email": "<EMAIL>",
      "mobile": "*********",
      "api_params": "W3siaWQiOiIzNTUiLCJmb3JtSWQiOiI1NiIsImxhYmVsIjoiRGVwb3NpdG9yIElkIiwib3JkZXJJZCI6IjAiLCJ2YWx1ZSI6IiIsInR5cGUiOiJQV0ROIiwiaXNFZGl0YWJsZSI6InRydWUiLCJpc1Zpc2libGUiOiJ0cnVlIiwidmFsaWRhdGlvbiI6eyJtYXgiOiIxMCIsIm1pbiI6IjEiLCJyZWdleCI6Il5bQS1aYS16MC05XXsxLDEwfSQifSwicG9zdEtleSI6ImRlcG9zaXRvcklkIiwidXJsIjoiICIsImFjdGlvblVybCI6IiAiLCJzbXNMYWJlbCI6IkRlcG9zaXRvciBJZCIsImlzVW5pcXVlUmVmIjoidHJ1ZSIsImlzRHluYW1pYyI6InRydWUiLCJwYXJlbnRJZCI6Ii0xIiwiaXNSZXF1aXJlZCI6InRydWUiLCJzdWJMYWJlbCI6IiAiLCJpZGVudGlmaWVyIjoiICIsIm5hbWUiOiJkZXBvc2l0b3JJZCJ9LHsiaWQiOiIyMDQ5IiwiZm9ybUlkIjoiNTYiLCJsYWJlbCI6IlJlLWVudGVyIERlcG9zaXRvciBJZCIsIm9yZGVySWQiOiIxIiwidmFsdWUiOiIiLCJ0eXBlIjoiTiIsImlzRWRpdGFibGUiOiJ0cnVlIiwiaXNWaXNpYmxlIjoidHJ1ZSIsInZhbGlkYXRpb24iOnsibWF4IjoiMTAiLCJtaW4iOiIxIiwicmVnZXgiOiJeW0EtWmEtejAtOV17MSwxMH0kIn0sInBvc3RLZXkiOiJkZXBvc2l0b3JJZCIsInVybCI6IiAiLCJhY3Rpb25VcmwiOiIgIiwiaXNEeW5hbWljIjoidHJ1ZSIsInBhcmVudElkIjoiLTEiLCJpc1JlcXVpcmVkIjoidHJ1ZSIsInN1YkxhYmVsIjoiICIsImlkZW50aWZpZXIiOiIgIiwibmFtZSI6ImRlcG9zaXRvcklkIn1d",
      "cms_ma_user_id": 18960,
      "cms_userid": 12,
      "bank_name": null,
      "account_name": null,
      "account_no": null,
      "ifsc_code": null,
      "company_reg_no": "********",
      "contact_name": "Parent Merchant",
      "gst_state_name": "Delhi"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCmsMerchantDetails": {
      "status": 400,
      "respcode": 1002,
      "message": "Fail: No Records",
    }
  }
}
*/

// -------------- Get CMS Amount -----------------
/**
* @api {post} /cms 3) Get CMS Amount
* @apiName getCmsAmount
* @apiGroup CMS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
query{
  getCmsAmount(ma_user_id: 18989, userid: 1, cms_ma_user_id: 18962, client_id: "2",form_data: "****************************************") {
    status
    respcode
    message
    ma_cms_merchant_on_boarding_id
    merchant_legal_name
    api_params
    api_params_view
    cms_unique_id
  }
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getCmsAmount(ma_user_id: 18989, userid: 1, cms_ma_user_id: 18962, client_id: \"2\",form_data: \"****************************************\") {status,respcode,message,ma_cms_merchant_on_boarding_id,merchant_legal_name,api_params,cms_unique_id}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCmsAmount": {
      "status": 200,
      "respcode": 1000,
      "message": "Success",
      "ma_cms_merchant_on_boarding_id": 3,
      "merchant_legal_name": "SimpleICash",
      "api_params": "W3siaWQiOiIzNjkiLCJmb3JtSWQiOiI1NyIsImxhYmVsIjoiQW1vdW50Iiwib3JkZXJJZCI6IjEyIiwidmFsdWUiOiIiLCJ0eXBlIjoiTiIsImlzRWRpdGFibGUiOiJ0cnVlIiwiaXNWaXNpYmxlIjoidHJ1ZSIsInZhbGlkYXRpb24iOnsibWF4IjoiNCIsIm1pbiI6IjEiLCJyZWdleCI6Il5bMC05XXsxLDR9JCIsIm1heFZhbHVlIjoiODAwMCIsIm1pblZhbHVlIjoiNTAwIiwiZml4ZWQiOiIgIn0sInBvc3RLZXkiOiJhbW91bnQiLCJ1cmwiOiIgIiwiYWN0aW9uVXJsIjoiICIsImlzRHluYW1pYyI6InRydWUiLCJwYXJlbnRJZCI6Ii0xIiwiaXNSZXF1aXJlZCI6InRydWUiLCJzdWJMYWJlbCI6IiAiLCJpZGVudGlmaWVyIjoiICIsImZpZWxkTGF5b3V0IjoiVFdPX0NPTFVNTiIsIm5hbWUiOiJhbW91bnQifSx7ImlkIjoiMzU4IiwiZm9ybUlkIjoiNTciLCJsYWJlbCI6IkRlcG9zaXRvciBJZCIsIm9yZGVySWQiOiIxIiwidmFsdWUiOiIxMjM0NTY3ODkwIiwidHlwZSI6IkFOIiwiaXNFZGl0YWJsZSI6ImZhbHNlIiwiaXNWaXNpYmxlIjoidHJ1ZSIsInBvc3RLZXkiOiJkZXBvc2l0b3JJZCIsInVybCI6IiAiLCJhY3Rpb25VcmwiOiIgIiwic21zTGFiZWwiOiJEZXBvc2l0b3IgSWQiLCJpc1VuaXF1ZVJlZiI6InRydWUiLCJpc0R5bmFtaWMiOiJ0cnVlIiwicGFyZW50SWQiOiItMSIsImlzUmVxdWlyZWQiOiJ0cnVlIiwic3ViTGFiZWwiOiIgIiwiaWRlbnRpZmllciI6IiAiLCJmaWVsZExheW91dCI6IlRXT19DT0xVTU4iLCJuYW1lIjoiZGVwb3NpdG9ySWQifSx7ImlkIjoiMzYzIiwiZm9ybUlkIjoiNTciLCJsYWJlbCI6Ik1pbmltdW0gQ2FzaCBEZXBvc2l0Iiwib3JkZXJJZCI6IjYiLCJ2YWx1ZSI6IjIwMC4wIiwidHlwZSI6IkFOIiwiaXNFZGl0YWJsZSI6ImZhbHNlIiwiaXNWaXNpYmxlIjoidHJ1ZSIsInBvc3RLZXkiOiJtaW5DYXNoRGVwb3NpdCIsInVybCI6IiAiLCJhY3Rpb25VcmwiOiIgIiwiaXNEeW5hbWljIjoidHJ1ZSIsInBhcmVudElkIjoiLTEiLCJpc1JlcXVpcmVkIjoidHJ1ZSIsInN1YkxhYmVsIjoiICIsImlkZW50aWZpZXIiOiIgIiwiZmllbGRMYXlvdXQiOiJUV09fQ09MVU1OIiwibmFtZSI6Im1pbkNhc2hEZXBvc2l0In0seyJpZCI6IjM1OSIsImZvcm1JZCI6IjU3IiwibGFiZWwiOiJEZXBvc2l0b3IgTmFtZSIsIm9yZGVySWQiOiIyIiwidmFsdWUiOiJIZW1hbnRoMSIsInR5cGUiOiJBTiIsImlzRWRpdGFibGUiOiJmYWxzZSIsImlzVmlzaWJsZSI6InRydWUiLCJwb3N0S2V5IjoiZGVwb3NpdG9yTmFtZSIsInVybCI6IiAiLCJhY3Rpb25VcmwiOiIgIiwic21zTGFiZWwiOiJEZXBvc2l0b3IgTmFtZSIsImlzRHluYW1pYyI6InRydWUiLCJwYXJlbnRJZCI6Ii0xIiwiaXNSZXF1aXJlZCI6InRydWUiLCJzdWJMYWJlbCI6IiAiLCJpZGVudGlmaWVyIjoiICIsImZpZWxkTGF5b3V0IjoiVFdPX0NPTFVNTiIsIm5hbWUiOiJkZXBvc2l0b3JOYW1lIn1d",
      "api_params_view": "W3sibGFiZWwiOiJMYXRlIFBheW1lbnQgQ2hhcmdlIiwidmFsdWUiOm51bGx9LHsibGFiZWwiOiJDaGVxdWUgQm91bmNlIENoYXJnZSIsInZhbHVlIjoi4oK5IDU5NDYuMDAifSx7ImxhYmVsIjoiRHVlIERhdGUiLCJ2YWx1ZSI6IjA4LUp1bC0yMDIxIn0seyJsYWJlbCI6Ik5leHQgRU1JIER1ZSIsInZhbHVlIjoi4oK5IDAifSx7ImxhYmVsIjoiRnV0dXJlIEluc3RhbGxtZW50IE51bWJlciIsInZhbHVlIjpudWxsfSx7ImxhYmVsIjoiT3V0c3RhbmRpbmcgQW1vdW50IiwidmFsdWUiOm51bGx9LHsibGFiZWwiOiJJbnN0YWxsbWVudCBPdmVyZHVlIiwidmFsdWUiOm51bGx9LHsibGFiZWwiOiJPdGhlciBEdWUiLCJ2YWx1ZSI6bnVsbH0seyJsYWJlbCI6Ik5ldCBSZWNlaXZhYmxlIiwidmFsdWUiOm51bGx9LHsibGFiZWwiOiJQYXJ0bmVyIElkIiwidmFsdWUiOiJBSVJQQVkifV0=",
      "cms_unique_id": "171"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCmsAmount": {
      "status": 400,
      "respcode": 1002,
      "message": "Fail: No Records",
    }
  }
}
*/

// -------------- Send OTP -----------------
/**
* @api {post} /cms 4) Send OTP
* @apiName sendOtp
* @apiGroup CMS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation {
    sendOtp(
      ma_user_id: 18989,
      userid: 1,
      cms_ma_user_id: 18960,
      cms_userid: 12,
      ma_cms_merchant_on_boarding_id: 3,
      amount: 502,
      mobile_number: "**********",
      bank_name: "State Bank of India"
      cms_unique_id: "171",
      security_pin: "0011",
      client_id: "2",
    ) {
      status
      respcode
      message
      aggregator_order_id
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{sendOtp(ma_user_id: 18989, userid: 1, cms_ma_user_id: 18960, cms_userid: 12, ma_cms_merchant_on_boarding_id: 3, amount: 502, mobile_number: \"**********\", bank_name: \"State Bank of India\" cms_unique_id: \"171\", security_pin: \"0011\", client_id: \"2\",){status,respcode,message,aggregator_order_id}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "sendOtp": {
      "status": 200,
      "respcode": 2002,
      "message": "Success: OTP Sent to customer",
      "aggregator_order_id": "MACMS31981607677610"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "sendOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "aggregator_order_id": null
    }
  }
}
*/

// -------------- Re-send OTP -----------------
/**
* @api {post} /cms 5) Re-send OTP
* @apiName resendOtp
* @apiGroup CMS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation {
    resendOtp(
      ma_user_id: 18989,
      userid: 1,
      aggregator_order_id: "MACMS31981607677610"
      mobile_number: "**********",
    ) {
      status
      respcode
      message
      aggregator_order_id
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{resendOtp(ma_user_id: 18989, userid: 1, aggregator_order_id: \"MACMS31981607677610\" mobile_number: \"**********\",){status, respcode, message, aggregator_order_id}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "resendOtp": {
      "status": 200,
      "respcode": 1000,
      "message": "Success",
      "aggregator_order_id": "MACMS31981607677610"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "resendOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "aggregator_order_id": null
    }
  }
}
*/

// -------------- Verify OTP -----------------
/**
* @api {post} /cms 6) Verify OTP
* @apiName verifyOtp
* @apiGroup CMS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation {
    verifyOtp(
      ma_user_id: 18989,
      userid: 1,
      cms_ma_user_id: 18960,
      cms_userid: 12,
      ma_cms_merchant_on_boarding_id: 3,
      amount: 502,
      aggregator_order_id: "MACMS50521606797606"
      mobile_number: "**********",
      cms_unique_id: "171",
      client_id: "2",
      form_data: "eyJkZXBvc2l0b3JJZCI6IjEyMzQ1Njc4OTAiLCJhbW91bnQiOiI1MDIifQ==",
      otp: "123456",
    ) {
      status
      respcode
      message
      contact_name
      mobile
      merchant_name
      amount
      transaction_charge
      aggregator_order_id
      transaction_status
      transaction_time
    }
  }
* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation {verifyOtp(ma_user_id: 18989, userid: 1, cms_ma_user_id: 18960, cms_userid: 12, ma_cms_merchant_on_boarding_id: 3, amount: 502, aggregator_order_id: \"MACMS50521606797606\" mobile_number: \"**********\", cms_unique_id: \"171\", client_id: \"2\", form_data: \"eyJkZXBvc2l0b3JJZCI6IjEyMzQ1Njc4OTAiLCJhbW91bnQiOiI1MDIifQ==\", otp: \"123456\"){status, respcode, message, contact_name, mobile, merchant_name, amount, transaction_charge, aggregator_order_id, transaction_status, transaction_time}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 200,
      "respcode": 1000,
      "message": "Success",
      "contact_name": "Parent Merchant",
      "mobile": "**********",
      "merchant_name": "SimpleICash",
      "amount": "1377.00",
      "transaction_charge": 0,
      "account_no": null,
      "transaction_status": "SUCCESS",
      "transaction_time": "15-12-2020 04:00:36"
    }
  }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/
