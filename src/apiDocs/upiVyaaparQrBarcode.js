// -------------- UPI Vyaapaar QR Code API-----------------

/**
* @api {post} /transaction 1) Get QR Barcode String
* @apiName getQrCodeForUpi
* @apiGroup UPI Vyaapaar QR Code API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{getQrCodeForUpi(ma_user_id:18999,userid:2079){status,message,respcode,barcode_string}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get QC barcode string for Customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} barcode_string API barcode string.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"getQrCodeForUpi": {"status": 200,"message": "Success","respcode": 1000,"barcode_string": "upi://pay?pa=aps.m268112@icici&pn=Shiva%20Mobiles&tr=APS26112"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} barcode_string  API barcode string.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"getQrCodeForUpi":{"status":400,"respcode": 1001,"message":"Something Went Wrong","barcode_string":null}}}
*/
