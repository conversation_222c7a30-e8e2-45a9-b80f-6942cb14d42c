// -------------- Transaction Details -----------------
/**
* @api {post} /transaction 1) Get Transaction Details
* @apiName TransactionDetails
* @apiGroup TRANSACTION

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.1j9vwDrDFLXnpsPzDszQZ8WMkL5puSuAbe0mGT7Hhjs"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{transactionDetails(userid:1249,ma_user_id:28641,datefrom:"2021-04-06",dateto:"2021-04-06",limit:10,offset:0,bene_account_name:\"\",bene_account_number:\"\",aggregator_txn_id:324234,mobile_number:\"\",,,){status,respcode,message,nextFlag,transactionList{ma_user_id,aggregator_txn_id,aggregator_order_id,transaction_status,amount,addedon,transaction_type,ma_transaction_master_id,mobile_number,lts_flag,receipt_flag,bank_name}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>Get Transaction Details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success--Response:
HTTP/1.1 200 OK
{"data":{"transactionDetails":{"status":200,"respcode":1000,"message":"Success","nextFlag":false,"transactionList":[{"ma_user_id":28641,"aggregator_txn_id":"0","aggregator_order_id":"MACMS74761614262591","transaction_status":"SUCCESS","amount":"14.00","addedon":"25-02-2021 19:46:33","transaction_type":"CMS","ma_transaction_master_id":33292,"mobile_number":"**********","lts_flag":false,"receipt_flag":"YES","bank_name":"Suryoday Small Finance Bank"}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "transactionDetails": {
            "status": 400,
            "message": "Fail: No Records",
            "TransactionDetails": null
        }
    }
}
*/

// -------------- Retailer Transaction Details -----------------
/**
* @api {post} /transaction 2) Get Retailer Transaction Details
* @apiName retailerTransactionDetails
* @apiGroup TRANSACTION

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.1j9vwDrDFLXnpsPzDszQZ8WMkL5puSuAbe0mGT7Hhjs"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{retailerTransactionDetails(userid:1527,ma_user_id:*********,datefrom:\"2021-04-15\",dateto:\"2021-04-20\",limit:10,offset:0,distributor_list:*********,retailer_list:28640,bene_account_name:\"IDFC\",bene_account_number:\"\",mobile_number:\"**********\",,){status,respcode,message,nextFlag,retailerTransactionList{aggregator_order_id,transaction_status,amount,addedon,transaction_type,ma_transaction_master_id,mobile_number,retailer_id,retailer_name,distributor_id,distributor_company,super_distributor_id,super_distributor_company,settlement_date,utr_no,retailer_id,commission_amount,bene_account_name,bene_account_number,bank_rrn,remarks}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>Get Retailer Transaction Details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success--Response:
HTTP/1.1 200 OK
{
    "data": {
        "retailerTransactionDetails": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "nextFlag": false,
            "retailerTransactionList": [
                {
                    "aggregator_order_id": "MAGOLD12511618922407",
                    "transaction_status": "FAILED",
                    "amount": "504.39",
                    "addedon": "20-04-2021 18:10:8",
                    "transaction_type": "GOLD",
                    "ma_transaction_master_id": 36029,
                    "mobile_number": "**********",
                    "retailer_id": 28640,
                    "retailer_name": "Frontend4  retailer",
                    "distributor_id": *********,
                    "distributor_company": "sinalkar pvt ltd",
                    "super_distributor_id": *********,
                    "super_distributor_company": "Midhun",
                    "settlement_date": null,
                    "utr_no": null,
                    "commission_amount": "0.00",
                    "bene_account_name": "IDFC",
                    "bene_account_number": "**********",
                    "bank_rrn": null,
                    "remarks": "GOLD Buy Transaction"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.
* @apiError {Int} distributor_list  Distributor Profile ID (Single).
* @apiError {Int} retailer_list  Retailer Profile ID (Single)
* @apiError {String} bene_account_name  bene account name
* @apiError {String} bene_account_number  bene account number.
* @apiError {String} mobile_number  mobile number.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "retailerTransactionDetails": {
            "status": 400,
            "message": "Fail: No Records",
            "retailerTransactionDetails": null
        }
    }
}
*/

// -------------- BTQR String -----------------
/**
* @api {post} /transaction 3) Get BTQR String
* @apiName btqrString
* @apiGroup TRANSACTION

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.cvVOj6369vQ2VCF8yxcV3mRPr4eb5G1ySpXMmK6hq-o"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{btqrString(userid:2321321,ma_user_id:18924){status,respcode,message,btqrString}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>Get BTQR String</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success--Response:
HTTP/1.1 200 OK
{
    "data": {
        "btqrUser": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "btqrString": "test"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "btqrUser": {
            "status": 400,
            "respcode": 1002,
            "message": "Fail: No Records",
            "btqrString": null
        }
    }
}
*/
