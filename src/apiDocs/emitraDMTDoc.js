// -------------------------- Remitter <PERSON>gin --------------------------
/**
* @api {post} /remitterLogin 01) Remitter Login
* @apiName emitradmtemitterLogin
* @apiGroup Emitra - DMT

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    remitterlogin(
      userid: 7556,
      ma_user_id: 28479,
      mobile_number: "**********",
      countrycode: "IN",
      benificiary_list: NO,
      customerFieldJson: "",
      remitter_name: "emitra Rane"
    ){
      status,
      message,
      respcode,
      uic,
      sessionRQ
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Checks for customer in our database through mobile number or vrn</p>
<pre><code>{"query":"mutation{remitterlogin(userid:7556,ma_user_id:28479,mobile_number:\"**********\",countrycode:\"IN\",benificiary_list:NO,customerFieldJson:\"\",remitter_name:\"emitra Rane\"){status,message,respcode,uic, sessionRQ}}"}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_customers
* @apiSuccess {String} ext_request_number
* @apiSuccess {String} ext_txn_number

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "remitterlogin": {
    "status": 200,
    "message": "Success: Remitter registered successfully",
    "respcode": 2004,
    "uic": "**********53184",
    "sessionRQ": "49981351e449ed270edce061934fafc9"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "status": 400,
  "message": "Request Failed"
}
*/

// --------------------------- Bene Verify ----------------------------
/**
* @api {post} /beneverify 01) Bene Verify
* @apiName emitradmtbeneVerify
* @apiGroup Emitra - DMT

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    beneverify(
      ma_user_id:28479,
      userid: 7556,
      beneficiary_name: "sirajcancelcheck",
      ma_bank_master_id: 31,
      bank_name: "Hdfc Bank",
      account_number:"*********",
      re_account_number:"*********",
      mobile_number:"**********",
      ben_mobile_number:"",
      ifsc_code:"HDFC0000001",
      uic:"**********53184",
      relationship:"",
      bank_verify:true,
      aggregator_order_id:"MAWEB84{{$randomBankAccount}}",
      account_type:SAVINGS,
      service_id:5483,
      sessionRQ: "49981351e449ed270edce061934fafc9"
    ){
      status,
      message,
      respcode
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Checks for customer in our database through mobile number or vrn</p>
<pre><code>{"query":"mutation{beneverify(ma_user_id:28479,userid: 7556,beneficiary_name: \"sirajcancelcheck\", ma_bank_master_id: 31,bank_name: \"Hdfc Bank\",account_number:\"*********\",re_account_number:\"*********\",mobile_number:\"**********\",ben_mobile_number:\"\",ifsc_code:\"HDFC0000001\",uic:\"**********53184\",relationship:\"\",bank_verify:true,aggregator_order_id:\"MAWEB84{{$randomBankAccount}}\",account_type:SAVINGS,service_id:5483, sessionRQ: \"49981351e449ed270edce061934fafc9\"){status,message,respcode}}"}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_customers
* @apiSuccess {String} ext_request_number
* @apiSuccess {String} ext_txn_number

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "beneverify": {
            "status": 200,
            "message": "Success",
            "respcode": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data":{
    "beneverify": {
      "status":400,
      "message":"Fail: NSDL Bank Timeout @verifybeneficiary [MAWEB84278241099792]",
      "respcode":1028
    }
  }
}

*/
