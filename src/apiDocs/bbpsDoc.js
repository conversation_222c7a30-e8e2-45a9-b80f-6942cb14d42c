// -------------- GET Utility -----------------
/**
* @api {post} /bbps 1) Get Utility
* @apiName getBillPaymentUtility
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getBillPaymentUtility(
      ma_user_id: 28641,
      userid: 1249
    ) {
      status,
      message,
      respcode,
      utility_data{
        key,
        value,
        app_icon,
        web_icon
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getBillPaymentUtility(ma_user_id: 28641, userid: 1249) {status, message, respcode, utility_data{key, value, app_icon, web_icon}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {Array} utility_data  Array of Utilities available in BBPS.
* @apiSuccess {String} utility_data.key id of utility.
* @apiSuccess {String} utility_data.value  name of utility.
* @apiSuccess {String} utility_data.app_icon icon url for app.
* @apiSuccess {String} utility_data.web_icon icon url for web.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
  {
    "data": {
      "getBillPaymentUtility": {
        "status": 200,
        "message": "Success",
        "respcode": 1000,
        "utility_data": [{
          "key": 10,
          "value": Mobile Postpaid,
          "app_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/app_icons/1649052461_Mobile.png",
          "web_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/web_icons/1649052461_Mobile.png"
        },{
          "key": 16,
          "value": Recharge,
          "app_icon": "C:\\fakepath\\sunflower.jpg",
          "web_icon":"C:\\fakepath\\img.jpg"
        }.
        ...
        ,{
          "key": 1,
          "value": Electricity,
          "app_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/app_icons/1649052488_electricity.png",
          "web_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/web_icons/1649052488_electricity.png"
        }]
      }
    }
  }

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillPaymentUtility": {
      "status": 400,
      "message": "Fail: Something went wrong",
      "respcode": 1001,
      "utility_data": null
    }
  }
}
*/

// -------------- GET Providers -----------------
/**
* @api {post} /bbps 2) Get Providers
* @apiName getBillPaymentProviders
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getBillPaymentProviders(
      ma_user_id: 28641,
      userid:1249,
      utility_id: 1,
      limit: number,
      offset: number,
      provider_name?: string
    ){
      status,
      message,
      respcode,providers_data,
      providers_data_json{
        BILLER_MASTER_ID,
        provider_name,
        provider_id,
        fields_validation,
        biller_logo,
        biller_bill_copy,
        check_amount_req,
        raw_fields{
          name,
          validation,
          postKey,
          type
        },
        utility_id,
        partial_pay,
        payment_methods{
          autopay_allowed,
          min_limit,
          paylater_allowed,
          payment_method
        },
        action_type
      }
      providers_count,
      nextFlag
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getBillPaymentProviders(ma_user_id: 28641, userid:1249, utility_id: 1, limit: 20, offset: 0, provider_name: "A"){status, message, respcode,providers_data, providers_data_json{BILLER_MASTER_ID,provider_name,provider_id,fields_validation,biller_logo,biller_bill_copy,check_amount_req,raw_fields{name,validation,postKey,type},utility_id,partial_pay,payment_methods{autopay_allowed,min_limit,paylater_allowed,payment_method},action_type,providers_count,nextFlag}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} providers_data base64 string of providers_data Object.
* @apiSuccess {Array} providers_data_json Array of providers data.
* @apiSuccess {String} providers_data_json.BILLER_MASTER_ID Id of biller.
* @apiSuccess {String} providers_data_json.provider_name name of provider.
* @apiSuccess {String} providers_data_json.provider_id id of provider.
* @apiSuccess {String} providers_data_json.fields_validation validation regex for accountid.
* @apiSuccess {String} providers_data_json.biller_logo Logo URL of the biller.
* @apiSuccess {String} providers_data_json.biller_bill_copy no idea.
* @apiSuccess {String} providers_data_json.check_amount_req instapay or not.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillPaymentProviders": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "providers_data": "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",
      "providers_data_json": [{
        "BILLER_MASTER_ID": "300",
        "provider_name": "Ajmer Vidyut Vitran Nigam (AVVNL)",
        "provider_id": "298",
        "fields_validation": "^[0-9]{12}$",
        "biller_logo": "https://payments.billdesk.com/hg/images/billerlogo/AVVNL0000RAJ01.gif",
        "biller_bill_copy": null,
        "check_amount_req": "instapay",
        "raw_fields": [{
          "name": "K Number",
          "validation": "^[0-9]{12}$",
          "postKey": "KNumber",
          "type": "string"
        }],
        "utility_id": "1",
        "partial_pay": "Y",
        "payment_methods": [{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "DebitCard"
        },{
          "autopay_allowed":"N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "CreditCard"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "BankAccount"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "UPI"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "Wallet"
        }],
        "action_type": "instapay"
      },{
        "BILLER_MASTER_ID": "394",
        "provider_name": "Assam Power (APDCL NON-RAPDR)",
        "provider_id": "387",
        "fields_validation": "^[0-9]{12}$",
        "biller_logo": "https://payments.billdesk.com/hg/images/billerlogo/APDCL0000ASM02.gif",
        "biller_bill_copy": null,
        "check_amount_req": "instapay",
        "raw_fields": [{
          "name": "Consumer ID",
          "validation":"^[0-9]{12}$",
          "postKey":"ConsumerID",
          "type":"string"
        }],
        "utility_id": "1",
        "partial_pay": "Y",
        "payment_methods": [{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "DebitCard"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "CreditCard"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "BankAccount"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "UPI"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "Wallet"
        }],
        "action_type": "instapay"
      },{
        "BILLER_MASTER_ID": "310",
        "provider_name": "Assam Power (APDCL RAPDR)",
        "provider_id": "308",
        "fields_validation": "^[0-9]{11}$",
        "biller_logo": "https://payments.billdesk.com/hg/images/billerlogo/APDCL0000ASM01.gif",
        "biller_bill_copy": null,
        "check_amount_req": "instapay",
        "raw_fields": [{
          "name": "Consumer ID",
          "validation": "^[0-9]{11}$",
          "postKey": "ConsumerID",
          "type": "string"
        }],
        "utility_id": "1",
        "partial_pay": "N",
        "payment_methods": [{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "DebitCard"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "CreditCard"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "BankAccount"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "UPI"
        },{
          "autopay_allowed": "N",
          "min_limit": "1.00",
          "paylater_allowed": "N",
          "payment_method": "Wallet"
        }],
        "action_type": "instapay"
      }],
      providers_count: 20,
      nextFlag: false
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillPaymentProviders": {
      "status": 400,
      "respcode": 1002,
      "message": "Fail: No Records",
    }
  }
}
*/

// -------------- Init Makepayment -----------------
/**
* @api {post} /bbps 3) Init Makepayment
* @apiName initMakePayment
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    initMakePayment(
      ma_user_id: 28641,
      userid: 1249,
      provider_id: 1,
      utility_id: 1,
      action_type: "instapay",
      aggregator_order_id: "MATEST1234100002021",
      form_data: "eyAiQ29uc3VtZXJOdW1iZXIiOiAiNzcxMDMwNDQ5IiB9",
      mobile_number: "**********",
      BILLER_MASTER_ID: "405"
    ){
      status,
      message,
      respcode,
      amount,
      surcharge,
      bill_response,
      bill_response_json{
        objectid,
        billid,
        billstatus,
        billnumber,
        billperiod,
        net_billamount,
        customer_name,
        billduedate,
        billamount,
        billdate
      },
      doPayment
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{initMakePayment(ma_user_id: 28641, userid: 1249, provider_id: 1, utility_id: 1, action_type: \"instapay\", aggregator_order_id: \"MATEST1234100002021\", form_data: \"eyAiQ29uc3VtZXJOdW1iZXIiOiAiNzcxMDMwNDQ5IiB9\", mobile_number: \"**********\", BILLER_MASTER_ID: \"405\"){status, message, respcode, amount, surcharge, bill_response, bill_response_json{objectid, billid, billstatus, billnumber, billperiod, net_billamount, customer_name, billduedate, billamount, billdate},doPayment}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "initMakePayment": {
      "status": 200,
      "message": "Success",
      "respcode":1000,"amount": "840.00",
      "surcharge": "3.00",
      "bill_response": "eyJvYmplY3RpZCI6ImJpbGwiLCJiaWxsaWQiOiJIR0ExVjBCMjcyMDAwMDA5MTI4NUIiLCJiaWxsc3RhdHVzIjoiVU5QQUlEIiwiYmlsbG51bWJlciI6Ijk4NTAwNjg2NDgxIiwiYmlsbHBlcmlvZCI6IjIwMTkvMDEiLCJuZXRfYmlsbGFtb3VudCI6Ijg0MC4wMCIsImN1c3RvbWVyX25hbWUiOiJTQVRZQUJIQU1BIFIgSyBUUklQQVRISSAuIiwiYmlsbGR1ZWRhdGUiOiIzMC0wMS0yMDE5IiwiYmlsbGFtb3VudCI6Ijg0MC4wMCIsImJpbGxkYXRlIjoiMDktMDEtMjAxOSJ9",
      "bill_response_json": {
        "objectid": "bill",
        "billid": "HGA1V0B2720000091285B",
        "billstatus": "UNPAID",
        "billnumber": "98500686481",
        "billperiod": "2019/01",
        "net_billamount": "840.00",
        "customer_name": "SATYABHAMA R K TRIPATHI .",
        "billduedate": "30-01-2019",
        "billamount": "840.00",
        "billdate": "09-01-2019"
      },
      "doPayment": true
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "initMakePayment": {
      "status": 200,
      "message": "Error: Please enter valid Consumer Number",
      "respcode":14004,
      "amount": null,
      "surcharge": null,
      "bill_response": null,
      "bill_response_json": null,
      "doPayment": false
    }
  }
}
*/

// -------------- Do Point Bank Payment -----------------
/**
* @api {post} /bbps 4) Do Point Bank Payment
* @apiName doPointBankPayment
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    doPointBankPayment(
      ma_user_id: 28641,
      userid: 1249,
      action_type: "instapay",
      aggregator_order_id: "MATEST1234100002029",
      provider_id: 1,
      utility_id: 1,
      security_pin: "1515",
      mobile_number: "**********"
    ){
      status,
      message,
      respcode,
      customer_mobile,
      amount,
      aggregator_order_id,
      merchant_name,
      merchant_mobile,
      transaction_time,
      transaction_status,
      transaction_id,
      provider_name,
      utility_name,
      transaction_charges,
      transaction_amount,
      transaction_type,
      bill_response
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{doPointBankPayment(ma_user_id: 28641, userid: 1249, action_type: \"instapay\", aggregator_order_id: \"MATEST1234100002029\", provider_id: 1, utility_id: 1, security_pin: \"1515\", mobile_number: \"**********\"){status,message,respcode,customer_mobile,amount,aggregator_order_id,merchant_name,,merchant_mobile,transaction_time,transaction_status,transaction_id,provider_name,utility_name,transaction_charges,,transaction_amount,transaction_type,bill_response}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "doPointBankPayment": {
      "status": 200,
      "message": "Transaction in process",
      "respcode":1130,
      "customer_mobile": "**********",
      "amount": "843.00",
      "aggregator_order_id": "MAWEB20411650886479",
      "merchant_name": "Backend3  retailer",
      "merchant_mobile": "**********",
      "transaction_time": "25-04-2022 05:06:19",
      "transaction_status": "PENDING",
      "transaction_id": "MAWEB20411650886479",
      "provider_name": "Ajmer Vidyut Vitran Nigam (AVVNL)",
      "utility_name": "Electricity",
      "transaction_charges": "3",
      "transaction_amount": "840",
      "bill_response": "W10="
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "doPointBankPayment": {
      "status": 200,
      "message": "Fail: Transaction not found",
      "respcode":1012,
    }
  }
}
*/

// -------------- Biller Registration -----------------
/**
* @api {post} /bbpsregisterstatus 5) Biller Registration
* @apiName Biller Registration
* @apiGroup BBPS

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "USER_ID":"28641",
    "CUSTOMER_ID":"1",
    "ACCOUNTID":"*********",
    "REQUESTNUMBER":"MATEST1234100002024",
    "STATUS":"200",
    "ACTION":"REGISTER_RESPONSE"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<div>
  <pre><code>{ "USER_ID":"28641", "CUSTOMER_ID":"1", "ACCOUNTID":"*********", "REQUESTNUMBER":"MATEST1234100002024", "STATUS":"200", "ACTION":"REGISTER_RESPONSE" }</code></pre>
  <p class="type" style="background-color: #e535ab"> THIS API IS ONLY FOR ACQUIRING (MA) TEAM!</p>
</div>

* @apiError {String} STATUS API status code.
* @apiError {String} MSG API message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "STATUS": 200,
  "MSG": "Billers Registered Successfully."
}

* @apiError {String} STATUS API status code.
* @apiError {String} MSG API message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  STATUS: 400,
  MSG: 'Biller Registration Failed'
}
*/

// -------------- New Bill -----------------
/**
* @api {post} /bbpsnewbill 6) New Bill
* @apiName New Bill
* @apiGroup BBPS

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "USER_ID":"28641",
    "CUSTOMER_ID":"1",
    "ACCOUNTID":"*********",
    "REQUESTNUMBER":"MATEST1234100002024",
    "BILLAMOUNT":"500",
    "BILLID":"1"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<div>
  <pre><code>{ "USER_ID":"28641", "CUSTOMER_ID":"1", "ACCOUNTID":"*********", "REQUESTNUMBER":"MATEST1234100002024", "BILLAMOUNT":"500", "BILLID":"1" }</code></pre>
  <p class="type" style="background-color: #e535ab"> THIS API IS ONLY FOR ACQUIRING (MA) TEAM!</p>
</div>

* @apiError {String} STATUS API status code.
* @apiError {String} MSG API message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  STATUS: 200,
  MSG: 'Biller Notified Successfully.'
}

* @apiError {String} STATUS API status code.
* @apiError {String} MSG API message.

* @apiErrorExample Error-Response-1:
HTTP/1.1 200 OK
{
  STATUS: 400,
  MSG: 'Notification Failed'
}
* @apiErrorExample Error-Response-2:
HTTP/1.1 200 OK
{
  STATUS: 400,
  MSG: 'Biller Not Registered.'
}
* @apiErrorExample Error-Response-3:
HTTP/1.1 200 OK
{
  STATUS: 400,
  MSG: 'Biller Notification Failed'
}
*/

// -------------- BBPS biller -----------------
/**
* @api {post} /bbps 7) BBPS Biller Availability
* @apiName getBillerAvailability
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getBillerAvailability(
      ma_user_id: 28641,
      userid: 1249
      ){
        status,
        message,
        respcode,
        fluctuatingBiller{
          UTILITY_ID,
          BILLER_NAME,
          BILLER_MASTER_ID,
          BILLERALERTID,
          BILLER_ID,
          BILLERSTATE
        },
        offlineBiller{
          UTILITY_ID,
          BILLER_NAME,
          BILLER_MASTER_ID,
          BILLERALERTID,
          BILLER_ID,
          BILLERSTATE
        }
      }}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getBillerAvailability(ma_user_id: 28641, userid: 1249){status,message,respcode,fluctuatingBiller{UTILITY_ID,BILLER_NAME,BILLER_MASTER_ID,BILLERALERTID,BILLER_ID,BILLERSTATE},offlineBiller{UTILITY_ID,BILLER_NAME,BILLER_MASTER_ID,BILLERALERTID,BILLER_ID,BILLERSTATE}}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillerAvailability": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "fluctuatingBiller": [
                {
                    "BILLER_MASTER_ID": "385",
                    "OBJECTID": null,
                    "BILLERALERTID": "BSA1E0E2190000001234",
                    "SOURCEID": null,
                    "BILLER_ID": "MAHA00000MAH01",
                    "BILLERSTATE": "FLUCTUATING",
                    "STARTTIME": "10-05-2022 20:00:00",
                    "UPDATETIME": "10-05-2022 19:40:32",
                    "ENDTIME": "30-06-2022 23:59:00"
                },
                {
                    "BILLER_MASTER_ID": "382",
                    "OBJECTID": null,
                    "BILLERALERTID": "BSA1E0F0200000001233",
                    "SOURCEID": null,
                    "BILLER_ID": "MAHA00000MUM01",
                    "BILLERSTATE": "FLUCTUATING",
                    "STARTTIME": "10-05-2022 20:00:58",
                    "UPDATETIME": "10-05-2022 19:38:35",
                    "ENDTIME": "30-06-2022 23:59:58"
                }
            ],
            "activeBiller": [
              {
                    "BILLER_MASTER_ID": "281",
                    "OBJECTID": null,
                    "BILLERALERTID": "BSA1E145D30000001232",
                    "SOURCEID": null,
                    "BILLER_ID": "TATASKY00NAT01",
                    "BILLERSTATE": "INACTIVE",
                    "STARTTIME": "10-05-2022 20:00:33",
                    "UPDATETIME": "10-05-2022 19:36:07",
                    "ENDTIME": "30-06-2022 23:59:33"
              }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillerAvailability": {
      "status": 200,
      "message": "Success",
      "respcode":1000,
      "fluctuatingBiller": [],
      "activeBiller": []
    }
  }
}
*/

// -------------- BBPS biller - For Specific Biller  -----------------
/**
* @api {post} /bbps 7) BBPS Biller Availability - Specific Biller
* @apiName getBillerAvailability
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  query{
    getBillerAvailability(
      ma_user_id: 28641,
      userid: 1249,
      billerid: 385
      ){
        status,
        message,
        respcode,
        billerDetails
      }}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getBillerAvailability(ma_user_id: 28641, userid: 1249, billerid: 385){status,message,respcode,billerDetails}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillerAvailability": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "billerDetails": {
                "message_status": "Biller Offline.",
                "message_remark": "Bill Payment may fail"
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillerAvailability": {
      "status": 200,
      "message": "Success",
      "respcode":1000,
      "billerDetails": null
    }
  }
}
*/

// -------------- BBPS Providers Upate Api - For Specific Utility  -----------------
/**
* @api {post} /bbps 8) BBPS Providers Upate Api - Specific Utility
* @apiName bbpsproviderspecific
* @apiGroup BBPS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  { "query": "[{"key": 19, "value": "Education", page?:1}]" }

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query": "[{"key": 19, "value": "Education", page?:1}]"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  status: 200,
  respcode: 1000,
  message: "Success"
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  status: 400,
  respcode: 1001,
  message: "Fail: Something Went wrong"
}
*/
