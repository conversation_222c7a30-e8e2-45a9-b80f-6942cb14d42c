// -------------- Create Khata Book -----------------
/**
* @api {post} /khatabookAccountV2 Add khatabookAccount
* @apiName addKhata
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{addKhata(ma_user_id:28479,userid: 7556,business_name: \"DMT Khata\"){status, message,respcode,ma_kb_account_master_id,action_code}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>Create a new khata against the retailer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addKhata": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "ma_kb_account_master_id": 5
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addKhata": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "ma_kb_account_master_id": null
      "action_code": 1001
    }
  }
}
*/

// -------------- Edit Khata Book -----------------
/**
* @api {post} /khatabookAccountV2 Edit khatabookAccount
* @apiName editKhata
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{editKhata(ma_user_id:28479,userid: 7556,business_name: \"Mobile Recharge Khata\",ma_kb_account_master_id: 1){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>User can edit the khata name. ma_kb_account_master_id parameter can be fetched from the Khatalist api.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "editKhata": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "editKhata": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/

// -------------- Khata Book List -----------------
/**
* @api {post} /khatabookAccountV2 List khatabookAccount
* @apiName getKhataList
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getKhataList(ma_user_id:28479,userid:7556,offset:0,limit:10,filtertype:MERCHANTWISE){status, message,respcode,nextFlag,khatadata{name,ma_kb_account_master_id,ma_user_id,userid,business_name,mobile,addedon,action_code}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api will fetch all the khatas against the retailer.
filtertype : This parameter can take two values: MERCHANTWISE - Will return all khata books for the retailer. USERWISE - Will return only those khata books added by the particular retailer user
Additional option is provided to filter on basis of business name. Parameter : business_name(String)</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} nextFlag  Pagination Flag.
* @apiSuccess {Array} khatadata  Array of Khata book details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getKhataList": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "nextFlag": false,
            "khatadata": [
                {
                    "name": "AirpayRT6  ",
                    "ma_kb_account_master_id": 4,
                    "ma_user_id": 28479,
                    "userid": 7556,
                    "business_name": "DMT Khata",
                    "mobile": "**********",
                    "addedon": "03-11-2020 11:46:5"
                },
                {
                    "name": "AirpayRT6  ",
                    "ma_kb_account_master_id": 1,
                    "ma_user_id": 28479,
                    "userid": 7556,
                    "business_name": "Mobile Recharge Khata",
                    "mobile": "**********",
                    "addedon": "03-11-2020 11:21:26"
                }
            ],
          "action_code": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.
* @apiError {String} nextFlag  Pagination Flag.
* @apiError {Array} khatadata  Array of Khata book details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getKhataList": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "nextFlag": false,
      "khatadata": [],
      "action_code": 1001
    }
  }
}
*/

// -------------- Delete Khata Book -----------------
/**
* @api {post} /khatabookAccountV2 Delete khatabookAccount
* @apiName deleteKhata
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{deleteKhata(ma_user_id:28479,userid: 7556,ma_kb_account_master_id: 1){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>User can delete the khata book. ma_kb_account_master_id parameter can be fetched from the Khatalist api.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "deleteKhata": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "deleteKhata": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/

// -------------- Add Customer -----------------
/**
* @api {post} /khatabookCustomerV2 Add khatabookCustomer
* @apiName addCustomer
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{addCustomer(ma_user_id:28479,userid: 7556,name: \"Test\",mobile:\"**********\",ma_kb_account_master_id:4){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to create a customer against khata book. Apart from the mandatory parameters, some optional parameters can also be passed in the request.
Optional Parameters - business_name(String), email(String), address1(String), address2(String), pincode(Int), state(Int), city(Int), attachment(String) , uploadid(INT) - In case of attachment</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addCustomer": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addCustomer": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001
      "action_code": 1001
    }
  }
}
*/

// -------------- Edit Customer -----------------
/**
* @api {post} /khatabookCustomerV2 Edit khatabookCustomer
* @apiName editCustomer
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{editCustomer(ma_user_id:28479,userid: 7556,name: \"Test\",mobile:\"**********\",ma_kb_account_master_id:4,uuid: \"****************\"){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to edit a customer against khata book. UUID parameter can be fetched in customer list api. Apart from the mandatory parameters, some optional parameters can also be passed in the request.
Optional Parameters - business_name(String), email(String), address1(String), address2(String), pincode(Int), state(Int), city(Int) </code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "editCustomer": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "editCustomer": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/

// -------------- Customer List -----------------
/**
* @api {post} /khatabookCustomerV2 List khatabookCustomer
* @apiName getCustomerList
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getCustomerList(ma_user_id:28479,userid:7556,offset:0,limit:10,ma_kb_account_master_id:4){status, message,respcode,nextFlag,customer_data{name,uuid,ma_user_id,userid,business_name,mobile,state_name,city_name,addedon,action_code}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api will fetch all the customers against a khata book. There is an option to filter records basis of mobile number.
Parameter - mobile(String)</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} nextFlag  Pagination Flag.
* @apiSuccess {Array} customer_data  Array of Khata book customers.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getCustomerList": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "nextFlag": false,
            "customer_data": [
                {
                    "name": "Test",
                    "uuid": "1604384205762031",
                    "ma_user_id": 28479,
                    "userid": 7556,
                    "business_name": "Khata Enterprises",
                    "mobile": "9652364896",
                    "state_name": "Andaman and Nicobar Islands",
                    "city_name": "Bombuflat",
                    "addedon": "03-11-2020 11:48:20"
                }
            ],
        "action_code": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.
* @apiError {String} nextFlag  Pagination Flag.
* @apiError {Array} customer_data  Array of Khata book customers.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCustomerList": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "nextFlag": false,
      "customer_data": [],
      "action_code": 1001
    }
  }
}
*/

// -------------- Create Khata Book Transaction -----------------
/**
* @api {post} /khatabookTransactionV2 Create khatabookTransaction
* @apiName createTransaction
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{createTransaction(ma_user_id:28479,userid: 7556,uuid: \"****************\",amount:\"200\",ma_kb_account_master_id:4,transaction_date: \"2020-11-03\",credit_type: CREDIT_RECEIVED){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to create a khatabook transaction. Apart from the mandatory parameters, some optional parameters can also be passed in the request.
Optional Parameters - description(String), attachment(String),  uploadid(INT) - In case of attachment, bill_no(String).
 credit_type - This parameter takes two enum values which decide the type of transaction whether credit given or credit received.
 CREDIT_RECEIVED - Amount received
 CREDIT_GIVEN - Amount given</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "createTransaction": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "createTransaction": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/

// -------------- Transaction List -----------------
/**
* @api {post} /khatabookTransactionV2 khatabookTransaction List
* @apiName getTransactionList
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getTransactionList(ma_user_id:28479,userid:7556,offset:0,limit:10,ma_kb_account_master_id:4){status, message,respcode,nextFlag,Transactiondata{ma_user_id,uuid,closing_balance,amount,credit_type,transaction_date,ma_kb_account_master_id,description,addedon,attachment,bill_no,action_code}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api will fetch all the transactions  against a khata book. There is an option to get transactions at customer level.Need to pass optional parameter uuid for the same.
Parameter - uuid(String). Additionally, we can filter basis on date and amount. Parameter - datefrom (String), dateto (String), amount (String)</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} nextFlag  Pagination Flag.
* @apiSuccess {Array} Transactiondata  Array of Khata book transactions.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getTransactionList": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "nextFlag": false,
            "Transactiondata": [
                {
                    "ma_user_id": 28479,
                    "uuid": "****************",
                    "closing_balance": "-100.00",
                    "amount": "-200.00",
                    "credit_type": "CREDIT_RECEIVED",
                    "transaction_date": "2020-11-03",
                    "ma_kb_account_master_id": 4,
                    "description": "Money transfer Received",
                    "addedon": "04-11-2020 10:33:15",
                    "bill_no": "B10235"
                },
                {
                    "ma_user_id": 28479,
                    "uuid": "****************",
                    "closing_balance": "100.00",
                    "amount": "-200.00",
                    "credit_type": "CREDIT_RECEIVED",
                    "transaction_date": "2020-11-03",
                    "ma_kb_account_master_id": 4,
                    "description": "Money transfer Received",
                    "addedon": "04-11-2020 10:32:0",
                    "bill_no": "B10239"
                },
                {
                    "ma_user_id": 28479,
                    "uuid": "****************",
                    "closing_balance": "300.00",
                    "amount": "300.00",
                    "credit_type": "CREDIT_GIVEN",
                    "transaction_date": "2020-11-03",
                    "ma_kb_account_master_id": 4,
                    "description": "Money transfer",
                    "addedon": "04-11-2020 10:28:17",
                    "bill_no": "B10237"
                }
            ],
          "action_code": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.
* @apiError {String} nextFlag  Pagination Flag.
* @apiError {Array} Transactiondata  Array of Khata book transactions.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getTransactionList": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "nextFlag": false,
      "Transactiondata": [],
      "action_code": 1001
    }
  }
}
*/

// -------------- Customer List Balances-----------------
/**
* @api {post} /khatabookTransactionV2 Customer Wise Balance List
* @apiName getCustomerWiseBalance
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getCustomerWiseBalance(ma_user_id:28479,userid:7556,offset:0,limit:10,ma_kb_account_master_id:4){status, message,respcode,nextFlag,CustomerList{ma_user_id,uuid,total_amount_given,total_amount_received,customer_name,updatedon,email,mobile,attachment,final_bal,flag,action_code}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api will fetch all the customers against a khata with their balances. There is an option provided to filter the records based on customer name.
Parameter - customer_name(String). An additional parameter (flag) is passed in the response with values as CREDIT_RECEIVED or CREDIT_GIVEN. This can be used to identify the positive and negative amount and display the colour and description accordingly.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} nextFlag  Pagination Flag.
* @apiSuccess {Array} CustomerList  Array .

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getCustomerWiseBalance": {
            "nextFlag": false,
            "CustomerList": [
                {
                    "ma_user_id": 28479,
                    "uuid": "1604384205762031",
                    "total_amount_given": "400.00",
                    "total_amount_received": "300.00",
                    "customer_name": "Test",
                    "updatedon": "17-11-2020 16:2:43",
                    "email": null,
                    "mobile": "9652364896",
                    "attachment": null,
                    "final_bal": "100.00",
                    "flag": "CREDIT_GIVEN"
                },
                {
                    "ma_user_id": 28479,
                    "uuid": "****************",
                    "total_amount_given": "500.00",
                    "total_amount_received": "700.00",
                    "customer_name": "Test",
                    "updatedon": "12-11-2020 15:35:55",
                    "email": null,
                    "mobile": "**********",
                    "attachment": null,
                    "final_bal": "200.00",
                    "flag": "CREDIT_RECEIVED"
                }
            ],
          "action_code": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.
* @apiError {String} nextFlag  Pagination Flag.
* @apiError {Array} CustomerList  Array .

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCustomerWiseBalance": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "nextFlag": false,
      "CustomerList": [],
      "action_code": 1001
    }
  }
}
*/

// -------------- Khata Book Account Balance -----------------
/**
* @api {post} /khatabookTransaction Khataw Book Balance
* @apiName getAccountWiseBalance
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getAccountWiseBalance(ma_user_id:28479,userid:7556,ma_kb_account_master_id:4){status, message,respcode,credit_given,credit_received,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to fetch the credit amount received and credit amount given against a particular khata</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} credit_given  total credit amount given.
* @apiSuccess {String} credit_received  total credit amount received.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getAccountWiseBalance": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "credit_given": "300.00",
            "credit_received": "400.00",
            "action_code": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.
* @apiError {String} credit_given  total credit amount given.
* @apiError {String} credit_received  total credit amount received.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getAccountWiseBalance": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "credit_given": "0.00",
      "credit_received": "0.00",
      "action_code": 1001
    }
  }
}
*/
// -------------- Set Due Date -----------------
/**
* @api {post} /khatabookCustomerV2 Set Reminder Due Date
* @apiName setDueDate
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{setDueDate(ma_user_id:28479,userid: 7556,due_date: \"2020-11-20\",ma_kb_account_master_id:4,uuid: \"****************\"){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to set  payment reminder due date</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "setDueDate": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "setDueDate": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/
// -------------- Sent Payment Reminder -----------------
/**
* @api {post} /khatabookCustomerV2 Sent Payment Reminder
* @apiName sentPaymentReminder
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{sentPaymentReminder(ma_user_id:28479,userid: 7556,ma_kb_account_master_id:4,uuid: \"****************\"){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to sent immediate payment reminder</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "sentPaymentReminder": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "sentPaymentReminder": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/
// -------------- File Upload -----------------
/**
* @api {post} /upload File Upload
* @apiName upload
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} request json request
* @apiParamExample {json} Request-Example:
{ "base64String" : "iVBORw0KGgoAAAANSUhEUgAABVYAAAMACAYAAADPPjzCAAAAAXNSR0IArs4cL7X2FEsKQ72NZRX7KxBpu/rDDN/6njUmi9WjTfeeOPFfwPeQJunDA3EYwAAAABJRU5ErkJggg==","ma_user_id":"28479","userid":"7556","module": "khatabook"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to upload files</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uploadid  Uploadid against the file.
* @apiSuccess {String} basepath  basepath of file.
* @apiSuccess {String} fullpath  full path of file.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  {
  "status":200,
  "message":"Success",
  "uploadid":10,
  "basepath":"1a0141d472fb2d0a0be41810f7cd2250812b7830/1606282702.png",
  "fullpath":"https://retailappdocs.s3.ap-south-1.amazonaws.com/1a0141d472fb2d0a0be41810f7cd2250812b7830/1606282702.png"
}
}
* @apiError {String} status API status code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  {
  "status":400,
  "message":"Error In File Upload"
  }
}
*/
// -------------- Edit Khata Book Transaction -----------------
/**
* @api {post} /khatabookTransactionV2 Edit khatabookTransaction
* @apiName editTransaction
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{editTransaction(ma_user_id:28479,userid: 7556,uuid: \"****************\",amount:\"200\",ma_kb_transaction_master_id:12,transaction_date: \"2020-12-03\",description:\"Money transfer Received\",attachment: \"test.png\",uploadid:5){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to edit a khatabook transaction.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "editTransaction": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "editTransaction": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/

// -------------- Delete Khata Book Transaction -----------------
/**
* @api {post} /khatabookTransactionV2 Delete khatabookTransaction
* @apiName deleteTransaction
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{deleteTransaction(ma_user_id:28479,userid: 7556,ma_kb_transaction_master_id:13){status, message,respcode,action_code}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to delete a khatabook transaction.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "deleteTransaction": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "deleteTransaction": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/

// -------------- Get Customer Balance -----------------
/**
* @api {post} /khatabookTransactionV2 Delete khatabookTransaction
* @apiName getCustomerBalance
* @apiGroup KHATABOOK-V2

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getCustomerBalance(ma_user_id:28479,userid:7556,uuid:\"****************\"){credit_given,credit_received,flag,final_bal,action_code,customer_data{ name,email,mobile,attachment}}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to get customer balance.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getCustomerBalance": {
            "credit_given": "1500.00",
            "credit_received": "1000.00",
            "flag": "CREDIT_GIVEN",
            "final_bal": "500.00",
            "action_code": 1000,
            "customer_data": [
                {
                    "name": "Test",
                    "email": null,
                    "mobile": "9652364881",
                    "attachment": ""
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCustomerBalance": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "action_code": 1001
    }
  }
}
*/
