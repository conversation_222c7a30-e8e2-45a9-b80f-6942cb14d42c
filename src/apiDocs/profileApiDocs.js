// -------------- Profile Details - APIs -----------------
// -------------- Get Profile Details -----------------
/**
* @api {post} /Profile 1) Get Profile Details
* @apiName getProfileDetails
* @apiGroup Profile

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
  getProfileDetails(ma_user_id: 28791, userid: 1460) {
    status
    message
    respcode
    profileDetails
  }
}
 
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Get the Details of the User</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getProfileDetails": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "profileDetails": {
        "secret": "RbVqe3xTjiwvlYCB",
        "username": "2374952",
        "password": "EpSl3xNJ",
        "user_id": 1460,
        "mid": 28776,
        "gcm_key": "",
        "mer_name": null,
        "gst": null,
        "pan": null,
        "address": "mumbai",
        "city": "Mumbai",
        "state": "Maharashtra",
        "country": "india",
        "email": "<EMAIL>",
        "mobile": "7736969460",
        "pincode": 300040,
        "viracc": "",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvc3JldGFpbGEuYWlycGF5Lm5pbmphXC9hcGlcL2xvZ2luIiwiaWF0IjoxNTk4NTQxMzY3LCJleHAiOjE1OTg2Mjc3NjcsIm5iZiI6MTU5ODU0MTM2NywianRpIjoiekR5enlEbzFkQ2pjcmFFcCIsInB1YmxpY2tleSI6IjExMTgifQ.s34W54iyD-W4uURaumdjrq-g6aL7p5i78eP0HNwvAjU",
        "dist_username": "2374952",
        "dist_password": "EpSl3xNJ",
        "dist_apikey": "RbVqe3xTjiwvlYCB",
        "dist_mid": 18999,
        "dist_name": "Ahindra Sinha",
        "dist_company": null,
        "dist_mobile": "9167068433",
        "dt_username": "",
        "dt_password": "",
        "dt_apikey": "",
        "dt_mid": "",
        "dt_name": "",
        "dt_company": "",
        "dt_mobile": "",
        "user_type": "RETAILER",
        "gcm_id": "",
        "logo": "",
        "esign_link": "",
        "created": "12-17-2019",
        "valid_upto": "12-17-2020",
        "message": "success",
        "respcode": 1000,
        "checksum": "5258a3fca1238558cf6d1bb45fcb2ebe",
        "status": 200
      }
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getProfileDetails": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "profileDetails": null
    }
  }
}
*/

// -------------- Get Call Center Data -----------------
/**
* @api {post} /Profile  2) Get Call Center Data
* @apiName getCallCenterDetails
* @apiGroup Profile

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  {
    getCallCenterDetails(ma_user_id: 28791, userid: 1460) {
        status,
        message,
        respcode,
        callCenterData
    }
}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used to get the Call center details. </code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCallCenterDetails": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "callCenterData": {
        "tsmmobile": 7736969411,
        "tsmemail": "<EMAIL>",
        "tsmname": "Anshad tets",
        "asmmobile": "",
        "asmname": "",
        "asmemail": "",
        "supportnumber": "02268870500",
        "supportemail": "<EMAIL>",
        "supporttime": "9am to 9pm",
      }
    }
  }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCallCenterDetails": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "callCenterData": null
    }
  }
}
*/

// -------------- Get Channel Listing -----------------
/**
* @api {post} /Profile 3) Get Channel Listing Data
* @apiName getChannelListing
* @apiGroup Profile

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  {
  getChannelListing(ma_user_id: 28791, userid: 1460) {
    status
    message
    respcode
    channelData{
      title,
      channels{
        status,
        display_name,
        order,
        web_icon,
        app_icon,
        key
      }
    }
  }
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Channels assigned to user</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getChannelListing": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "channelData": [
        {
          "title": "Recharge & Billpay",
          "channels": [
            {
              "status": true,
              "display_name": "Mobile Postpaid",
              "order": "1",
              "web_icon": "test",
              "app_icon": "test",
              "key": "10"
            },
            {
              "status": true,
              "display_name": "Recharge",
              "order": "2",
              "web_icon": "test",
              "app_icon": "test",
              "key": "16"
            },
            {
              "status": true,
              "display_name": "Electricity",
              "order": "3",
              "web_icon": "test",
              "app_icon": "test",
              "key": "1"
            },
            {
              "status": true,
              "display_name": "Gas",
              "order": "4",
              "web_icon": "test",
              "app_icon": "test",
              "key": "3"
            },
            {
              "status": true,
              "display_name": "Water",
              "order": "5",
              "web_icon": "test",
              "app_icon": "test",
              "key": "8"
            },
            {
              "status": true,
              "display_name": "Broadband Postpaid",
              "order": "6",
              "web_icon": "test",
              "app_icon": "test",
              "key": "12"
            },
            {
              "status": true,
              "display_name": "Tax",
              "order": "7",
              "web_icon": "test",
              "app_icon": "test",
              "key": "9"
            },
            {
              "status": true,
              "display_name": "Mobile Prepaid",
              "order": "8",
              "web_icon": "test",
              "app_icon": "test",
              "key": "17"
            },
            {
              "status": true,
              "display_name": "Loan",
              "order": "9",
              "web_icon": "test",
              "app_icon": "test",
              "key": "15"
            },
            {
              "status": true,
              "display_name": "Rental Payment",
              "order": "10",
              "web_icon": "test",
              "app_icon": "test",
              "key": "14"
            },
            {
              "status": true,
              "display_name": "Subscription",
              "order": "11",
              "web_icon": "test",
              "app_icon": "test",
              "key": "13"
            },
            {
              "status": true,
              "display_name": "Landline Postpaid",
              "order": "12",
              "web_icon": "test",
              "app_icon": "test",
              "key": "11"
            },
            {
              "status": true,
              "display_name": "Mutual Fund",
              "order": "13",
              "web_icon": "test",
              "app_icon": "test",
              "key": "7"
            },
            {
              "status": true,
              "display_name": "Credit Card",
              "order": "14",
              "web_icon": "test",
              "app_icon": "test",
              "key": "6"
            },
            {
              "status": true,
              "display_name": "Donation",
              "order": "15",
              "web_icon": "test",
              "app_icon": "test",
              "key": "5"
            },
            {
              "status": true,
              "display_name": "Insurance",
              "order": "16",
              "web_icon": "test",
              "app_icon": "test",
              "key": "4"
            },
            {
              "status": true,
              "display_name": "Telecom",
              "order": "17",
              "web_icon": "test",
              "app_icon": "test",
              "key": "2"
            }
          ]
        },
        {
          "title": "Featured Services",
          "channels": [
            {
              "status": true,
              "display_name": "AEPS",
              "order": "1",
              "web_icon": "www,x",
              "app_icon": null,
              "key": "AEPS"
            },
            {
              "status": true,
              "display_name": "Transfer Money",
              "order": "6",
              "web_icon": "test",
              "app_icon": "test",
              "key": "DMT"
            },
            {
              "status": true,
              "display_name": "Recharge & Billpay",
              "order": "2",
              "web_icon": "test",
              "app_icon": "test",
              "key": "BBPS"
            },
            {
              "status": true,
              "display_name": "Insurance",
              "order": "9",
              "web_icon": "test",
              "app_icon": "test",
              "key": "INSURANCE"
            }
          ]
        }
      ]
    }
  }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getChannelListing": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "channelData": null
    }
  }
}
*/

// -------------- Get Insurance Categories -----------------
/**
* @api {post} /Profile 4) Get Insurance Category Data
* @apiName getInsuranceCategory
* @apiGroup Profile

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  {
    {
        getInsuranceCategory(ma_user_id: 28791, userid: 1460) {
        status
        message
        respcode
        insuranceList{
            category_name,
            icon,
            category_code
        }
  }
}

}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for getting Insurance Categories</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getInsuranceCategory": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "insuranceList": [
        {
          "category_name": "Tw Wheeler Comprehensive",
          "icon": "https://HealthInsurance.png",
          "category_code": "TWC"
        },
        {
          "category_name": "Life/Term Insurance",
          "icon": "https://HealthInsurance.png",
          "category_code": "LT"
        }
      ]
    }
  }
}
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getInsuranceCategory": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "insuranceList": null
    }
  }
}
*/

// -------------- v2Dashboard -----------------
/**
* @api {post} /Profile 5) v2Dashboard
* @apiName v2Dashboard
* @apiGroup Profile

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    v2Dashboard(ma_user_id:28620, userid:1148,channelList:true, displayTypeMarquee:true, displayTypeNotice:true, getKyc:true)
    {
      status,message,respcode,action_code,channelData{title,channels{order,status,display_name,key,web_icon,app_icon}},
      displayTypeMarquee{messageText,addedon,url,clickableFlag},displayTypeNotice{messageText,addedon,url,clickableFlag},
      getKyc{merchantType,pendingEKyc{ekyc_id,bank_name,bank_logo,bank_status,biometric_kyc,ekyc_biometric_status,
      ekyc_otp_status,orderid,other_params}}
    }
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{
    v2Dashboard(ma_user_id:28620, userid:1148,channelList:true, displayTypeMarquee:true, displayTypeNotice:true, getKyc:true)
    {
      status,message,respcode,action_code,channelData{title,channels{order,status,display_name,key,web_icon,app_icon}},
      displayTypeMarquee{messageText,addedon,url,clickableFlag},displayTypeNotice{messageText,addedon,url,clickableFlag},
      getKyc{merchantType,pendingEKyc{ekyc_id,bank_name,bank_logo,bank_status,biometric_kyc,ekyc_biometric_status,
      ekyc_otp_status,orderid,other_params}}
    }
}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "v2Dashboard": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "action_code": 1000,
      "channelData": [
        {
          "title": "Recharge & Billpay",
          "channels": [
            {
              "order": "1",
              "status": true,
              "display_name": "Mobile Postpaid",
              "key": "10",
              "web_icon": "test",
              "app_icon": "test"
            },
            {
              "order": "2",
              "status": true,
              "display_name": "Recharge",
              "key": "16",
              "web_icon": "test",
              "app_icon": "test"
            }
          ]
        },
        {
          "title": "Featured Services",
          "channels": [
            {
              "order": "1",
              "status": true,
              "display_name": "AEPS",
              "key": "AEPS",
              "web_icon": "www,x",
              "app_icon": ""
            },
            {
              "order": "2",
              "status": true,
              "display_name": "Recharge & Billpay",
              "key": "BBPS",
              "web_icon": "test",
              "app_icon": "test"
            },
            {
              "order": "6",
              "status": true,
              "display_name": "Transfer Money",
              "key": "DMT",
              "web_icon": "test",
              "app_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/ekycBankMaster/wtK7Py4FPfkHqUjBcM3Oe8bFEOmV3VyjRiiC1RMZ.png"
            },
            {
              "order": "7",
              "status": true,
              "display_name": "FASTag",
              "key": "FASTAG",
              "web_icon": "test",
              "app_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/ekycBankMaster/Nd73oIp7jEYdXEqXiPdDkVovKJs2SEy7XhxPBTZ8.png"
            },
            {
              "order": "8",
              "status": true,
              "display_name": "GOLD",
              "key": "GOLD",
              "web_icon": "test",
              "app_icon": "https://retailappdocs.s3.ap-south-1.amazonaws.com/ekycBankMaster/i29ro8uvIzdQkA6Kp6kTpPABW25DLn3V7WgrS8bU.png"
            },
          ]
        }
      ],
      "displayTypeMarquee": [
        {
          "messageText": "Increase your business with a Double DMT limit. Connect with our representative today. Download the app arpy.in/apvp.",
          "addedon": "18-06-2021",
          "url": null,
          "clickableFlag": false
        },
        {
          "messageText": "marque2",
          "addedon": "26-05-2021",
          "url": "https://retaila.airpay.ninja/masters/notices/create2",
          "clickableFlag": true
        },
        {
          "messageText": "marque1",
          "addedon": "26-05-2021",
          "url": "https://retaila.airpay.ninja/masters/notices/create",
          "clickableFlag": true
        },
        {
          "messageText": "Priya vyaapaari, abh kamaye zyada DMT commission 3 mahine tak Rs.101 ke plan se. Sampark karein TSM",
          "addedon": "20-04-2021",
          "url": null,
          "clickableFlag": false
        }
      ],
      "displayTypeNotice": [
        {
          "messageText": "Test",
          "addedon": "03-09-2020",
          "url": null,
          "clickableFlag": false
        },
        {
          "messageText": "Test",
          "addedon": "03-09-2020",
          "url": null,
          "clickableFlag": false
        },
        {
          "messageText": "Test",
          "addedon": "03-09-2020",
          "url": null,
          "clickableFlag": false
        }
      ],
      "getKyc": [
        {
          "merchantType": "MERCHANT",
          "pendingEKyc": [
            {
              "ekyc_id": 1,
              "bank_name": "Fingpay",
              "bank_logo": "https://retailappdocs.s3.ap-south-1.amazonaws.com/ekycBankMaster/4cAAKRkq2FkbicusjQyWgXGt2ewL5DUyMhuSgMKA.png",
              "bank_status": "A",
              "biometric_kyc": "YES",
              "ekyc_biometric_status": "PENDING",
              "ekyc_otp_status": "PENDING",
              "orderid": null,
              "other_params": "{\"wadh\":\"E0jzJ/P8UopUHAieZn8CKqS4WPMi5ZSYXgfnlfkWjrc=\"}"
            }
          ]
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "v2Dashboard": {
      "status": 400,
      "message": "Fail",
      "respcode": 1001,
      "action_code": 1001,
      "channelData": null,
      "displayTypeMarquee": null,
      "displayTypeNotice": null,
      "getKyc": null
    }
  }
}
*/