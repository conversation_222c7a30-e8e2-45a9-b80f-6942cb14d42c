Steps for API documentation

Airpay Retail API Docs 
----------------------
----------------------

1 ) Installation
-----------------
npm install apidoc -g

2) Take pull from retaildocs repository
---------------------------------------
http://gitlab.airpay.co.in/core/retaildocs.git


3) Add api docs comments for API in your code according to apidocs format
-------------------------------------------------------------------------
Format for api comments:
  /**
   * @api {post} /aepstransaction AEPS transaction
   * @apiName doAepsTransaction
   * @apiGroup Transaction
   *
   * @apiHeader {String} Authorization JWT Token.
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
   *     }
   *
   * @apiParam {query} string Encrypted Query/Mutation.
   * @apiParamExample {json} Request-Example:
   *     {
   *       doAepsTransaction(
   *          ma_user_id:28479,
   *          userid:7556,
   *          aggregator_order_id:"MAWEB91641602594665",
   *          remarks:"AEPS TRANSACTION",
   *          mobile_number:"**********",
   *          customer_name:"",
   *          customer_aadhaar:"************",
   *          captureResponse:"eyJQaWREYXRhdHlwZSI6Il",
   *          cardnumberORUID:"****************************************************************************************************************************************************************",
   *          languageCode: "en",
   *          latitude:"19.7514798",
   *          longitude:"75.7138884",
   *          mobileNumber:"**********",
   *          paymentType:"B",
   *          requestRemarks:"",
   *          timestamp:"**********",
   *          transactionType:"BE",
   *          merchantUserName: "",
   *          merchantPin: "",
   *          subMerchantId: "",
   *          call_type: "aeps",
   *          bank_name: "HDFC",
   *          email:"<EMAIL>",
   *          privatekey:"6bc2ceef179b4f0b003fa9f6f163f3869505a9cdb21d84f36ba2c80ad9f511c1",
   *          checksum:"8324294d36e48fc5e9779cc6f88348c7",
   *          mer_dom:"Imh0dHBzOi8vYXBtZXJjaGFudGFwcC5ub3dwYXkuY28uaW4i")
   *         {status,message,data}
   *      }
   *
   * @apiDescription <code>GraphQL Request</code>.
    * <pre><code>{"query":"mutation{doAepsTransaction(ma_user_id:28479,userid:7556,aggregator_order_id:"MAWEB91641602594665",remarks:"AEPS TRANSACTION",mobile_number:"**********",customer_name:"",customer_aadhaar:"************",captureResponse:"eyJQaWREYXRhdHlwZSI6Il", cardnumberORUID:"****************************************************************************************************************************************************************", languageCode: "en",latitude:"19.7514798",longitude:"75.7138884",mobileNumber:"**********",paymentType:"B",requestRemarks:"",timestamp:"**********",transactionType:"BE",merchantUserName: "",merchantPin: "", subMerchantId: "",call_type: "aeps",bank_name: "HDFC",email:"<EMAIL>",privatekey:"6bc2ceef179b4f0b003fa9f6f163f3869505a9cdb21d84f36ba2c80ad9f511c1",checksum:"8324294d36e48fc5e9779cc6f88348c7",mer_dom:"Imh0dHBzOi8vYXBtZXJjaGFudGFwcC5ub3dwYXkuY28uaW4i"){status,message,data}}"}</code></pre>
    *
   *
   * @apiSuccess {String} status API status code.
   * @apiSuccess {String} respcode  API response code.
   * @apiSuccess {String} message  API response message.
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *     {
   *      "data": {
   *        "doAepsTransaction": {
   *          "status": 200,
   *          "message": "Success",
   *          "data": "eyJzdGF0dXMiOmZhbHNlLCJtZXNzYWdlIjoiRHVwbGljYXRlIEJpb21ldHJpYyBkYXRhIiwiZGF0YSI6bnVsbCwic3RhdHVzQ29kZSI6MTAwMjd9"
   *        }
   *      }
   *    }
   *
   * @apiError {String} status API status code.
   * @apiError {String} respcode  API response code.
   * @apiError {String} message  API response message.
   *
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 200 OK
   *     {
   *      "data": {
   *        "doAepsTransaction": {
   *          "status": 400,
   *          "respcode": 1001,
   *          "message": "Something went wrong"
   *        }
   *      }
   *    }
   */


4) After adding api comments update the docs with below command
---------------------------------------------------------------
apidoc -i merchantapp/src/ -o path_to_docs_folder/ 

-i -> is the input folder to search for docs  --- mechant app src folder
-o -> is the output folder to generate docs --- docs folder

5) After updating the docs push it to retaildocs repo
-----------------------------------------------------

6) Make a host entry for - ************* retaildocs.airpay.ninja
----------------------------------------------------------------

7) You can check the api docs UI at https://retaildocs.airpay.ninja
-------------------------------------------------------------------

NOTE : There is no deployment after pushing the docs. 
You need to login to ssh root@************* 
password : root123

Go to /var/www/retail-wiki
and take a pull from master. Docs will be updated after taking the pull.
