// -------------- UMANG Iframe URL API-----------------

/**
* @api {post} /umang) Log user activity
* @apiName getUmangIframe
* @apiGroup UMNAG API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getUmangIframe(ma_user_id: 2925521, userid: 1) {status,respcode,message,umang_iframe_url}}"}

* @apiParamExample {json} Request-Example: for APP
{"query":"{getUmangIframe(ma_user_id: 2925521, userid: 1) {status,respcode,message,umang_iframe_url,action_code}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Iframe for UMANG</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} umang iframe url string.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getUmangIframe": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "umang_iframe_url": "https://web.umang.gov.in/assistive?tenantId=airpay.co.in&domain=airpay.co.in&token=6p963412173089bbja64y53dbdb88hea15ty689c23a5c50ce71642fde03hd10f&mno=9833573307"
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} umang_iframe_url  API barcode string.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": { "getUmangIframe": { "message": "fail : Something went wrong.", "respcode": 1001, "status": 400}}}
*/

/**
* @api {post} /umang) Log user activity
* @apiName getUmangReceiptDetails
* @apiGroup UMNAG API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getUmangReceiptDetails(ma_user_id: 2925521,userid:1, ma_transaction_master_id: 1) {status,respcode,message, department_name,   agent_id,          service_name,          tracker_id,          transaction_status}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get UMANG Transaction Receipt Details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} department_name.
* @apiSuccess {String} agent_id.
* @apiSuccess {String} service_name.
* @apiSuccess {String} tracker_id.
* @apiSuccess {String} transaction_status.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getUmangReceiptDetails": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "department_name": "COWIN",
            "agent_id": "9876543216",
            "service_name": "GenerateOTP_v2",
            "tracker_id": "44d166082220223",
            "transaction_status": null
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": { "getUmangReceiptDetails": { "message": "fail : Something went wrong.", "respcode": 1001, "status": 400}}}
*/
/**
* @api {post} /umang) Log user activity
* @apiName getUmangTransactionDetails
* @apiGroup UMNAG API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getUmangTransactionDetails(userid:2294,ma_user_id:29253,datefrom: \"2022-09-21\",dateto: \"2022-09-29\",limit:10,offset:0){status,respcode,message,nextFlag,transactionList{merchant_name,ma_assesstive_details_id,partner_id,agent_mobile_no,department_id,department_name,service_id,service_name,txn_date_time,reference_txn_id,dep_state_code,udf1,udf2,transaction_type,sub_service_id}}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get UMANG Transaction Receipt Details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} nextFlag.
* @apiSuccess {String} transactionList.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getUmangTransactionDetails": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "nextFlag": false,
            "merchant_name": null,
            "transactionList": [
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 32,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "",
                    "agent_mobile_no": "null",
                    "department_id": 369,
                    "department_name": "Esanjeevani",
                    "service_id": 1723,
                    "service_name": "getcitymaster",
                    "txn_date_time": "1664340188000",
                    "reference_txn_id": "null",
                    "dep_state_code": null,
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 31,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "",
                    "agent_mobile_no": "null",
                    "department_id": 369,
                    "department_name": "Esanjeevani",
                    "service_id": 1720,
                    "service_name": "getgendermaster",
                    "txn_date_time": "1664340172000",
                    "reference_txn_id": "null",
                    "dep_state_code": null,
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 30,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "",
                    "agent_mobile_no": "null",
                    "department_id": 369,
                    "department_name": "Esanjeevani",
                    "service_id": 1720,
                    "service_name": "getstatemaster",
                    "txn_date_time": "1664340172000",
                    "reference_txn_id": "null",
                    "dep_state_code": null,
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 29,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "",
                    "agent_mobile_no": "null",
                    "department_id": 369,
                    "department_name": "Esanjeevani",
                    "service_id": 1719,
                    "service_name": "patientverifyotp",
                    "txn_date_time": "1664340153000",
                    "reference_txn_id": "null",
                    "dep_state_code": null,
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 28,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "",
                    "agent_mobile_no": "null",
                    "department_id": 369,
                    "department_name": "Esanjeevani",
                    "service_id": 1717,
                    "service_name": "landingStats",
                    "txn_date_time": "1664340045000",
                    "reference_txn_id": "null",
                    "dep_state_code": null,
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 27,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "29299",
                    "agent_mobile_no": "9619865113",
                    "department_id": 355,
                    "department_name": "Co-WIN",
                    "service_id": 1606,
                    "service_name": "Nearest Vaccination Centre",
                    "txn_date_time": "1664337678000",
                    "reference_txn_id": "n1664337678204",
                    "dep_state_code": "centre",
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 26,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "29299",
                    "agent_mobile_no": "9619865113",
                    "department_id": 376,
                    "department_name": "CRIS NGET",
                    "service_id": 1968,
                    "service_name": "Train Schedule",
                    "txn_date_time": "1664277387000",
                    "reference_txn_id": "k1664277385446",
                    "dep_state_code": "centre",
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 25,
                    "partner_id": "airpay",
                    "ma_user_id": "",
                    "agent_mobile_no": "9876543216",
                    "department_id": 355,
                    "department_name": "COWIN",
                    "service_id": 1604,
                    "service_name": "GenerateOTP_v2",
                    "txn_date_time": "1660822202000",
                    "reference_txn_id": "44d166082220223",
                    "dep_state_code": null,
                    "udf1": "t1",
                    "udf2": "t2",
                    "transaction_type": "Leaf",
                    "sub_service_id": null
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 24,
                    "partner_id": "airpay",
                    "ma_user_id": "",
                    "agent_mobile_no": "9876543216",
                    "department_id": 355,
                    "department_name": "COWIN",
                    "service_id": 1604,
                    "service_name": "GenerateOTP_v2",
                    "txn_date_time": "1660822202000",
                    "reference_txn_id": "44d166082220223",
                    "dep_state_code": null,
                    "udf1": "t1",
                    "udf2": "t2",
                    "transaction_type": "Leaf",
                    "sub_service_id": null
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 23,
                    "partner_id": "airpay.co.in",
                    "ma_user_id": "",
                    "agent_mobile_no": "9566235315",
                    "department_id": 355,
                    "department_name": "Co-WIN",
                    "service_id": 1605,
                    "service_name": "Download Vaccination Certifcate",
                    "txn_date_time": "1664187066000",
                    "reference_txn_id": "i1664187064038",
                    "dep_state_code": "centre",
                    "udf1": null,
                    "udf2": null,
                    "transaction_type": "end",
                    "sub_service_id": "0"
                },
                {
                    "merchant_name": "Eldhose Eldhose",
                    "ma_assesstive_details_id": 22,
                    "partner_id": "airpay",
                    "ma_user_id": "29240",
                    "agent_mobile_no": "9167788941",
                    "department_id": 355,
                    "department_name": "COWIN",
                    "service_id": 1604,
                    "service_name": "GenerateOTP_v2",
                    "txn_date_time": "1660822202000",
                    "reference_txn_id": "d1660822202239",
                    "dep_state_code": null,
                    "udf1": "t1",
                    "udf2": "t2",
                    "transaction_type": "Leaf",
                    "sub_service_id": null
                },
                {
                    "merchant_name": null,
                    "ma_assesstive_details_id": 21,
                    "partner_id": "airpay",
                    "ma_user_id": "",
                    "agent_mobile_no": "9876543216",
                    "department_id": 355,
                    "department_name": "COWIN",
                    "service_id": 1604,
                    "service_name": "GenerateOTP_v2",
                    "txn_date_time": "1660822202000",
                    "reference_txn_id": "d1660822202239",
                    "dep_state_code": null,
                    "udf1": "t1",
                    "udf2": "t2",
                    "transaction_type": "Leaf",
                    "sub_service_id": null
                }
            ]
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": { "getUmangTransactionDetails": { "message": "fail : Something went wrong.", "respcode": 1001, "status": 400}}}
*/
