// -------------- Credit Reversal API-----------------

/**
* @api {post} /user 1) Send Reverse Credit Otp
* @apiName sendReverseCreditOtp
* @apiGroup CREDIT REVERSAL API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{sendReverseCreditOtp(ma_user_id:100046,userid:12174, transaction_orderid: "MATOPUP00199", creditamount: 1000, transaction_orderdate: "07-12-2021 16:54:12", retailerid : 29138, action: SEND ){status,message,respcode,orderid, transaction_orderdate, transaction_orderid,creditamount,retailerid}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API send Otp to Retailer for credit reversal request.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  API otp order id.
* @apiSuccess {String} transaction_orderdate  API transaction order date.
* @apiSuccess {String} transaction_orderid  API transaction order id.
* @apiSuccess {String} creditamount  API credit amount.
* @apiSuccess {String} retailerid  API retailer id message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"sendReverseCreditOtp": {"status": 200,"message": "Success","respcode": 1000,"orderid": "MACR78081639119743","transaction_orderdate": "07-12-2021 16:54:12","transaction_orderid": "MATOPUP00199","creditamount": "1000.00","retailerid": 29138}}}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"sendReverseCreditOtp": {"status": 400,"message": "Fail: Something Went Wrong.","respcode": 1028}}}
*/

/**
* @api {post} /user 2) Resend Reverse Credit Otp
* @apiName resendReverseCreditOtp
* @apiGroup CREDIT REVERSAL API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{resendReverseCreditOtp(ma_user_id:100046,userid:12174, transaction_orderid: "MARC123456789", creditamount: 1000, transaction_orderdate: "07-12-2021 16:54:12", retailerid : 29138, orderid: "MACR61701638869206", action: RESEND ){status,message,respcode,orderid, transaction_orderdate, transaction_orderid,creditamount,retailerid}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to resend the Otp for Credit Reversal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  API otp order id.
* @apiSuccess {String} transaction_orderdate  API transaction order date.
* @apiSuccess {String} transaction_orderid  API transaction order id.
* @apiSuccess {String} creditamount  API credit amount.
* @apiSuccess {String} retailerid  API retailer id message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"resendReverseCreditOtp": {"status": 200,"message": "Success: OTP sent successfully","respcode": 1000,"orderid": "MACR78081639119743","transaction_orderdate": "07-12-2021 16:54:12","transaction_orderid": "MATOPUP00199","creditamount": "1000.00","retailerid": 29138}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"resendReverseCreditOtp": {"status": 400,"message": "Fail: Something Went Wrong.","respcode": 1028}}}
*/

/**
* @api {post} /user 3) Verify Reverse Credit Otp
* @apiName verifyReverseCreditOtp
* @apiGroup CREDIT REVERSAL API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{verifyReverseCreditOtp(ma_user_id:100046,userid:12174, transaction_orderid: "MATOPUP001", creditamount: 1000, transaction_orderdate: "07-12-2021 16:54:12", retailerid : 29138, orderid: "MACR78081639119743", otp:"123456", action: VERIFY ){status,message,respcode,orderid, transaction_orderdate, transaction_orderid,creditamount,retailerid}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to verify the Otp & debit the amount from retailer and credit to distributors wallet.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"verifyReverseCreditOtp": {"status": 200,"message": "Credit Reversal Completed Successfully,"respcode": 1000}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"verifyReverseCreditOtp": {"status": 400,"message": "Fail: Something Went Wrong","respcode": 1001}}}
*/