// -------------- Add support ticket -----------------
/**
* @api {post} /supportticket 1) Add support ticket
* @apiName addSupportTicket
* @apiGroup Support Ticket

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  addSupportTicket(
    subject:"test",
    description: "test",
    support_type:"Settlement",
    support_status: RAISED,
    userid:1251,
    order_id:""
  ){status,respcode,message}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{addSupportTicket(subject:"test",description: "test",support_type:"Settlement",support_status: RAISED,userid:1251,order_id:""){status,respcode,message}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "addSupportTicket": {
      "status":200,
      "respcode":1000,
      "message":"Success"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
"data": {
  "addSupportTicket": {
    "status": 400,
    "respcode": 1001,
    "message": "Something went wrong"
  }
}
}
*/
