// -------------- Get Verify Beneficiary List-----------------
/**
* @api {post} /beneficiary 1) Get Verify Beneficiary List
* @apiName getBeneVerifyBankList
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{
    "query": "{getBeneVerifyBankList(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"***************\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status,respcode,message,beneVerifyBankList{bank_name,bank_logo,bankRegisterStatus,ma_bank_on_boarding_id}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{getBeneVerifyBankList(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"***************\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status,respcode,message,beneVerifyBankList{bank_name,bank_logo,bankRegisterStatus,ma_bank_on_boarding_id}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} beneVerifyBankList  Bank List

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBeneVerifyBankList": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "beneVerifyBankList": [
                {
                    "bank_name": "AIRTEL",
                    "bank_logo": "https://i.ibb.co/sv8GsNS/Airtel-Payments-Bank-Logo.jpg",
                    "bankRegisterStatus": "REGISTERED",
                    "ma_bank_on_boarding_id": 10
                }
            ]
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} beneVerifyBankList  Bank List

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBeneVerifyBankList": {
            "status": 400,
            "respcode": 1001,
            "message": "Fail: Something went wrong",
            "beneVerifyBankList": []
        }
    }
}
*/

// -------------- Verify and Add Beneficiary -----------------
/**
* @api {post} /beneficiary 2) Verify and Add Beneficiary
* @apiName addBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } beneficiary_name BENEFICIARY NAME
* @apiParam { IntValue } ma_bank_master_id MA BANK MASTER ID
* @apiParam { IntValue } ma_bank_verification_id MA BANK ON BOARDING_ID OPTIONAL
* @apiParam { StringValue } account_number ACCOUNT NUMBER
* @apiParam { StringValue } re_account_number RE ACCOUNT NUMBER
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } ben_mobile_number BEN MOBILE NUMBER
* @apiParam { StringValue } ifsc_code IFSC CODE
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } relationship RELATIONSHIP
* @apiParam { BooleanValue } bank_verify BANK VERIFY
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{addBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Sanjay Ben two\", ma_bank_master_id: 75,account_number:\"**********\",re_account_number:\"**********\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"SBIN0000054\",uic:\"***************\",relationship:\"Brother\",bank_verify:true,aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\",ma_bank_verification_id:5){status, message,respcode,orderid,isOtpRequired,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id,bankRegisterStatus}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{addBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Sanjay Ben two\", ma_bank_master_id: 75,account_number:\"**********\",re_account_number:\"**********\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"SBIN0000054\",uic:\"***************\",relationship:\"Brother\",bank_verify:true,aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status, message,respcode,orderid,isOtpRequired,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id,bankRegisterStatus}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {Boolean} isOtpRequired  OTP POPUP Flag.
* @apiSuccess {Number} ma_beneficiaries_id  Beneficiary Id
* @apiSuccess {Number} ma_bene_verification_id  Verification Id
* @apiSuccess {Number} verification_message  Verification message
* @apiSuccess {String} bank_bene_name  Bank Bene Name
* @apiSuccess {String} bank_account_number  Bank account number
* @apiSuccess {String} bene_verify_status  VERIFIED/PENDING/FAILED/UNVERIFIED/PENDING/INITIATED
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiSuccessExample Success-isOtpRequired-False-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": null,
            "isOtpRequired": false,
            "ma_beneficiaries_id": "537",
            "ma_bene_verification_id": 148,
            "verification_message": "Bank Verified & Beneficiary name empty",
            "bank_bene_name": "Rajkumar Changedd",
            "bank_account_number": "**********",
            "bene_verify_status": "VERIFIED",
            "ben_mobile_number": null,
            "ifsc_code": "SBIN0000054",
            "tryWithOtherBank": null
        }
    }
}
* @apiSuccessExample Success-isOtpRequired-True-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": null,
            "isOtpRequired": true,
            "ma_beneficiaries_id": "537",
            "ma_bene_verification_id": 148,
            "verification_message": "Bank Verified & Beneficiary name empty",
            "bank_bene_name": "Rajkumar Changedd",
            "bank_account_number": "**********",
            "bene_verify_status": "VERIFIED",
            "ben_mobile_number": null,
            "ifsc_code": "SBIN0000054",
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.
* @apiError {Array} tryWithOtherBank  Try with other bank array in which customer can try with another bank

* @apiErrorExample Error-Bene-Already-Exists-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 400,
            "message": "Fail: Beneficiary already exists",
            "respcode": 1032,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null,
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.
* @apiError {Array} tryWithOtherBank  Try with other bank array in which customer can try with another bank

* @apiErrorExample Error-tryWithOtherBank-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 400,
            "message": "Fail: NSDL Bank Timeout @verifybeneficiary [MAW13780917]",
            "respcode": 1028,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null,
            "tryWithOtherBank": [
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "ma_bank_on_boarding_id": 9,
                    "bankRegisterStatus": "REGISTERED"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid account number provided :account_number ",
            "respcode": 1089,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null
        }
    }
}
*/

// -------------- ReSendOTP Beneficiary-----------------
/**
* @api {post} /beneficiary 3) ReSendOTP Beneficiary
* @apiName addBeneficiaryResendOtp
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{"query":"mutation{addBeneficiaryResendOtp(ma_user_id:28479,userid:7556,mobile_number:\"{{mobile_number}}\",orderid:\"{{orderid}}\",sessionRQ:\"{{sessionRQ}}\"){status,message,respcode}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{addBeneficiaryResendOtp(ma_user_id:28479,userid:7556,mobile_number:\"{{mobile_number}}\",orderid:\"{{orderid}}\",sessionRQ:\"{{sessionRQ}}\"){status,message,respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {Number} beneVerifyBankList  Bank List

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiaryResendOtp": {
            "status": 200,
            "message": "Success: OTP Sent to customer",
            "respcode": 2002
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiaryResendOtp": {
            "status": 400,
            "message": "Fail: Something went wrong",
            "respcode": 1010
        }
    }
}
*/

// -------------- Add Beneficiary with Security PIN -----------------
/**
* @api {post} /beneficiary 4) Add Beneficiary with Security PIN
* @apiName addBeneficiaryWithSecurityPin
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } beneficiary_name BENEFICIARY NAME
* @apiParam { IntValue } ma_bank_master_id MA BANK MASTER ID
* @apiParam { StringValue } account_number ACCOUNT NUMBER
* @apiParam { StringValue } re_account_number RE ACCOUNT NUMBER
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } ben_mobile_number BEN MOBILE NUMBER
* @apiParam { StringValue } ifsc_code IFSC CODE
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } relationship RELATIONSHIP
* @apiParam { BooleanValue } bank_verify BANK VERIFY
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{addBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Sanjay Ben two\", ma_bank_master_id: 75,account_number:\"**********\",re_account_number:\"**********\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"SBIN0000055\",uic:\"**********31401\",relationship:\"Brother\",bank_verify:false,aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status, message,respcode,orderid,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{addBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Sanjay Ben two\", ma_bank_master_id: 75,account_number:\"**********\",re_account_number:\"**********\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"SBIN0000055\",uic:\"**********31401\",relationship:\"Brother\",bank_verify:false,aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status, message,respcode,orderid,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {Number} ma_beneficiaries_id  Beneficiary Id
* @apiSuccess {Number} ma_bene_verification_id  Verification Id
* @apiSuccess {Number} verification_message  Verification message
* @apiSuccess {String} bank_bene_name  Bank Bene Name
* @apiSuccess {String} bank_account_number  Bank account number
* @apiSuccess {String} bene_verify_status VERIFIED/PENDING/FAILED/UNVERIFIED/PENDING/INITIATED

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": "MAW35119321",
            "ma_beneficiaries_id": "538",
            "ma_bene_verification_id": 0,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": "SBIN0000055",
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid account number provided :account_number ",
            "respcode": 1089,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null
        }
    }
}
*/

// -------------- Confirm Verified Beneficiary -----------------
/**
* @api {post} /beneficiary 2.1) Confirm Verified Beneficiary
* @apiName confirmVerifiedBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { BooleanValue } bank_verify bank Verify
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { IntValue } ma_bene_verification_id Verification ID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { BooleanValue } isOtpRequired
* @apiParam { StringValue } otp OPTIONAL
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{confirmVerifiedBeneficiary(ma_user_id:28479,userid: 7556,orderid:\"MAW72436149\",mobile_number:\"**********\",bank_verify: true, ma_bene_verification_id: 154,ma_beneficiaries_id:544,uic:\"***************\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\",isOtpRequired:true,otp:\"886323\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{confirmVerifiedBeneficiary(ma_user_id:28479,userid: 7556,orderid:\"MAW72436149\",mobile_number:\"**********\",bank_verify: true, ma_bene_verification_id: 154,ma_beneficiaries_id:544,uic:\"***************\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"
}</code></pre>

* @apiParamExample {json} Request-Example-Without-OTP:
{
    "query": "mutation{confirmVerifiedBeneficiary(ma_user_id:28479,userid: 7556,orderid:\"MAW72436149\",mobile_number:\"**********\",bank_verify: true, ma_bene_verification_id: 154,ma_beneficiaries_id:544,uic:\"***************\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\",isOtpRequired:false,otp:\"\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{confirmVerifiedBeneficiary(ma_user_id:28479,userid: 7556,orderid:\"MAW72436149\",mobile_number:\"**********\",bank_verify: true, ma_bene_verification_id: 154,ma_beneficiaries_id:544,uic:\"***************\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  Order id
* @apiSuccess {Array} beneficiary_list Recent created bene details return

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmVerifiedBeneficiary": {
            "status": 200,
            "message": "Success: Beneficiary added successfully",
            "respcode": 1087,
            "beneficiary_list": [
                {
                    "beneficiary_name": "Sanjay Ben two",
                    "ifsc_code": "HDFC0000004",
                    "account_number": "*********",
                    "ben_mobile_number": "**********",
                    "relationship": "Brother"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmVerifiedBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid account number provided :account_number ",
            "respcode": 1089,
            "beneficiary_list": null
        }
    }
}
*/

// -------------- Confirm Verify Beneficiary With OTP -----------------
/**
* @api {post} /beneficiary 5) Verify Beneficiary With OTP
* @apiName sendOtpVerifiedBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } beneficiary_name BENEFICIARY NAME
* @apiParam { IntValue } ma_bank_master_id MA BANK MASTER ID
* @apiParam { StringValue } account_number ACCOUNT NUMBER
* @apiParam { StringValue } re_account_number RE ACCOUNT NUMBER
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } ben_mobile_number BEN MOBILE NUMBER
* @apiParam { StringValue } ifsc_code IFSC CODE
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } relationship RELATIONSHIP
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{sendOtpVerifiedBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Swarup Das\", ma_bank_master_id: 62,account_number:\"*********0001\",re_account_number:\"*********0001\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"SBIN0002150\",uic:\"***************\",relationship:\"Brother\",aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"09eafe8c2f9105d79a7fa05c02e63c9c\",ma_bene_verification_id:13){status, message,respcode,orderid,isOTPRequired,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id,bankRegisterStatus}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{sendOtpVerifiedBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Swarup Das\", ma_bank_master_id: 62,account_number:\"*********0001\",re_account_number:\"*********0001\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"SBIN0002150\",uic:\"***************\",relationship:\"Brother\",aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"09eafe8c2f9105d79a7fa05c02e63c9c\",ma_bene_verification_id:13){status, message,respcode,orderid,isOTPRequired,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id,bankRegisterStatus}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  Order id
* @apiSuccess {Array} beneficiary_list Recent created bene details return

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "sendOtpVerifiedBeneficiary": {
            "status": 200,
            "message": "Success: OTP Sent to customer",
            "respcode": 2002,
            "orderid": "MACO94831631518033",
            "ma_beneficiaries_id": "559",
            "ma_bene_verification_id": 0,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": "IDIB000S152",
            "tryWithOtherBank": null
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} orderid  order id

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid account number provided :account_number ",
            "respcode": 1089,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} orderid  order id

* @apiErrorExample Error-Wrong-Pin-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyBeneficiaryOtp": {
            "status": 400,
            "message": "Fail: Wrong pin entered  Attempt 5 out of 5 ACCOUNT LOCKED ",
            "respcode": 1102,
            "beneficiary_list": null
        }
    }
}
*/

// -------------- Verify Beneficiary with Security Pin -----------------
/**
* @api {post} /beneficiary 6) Verify Beneficiary with Security Pin
* @apiName verifyBeneficiaryOtp
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } security_pin SECURITY PIN Optional
* @apiParam { StringValue } otp Optional
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { Boolean } isOtpRequired
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParamExample {json} Request-Security-Pin-Example:
{"query":"mutation{verifyBeneficiaryOtp(ma_user_id:28479,userid: 7556,orderid:\"MAW12944691\",mobile_number:\"**********\",security_pin:\"0011\",otp:\"\",isOtpRequired:false,ma_beneficiaries_id:538,uic:\"**********31401\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"}
* @apiParamExample {json} Request-OTP-Example:
{"query":"mutation{verifyBeneficiaryOtp(ma_user_id:28479,userid: 7556,orderid:\"MAW12944691\",mobile_number:\"**********\",security_pin:\"\",otp:\"6778\",isOtpRequired:true,ma_beneficiaries_id:538,uic:\"**********31401\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{verifyBeneficiaryOtp(ma_user_id:28479,userid: 7556,orderid:\"MAW12944691\",mobile_number:\"**********\",security_pin:\"0011\",ma_beneficiaries_id:538,uic:\"**********31401\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status, message,respcode,beneficiary_list{beneficiary_name,ifsc_code,account_number,ben_mobile_number,relationship}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  Order id
* @apiSuccess {Array} beneficiary_list Recent created bene details return

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyBeneficiaryOtp": {
            "status": 200,
            "message": "Success: Beneficiary added successfully",
            "respcode": 1087,
            "beneficiary_list": [
                {
                    "beneficiary_name": "Sanjay Ben two",
                    "ifsc_code": "SBIN0000055",
                    "account_number": "**********",
                    "ben_mobile_number": "**********",
                    "relationship": "Brother"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} orderid  order id

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyBeneficiaryOtp": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~****************",
            "respcode": 1161,
            "beneficiary_list": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} orderid  order id

* @apiErrorExample Error-Wrong-Pin-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyBeneficiaryOtp": {
            "status": 400,
            "message": "Fail: Wrong pin entered  Attempt 5 out of 5 ACCOUNT LOCKED ",
            "respcode": 1102,
            "beneficiary_list": null
        }
    }
}
*/

// -------------- Get Beneficiary List -----------------
/**
* @api {post} /beneficiary 7) Get Beneficiary List
* @apiName getBeneficiaries
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { IntValue } limit LIMIT
* @apiParam { IntValue } offset OFFSET
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{
    "query": "{getBeneficiaries(ma_user_id:28479,userid: 7556,uic:\"**********31401\", limit:2, offset:0,sessionRQ: \"0ca971778a879452da8f7834b5899cde\"){status, message,respcode,beneficiary_list{account_number,beneficiary_name,ben_mobile_number,ma_beneficiaries_id,bank_name,,bene_verify_status,delBankList{bank_name,ma_bank_on_boarding_id}},nextFlag}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{getBeneficiaries(ma_user_id:28479,userid: 7556,uic:\"**********31401\", limit:2, offset:0,sessionRQ: \"0ca971778a879452da8f7834b5899cde\"){status, message,respcode,beneficiary_list{account_number,beneficiary_name,ben_mobile_number,ma_beneficiaries_id,bank_name,,bene_verify_status,delBankList{bank_name,ma_bank_on_boarding_id}},nextFlag}}"
}</code></pre>

* @apiSuccess {Array} getBeneficiaries API status code.
* @apiSuccess {ArrayItem} getBeneficiaries.account_number Account Number.
* @apiSuccess {ArrayItem} getBeneficiaries.beneficiary_name Beneficiary Name.
* @apiSuccess {ArrayItem} getBeneficiaries.mobile_number Mobile Number.
* @apiSuccess {ArrayItem} getBeneficiaries.ma_beneficiaries_id Beneficiary Id.
* @apiSuccess {ArrayItem} getBeneficiaries.bank_name Beneficiary Bank name.
* @apiSuccess {ArrayItem} getBeneficiaries.bene_verify_status VERIFIED/PENDING/FAILED/UNVERIFIED/PENDING/INITIATED.
* @apiSuccess {ArrayItem} getBeneficiaries.delBankList Array of Delete bene list

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBeneficiaries": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "beneficiary_list": [
                {
                    "account_number": "*********",
                    "beneficiary_name": "UJJAWAL SUDHIR  JHA",
                    "ben_mobile_number": "**********",
                    "ma_beneficiaries_id": 547,
                    "bank_name": "The Dhanalakshmi Bank Ltd, CHANDERNAGAR",
                    "bene_verify_status": "VERIFIED",
                    "delBankList": [
                        {
                            "bank_name": "NSDL",
                            "ma_bank_on_boarding_id": 5
                        },
                        {
                            "bank_name": "PAYTM",
                            "ma_bank_on_boarding_id": 9
                        }
                    ]
                },
                {
                    "account_number": "**********",
                    "beneficiary_name": "Rajkumar Changedd",
                    "ben_mobile_number": "**********",
                    "ma_beneficiaries_id": 539,
                    "bank_name": "The Dhanalakshmi Bank Ltd, CHAPRA",
                    "bene_verify_status": "UNVERIFIED",
                    "delBankList": []
                }
            ],
            "nextFlag": false
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uic  Unique customer code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBeneficiaries": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~null",
            "respcode": 1161,
            "beneficiary_list": null,
            "nextFlag": null
        }
    }
}
*/

// -------------- Search Beneficiary -----------------
/**
* @api {post} /beneficiary 8) Search Beneficiary
* @apiName getBeneficiariesSearch
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { IntValue } limit LIMIT
* @apiParam { IntValue } offset OFFSET
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { EnumValue } search_type SEARCH TYPE
* @apiParam { StringValue } search_text SEARCH TEXT
* @apiParamExample {json} Request-Example:
{
    "query": "{getBeneficiaries(ma_user_id:28479,userid: 7556,uic:\"***************\", limit:5, offset:0,sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\",search_type:ACCOUNTNO,search_text:\"5001*********\"){status, message,respcode,beneficiary_list{account_number,beneficiary_name,ben_mobile_number,ma_beneficiaries_id,bank_name,,bene_verify_status,delBankList{bank_name,ma_bank_on_boarding_id}},nextFlag}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{getBeneficiaries(ma_user_id:28479,userid: 7556,uic:\"***************\", limit:5, offset:0,sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\",search_type:ACCOUNTNO,search_text:\"5001*********\"){status, message,respcode,beneficiary_list{account_number,beneficiary_name,ben_mobile_number,ma_beneficiaries_id,bank_name,,bene_verify_status,delBankList{bank_name,ma_bank_on_boarding_id}},nextFlag}}"
}</code></pre>

* @apiSuccess {Array} getBeneficiaries API status code.
* @apiSuccess {ArrayItem} getBeneficiaries.account_number Account Number.
* @apiSuccess {ArrayItem} getBeneficiaries.beneficiary_name Beneficiary Name.
* @apiSuccess {ArrayItem} getBeneficiaries.mobile_number Mobile Number.
* @apiSuccess {ArrayItem} getBeneficiaries.ma_beneficiaries_id Beneficiary Id.
* @apiSuccess {ArrayItem} getBeneficiaries.bank_name Beneficiary Bank name.
* @apiSuccess {ArrayItem} getBeneficiaries.bene_verify_status VERIFIED/PENDING/FAILED/UNVERIFIED/PENDING/INITIATED.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBeneficiaries": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "beneficiary_list": [
                {
                    "account_number": "5001*********",
                    "beneficiary_name": "Rajkumar Changedd",
                    "ben_mobile_number": "**********",
                    "ma_beneficiaries_id": 514,
                    "bank_name": "The Dhanalakshmi Bank Ltd, ITC CENTRE,CHENNAI",
                    "bene_verify_status": "UNVERIFIED",
                    "delBankList": [
                        {
                            "bank_name": "PAYTM",
                            "ma_bank_on_boarding_id": 9
                        },
                        {
                            "bank_name": "NSDL",
                            "ma_bank_on_boarding_id": 5
                        }
                    ]
                }
            ],
            "nextFlag": false
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBeneficiaries": {
            "status": 400,
            "message": "Fail: Something went wrong!",
            "respcode": 1000,
            "beneficiary_list": null,
            "nextFlag": false
        }
    }
}
*/

// -------------- Try Verification with Another bank -----------------
/**
* @api {post} /beneficiary 9) Try Verification with Another bank
* @apiName trywithAddBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } beneficiary_name BENEFICIARY NAME
* @apiParam { IntValue } ma_bank_master_id MA BANK MASTER ID
* @apiParam { StringValue } account_number ACCOUNT NUMBER
* @apiParam { StringValue } re_account_number RE ACCOUNT NUMBER
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } ben_mobile_number BEN MOBILE NUMBER
* @apiParam { StringValue } ifsc_code IFSC CODE
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } relationship RELATIONSHIP
* @apiParam { BooleanValue } bank_verify BANK VERIFY
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{trywithAddBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Rajkumar Changedd\", ma_bank_master_id: 75,account_number:\"*********\",re_account_number:\"*********\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"HDFC0000004\",uic:\"**********31401\",relationship:\"Brother\",bank_verify:true,aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\",ma_bank_on_boarding_id:5){status, message,respcode,orderid,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{trywithAddBeneficiary(ma_user_id:28479,userid: 7556,beneficiary_name: \"Rajkumar Changedd\", ma_bank_master_id: 75,account_number:\"*********\",re_account_number:\"*********\",mobile_number:\"**********\",ben_mobile_number:\"**********\",ifsc_code: \"HDFC0000004\",uic:\"**********31401\",relationship:\"Brother\",bank_verify:true,aggregator_order_id:\"MAW{{$randomBankAccount}}\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\",ma_bank_on_boarding_id:5){status, message,respcode,orderid,ma_beneficiaries_id,ma_bene_verification_id,verification_message,bank_bene_name,bank_account_number,bene_verify_status,ben_mobile_number,ifsc_code,tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,ma_bank_on_boarding_id}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {Number} ma_beneficiaries_id  Beneficiary Id
* @apiSuccess {Number} ma_bene_verification_id  Verification Id
* @apiSuccess {Number} verification_message  Verification message
* @apiSuccess {String} bank_bene_name  Bank Bene Name
* @apiSuccess {String} bank_account_number  Bank account number
* @apiSuccess {String} bene_verify_status  VERIFIED/PENDING/FAILED/UNVERIFIED/PENDING/INITIATED
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array in which customer can try with another bank

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAddBeneficiary": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": null,
            "ma_beneficiaries_id": "541",
            "ma_bene_verification_id": 154,
            "verification_message": "Bank Verified",
            "bank_bene_name": "UJJAWAL SUDHIR  JHA",
            "bank_account_number": "*********",
            "bene_verify_status": "VERIFIED",
            "ben_mobile_number": null,
            "ifsc_code": "HDFC0000004",
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.
* @apiError {Array} tryWithOtherBank  Try with other bank array in which customer can try with another bank

* @apiErrorExample Error-Invalid-Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAddBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********31401",
            "respcode": 1161,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null,
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Bank-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAddBeneficiary": {
            "status": 400,
            "message": "Fail: NSDL Beneficiary validation Failed 99~INVALID ACCOUNT|Transaction Failed. [MAW03072344]",
            "respcode": 1028,
            "orderid": null,
            "ma_beneficiaries_id": null,
            "ma_bene_verification_id": null,
            "verification_message": null,
            "bank_bene_name": null,
            "bank_account_number": null,
            "bene_verify_status": null,
            "ben_mobile_number": null,
            "ifsc_code": null,
            "tryWithOtherBank": [
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "ma_bank_on_boarding_id": 9,
                    "bankRegisterStatus": "REGISTERED"
                }
            ]
        }
    }
}
*/

// -------------- Delete Beneficiary Send OTP -----------------
/**
* @api {post} /beneficiary 10) Delete Beneficiary Send OTP
* @apiName deleteBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { StringValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{deleteBeneficiary(ma_user_id:28479,userid: 7556,uic:\"**********31401\",ma_beneficiaries_id:547,sessionRQ: \"0ca971778a879452da8f7834b5899cde\",ma_bank_on_boarding_id:\"9\"){status, message,respcode,orderid}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{deleteBeneficiary(ma_user_id:28479,userid: 7556,uic:\"**********31401\",ma_beneficiaries_id:547,sessionRQ: \"0ca971778a879452da8f7834b5899cde\",ma_bank_on_boarding_id:\"9\"){status, message,respcode,orderid}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  Order ID

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "deleteBeneficiary": {
            "status": 200,
            "message": "Success: OTP Sent to customer",
            "respcode": 2002,
            "orderid": "MADBE77251618499708"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Invalid-session-Response:
HTTP/1.1 200 OK
{
    "data": {
        "deleteBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********71857",
            "respcode": 1161,
            "orderid": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Bank-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "deleteBeneficiary": {
            "status": 400,
            "message": "Fail: PAYTM API Error ~ OTP Error Sent",
            "respcode": 1161,
            "orderid": null
        }
    }
}
*/

// -------------- Verify OTP Delete Beneficiary -----------------
/**
* @api {post} /beneficiary 11) Verify OTP Delete Beneficiary
* @apiName verifyDeleteBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { StringValue } otp OTP
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{verifyDeleteBeneficiary(ma_user_id:28479,userid: 7556,uic:\"**********31401\",ma_beneficiaries_id:547,sessionRQ: \"0ca971778a879452da8f7834b5899cde\",otp:\"505699\",orderid:\"MADBE77251618499708\",ma_bank_on_boarding_id:\"9\"){status, message,respcode,orderid,finalDelete}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{verifyDeleteBeneficiary(ma_user_id:28479,userid: 7556,uic:\"**********31401\",ma_beneficiaries_id:547,sessionRQ: \"0ca971778a879452da8f7834b5899cde\",otp:\"505699\",orderid:\"MADBE77251618499708\",ma_bank_on_boarding_id:\"9\"){status, message,respcode,orderid,finalDelete}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  Order ID
* @apiSuccess {Boolean} finalDelete  TRUE/FALSE ==> IF TRUE then bene has deleted, IF FALSE then bene has deleted only for given bank

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyDeleteBeneficiary": {
            "status": 200,
            "message": "Success: Beneficiary deleted successfully",
            "respcode": 2005,
            "orderid": null,
            "finalDelete": true
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Invalid-session-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyDeleteBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********71857",
            "respcode": 1161,
            "orderid": ,
            "finalDelete": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Bank-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyDeleteBeneficiary": {
            "status": 400,
            "message": "Fail: PAYTM API Failure ::: something went wrong while verifying otp",
            "respcode": 10005,
            "orderid": null,
            "finalDelete": null
        }
    }
}
*/

// -------------- Resent OTP Delete Beneficiary -----------------
/**
* @api {post} /beneficiary 12) Resent OTP Delete Beneficiary
* @apiName resentOtpDeleteBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { StringValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{resentOtpDeleteBeneficiary(ma_user_id:28479,userid:7556,uic:\"**********31401\",orderid:\"MADBE77251618499708\",sessionRQ:\"0ca971778a879452da8f7834b5899cde\",ma_beneficiaries_id:547,ma_bank_on_boarding_id:\"5\"){status,message,respcode}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{resentOtpDeleteBeneficiary(ma_user_id:28479,userid:7556,uic:\"**********31401\",orderid:\"MADBE77251618499708\",sessionRQ:\"0ca971778a879452da8f7834b5899cde\",ma_beneficiaries_id:547,ma_bank_on_boarding_id:\"5\"){status,message,respcode}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentOtpDeleteBeneficiary": {
            "status": 200,
            "message": "Success",
            "respcode": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Invalid-session-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentOtpDeleteBeneficiary": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********71857",
            "respcode": 1161
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Bank-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentOtpDeleteBeneficiary": {
            "status": 400,
            "message": "Fail: PAYTM API Failure ::: something went wrong while sentding otp",
            "respcode": 1161
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample No~Record-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentOtpDeleteBeneficiary": {
            "status": 400,
            "message": "Fail: Deletion of beneficiary failed~NoRecordFound~[DEL_BE]",
            "respcode": 1091
        }
    }
}
*/

// --------------check Bene Exist At Bank end with different ifsc code-----------------
/**
* @api {post} /beneficiary 13) check Bene Exist At Bank end with different ifsc code
* @apiName checkBeneExistAtBank
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { StringValue } account_number Account Number
* @apiParam { StringValue } ifsc_code IFSC Code

* @apiParamExample {json} Request-Example:
{
    "query": "{checkBeneExistAtBank(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"**********31401\",sessionRQ: \"cab3b294dd19af47d1d5fe9c4b86cbfd\",account_number:\"***********\",ifsc_code: \"SBIN0011407\"){status,respcode,message,beneExistAtBankDetails{beneficiary_name,account_number,ifsc_code,beneficiaryid}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{checkBeneExistAtBank(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"**********31401\",sessionRQ: \"cab3b294dd19af47d1d5fe9c4b86cbfd\",account_number:\"***********\",ifsc_code: \"SBIN0011407\"){status,respcode,message,beneExistAtBankDetails{beneficiary_name,account_number,ifsc_code,beneficiaryid}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code Case 1000 --> Allow to delete popup 1002 -->Ignore.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkBeneExistAtBank": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "beneExistAtBankDetails": {
                "beneficiary_name": "Test ene",
                "account_number": "***********",
                "ifsc_code": "SBIN0011406",
                "beneficiaryid": "10998"
            }
        }
    }
}

* @apiSuccessExample No-Record-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkBeneExistAtBank": {
            "status": 200,
            "respcode": 1002,
            "message": "Fail: No Records",
            "beneExistAtBankDetails": null
        }
    }
}

* @apiSuccessExample Same-Account-IfscCode-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkBeneExistAtBank": {
            "status": 200,
            "respcode": 1002,
            "message": "Success",
            "beneExistAtBankDetails": {
                "beneficiary_name": "Test ene",
                "account_number": "Test ene",
                "ifsc_code": "SBIN0011406",
                "beneficiaryid": "10998"
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Invalid-session-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkBeneExistAtBank": {
            "status": 400,
            "respcode": 1002,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********71857",
            "beneExistAtBankDetails": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Bank-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkBeneExistAtBank": {
            "status": 400,
            "message": "Fail: PAYTM API Failure ::: something went wrong",
            "respcode": 1161
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample No~Record-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkBeneExistAtBank": {
            "status": 400,
            "message": "Fail: Deletion of beneficiary failed~NoRecordFound~[DEL_BE]",
            "respcode": 1091
        }
    }
}
*/


// --------------delete BankSide Bene for different ifsc code with same account no-----------------
/**
* @api {post} /beneficiary 14) delete BankSide Bene for different ifsc code with same account no
* @apiName deleteBankSideBene
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { StringValue } account_number Account Number
* @apiParam { StringValue } ifsc_code IFSC Code
* @apiParam { StringValue } beneficiaryid Bank Side reference Id

* @apiParamExample {json} Request-Example:
{
    "query": "{deleteBankSideBene(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"**********31401\",sessionRQ: \"cab3b294dd19af47d1d5fe9c4b86cbfd\",account_number:\"***********\",ifsc_code: \"SBIN0011407\",beneficiaryid:\"10998\"){status,respcode,message}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{deleteBankSideBene(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"**********31401\",sessionRQ: \"cab3b294dd19af47d1d5fe9c4b86cbfd\",account_number:\"***********\",ifsc_code: \"SBIN0011407\",beneficiaryid:\"10998\"){status,respcode,message}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code Case 1000 --> Allow to delete popup 1002 -->Ignore.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "deleteBankSideBene": {
            "status": 200,
            "respcode": 2005,
            "message": "Success: Beneficiary deleted successfully"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Invalid-session-Response:
HTTP/1.1 200 OK
{
    "data": {
        "deleteBankSideBene": {
            "status": 400,
            "respcode": 1002,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********71857"
        }
    }
}

*/

// --------------verify Existing Beneficiary of Remitter-----------------
/**
* @api {post} /beneficiary 15) verify Existing Beneficiary of Remitter
* @apiName verifyExistingBeneficiary
* @apiGroup DMT_STEP_2_BENEFICIARY

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } aggregator_order_id Unique Order Id
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam {Number} ma_beneficiaries_id  Beneficiary Id
* @apiParam { IntValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{verifyExistingBeneficiary(ma_user_id:29188,userid: 2143, mobile_number:\"{{mobile_number}}\",uic:\"{{uic}}\",sessionRQ: \"{{sessionRQ}}\",aggregator_order_id:\"MAWEB{{$randomBankAccount}}\",ma_beneficiaries_id: {{ma_beneficiaries_id}} ,ma_bank_on_boarding_id: \"9\"){status,respcode,message,ma_bene_verification_id,bank_bene_name,bene_verify_status,verification_message}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{verifyExistingBeneficiary(ma_user_id:29188,userid: 2143, mobile_number:\"{{mobile_number}}\",uic:\"{{uic}}\",sessionRQ: \"{{sessionRQ}}\",aggregator_order_id:\"MAWEB{{$randomBankAccount}}\",ma_beneficiaries_id: {{ma_beneficiaries_id}} ,ma_bank_on_boarding_id: \"9\"){status,respcode,message,ma_bene_verification_id,bank_bene_name,bene_verify_status,verification_message}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Number} ma_bene_verification_id  Verification Id
* @apiSuccess {String} bank_bene_name  Bank Bene Name
* @apiSuccess {String} bene_verify_status  VERIFIED/PENDING/FAILED/UNVERIFIED/PENDING/INITIATED
* @apiSuccess {Number} verification_message  Verification message

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyExistingBeneficiary": {
          "status": 200,
          "respcode": 1000,
          "message": "Success",
          "ma_bene_verification_id": 8259,
          "bank_bene_name": null,
          "bene_verify_status": "PENDING",
          "verification_message": "Verification Pending from bank side!"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyExistingBeneficiary": {
            "status": 400,
            "respcode": 1001,
            "message": "Fail: Something went wrong!"
        }
    }
}

*/