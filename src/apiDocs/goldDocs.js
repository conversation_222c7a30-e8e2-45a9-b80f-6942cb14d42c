// -------------- Dvara Gold API-----------------

/**
* @api {post} /gold 1) Send Otp to Customer
* @apiName sendOtp
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{sendOtp(ma_user_id:28479,userid:7556,mobile:\"**********\",action:SEND){status,respcode,message,orderid}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to validate Customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  API order id.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"sendOtp": {"status": 200,"respcode": 2007,"message": "Success: OTP sent successfully","orderid": "MAGOLD24971614350867"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} orderid  API order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"sendOtp":{"status":400,"respcode": 1001,"message":"Something Went Wrong","orderid":null}}}
*/

/**
* @api {post} /gold 2) Verify Customer Otp
* @apiName verifyOtp
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{verifyOtp(ma_user_id:28479, userid:7556, otp:"123456", orderid:"MAGOLD24971614350867",action:VERIFY){status,respcode,message,userStatus}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to verify OTP for Customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} userStatus API Customer exits or not.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"verifyOtp": {"status": 200, "respcode": 1000, "message": "Success", "userStatus": "PENDING", "orderid": null, "customerDetails": "[{\"ma_gold_customer_id\":1,\"customer_name\":\"Sanjay Singh\",\"ext_customer_id\":\"***************\",\"dob\":\"2000-12-11T18:30:00.000Z\",\"gender\":\"m\",\"mobile\":\"**********\",\"email\":\"<EMAIL>\",\"user_status\":\"P\",\"address_line_one\":\"\",\"address_line_two\":\"first floor, plot no. 10, sector 19, akahs chs\",\"address_line_three\":\"Mumbai\",\"bank_name\":\"HDFC Bank1\",\"account_name\":\"Test Acoount1\",\"account_no\":\"**********\",\"ifsc_code\":\"HDFC0000592\",\"branch_name\":\"Maro1\",\"upi_address\":\"test@hadfc\",\"nominee_name\":\"ajay\",\"nominee_mobile\":\"**********\",\"nominee_relation\":\"Father\",\"guardian_name\":\"Test Guardian\",\"guardian_mobile\":\"**********\",\"guardian_relation\":\"Father\"}]"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} userStatus API Customer exits or not.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"verifyOtp":{"status":400,"respcode": 1001,"message":"Invalid Otp","userStatus":null}}}
*/

// -------------- Dvara Gold Lambda RE SEND OTP -----------------
/**
* @api {post} /gold 3) ReSend Cusotmer OTP
* @apiName resendOtp
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{resendOtp(ma_user_id:28479, userid:7556, orderid:"MAGOLD24971614350867", action:RESEND){status,respcode,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Re-send OTP for Customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"resendOtp": {"status": 200,"respcode": 1000,"message": "Success"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"resendOtp":{"status":400,"respcode":1001,"message":"Something went wrong"}}}
*/

// -------------- Dvara Gold Lambda ADD BRANCH -----------------
/**
* @api {post} /gold/agentbranch 4) Add Branch
* @apiName addBranches
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{addBranches(extBranchId:"EXT002391",remarks:"test branch on local", branchType:"district",branchName: "District Branch 1234", unitNumber:"02",streetName: "Thennali", district:"Madurai",pinCode: 625001, state:"IN-TN",stdCode: 20, country:"IN",gstNumber: "ABDCR123456", phone:"**********",bmExtId: "BM12456", bmFirstName:"SRATH",bmMiddleName: "SHAIL", bmLastName:"SHUKH",bmDob: "2019-08-26", bmGender:MALE,bmEmail: "<EMAIL>", bmMobile: "**********",bmLandline: "**********", bmUnitNo:"02",bmStreetName: "Thennali", bmDistrict:"Madurai",bmPinCode: 625001, bmState:"IN-TN",bmStdCode: 20, bmCountry:"IN", accountNumber: "*********", ifsc:"ABV123455",accountName: "Test Acoount", bankName:"ICICI Bank",bankBranchName: "Madurai"){status,respcode,message,branchData}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to add the Branch</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} branchData  API branch details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"addBranches": {"status": 200,"respcode": 1000,"message": "Success", "branchData": "[{"extBranchId":"EXT002392","branchType":"district","name":"District Branch 1234","communicationAddress":{"unitNumber":"02","streetName":"Thennali","district":"Madurai","pinCode":625001,"state":"IN-TN","stdCode":20,"country":"IN"},"gstNumber":"ABDCR123456","phone":"**********","branchManager":{"extId":"BM12456","name":{"first":"SRATH","middle":"SHAIL","last":"SHUKH"},"dob":"2019-08-26","gender":MALE,"email":"<EMAIL>","phone":{"mobile":"**********","landline":"**********"},"address":{"unitNumber":"02","streetName":"Thennali","district":"Madurai","pinCode":625001,"state":"IN-TN","stdCode":20,"country":"IN"}},"bankAccount":{"accountNumber":"*********","ifsc":"ABV123455","accountName":"Test Acoount","bankName":"ICICI Bank","branchName":"Madurai"}}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} branchData API branch details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"addBranches":{"status":400,"respcode":1001,"message":"extBranchId already exists", "branchData":null}}}
*/

// -------------- Dvara Gold Lambda Update BRANCH -----------------
/**
* @api {post} /gold/agentbranch 5) Update Branch
* @apiName updateBranches
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{updateBranches(extBranchId:"EXT002392",remarks:"test branch on local", branchType:"district",branchName: "District Branch 1234", unitNumber:"02",streetName: "Thennali", district:"Madurai",pinCode: 625001, state:"IN-TN",stdCode: 20, country:"IN",gstNumber: "ABDCR123456", phone:"**********",bmExtId: "BM12456", bmFirstName:"SRATH",bmMiddleName: "SHAIL", bmLastName:"SHUKH",bmDob: "2019-08-26", bmGender:MALE,bmEmail: "<EMAIL>", bmMobile: "**********",bmLandline: "**********", bmUnitNo:"02",bmStreetName: "Thennali", bmDistrict:"Madurai",bmPinCode: 625001, bmState:"IN-TN",bmStdCode: 20, bmCountry:"IN", accountNumber: "*********", ifsc:"ABV123455",accountName: "Test Acoount", bankName:"ICICI Bank",bankBranchName: "Madurai"){status,respcode,message,branchData}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to update the Branch</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} branchData  API branch details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"updateBranches": {"status": 200,"respcode": 1000,"message": "Success", "branchData": "{"extBranchId":"EXT002392","branchType":"district","name":"District Branch 123456","communicationAddress":{"unitNumber":"02","streetName":"Thennali","district":"Madurai","pinCode":625001,"state":"IN-TN","stdCode":20,"country":"IN"},"gstNumber":"ABDCR123456","phone":"**********","branchManager":{"extId":"BM12456","name":{"first":"SRATH","middle":"SHAIL","last":"SHUKH"},"dob":"2019-08-26","gender":MALE,"email":"<EMAIL>","phone":{"mobile":"**********","landline":"**********"},"address":{"unitNumber":"02","streetName":"Thennali","district":"Madurai","pinCode":625001,"state":"IN-TN","stdCode":20,"country":"IN"}},"bankAccount":{"accountNumber":"*********","ifsc":"ABV123455","accountName":"Test Acoount","bankName":"ICICI Bank","branchName":"Madurai"},"id":"b1b56560-704d-11eb-8c82-1bc36bf130e8"}"}}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} branchData API branch details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"resendOtp":{"status":400,"respcode":1001,"message":"extBranchId does not exists", "branchData":null}}}
*/

// -------------- Dvara Gold Lambda ADD AGENTS -----------------
/**
* @api {post} /gold/agentbranch 6) Add Agents
* @apiName addAgents
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{addAgents(extAgentId: "1102", extBranchId:"EXT002311", firstName:"Ajay",lastName: "Singh", houseNumber:"101",streetName:"abc",district:"Thenai",pinCode:625001,state:"IN-KL",country:"India",email:"<EMAIL>",mobile:"**********", accountNumber: "*********", ifsc:"ABV123455",accountName: "Test Acoount", bankName:"ICICI Bank",branchName: "Madurai"){status,respcode,message,branchData,agentData}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to add the Agents</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} agentData  API agent details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"addAgents": {"status": 200,"respcode": 1000,"message": "Success", "agentData": "[{"extAgentId":"1102","extBranchId":"EXT002311","name":{"first":"Ajay","middle":"","last":"Singh"},"phone":{"mobile":"**********"},"email":"<EMAIL>","address":{"houseNumber":"101","streetName":"abc","district":"Thenai","pinCode":625001,"state":"IN-KL","country":"India"},"bankAccount":{"accountNumber":"*********","ifsc":"ABV123455","accountName":"Test Acoount","bankName":"ICICI Bank","branchName":"Madurai"}}]"}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} agentData API agent details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"addAgents":{"status":400,"respcode":1001,"message":"extBranchId does not exists", "agentData":null}}}
*/

// -------------- Dvara Gold Lambda UPDATE AGENTS -----------------
/**
* @api {post} /gold/agentbranch 7) Update Agents
* @apiName updateAgents
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{updateAgents(extAgentId: "1102", extBranchId:"EXT002311", firstName:"Ajay",lastName: "Singh", houseNumber:"101",streetName:"abc",district:"Thenai",pinCode:625001,state:"IN-KL",country:"India",email:"<EMAIL>",mobile:"**********", accountNumber: "*********", ifsc:"ABV123455",accountName: "Test Acoount", bankName:"ICICI Bank",branchName: "Madurai"){status,respcode,message,branchData,agentData}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to update the Agents</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} agentData  API agent details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"updateAgents": {"status": 200,"respcode": 1000,"message": "Success", "agentData": "[{"extAgentId":"1102","extBranchId":"EXT002311","name":{"first":"Ajay","middle":"","last":"Singh"},"phone":{"mobile":"**********"},"email":"<EMAIL>","address":{"houseNumber":"101","streetName":"abc","district":"Thenai","pinCode":625001,"state":"IN-KL","country":"India"},"bankAccount":{"accountNumber":"*********","ifsc":"ABV123455","accountName":"Test Acoount","bankName":"ICICI Bank","branchName":"Madurai"}}]"}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} agentData API agent details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"updateAgents":{"status":400,"respcode":1001,"message":"extAgentId does not exists", "agentData":null}}}
*/

// -------------- Dvara Gold Lambda ADD CUSTOMERS -----------------
/**
* @api {post} /gold 8) Add Customer
* @apiName addCustomers
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{addCustomers(ma_user_id:28479,userid:7557,customerName:\"Anshad Singh2\",dob: \"1992-12-12\",gender:MALE, email: \"<EMAIL>\", mobile:\"**********\",documentId:\"ABDC56789012\",documentType:AADHAR, addressLineOne: \"first floor, plot no. 10, sector 19, hasan chs\", addressLineTwo: \"Mumbai\", addressLineThree:400701, accountNumber: \"**********\", ifsc:\"HDFC0000592\",accountName: \"Test Hassan\", bankName:\"HDFC Bank1\",branchName: \"Maro1\", upiAddress:\"test@hadfc\", nomineeName: \"ajay\", nomineeMobile: \"**********\", nomineeRelation: \"Father\", guardianName: \"Test Guardian\", guardianMobile: \"**********\", guardianRelation: \"Father\",ip:\"*******\",agentDeviceType: APP){status,respcode,message,customerData{customerName,dob,gender,addressLineOne,addressLineTwo,addressLineThree,email,mobile,documentId,documentType,accountNumber,ifsc,accountName,bankName,branchName,nomineeName,nomineeMobile,nomineeRelation,guardianName,guardianMobile,guardianRelation,extCustomerId,extAgentId,extBranchId},uploadList{fetchUrl,uploadUrl,uploadId,validForSeconds,ContentType}}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to add the Customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "addCustomers": {
            "status": 200,
            "respcode": 1000,
            "message": "success",
            "customerData": {
                "customerName": "Anshad Singh2",
                "dob": "12-12-1992",
                "gender": "MALE",
                "addressLineOne": "first floor, plot no. 10, sector 19, hasan chs",
                "addressLineTwo": "Mumbai",
                "addressLineThree": 400701,
                "email": "<EMAIL>",
                "mobile": "**********",
                "documentId": "ABDC56789012",
                "documentType": "AADHAR",
                "accountNumber": "**********",
                "ifsc": "HDFC0000592",
                "accountName": "Test Hassan",
                "bankName": "HDFC Bank1",
                "branchName": "Maro1",
                "nomineeName": "ajay",
                "nomineeMobile": "**********",
                "nomineeRelation": "Father",
                "guardianName": "Test Guardian",
                "guardianMobile": "**********",
                "guardianRelation": "Father",
                "extCustomerId": "***************",
                "extAgentId": "AGT2071",
                "extBranchId": "BR41"
            },
            "uploadList": {
                "fetchUrl": "/customers/uploaded/consent/b1c17303-d294-4978-a7c5-ac7030454491",
                "uploadUrl": "https://mygold-partneruploads-test.s3.ap-south-1.amazonaws.com/DV92/customer/***************/consent/7fd4345d-5415-49d5-b794-6cbd90d8960a.jpeg?Content-Type=image%2Fjpeg&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA55ACFUQBXKXYSUZP%2F20210930%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20210930T094734Z&X-Amz-Expires=90&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGoaCmFwLXNvdXRoLTEiRzBFAiEAxIOYhqO%2FhoMs0TcQK1JiW1MZeDz%2FcJUxVvrCJWP2uboCIBt4tLX4BtnY3ri2p7X0n3MMoVhm8%2BLiPkMR1OSesrVbKsMCCNP%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQAhoMOTU1NjM0Nzg3MzMxIgykQxDxL6FzD6Y%2BgmEqlwKsO9LxjgvpxoBi4sk6zWCmfkNs77WuYvq8F%2BTtXLToPojabpuCSZ8CNiEmrgsqcrjNF%2FlPXnH%2BQU7GgzI7DG4Xc2%2BF4kOk5T8OJ9CRJTwTLmPdFK7g6jiMrXFHBBuIK5AMzy9AWxJdAdI%2BY2Snn9h8Rwn5rUAUsTcil8Q4pyWSB9rY6%2B88nTLlvJbK7gy4wYlSEkGHoS8o4UBMevTHHkozV2iL6sJtizv8hyWt9jouEyZKskOEvk1P1DU5sGp7ZqDq%2B7FUq2YSx%2BO00VXlbPQLYSRo3qfj5x0CdM9PyXPY4q4sFw7SaV%2F37w9WzVjixf9uAvxfqN3qi0Q8t%2Bhc3%2FYORfuCDtnnUBuBNHRG61UCTwnp0OuI3YQw7I3WigY6mgGLTi%2F9rWcB8ZNN5CZQC6MHZoKQbbt6shKI%2FnzfEHhRH17YrM%2FA3eM9Rc1KlvrGfCaYOQwYWTzcQEK7TpU20vwu8n%2BUXeGFRFmZjmvEf%2B7%2B8TEqz2nCZSRPRJqnx%2BzdMqN49OA%2Ftc8B2wj%2FJkYYxPrTQYU%2BcJrUZg6q8sc46ql%2F9UTcVz9sruAF0dlDr7s2%2BE6jcDPhHgvBXA8a&X-Amz-Signature=15eebeeb97be4771500ce47d607bf3b3881064663caf8d0561e0dd7ae2cf3937&X-Amz-SignedHeaders=host",
                "uploadId": "b1c17303-d294-4978-a7c5-ac7030454491",
                "validForSeconds": 60,
                "ContentType": "image/jpeg"
            }
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"addCustomers":{"status":400,"respcode":1001,"message":"email cannot be blank"}}}
*/

// -------------- Dvara Gold Lambda UPLOAD CUSTOMER DOCUMENTS -----------------
/**
* @api {post} /gold 9) Upload Customer Documents
* @apiName uploadCustomerDoc
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{uploadCustomerDoc(userid: 123,ma_user_id: 11223,customerName: \"Amit Singh\",extCustomerId: \"**********42857\",documentId: \"ABDC56789012\", documentType: AADHAR, filename:\"customer.png\"){status,message,respcode,uploadList{fetchUrl,uploadUrl,uploadId,validForSeconds,ContentType}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Upload the Customer Documents</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"uploadCustomerDoc": {"status": 200,"message": "Success","respcode": 1000,"uploadList": {"fetchUrl": "/customers/uploaded/c1b5aba8-adc3-41f3-bccf-8ee1888671dd","uploadUrl": "https://mygold-partneruploads-test.s3.ap-south-1.amazonaws.com/DV92/customer/**********42857/consent/06afea83-2b6f-47a0-b733-b4a2ef0ca7d5.bin?Content-Type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA55ACFUQBRT23P2HN%2F20210303%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20210303T074624Z&X-Amz-Expires=90&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEJ%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRjBEAiAw5j%2BFMoKE0PZLoaCDkCVKpDstr%2F2I871L8jgNuhPudgIgSWD2NHmQ1mp5x%2FZQaMFynMnMOnjLd%2BzgHb6WmFX1pN4q%2FQEIuf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARABGgw5NTU2MzQ3ODczMzEiDFwgHXmJXkHA5IIYYSrRASOnQ8ihCF7d33hesXzz%2B6Btd1HDmV0CuYu6dIyKXeWh4xXLadO0IifewNEn3Z4uX5NNtq3FildhAVy5gkFulhgIY33QJON67C9DK8gCvZJU5zMsMYpYqt7r4fN7rvTEEMSUEFKKVdlowzU6NXRNCtMyIeEfGfl2Iygc3JxXCWOMpKQwQndOhu2yWfoWXnwq63xgXHv2YjkWomc5TWI%2BHQIaTnW%2BWqkI771N5V1oUklstWIH1UE009HNkgIQzr4%2BTHKHOefM0Pjh2bsqea8x5q%2FuMJf0%2FIEGOuEBJdLsVFkbTqVzQGwKXA%2B0y%2BTF9lamhsmq63j7p5bAX%2BFLJykiUt9l0%2BOHZMHFDR%2BIJ9ecccS4TNFpMs7pYxNrFu8rxIXwe%2FyRrzxYw4agjlUoFTYWQR4gKMN55DW3cLuCWkuMPcYBDcbSLGrBPRaA05RXhtc8uJP2E4ski2SFYZ1wOwNmyzpGII8c%2Fsz%2BzyNIurjjRmExSYyX1CTg9%2FGXn9xCPvmk2p2mpAu2CZj%2BkFnR7JPYFs6jZSWLTZjTzceeVMgtfHtuhoZ9VBmyBq6MM8LCd%2FHOaOpyrFGt1d3XAT9h&X-Amz-Signature=7e387e6c54bec2bd1e26e3ff5ba5eee5b6fba43430d075b3c2e94868f0585fcb&X-Amz-SignedHeaders=host","uploadId": "c1b5aba8-adc3-41f3-bccf-8ee1888671dd","validForSeconds": 60,"ContentType": "application/octet-stream"}}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"uploadCustomerDoc": {"status": 501,"message": "Unknown customer 98754321042857 for Partner DV92 ","respcode": 1001,"uploadList": null}}}
*/

// -------------- Dvara Gold Lambda UPLOAD CUSTOMER CONSENT DOCUMENTS -----------------
/** 
* @api {post} /gold 10)Upload Customer Consent Documents
* @apiName uploadCustomerConsentDoc
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{uploadCustomerConsentDoc(userid: 123,ma_user_id: 11223,customerName: \"Amit Singh\",extCustomerId: \"**********42857\",documentId: \"ABDC56789012\",filename: \"customer.png\"){status,message,respcode,uploadList{fetchUrl,uploadUrl,uploadId,validForSeconds,ContentType}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Upload the Customer Consent Documents</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"uploadCustomerConsentDoc": {"status": 200,"message": "Success","respcode": 1000,"uploadList": {"fetchUrl": "/customers/uploaded/consent/c1b5aba8-adc3-41f3-bccf-8ee1888671dd","uploadUrl": "https://mygold-partneruploads-test.s3.ap-south-1.amazonaws.com/DV92/customer/**********42857/consent/06afea83-2b6f-47a0-b733-b4a2ef0ca7d5.bin?Content-Type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIA55ACFUQBRT23P2HN%2F20210303%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20210303T074624Z&X-Amz-Expires=90&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEJ%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRjBEAiAw5j%2BFMoKE0PZLoaCDkCVKpDstr%2F2I871L8jgNuhPudgIgSWD2NHmQ1mp5x%2FZQaMFynMnMOnjLd%2BzgHb6WmFX1pN4q%2FQEIuf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARABGgw5NTU2MzQ3ODczMzEiDFwgHXmJXkHA5IIYYSrRASOnQ8ihCF7d33hesXzz%2B6Btd1HDmV0CuYu6dIyKXeWh4xXLadO0IifewNEn3Z4uX5NNtq3FildhAVy5gkFulhgIY33QJON67C9DK8gCvZJU5zMsMYpYqt7r4fN7rvTEEMSUEFKKVdlowzU6NXRNCtMyIeEfGfl2Iygc3JxXCWOMpKQwQndOhu2yWfoWXnwq63xgXHv2YjkWomc5TWI%2BHQIaTnW%2BWqkI771N5V1oUklstWIH1UE009HNkgIQzr4%2BTHKHOefM0Pjh2bsqea8x5q%2FuMJf0%2FIEGOuEBJdLsVFkbTqVzQGwKXA%2B0y%2BTF9lamhsmq63j7p5bAX%2BFLJykiUt9l0%2BOHZMHFDR%2BIJ9ecccS4TNFpMs7pYxNrFu8rxIXwe%2FyRrzxYw4agjlUoFTYWQR4gKMN55DW3cLuCWkuMPcYBDcbSLGrBPRaA05RXhtc8uJP2E4ski2SFYZ1wOwNmyzpGII8c%2Fsz%2BzyNIurjjRmExSYyX1CTg9%2FGXn9xCPvmk2p2mpAu2CZj%2BkFnR7JPYFs6jZSWLTZjTzceeVMgtfHtuhoZ9VBmyBq6MM8LCd%2FHOaOpyrFGt1d3XAT9h&X-Amz-Signature=7e387e6c54bec2bd1e26e3ff5ba5eee5b6fba43430d075b3c2e94868f0585fcb&X-Amz-SignedHeaders=host","uploadId": "c1b5aba8-adc3-41f3-bccf-8ee1888671dd","validForSeconds": 60,"ContentType": "application/octet-stream"}}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"uploadCustomerConsentDoc": {"status": 501,"message": "Unknown customer 98754321042857 for Partner DV92 ","respcode": 1001,"uploadList": null}}}
*/

// -------------- Dvara Gold Lambda ADD CUSTOMERS -----------------
/**
* @api {post} /gold 11) Update Customer
* @apiName updateCustomers
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{updateCustomers(ma_user_id:28479,userid:7556,extCustomerId:"**********76915", dob: "1994-12-12",gender:MALE, customerName:"Dilip Singh1",email: "<EMAIL>", mobile:"**********", addressLineOne: "second floor, plot no. 10, sector 19, akahs chs", addressLineTwo: "Mumbai-400423", accountNumber: "**********", ifsc:"HDFC0000454",accountName: "Test Acoount2", bankName:"ICICI Bank2",branchName: "Madurai2", upiAddress:"test12@hadfc", nomineeName: "ajay1", nomineeMobile: "**********", nomineeRelation: "Fathers", guardianName: "Test Guardian12", guardianMobile: "**********", guardianRelation: "Mother"){status,respcode,message,customerData}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to update the Customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"updateCustomers": {"status": 200,"respcode": 1000,"message": "success","customerData": "{"extCustomerId":"**********76915","customerName":"Dilip Singh1","dob":"1994-12-12","gender":"m","addressLineOne":"second floor, plot no. 10, sector 19, akahs chs","addressLineTwo":"Mumbai-400423","email":"<EMAIL>","mobile":"**********","accountNumber":"**********","ifsc":"HDFC0000454","accountName":"Test Acoount2","bankName":"ICICI Bank2","branchName":"Madurai2","upiAddress":"test12@hadfc","nomineeName":"ajay1","nomineeMobile":"**********","nomineeRelation":"Fathers","guardianName":"Test Guardian12","guardianMobile":"**********","guardianRelation":"Mother"}"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"updateCustomers":{"status":400,"respcode":1001,"message":"ifsc code not valid"}}}
*/

// -------------- Dvara Gold Lambda GET BULLION DETAILS FOR BUY GOLD -----------------
/**
* @api {post} /gold 12) Get Bullion Details for buy gold
* @apiName getBullionDetails
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{getBullionDetails(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\", rateType: BUY){status,respcode,message,bullionRateDetails{bullionId,bullionName,bullionShortName,purityDisplayValue,purityValue,rateType,ratePerGm,validUntil, rateId, extCustomerId,customerName,customerMobile,extAgentId,customerBankName,customerBranchName,customerAccountName,customerAccountNumber,customerBankIfsc,customerUpiAddress,minGoldWeight,availableBalance,goldMinAmount,taxRates{sTaxName,sTaxPercent,sTaxCode,cTaxName,cTaxCode,cTaxPercent}}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getBullionDetails": {
            "status": 200,
            "respcode": 1000,
            "message": "success",
            "bullionRateDetails": {
                "bullionId": "G3",
                "bullionName": "Gold",
                "bullionShortName": "G22K",
                "purityDisplayValue": "22Kt (91.6%)",
                "purityValue": "916",
                "rateType": "BUY",
                "ratePerGm": "4358.83",
                "validUntil": "19-03-2021 15:45:0",
                "rateId": "95fa8e5ec8bb3ae769d1701a7911f821faed588b81181a35be546eb228cf148b",
                "extCustomerId": "**********25071",
                "customerName": "Ajit Kumar",
                "customerMobile": "**********",
                "extAgentId": "1102",
                "extAgentId": "1102",
                "customerBankName": "ICICI Bank2",
                "customerBranchName": "Madurai2",
                "customerAccountName": "Test Acoount2",
                "customerAccountNumber": "XXXXXX7880",
                "customerBankIfsc": "HDFC0000454",
                "customerUpiAddress": "test12@hadfc",
                "minGoldWeight": "0.15",
                "availableBalance": 99.039,
                "goldMinAmount": "500",
                "taxRates": {
                    "sTaxName": "SGST",
                    "sTaxPercent": "1.5",
                    "sTaxCode": "sgst",
                    "cTaxName": "CGST",
                    "cTaxCode": "cgst",
                    "cTaxPercent": "1.5"
                }
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"getBullionDetails":{"status":400,"respcode":1001,"message":"Invalid customer - DV92/**********2501"}}}
*/

// -------------- Dvara Gold Lambda GET BULLION DETAILS FOR SELL GOLD -----------------
/**
* @api {post} /gold 13) Get Bullion Details for sell gold
* @apiName getBullionDetails
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{getBullionDetails(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\", rateType: SELL){status,respcode,message,bullionRateDetails{bullionId,bullionName,bullionShortName,purityDisplayValue,purityValue,rateType,ratePerGm,validUntil, rateId, extCustomerId,customerName,customerMobile,extAgentId,customerBankName,customerBranchName,customerAccountName,customerAccountNumber,customerBankIfsc,customerUpiAddress,minGoldWeight,availableBalance,goldMinAmount}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBullionDetails": {
      "status": 200,
      "respcode": 1000,
      "message": "success",
      "bullionRateDetails": {
        "bullionId": "G3",
        "bullionName": "Gold",
        "bullionShortName": "G22K",
        "purityDisplayValue": "22Kt (91.6%)",
        "purityValue": "916",
        "availableBalance": 10,
        "validUntil": "20-04-2021 20:30:00",
        "rateId": "9d22763fe0c443b4e326e503368aa11240dda83c00a341ac632d20b1a60d2b57",
        "rateType": "SELL",
        "ratePerGm": "4319.58",
        "extAgentId": "1102",
        "validUntil": "20-04-2021 20:30:00",
        "extCustomerId": "**********25071",
        "customerName": "Dilip Singh1",
        "customerMobile": "**********",
        "customerBankName": "ICICI Bank2",
        "customerBranchName": "Madurai2",
        "customerAccountName": "Test Acoount2",
        "customerAccountNumber": "XXXXXX7880",
        "customerBankIfsc": "HDFC0000454",
        "customerUpiAddress": "test12@hadfc",
        "minGoldWeight": "0.15",
        "availableBalance": 99.039,
        "goldMinAmount": "500",
      }
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"getBullionDetails":{"status":400,"respcode":1001,"message":"Invalid customer - DV92/**********2501"}}}
*/

// -------------- Dvara Gold Lambda ESTIMATE GOLD COST FOR BUY -----------------
/**
* @api {post} /gold 14) Estimate Gold cost for Buy
* @apiName getEstimateGoldCost
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{getEstimateGoldCost(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\", weightInGrams: \"2\", buyType: WEIGHT,bullionId: \"G3\", bullionName: \"Gold\", purityValue:\"916\",purityDisplayValue:\"22Kt (91.6)\",bullionShortName:\"G22K\" , ratePerGram: \"4358.83\", rateType: BUY, rateId: \"049851dcf77aba21f2373619eeb0c1d20b2d510b56c53e444646c97fb4746d72\",sTaxName: \"SGST\",sTaxPercent: \"1.5\",sTaxCode: \"sgst\",cTaxName: \"CGST\",cTaxPercent: \"1.5\",cTaxCode: \"csgt\",customerName: \"Ajit Kumar\",customerMobile:\"**********\",extAgentId: \"1102\"){status,respcode,message,estimationBuyDetails{weightInGrams,ratePerGram,amountToBuy,actualAmount,totalGstTaxRate,amountPayable,buyTypeValue,extCustomerId, bullionId, bullionName, rateTypeValue, rateId,sTaxNameVal,sTaxPercentVal,sTaxCodeVal,cTaxNameVal,cTaxCodeVal,cTaxPercentVal, extCustomerId,customerName,customerMobile,extAgentId,purityValue,purityDisplayValue,bullionShortName,availableBalance, currentTotalGoldValue, validUntil, customerName, customerMobile}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getEstimateGoldCost": {
            "status": 200,
            "respcode": 1000,
            "message": "success",
            "estimationBuyDetails": {
                "weightInGrams": "2",
                "ratePerGram": "4366.85",
                "amountToBuy": null,
                "actualAmount": "8733.70",
                "totalGstTaxRate": "262.01",
                "amountPayable": "8995.71",
                "buyTypeValue": "FixedWeight",
                "extCustomerId": "**********25071",
                "bullionId": "G3",
                "bullionName": "Gold",
                "bullionShortName": "G22K",
                "purityDisplayValue": "22Kt (91.6%)",
                "purityValue": "916",
                "rateTypeValue": "buy",
                "rateId": "049851dcf77aba21f2373619eeb0c1d20b2d510b56c53e444646c97fb4746d72",
                "sTaxNameVal": "SGST",
                "sTaxPercentVal": "1.5",
                "sTaxCodeVal": "sgst",
                "cTaxNameVal": "CGST",
                "cTaxCodeVal": "csgt",
                "cTaxPercentVal": "1.5",
                "customerName": "Ajit Kumar",
                "customerMobile": "**********",
                "extAgentId": "1102",
                "availableBalance": "0.343",
                "currentTotalGoldValue": "1570.64",
                "validUntil": "16-04-2021 17:15:00",
                "customerName": "Ajit Kumar",
                "customerMobile": "**********",
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"getEstimateGoldCost":{"status":400,"respcode":1001,"message":"Something went wrong"}}}
*/

// -------------- Dvara Gold Lambda ESTIMATE GOLD COST FOR SELL -----------------
/**
* @api {post} /gold 15) Estimate Gold cost for Sell
* @apiName getEstimateGoldCostForSell
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{getEstimateGoldCostForSell(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\", weightInGrams: \"2\",bullionId: \"G3\", bullionName: \"Gold\", ratePerGram: \"4358.83\", rateType: SELL, rateId: \"049851dcf77aba21f2373619eeb0c1d20b2d510b56c53e444646c97fb4746d72\",customerName: \"Ajit Kumar\",customerMobile:\"**********\", purityValue:\"916\",purityDisplayValue:\"22Kt (91.6)\",bullionShortName:\"G22K\"){status,respcode,message,estimationSellDetails{weightInGrams,ratePerGram,amountToBuy,amountPayable,buyTypeValue,extCustomerId, bullionId, bullionName, rateTypeValue, rateId,extCustomerId,customerName,customerMobile,purityValue,purityDisplayValue,bullionShortName,availableBalance, currentTotalGoldValue, validUntil, customerName, customerMobile}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getEstimateGoldCostForSell": {
      "status": 200,
      "respcode": 1000,
      "message": "success",
      "estimationSellDetails": {
        "ratePerGram": "4322.85",
        "amountPayable": "8645.70",
        "extCustomerId": "**********25071",
        "bullionId": "G3",
        "bullionName": "Gold",
        "rateTypeValue": "sell",
        "bullionShortName": "G22K",
        "purityDisplayValue": "22Kt (91.6%)",
        "purityValue": "916",
        "rateId": "8b02b3ce4df804c1195679c8f4c0c8363d6071e343941093a47ac7c318090808",
        "availableBalance": "0.343",
        "currentTotalGoldValue": "1570.64",
        "validUntil": "16-04-2021 17:15:00",
        "customerName": "Ajit Kumar",
        "customerMobile": "**********",
      }
    }
  }
}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"getEstimateGoldCostForSell":{"status":400,"respcode":1001,"message":"Something went wrong"}}}
*/

// -------------- Dvara Gold Lambda BUY GOLD ORDER API FOR FIXED WEIGHT -----------------
/**
* @api {post} /gold 16) Buy Order API - Fixed Weight
* @apiName buyGoldOrder
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{buyGoldOrder(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\", buyTypeValue:\"FixedWeight\",bullionId: \"G3\", bullionName: \"Gold\", purityValue:\"916\",purityDisplayValue:\"22Kt (91.6)\",bullionShortName:\"G22K\", ratePerGram: \"4366.85\", weightInGrams: \"2\",actualAmount: \"8733.70\", totalGstTaxRate: \"262.01\" ,totalOrderAmount: \"8995.71\", rateTypeValue: \"buy\", rateId: \"049851dcf77aba21f2373619eeb0c1d20b2d510b56c53e444646c97fb4746d72\",sTaxName: \"SGST\",sTaxPercent: \"1.5\",sTaxCode: \"sgst\",cTaxName: \"CGST\",cTaxPercent: \"1.5\",cTaxCode: \"cgst\",customerName: \"Ajit Kumar\",customerMobile:\"**********\",extAgentId: \"1102\",security_pin:\"1415\"){status,respcode,message,buyOrderResponse{transaction_id, transaction_reference_id,total_order_amount,transaction_time,buyTypeValue,purityValue,total_gold_weight,transactionCharges,customerName,customerMobile,customerBankName,customerBranchName,customerBankIfsc,customerAccountName,customerUpiAddress,customerAccountNumber,transaction_type}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "buyGoldOrder": {
            "status": 200,
            "respcode": 1000,
            "message": "Order Successful"
            "buyOrderResponse": {
                "transaction_id": "MAGOLD48371616148330",
                "transaction_reference_id": "ad76d780-889a-11eb-9054-417f56a3fe9d",
                "total_order_amount": "8995.71",
                "transaction_time": "19-03-2021 15:35:30",
                "buyTypeValue": "FixedWeight",
                "purityValue": "22Kt (91.6)",
                "total_gold_weight": "2",
                "transactionCharges": "262.01",
                "customerName": "Ajit Kumar",
                "customerMobile": "**********",
                "customerBankName": "ICICI Bank2",
                "customerBranchName": "Madurai2",
                "customerBankIfsc": "HDFC0000454",
                "customerAccountName": "Test Acoount2",
                "customerUpiAddress": "test12@hadfc",
                "customerAccountNumber": "XXXXXX7880",
                "transaction_type": "BUY"
            }
        }
    }
}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"buyGoldOrder": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","buyOrderResponse": null}}}
*/

// -------------- Dvara Gold Lambda BUY GOLD ORDER API FOR FIXED AMOUNT -----------------
/**
* @api {post} /gold 16) Buy Order API - Fixed Amount
* @apiName buyGoldOrder
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{buyGoldOrder(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\", buyTypeValue:\"FixedAmount\",bullionId: \"G3\", bullionName: \"Gold\", purityValue:\"916\",purityDisplayValue:\"22Kt (91.6)\",bullionShortName:\"G22K\",  ratePerGram: \"4358.83\", amountToBuy: \"5000\",actualAmount: \"4855.74\", totalGstTaxRate: \"145.67\" ,totalOrderAmount: \"5001.41\", rateTypeValue: \"buy\", weightInGrams:\"1.114\", rateId: \"95fa8e5ec8bb3ae769d1701a7911f821faed588b81181a35be546eb228cf148b\",sTaxName: \"SGST\",sTaxPercent: \"1.5\",sTaxCode: \"sgst\",cTaxName: \"CGST\",cTaxPercent: \"1.5\",cTaxCode: \"cgst\",customerName: \"Ajit Kumar\",customerMobile:\"**********\",extAgentId: \"1102\",security_pin:\"1415\"){status,respcode,message,buyOrderResponse{transaction_id, transaction_reference_id,total_order_amount,transaction_time,buyTypeValue,purityValue,total_gold_weight,transactionCharges,customerName,customerMobile,customerBankName,customerBranchName,customerBankIfsc,customerAccountName,customerUpiAddress,customerAccountNumber,transaction_type}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "buyGoldOrder": {
            "status": 200,
            "respcode": 1000,
            "message": "success",
            "buyOrderResponse": {
                "transaction_id": "MAGOLD48371616148330",
                "transaction_reference_id": "ad76d780-889a-11eb-9054-417f56a3fe9d",
                "total_order_amount": "5001.41",
                "transaction_time": "19-03-2021 15:35:30",
                "buyTypeValue": "FixedAmount",
                "purityValue": "22Kt (91.6)",
                "total_gold_weight": "1.114",
                "transactionCharges": "145.67",
                "customerName": "Ajit Kumar",
                "customerMobile": "**********",
                "customerBankName": "ICICI Bank2",
                "customerBranchName": "Madurai2",
                "customerBankIfsc": "HDFC0000454",
                "customerAccountName": "Test Acoount2",
                "customerUpiAddress": "test12@hadfc",
                "customerAccountNumber": "XXXXXX7880",
                "transaction_type": "BUY"
            }
        }
    }
}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"buyGoldOrder": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","buyOrderResponse": null}}}
*/

// -------------- Dvara Gold Lambda SELL GOLD ORDER API -----------------
/**
* @api {post} /gold 17) Sell Order API
* @apiName sellGoldOrder
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{sellGoldOrder(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\",bullionId: \"G3\", bullionName: \"Gold\", ratePerGram: \"4358.83\", amountToBuy: \"5000\",actualAmount: \"4855.74\",totalOrderAmount: \"5001.41\", rateTypeValue: \"sell\", weightInGrams:\"1.114\", rateId: \"95fa8e5ec8bb3ae769d1701a7911f821faed588b81181a35be546eb228cf148b\",customerName: \"Ajit Kumar\",customerMobile:\"**********\",security_pin:\"1415\",purityValue:\"916\",purityDisplayValue:\"22Kt (91.6)\",bullionShortName:\"G22K\"){status,respcode,message,sellOrderResponse{transaction_id, transaction_reference_id,total_order_amount,transaction_time,purityValue,total_gold_weight,transactionCharges,customerName,customerMobile,purityValue,customerBankName,customerBranchName,customerBankIfsc,customerAccountName,customerUpiAddress,customerAccountNumber,transaction_type,transaction_reason}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the bullion rate and other details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiError {String} customerData API user details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "sellGoldOrder": {
      "status": 200,
      "respcode": 1000,
      "message": "Transaction Successful",
      "sellOrderResponse": {
        "transaction_id": "MAGOLD36341616170135",
        "transaction_reference_id": "707dd4e0-88cd-11eb-9578-971a78e6fb0d",
        "total_order_amount": "8645.7",
        "transaction_time": "19-03-2021 21:38:55",
        "purityValue": "22Kt (91.6)",
        "total_gold_weight": "1",
        "transactionCharges": null,
        "customerName": "Anshad Hassan",
        "customerMobile": "**********",
        "customerBankName": "ICICI Bank2",
        "customerBranchName": "Madurai2",
        "customerBankIfsc": "HDFC0000454",
        "customerAccountName": "Test Acoount2",
        "customerUpiAddress": "test12@hadfc",
        "customerAccountNumber": "XXXXXX7880",
        "transaction_type": "SELL",
        "transaction_reason" : "",
      }
    }
  }
}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message.
* @apiError {String} customerData API user details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"sellGoldOrder": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","sellOrderResponse": null}}}
*/

// -------------- Dvara Gold Lambda Get Gold Transaction Details - Receipt API -----------------
/**
* @api {post} /gold 18) Get Gold Transaction Details API
* @apiName getGoldTransactionDetails
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{getGoldTransactionDetails(ma_user_id:28776,userid:1460,aggregator_order_id: "MAGOLD86641617091236"){status,respcode,message,merchant_name,transaction_id, transaction_reference_id,total_order_amount,transaction_time,buyTypeValue,purityValue,total_gold_weight,transactionCharges,customerName,customerMobile,transaction_status,api_params,bankName,customerBankName,customerBranchName,customerAccountName,customerAccountNumber,customerBankIfsc,customerUpiAddress,transaction_type,transaction_status,transaction_reason}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Get the receipt detail of Buy / Sell Order details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} aggregator_order_id API Order id.
* @apiSuccess {String} merchant_name API Merchant Name.
* @apiSuccess {String} transaction_id API Transactin Id.
* @apiSuccess {String} transaction_reference_id API Gold order Id.
* @apiSuccess {String} total_order_amount API Total Order Amount.
* @apiSuccess {String} transaction_time API Transaction Time.
* @apiSuccess {String} buyTypeValue API Weight or Amount.
* @apiSuccess {String} purityValue API Gold purity value.
* @apiSuccess {String} total_gold_weight API Gold weight in grams.
* @apiSuccess {String} transactionCharges API Transaction charges.
* @apiSuccess {String} customerName API Customer Name.
* @apiSuccess {String} customerMobile API Csutomer Mobile.
* @apiSuccess {String} transaction_status API Transaction Status.
* @apiSuccess {String} api_params API params.
* @apiSuccess {String} bankName Name of bank
* @apiSuccess {String} customerBankName API Customer Bank Name
* @apiSuccess {String} customerBranchName API Customer Branch Name
* @apiSuccess {String} customerAccountName Customer Account Name
* @apiSuccess {String} customerAccountNumber Customer Account Number
* @apiSuccess {String} customerBankIfsc Customer Bank Ifsc code
* @apiSuccess {String} customerUpiAddress Customer Upi Address
* @apiSuccess {String} transaction_type Transaction Type
* @apiSuccess {String} transaction_status Transaction Status
* @apiSuccess {String} transaction_reason Transaction Fail Reason

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"getGoldTransactionDetails":{"status":200,"respcode":1000,"message":"Success","merchant_name":"Frontend3 retailer","transaction_id":"MAGOLD86641617091236","transaction_reference_id":"059d9a70-912e-11eb-98cd-b9947ff4c191","total_order_amount":"502.21","transaction_time":"30-03-2021 01:30:36","buyTypeValue":"FixedAmount","purityValue":"22Kt (91.6%)","total_gold_weight":"0","transactionCharges":"14.63","customerName":"Ajit Kumar","customerMobile":"**********","api_params":"W10="}}}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message..

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getGoldTransactionDetails": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","merchant_name": null}}}
*/

// -------------- Dvara Gold Lambda Validate UPI Address API -----------------
/**
* @api {post} /gold 19) Validate UPI Address API
* @apiName validateUpiAddress
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{validateUpiAddress(ma_user_id:28479,userid:7556,upiAddress:\"ritesh@hdfc\"){status,respcode,message,isValid,customerName}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to validate the UPI Address</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} isValid API Upi valid or not.
* @apiSuccess {String} customerName API Customer Name.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"validateUpiAddress": {"status": 200,"respcode": 1000,"message": "success","isValid": "true","customerName": "wxyz"}}}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message..

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"validateUpiAddress": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","customerName": null}}}
*/

// -------------- Dvara Gold Lambda Validate Get Order Details API -----------------
/**
* @api {post} /gold 20) Get Order Details
* @apiName getOrderDetails
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{ "query" : "{getOrderDetails(ma_user_id:28479,userid:7556,extCustomerId:\"**********25071\",orderId: \"b55f2c60-881b-11eb-a88c-e3a380a09b29\",transactionType: BUY){status,respcode,message,orderDetails{id,ordernumber,status,weightInGm,requestDate,rateInrPerGm,orderTotalValueInr,type,sellType,payoutMode,buyType,bullionTotal,completiondate,marketSellRate,taxInvoiceDate,taxInvoiceNumber,advancerecipt}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Order Details buy / sell</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderDetails API Order Details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"getOrderDetails": {"status": 200,"respcode": 1000,"message": "success","orderDetails": {"id": "eb-a88c-e3a380a09b29", "ordernumber": "DV92-105","status": "complete","weightInGm": "2", "requestDate": "19-03-2021 0:26:55","rateInrPerGm": "4346.8", "orderTotalValueInr": "8954.41","type": "buy","sellType": null,"payoutMode": null,"buyType": "FixedWeight","bullionTotal": "8693.6","completiondate": "19-03-2021 0:27:0","marketSellRate": null, "taxInvoiceDate": null, "taxInvoiceNumber": null,"advancerecipt": "ADV/20-21/2070" }}}}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message..

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getOrderDetails": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","orderDetails": null}}}
*/

// -------------- Dvara Gold Lambda Validate Get Order Details API -----------------
/**
* @api {post} /gold 21) Get Dvara Passbook
* @apiName getDvaraPassbookData
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{ "query" : "{getDvaraPassbookData(ma_user_id:28479,userid:7556,extCustomerId:"**********25071"){status,respcode,message,passbookDetails{id,ordernumber,status,weightInGm,requestDate,rateInrPerGm,orderTotalValueInr,type,buyType,bullionTotal,completiondate}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Order Details of customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} passbookDetails API Passbook with Order Details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{ "data": {"getDvaraPassbookData": {"status": 200,"respcode": 1000,"message": null,"passbookDetails": [{"id": "699242e0-88c5-11eb-a001-e56fc896970f","ordernumber": "DV92-153","status": "complete","weightInGm": "10", "requestDate": "19-03-2021 20:41:42","rateInrPerGm": "4351.31","orderTotalValueInr": "44818.49","type": "buy","buyType": "FixedWeight","bullionTotal": "43513.1","completiondate": "19-03-2021 20:41:45"},]}}}


* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message..

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getDvaraPassbookData": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","passbookDetails": null}}}
*/

// -------------- Dvara Gold Lambda Validate Buy Gold Payment Order Settlement API -----------------
/**
* @api {post} /gold/agentbranch 22) Buy Gold Payment Order Settlement
* @apiName buyGoldPaymentOrderSettlement
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query" : "{buyGoldPaymentOrderSettlement(ma_user_id:28943,userid:1868,orderIdArray:\"M2NjYTczOTAtYTM2NC0xMWViLWFlYTUtMzUxYWRiNjgzNzBkLDJjMGRjMjMwLWEzNWMtMTFlYi05ODVlLTkxY2Q0NGE2NmNhYywzY2NhNzM5MC1hMzY0LTExZWItYWVhNS0zNTFhZGI2ODM3MGQsMmMwZGMyMzAtYTM1Yy0xMWViLTk4NWUtOTFjZDQ0YTY2Y2FjLDNjY2E3MzkwLWEzNjQtMTFlYi1hZWE1LTM1MWFkYjY4MzcwZCwyYzBkYzIzMC1hMzVjLTExZWItOTg1ZS05MWNkNDRhNjZjYWMsM2NjYTczOTAtYTM2NC0xMWViLWFlYTUtMzUxYWRiNjgzNzBkLDJjMGRjMjMwLWEzNWMtMTFlYi05ODVlLTkxY2Q0NGE2NmNhYywzY2NhNzM5MC1hMzY0LTExZWItYWVhNS0zNTFhZGI2ODM3MGQsMmMwZGMyMzAtYTM1Yy0xMWViLTk4NWUtOTFjZDQ0YTY2Y2FjLDNjY2E3MzkwLWEzNjQtMTFlYi1hZWE1LTM1MWFkYjY4MzcwZCwyYzBkYzIzMC1hMzVjLTExZWItOTg1ZS05MWNkNDRhNjZjYWMsM2NjYTczOTAtYTM2NC0xMWViLWFlYTUtMzUxYWRiNjgzNzBkLDJjMGRjMjMwLWEzNWMtMTFlYi05ODVlLTkxY2Q0NGE2NmNhYw==\",paymentTotalValueInr:1206.10,paymentDate:\"2021-04-22\",txnReference: \"2342134243\",paymentInstrumentType: \"NEFT\"){status,respcode,message,paymentId,paymentTotalValueInr}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Order Details of customer</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} passbookDetails API Passbook with Order Details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"buyGoldPaymentOrderSettlement": {"status": 200,"respcode": 1000,"message": "Success","paymentId": "dae74cd0-a407-11eb-8b27-bdbbbcd01a1b","paymentTotalValueInr": 1206.1}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message..

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"buyGoldPaymentOrderSettlement": {"status": 400,"respcode": 1001,"message": "Fail: Something went wrong","paymentId": null, "paymentTotalValueInr": null}}}
*/

// -------------- Dvara Gold Lambda Get Gold Float Balance API -----------------
/**
* @api {post} /gold 23) Get Gold Float Balance
* @apiName getGoldFloatBalance
* @apiGroup DVARA GOLD LAMBDA API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getGoldFloatBalance(ma_user_id:39440,userid:12174){status,message,respcode,goldFloatBalance}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Float Balance of Gold</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} goldFloatBalance API Float Balance of Gold.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"getGoldFloatBalance": {"status": 200,"message": "Success","respcode": 1000,"goldFloatBalance": 5788.84}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message API response message..

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getGoldFloatBalance": {"status": 400,"message": "Fail: Gold float balance is too low.","respcode": 1028,"goldFloatBalance": null}}}
*/