// -------------- GET TRANSFER BANK LIST -----------------
/**
* @api {post} /custlogin 1) GET TRANSFER BANK LIST
* @apiName getTransferBankList
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID

* @apiParamExample {json} Request-Example:
{
    "query": "{getTransferBankList(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"**********71857\",sessionRQ: \"b66f7863a32fcace95c99247a012863b\",ma_beneficiaries_id: 493){status,respcode,message,transferBankList{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{getTransferBankList(ma_user_id:28479,userid:7556 mobile_number:\"**********\",uic:\"**********71857\",sessionRQ: \"b66f7863a32fcace95c99247a012863b\",ma_beneficiaries_id: 493){status,respcode,message,transferBankList{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} transferBankList  Array of available bank list with modes.
* @apiSuccess {ArrayItem} transferBankList.bank_name Bank name
* @apiSuccess {ArrayItem} transferBankList.bank_logo Bank Logo URL
* @apiSuccess {ArrayItem} transferBankList.dailyLimit Daily Allowed Limit
* @apiSuccess {ArrayItem} transferBankList.monthlyLimit Monthly Allowed Limit
* @apiSuccess {ArrayItem} transferBankList.remainingLimit Remaining limit from Bank Side
* @apiSuccess {ArrayItem} transferBankList.consumedLimit Bank side consumed/used limit
* @apiSuccess {ArrayItem} transferBankList.bankRegisterStatus Remitter REGISTERED Status
* @apiSuccess {Array} transferBankList.transfer_mode Bank Transfer Mode Array

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getTransferBankList": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "transferBankList": [
                {
                    "bank_name": "NSDL",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 25000,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "REGISTERED",
                    "transfer_mode": [
                        {
                            "ma_bank_type_id": 13,
                            "transaction_type": "IMPS",
                            "priority": 1
                        },
                        {
                            "ma_bank_type_id": 10,
                            "transaction_type": "NEFT",
                            "priority": 1
                        }
                    ]
                },
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 0,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "UNREGISTERED",
                    "transfer_mode": [
                        {
                            "ma_bank_type_id": 14,
                            "transaction_type": "IMPS",
                            "priority": 1
                        },
                        {
                            "ma_bank_type_id": 15,
                            "transaction_type": "NEFT",
                            "priority": 2
                        }
                    ]
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiSuccess {Array} transferBankList  Array of available bank list with modes.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getTransferBankList": {
            "status": 400,
            "respcode": 1161,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********71857%^%",
            "transferBankList": null
        }
    }
}
*/


// -------------- Accept Amount Validation & Get Transfer Charges -----------------
/**
* @api {post} /transfers 2) Accept Amount Validation & Get Transfer Charges 
* @apiName acceptAmount
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } amount AMOUNT
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { EnumValue } transfer_mode TRANSFER MODE
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_type_id MA BANK TYPE ID
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{acceptAmount(ma_user_id:28479,userid:7556,aggregator_order_id:\"MAWEB{{$randomBankAccount}}\",amount:\"100\",uic:\"***************\",mobile_number:\"**********\",ma_beneficiaries_id:541,transfer_mode:IMPS,sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\",ma_bank_type_id:13){status,message,respcode, amountData{transaction_charges,credit_balance,total_amount}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{acceptAmount(ma_user_id:28479,userid:7556,aggregator_order_id:\"MAWEB{{$randomBankAccount}}\",amount:\"5100\",uic:\"**********71857\",mobile_number:\"**********\",ma_beneficiaries_id:493,transfer_mode:IMPS,sessionRQ: \"b66f7863a32fcace95c99247a012863b\",ma_bank_type_id:14){status,message,respcode, amountData{transaction_charges,credit_balance,total_amount}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} amountData Transaction amount data.
* @apiSuccess {Number} amountData.transaction_charges  Transaction charges against amount.
* @apiSuccess {Number} amountData.credit_balance  Transaction creadit balance.
* @apiSuccess {Number} amountData.total_amount  Transaction amount.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "acceptAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "amountData": {
                "transaction_charges": 10,
                "credit_balance": 31679.7123,
                "total_amount": 110
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample UnRegistered~Remitter~Response:
HTTP/1.1 200 OK
{
    "data": {
        "acceptAmount": {
            "status": 400,
            "message": "Fail: Customer ********** mobile not registered with PAYTM bank [IS_REGISTER]",
            "respcode": 1165,
            "amountData": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Session~Not~Found~Response:
HTTP/1.1 200 OK
{
    "data": {
        "acceptAmount": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********718571",
            "respcode": 1161,
            "amountData": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error~Response:
HTTP/1.1 200 OK
{
    "data": {
        "acceptAmount": {
            "status": 400,
            "message": "Fail: Something Went wrong [GET_SESSION]",
            "respcode": 1001,
            "amountData": null
        }
    }
}
*/


// -------------- Confirm Amount With Actual Transfer -----------------
/**
* @api {post} /transfers 3) Confirm Amount With Actual Transfer
* @apiName confirmAmount
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { StringValue } amount AMOUNT
* @apiParam { EnumValue } transfer_list TRANSFER LIST
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } ma_beneficiaries_id MA BENEFICIARIES ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } userid USERID
* @apiParam { IntValue } transaction_charges TRANSACTION CHARGES
* @apiParam { StringValue } security_pin SECURITY PIN
* @apiParam { EnumValue } transfer_mode TRANSFER MODE
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } pan PAN
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_type_id MA BANK TYPE ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{confirmAmount(amount:\"100\", transfer_list:YES, aggregator_order_id:\"AMAPM{{$randomBankAccount}}\", ma_user_id:28479, ma_beneficiaries_id:533, mobile_number:\"**********\", userid:7556, transaction_charges:10,security_pin:\"0011\",transfer_mode:IMPS, uic:\"**********71857\",pan:\"\",sessionRQ: \"ca2096e1d1e3fbe2278a736ae7d3086d\",ma_bank_type_id:14){status,message,respcode,orderid,amount,addedon,status,message,respcode,beneficiary_name,account_number,bank_name,uic,mobile_number,transaction_charges,transaction_status,remitter_name,merchant_name,transaction_id,net_amount,bank_logo,agent_bank_name,agent_id,ma_transaction_master_id,transfersData{transfer_status,ma_transfers_id,utr_number,addedon,bank_charges,transfer_amount,total_amount,bank_response,transfer_mode,agent_bank_name,bank_logo},tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{confirmAmount(amount:\"100\", transfer_list:YES, aggregator_order_id:\"AMAPM{{$randomBankAccount}}\", ma_user_id:28479, ma_beneficiaries_id:533, mobile_number:\"**********\", userid:7556, transaction_charges:10,security_pin:\"0011\",transfer_mode:IMPS, uic:\"**********71857\",pan:\"\",sessionRQ: \"ca2096e1d1e3fbe2278a736ae7d3086d\",ma_bank_type_id:14){status,message,respcode,orderid,amount,addedon,status,message,respcode,beneficiary_name,account_number,bank_name,uic,mobile_number,transaction_charges,transaction_status,remitter_name,merchant_name,transaction_id,net_amount,bank_logo,agent_bank_name,agent_id,ma_transaction_master_id,transfersData{transfer_status,ma_transfers_id,utr_number,addedon,bank_charges,transfer_amount,total_amount,bank_response,transfer_mode,agent_bank_name,bank_logo},tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": "AMAPM97939068",
            "amount": "110.00",
            "addedon": "16-02-2021 17:17:16",
            "beneficiary_name": "Rajkumar Changedd",
            "account_number": "**********",
            "bank_name": "The Dhanalakshmi Bank Ltd",
            "uic": "**********71857",
            "mobile_number": "**********",
            "transaction_charges": "10.00",
            "transaction_status": "SUCCESS",
            "remitter_name": "Sanjays Sinalkars",
            "merchant_name": "AirpayRT6  ",
            "transaction_id": "AMAPM97939068",
            "net_amount": "100.00",
            "bank_logo": "",
            "agent_bank_name": "PAYTM",
            "agent_id": "9",
            "ma_transaction_master_id": 5639,
            "transfersData": [
                {
                    "transfer_status": "SUCCESS",
                    "ma_transfers_id": 1376,
                    "utr_number": "***********",
                    "addedon": "16-02-2021 17:17:17",
                    "bank_charges": "10.00",
                    "transfer_amount": "100.00",
                    "total_amount": "110.00",
                    "bank_response": "Transfer Successful",
                    "transfer_mode": "IMPS",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                }
            ],
            "tryWithOtherBank": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiSuccessExample Pending-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": "AMAPM91276570",
            "amount": "110.00",
            "addedon": "02-03-2021 13:41:36",
            "beneficiary_name": "Rajkumar Changedd",
            "account_number": "**********",
            "bank_name": "The Dhanalakshmi Bank Ltd",
            "uic": "***************",
            "mobile_number": "**********",
            "transaction_charges": "10.00",
            "transaction_status": "PENDING",
            "remitter_name": "Ritesh Nayak",
            "merchant_name": "AirpayRT6  ",
            "transaction_id": "AMAPM91276570",
            "net_amount": "100.00",
            "bank_logo": "",
            "agent_bank_name": "PAYTM",
            "agent_id": "9",
            "ma_transaction_master_id": 5670,
            "transfersData": [
                {
                    "transfer_status": "PENDING",
                    "ma_transfers_id": 1392,
                    "utr_number": "***********",
                    "addedon": "02-03-2021 13:41:36",
                    "bank_charges": "10.00",
                    "transfer_amount": "100.00",
                    "total_amount": "110.00",
                    "bank_response": "Money transfer failed because no response was received from beneficiary bank",
                    "transfer_mode": "IMPS",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                }
            ],
            "tryWithOtherBank": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiSuccessExample Partial-Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "transfersData": [
                {
                    "transfer_status": "SUCCESS",
                    "ma_transfers_id": 1343,
                    "utr_number": "",
                    "addedon": "09-02-2021 11:1:28",
                    "bank_charges": "10.00",
                    "transfer_amount": "100.00",
                    "bank_response": "Transfer Successful",
                    "rrn": "",
                    "benename": "",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                },
                {
                    "transfer_status": "FAILED",
                    "ma_transfers_id": 1344,
                    "utr_number": "",
                    "addedon": "09-02-2021 11:1:28",
                    "bank_charges": "10.00",
                    "transfer_amount": "100.00",
                    "bank_response": "Your request was declined due to an internal error. Please try again after sometime.",
                    "rrn": "",
                    "benename": "",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                }
            ],
            "tryWithOtherBank": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiErrorExample Transfer-Failure-tryWithOtherBank-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": "AMAPM05438609",
            "amount": "5160.00",
            "addedon": "02-03-2021 12:32:38",
            "beneficiary_name": "UJJAWAL SUDHIR  JHA",
            "account_number": "*********",
            "bank_name": "The Dhanalakshmi Bank Ltd",
            "uic": "***************",
            "mobile_number": "**********",
            "transaction_charges": "60.00",
            "transaction_status": "FAILED",
            "remitter_name": "Ritesh Nayak",
            "merchant_name": "AirpayRT6  ",
            "transaction_id": "AMAPM05438609",
            "net_amount": "5100.00",
            "bank_logo": "",
            "agent_bank_name": "PAYTM",
            "agent_id": "9",
            "ma_transaction_master_id": 5665,
            "transfersData": [
                {
                    "transfer_status": "FAILED",
                    "ma_transfers_id": 1386,
                    "utr_number": "***********",
                    "addedon": "02-03-2021 12:32:38",
                    "bank_charges": "50.00",
                    "transfer_amount": "5000.00",
                    "total_amount": "5050.00",
                    "bank_response": "Generic error",
                    "transfer_mode": "IMPS",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                },
                {
                    "transfer_status": "FAILED",
                    "ma_transfers_id": 1387,
                    "utr_number": "***********",
                    "addedon": "02-03-2021 12:32:38",
                    "bank_charges": "10.00",
                    "transfer_amount": "100.00",
                    "total_amount": "110.00",
                    "bank_response": "INVALID BENEFICIARY MOBILE",
                    "transfer_mode": "IMPS",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                }
            ],
            "tryWithOtherBank": [
                {
                    "bank_name": "NSDL",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "transfer_mode": [
                        {
                            "ma_bank_type_id": 13,
                            "transaction_type": "IMPS",
                            "priority": 1
                        },
                        {
                            "ma_bank_type_id": 10,
                            "transaction_type": "NEFT",
                            "priority": 2
                        }
                    ],
                    "bankRegisterStatus": "REGISTERED"
                }
            ]
        }
    }
}


* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "confirmAmount": {
            "status": 400,
            "message": "Fail: Bank Handler not found for further processing [ADD_BENE_BANK]",
            "respcode": 1071,
            "orderid": null,
            "amount": null,
            "addedon": null,
            "beneficiary_name": null,
            "account_number": null,
            "bank_name": null,
            "uic": null,
            "mobile_number": null,
            "transaction_charges": null,
            "transaction_status": null,
            "remitter_name": null,
            "merchant_name": null,
            "transaction_id": null,
            "net_amount": null,
            "bank_logo": null,
            "agent_bank_name": null,
            "agent_id": null,
            "ma_transaction_master_id": null,
            "transfersData": null,
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response-Wrong-Charges:
HTTP/1.1 200 OK
{
    "data": {
        "confirmAmount": {
            "status": 400,
            "message": "Fail: Transaction charges not matched [COMMISSION]~10~60",
            "respcode": 1167,
            "orderid": null,
            "amount": null,
            "addedon": null,
            "beneficiary_name": null,
            "account_number": null,
            "bank_name": null,
            "uic": null,
            "mobile_number": null,
            "transaction_charges": null,
            "transaction_status": null,
            "remitter_name": null,
            "merchant_name": null,
            "transaction_id": null,
            "net_amount": null,
            "bank_logo": null,
            "agent_bank_name": null,
            "agent_id": null,
            "ma_transaction_master_id": null,
            "transfersData": null,
            "tryWithOtherBank": null
        }
    }
}
*/


// -------------- Transfer Receipt -----------------
/**
* @api {post} /transfers 4) Transfer Receipt
* @apiName transferDetails
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } ma_transaction_master_id MA TRANSACTION MASTER ID
* @apiParam { EnumValue } receipt_flag RECEIPT FLAG
* @apiParamExample {json} Request-Example:
{"query":"{transferDetails(ma_user_id:28641, userid:1249,uic:"undefined",mobile_number:"**********",ma_transaction_master_id:35478,receipt_flag:YES){orderid,amount,addedon,status,message,respcode,beneficiary_name,account_number,bank_name,uic,mobile_number,transaction_charges,transaction_id,remitter_name,merchant_name,net_amount,bank_logo,agent_bank_name,agent_id,transaction_status,transfersData{transfer_status,ma_transfers_id,utr_number,addedon,bank_charges,transfer_amount,total_amount,bank_response,agent_bank_name,bank_logo,transfer_mode},tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"}



* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{transferDetails(ma_user_id:28641, userid:1249,uic:"undefined",mobile_number:"**********",ma_transaction_master_id:35478,receipt_flag:YES){orderid,amount,addedon,status,message,respcode,beneficiary_name,account_number,bank_name,uic,mobile_number,transaction_charges,transaction_id,remitter_name,merchant_name,net_amount,bank_logo,agent_bank_name,agent_id,transaction_status,transfersData{transfer_status,ma_transfers_id,utr_number,addedon,bank_charges,transfer_amount,total_amount,bank_response,agent_bank_name,bank_logo,transfer_mode},tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "transferDetails": {
      "orderid": "MAWEB56951617991827",
      "amount": "112.00",
      "addedon": "09-04-2021 23:37:37",
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "beneficiary_name": "Tester",
      "account_number": "**********",
      "bank_name": "Hdfc Bank",
      "uic": "**********81680",
      "mobile_number": "**********",
      "transaction_charges": "12.00",
      "transaction_id": "MAWEB56951617991827",
      "remitter_name": "Test Stag",
      "merchant_name": "Backend3 retailer",
      "net_amount": "100.00",
      "bank_logo": "https://retailappdocs.s3.ap-south-1.amazonaws.com/bank_logos/Bank1617615563.png",
      "agent_bank_name": "PAYTM",
      "agent_id": "9",
      "transaction_status": "FAILED",
      "transfersData": [
        {
          "transfer_status": "FAILED",
          "ma_transfers_id": 8532,
          "utr_number": "",
          "addedon": "09-04-2021 23:37:38",
          "bank_charges": "12.00",
          "transfer_amount": "100.00",
          "total_amount": "112.00",
          "bank_response": "Your request was declined due to an internal error. Please try again after sometime.",
          "agent_bank_name": "PAYTM",
          "bank_logo": "https://retailappdocs.s3.ap-south-1.amazonaws.com/bank_logos/Bank1617615563.png",
          "transfer_mode": "NEFT"
        }
      ],
      "tryWithOtherBank": []
    }
  }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed

* @apiSuccessExample Failed-Transfer-tryWithOtherBank-Response:
HTTP/1.1 200 OK
{
    "data": {
        "transferDetails": {
            "amount": "310.00",
            "addedon": "15-02-2021 18:53:15",
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "beneficiary_name": "Rajkumar Changedd",
            "account_number": "**********",
            "bank_name": "The Dhanalakshmi Bank Ltd",
            "uic": "**********71857",
            "mobile_number": "**********",
            "transaction_charges": "10.00",
            "transaction_status": "FAILED",
            "transfersData": [
                {
                    "transfer_status": "FAILED",
                    "ma_transfers_id": 1369,
                    "utr_number": "",
                    "addedon": "15-02-2021 18:53:15",
                    "bank_charges": "10.00",
                    "transfer_amount": "300.00",
                    "total_amount": "310.00",
                    "bank_response": "",
                    "agent_bank_name": "NSDL",
                    "bank_logo": null
                }
            ],
            "tryWithOtherBank": [
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "transfer_mode": [
                        {
                            "ma_bank_type_id": 14,
                            "transaction_type": "IMPS",
                            "priority": 1
                        },
                        {
                            "ma_bank_type_id": 15,
                            "transaction_type": "NEFT",
                            "priority": 2
                        }
                    ],
                    "bankRegisterStatus": "REGISTERED"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error~Response:
HTTP/1.1 200 OK
{
    "data": {
        "transferDetails": {
            "orderid": null,
            "amount": null,
            "addedon": null,
            "status": 400,
            "message": "Receipt not available",
            "respcode": null,
            "beneficiary_name": null,
            "account_number": null,
            "bank_name": null,
            "uic": null,
            "mobile_number": null,
            "transaction_charges": null,
            "transaction_status": null,
            "transfersData": [],
            "tryWithOtherBank": null
        }
    }
}
*/


// -------------- Try With Another Bank Accept Amount -----------------
/**
* @api {post} /transfers 5) Try With Another Bank Accept Amount
* @apiName trywithAcceptAmount
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } ref_aggregator_order_id REF AGGREGATOR ORDER ID
* @apiParam { IntValue } ma_bank_type_id MA BANK TYPE ID
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{trywithAcceptAmount(ma_user_id:28479,userid:7556,aggregator_order_id:\"MAWEB{{$randomBankAccount}}\",ref_aggregator_order_id:\"AMAPM91317345\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\",ma_bank_type_id:13){status,message,respcode,uic,mobile_number,ma_beneficiaries_id,transfer_mode,sessionRQ,amountData{transaction_charges,credit_balance,amount,total_amount}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{trywithAcceptAmount(ma_user_id:28479,userid:7556,aggregator_order_id:\"MAWEB{{$randomBankAccount}}\",ref_aggregator_order_id:\"AMAPM91317345\",ma_bank_type_id:13){status,message,respcode,uic,mobile_number,ma_beneficiaries_id,transfer_mode,sessionRQ,amountData{transaction_charges,credit_balance,amount,total_amount}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uic  Customer UIC Number
* @apiSuccess {mobile_number} uic  Customer Mobile Number
* @apiSuccess {Number} ma_beneficiaries_id  Beneficiary  Primary Id
* @apiSuccess {String} transfer_mode  Transfer Mode IMPS/NEFT
* @apiSuccess {String} sessionRQ  Session request id which is referecne for current dmt session with bank priorites
* @apiSuccess {Object} amountData Transaction amount data.
* @apiSuccess {Number} amountData.transaction_charges  Transaction charges against amount.
* @apiSuccess {Number} amountData.credit_balance  Transaction creadit balance.
* @apiSuccess {Number} amountData.total_amount  Transaction amount.


* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAcceptAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "uic": "***************",
            "mobile_number": "**********",
            "ma_beneficiaries_id": 541,
            "transfer_mode": "IMPS",
            "sessionRQ": "54d735a33fb38c3ca27441ceaa7f2b7f",
            "amountData": {
                "transaction_charges": 10,
                "credit_balance": 10269.7123,
                "amount": 100,
                "total_amount": 110
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample UnRegistered~Remitter~Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAcceptAmount": {
            "status": 400,
            "message": "Fail: Customer ********** mobile not registered with PAYTM bank [IS_REGISTER]",
            "respcode": 1165,
            "amountData": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Session~Not~Found~Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAcceptAmount": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~**********718571",
            "respcode": 1161,
            "amountData": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error~Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAcceptAmount": {
            "status": 400,
            "message": "Fail: Something Went wrong [GET_SESSION]",
            "respcode": 1001,
            "amountData": null
        }
    }
}
*/


// -------------- Try With Another Bank Confirm Amount -----------------
/**
* @api {post} /transfers 6) Try With Another Bank Confirm Amount
* @apiName trywithConfirmAmount
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } aggregator_order_id AGGREGATOR ORDER ID
* @apiParam { StringValue } ref_aggregator_order_id REF AGGREGATOR ORDER ID
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_type_id MA BANK TYPE ID

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{trywithConfirmAmount(amount:\"100\", transfer_list:YES, aggregator_order_id:\"AMAPM{{$randomBankAccount}}\", ma_user_id:28479, ma_beneficiaries_id:533, mobile_number:\"**********\", userid:7556, transaction_charges:10,security_pin:\"0011\",transfer_mode:IMPS, uic:\"**********71857\",pan:\"\",sessionRQ: \"ca2096e1d1e3fbe2278a736ae7d3086d\",ma_bank_type_id:14,ref_aggregator_order_id:\"AMAPM83644438\"){status,message,respcode,orderid,amount,addedon,status,message,respcode,beneficiary_name,account_number,bank_name,uic,mobile_number,transaction_charges,transaction_status,remitter_name,merchant_name,transaction_id,net_amount,bank_logo,agent_bank_name,agent_id,ma_transaction_master_id,transfersData{transfer_status,ma_transfers_id,utr_number,addedon,bank_charges,transfer_amount,total_amount,bank_response,transfer_mode,agent_bank_name,bank_logo},tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{trywithConfirmAmount(amount:\"100\", transfer_list:YES, aggregator_order_id:\"AMAPM{{$randomBankAccount}}\", ma_user_id:28479, ma_beneficiaries_id:533, mobile_number:\"**********\", userid:7556, transaction_charges:10,security_pin:\"0011\",transfer_mode:IMPS, uic:\"**********71857\",pan:\"\",sessionRQ: \"ca2096e1d1e3fbe2278a736ae7d3086d\",ma_bank_type_id:14,ref_aggregator_order_id:\"AMAPM83644438\"){status,message,respcode,orderid,amount,addedon,status,message,respcode,beneficiary_name,account_number,bank_name,uic,mobile_number,transaction_charges,transaction_status,remitter_name,merchant_name,transaction_id,net_amount,bank_logo,agent_bank_name,agent_id,ma_transaction_master_id,transfersData{transfer_status,ma_transfers_id,utr_number,addedon,bank_charges,transfer_amount,total_amount,bank_response,transfer_mode,agent_bank_name,bank_logo},tryWithOtherBank{bank_name,bank_logo,dailyLimit,monthlyLimit,transfer_mode{ma_bank_type_id,transaction_type,priority}}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Object} orderid Order Id.
* @apiSuccess {String} amount Transaction Total Amount
* @apiSuccess {String} addedon Transaction Time
* @apiSuccess {String} beneficiary_name  Beneficiary Name
* @apiSuccess {String} account_number  Beneficiary Account number
* @apiSuccess {String} bank_name  Beneficiary Bank Name
* @apiSuccess {String} uic Customer UIC Number
* @apiSuccess {String} mobile_number Customer mobile number
* @apiSuccess {String} transaction_charges Transcation Charges
* @apiSuccess {String} transaction_status SUCCESS/FAILED/PENDING/PARTIALSUCCESS
* @apiSuccess {String} remitter_name Remitter Name
* @apiSuccess {String} merchant_name Merchant Name
* @apiSuccess {String} transaction_id Order id / Transaction Id
* @apiSuccess {String} net_amount Net Amount without Charges
* @apiSuccess {String} bank_logo Transaction bank Logo
* @apiSuccess {String} agent_bank_name Transaction Bank Name
* @apiSuccess {String} agent_id Bank Side Agent Id
* @apiSuccess {String} ma_transaction_master_id Transaction Primary Id
* @apiSuccess {Array} transfersData Transfer batch data
* @apiSuccess {Array} tryWithOtherBank  Try with other bank array enabled in which customer can try with another bank if current transaction failed


* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithConfirmAmount": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "orderid": "AMAPM77842643",
            "amount": "110.00",
            "addedon": "17-02-2021 12:50:3",
            "beneficiary_name": "Rajkumar Changedd",
            "account_number": "**********",
            "bank_name": "The Dhanalakshmi Bank Ltd",
            "uic": "**********71857",
            "mobile_number": "**********",
            "transaction_charges": "10.00",
            "transaction_status": "SUCCESS",
            "remitter_name": "Sanjays Sinalkars",
            "merchant_name": "AirpayRT6  ",
            "transaction_id": "AMAPM77842643",
            "net_amount": "100.00",
            "bank_logo": "",
            "agent_bank_name": "PAYTM",
            "agent_id": "9",
            "ma_transaction_master_id": 5640,
            "transfersData": [
                {
                    "transfer_status": "SUCCESS",
                    "ma_transfers_id": 1377,
                    "utr_number": "***********",
                    "addedon": "17-02-2021 12:50:4",
                    "bank_charges": "10.00",
                    "transfer_amount": "100.00",
                    "total_amount": "110.00",
                    "bank_response": "Transfer Successful",
                    "transfer_mode": "IMPS",
                    "agent_bank_name": "PAYTM",
                    "bank_logo": null
                }
            ],
            "tryWithOtherBank": []
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample UnRegistered~Remitter~Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithAcceptAmount": {
            "status": 400,
            "message": "Fail: Customer ********** mobile not registered with PAYTM bank [IS_REGISTER]",
            "respcode": 1165,
            "amountData": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Invalid-Transaction~Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithConfirmAmount": {
            "status": 400,
            "message": "Fail: Transaction not allowed!~[TRYACPT_AMT]",
            "respcode": 1191,
            "orderid": null,
            "amount": null,
            "addedon": null,
            "beneficiary_name": null,
            "account_number": null,
            "bank_name": null,
            "uic": null,
            "mobile_number": null,
            "transaction_charges": null,
            "transaction_status": null,
            "remitter_name": null,
            "merchant_name": null,
            "transaction_id": null,
            "net_amount": null,
            "bank_logo": null,
            "agent_bank_name": null,
            "agent_id": null,
            "ma_transaction_master_id": null,
            "transfersData": null,
            "tryWithOtherBank": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error~Response:
HTTP/1.1 200 OK
{
    "data": {
        "trywithConfirmAmount": {
            "status": 400,
            "message": "Fail: Something Went wrong [GET_SESSION]",
            "respcode": 1001,
            "orderid": null,
            "amount": null,
            "addedon": null,
            "beneficiary_name": null,
            "account_number": null,
            "bank_name": null,
            "uic": null,
            "mobile_number": null,
            "transaction_charges": null,
            "transaction_status": null,
            "remitter_name": null,
            "merchant_name": null,
            "transaction_id": null,
            "net_amount": null,
            "bank_logo": null,
            "agent_bank_name": null,
            "agent_id": null,
            "ma_transaction_master_id": null,
            "transfersData": null,
            "tryWithOtherBank": null
        }
    }
}
*/

// -------------- GET TRANSFER REFUND LIST -----------------
/**
* @api {post} /transfers 7) GET TRANSFER REFUND LIST
* @apiName refundtTransferListDetails
* @apiGroup DMT_STEP_3_TRANSFER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{
    "query": "{refundtTransferListDetails(ma_user_id:28479,userid:7556,uic:\"***************\",mobile_number:\"**********\",sessionRQ: \"0ca971778a879452da8f7834b5899cde\"){status,respcode,message,transfersData {ma_transfers_id,transfer_status,bank_charges,total_amount,refund_flag,bank_response,aggregator_order_id,transaction_date,agent_bank_name}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{refundtTransferListDetails(ma_user_id:28479,userid:7556,uic:\"***************\",mobile_number:\"**********\",sessionRQ: \"0ca971778a879452da8f7834b5899cde\"){status,respcode,message,transfersData {ma_transfers_id,transfer_status,bank_charges,total_amount,refund_flag,bank_response,aggregator_order_id,transaction_date,agent_bank_name}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} transfersData  Array of refunds
* @apiSuccess {ArrayItem} transfersData.ma_transfers_id Transfer id
* @apiSuccess {ArrayItem} transfersData.transfer_status Transfer Status PENDING
* @apiSuccess {ArrayItem} transfersData.bank_charges Bank Charges
* @apiSuccess {ArrayItem} transfersData.total_amount Total Transfer Amount including Charges
* @apiSuccess {ArrayItem} transfersData.refund_flag Refund Flag YES/NO
* @apiSuccess {ArrayItem} transfersData.bank_response Bank Response
* @apiSuccess {ArrayItem} transfersData.aggregator_order_id Transaction Order Id
* @apiSuccess {Array} transferBankList.transaction_date Transaction date
* @apiSuccess {Array} transferBankList.agent_bank_name Agent Bank Name ~ NSDL/PAYTM

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "refundtTransferListDetails": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "transfersData": [
                {
                    "ma_transfers_id": 1435,
                    "transfer_status": "PENDING",
                    "bank_charges": "12.00",
                    "total_amount": "113.00",
                    "refund_flag": "YES",
                    "bank_response": "Your transfer request was unsuccessful due to an internal error. Please try again after some time.",
                    "aggregator_order_id": "AMAPM26158198",
                    "transaction_date": "12-04-2021 16:0:0",
                    "agent_bank_name": "PAYTM"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiSuccess {Array} transfersData  Array of refun list will be empty.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "refundtTransferListDetails": {
            "status": 400,
            "respcode": null,
            "message": "Fail: No Records",
            "transfersData": []
        }
    }
}
*/

// -------------- Transfers Initiated Cron -----------------
/**
* @api {post} /transferscron Transfers Initiated Cron
* @apiName transferscron
* @apiGroup DMT_STEP_3_TRANSFER

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>
    Checks for transfers transaction that aren't available in transfers table and reverse or refunds based on the points ledger entries.
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "status":200,
    "message":"Success",
    "respcode":1000
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiSuccess {Array} transferBankList  Array of available bank list with modes.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "status":400,
    "message":"Fail: Something went wrong",
    "respcode":1001
}
*/
