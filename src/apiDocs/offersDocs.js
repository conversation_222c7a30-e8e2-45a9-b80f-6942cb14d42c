// -------------- AIRPAY OFFERS API-----------------

/**
* @api {post} /offers) Log user activity
* @apiName verifyTransaction
* @apiGroup OFFER API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{verifyTransaction(affiliate_id: 1,transaction_id: \"MAAPP01201592482988\"){status,message,respcode,offerData{amount,transactiontime,customer,expiry},checksum}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Verify Transaction</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} offer detail data.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyTransaction": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "offerData": {
                "amount": 100,
                "transactiontime": "18-06-2020 05:53:10",
                "customer": "Customer",
                "expiry": "19-06-2020 05:53:10"
            },
            "checksum": "7657f56bc3772b296c8bfb0ddc80da9bc3ad315182f6bcf6dca6dd2fde3b89a8"
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} umang_iframe_url  API barcode string.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": { "getUmangIframe": { "message": "fail : Something went wrong.", "respcode": 1001, "status": 400}}}
*/
