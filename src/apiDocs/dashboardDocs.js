// ----------------------------------------- GET BUSINESS OVERVIEW FO RETAILER--------------------------------------------------
/**
 * @api {post} /merchantdashboard 1. Get Business Overview Of RT/DT/SDT
 * @apiName businessOverview
 * @apiGroup Dashboard
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:

 {"query":"{businessOverview(ma_user_id:100000030,userid:1227){status,message,respcode,businessOverview{dailyTransactionVolume,previousDayTransactionVolume,percentageForDailyVolume,growthRateFlagForDailyVolume,monthlyTransactionVolume,previousMonthTransactionVolume,percentageForMonthlyVolume,growthRateFlagForMonthlyVolume,weeklyTransactionVolume,previousWeekTransactionVolume,percentageForWeeklyVolume,growthRateFlagForWeeklyVolume,totalNumberOfTransactionOfCurrentMonth,totalNumberOfTransactionOfPreviousMonth,percentageForTransactions,growthRateFlagForTransactions}}}"}

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to give overview of the business to RT/DT/SDT</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} businessOverview  businesss details object.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "businessOverview": {
            "status": "200",
            "message": "Success",
            "respcode": "1000",
            "businessOverview": {
                "dailyTransactionVolume": "650",
                "previousDayTransactionVolume": "0",
                "percentageForDailyVolume": "0%",
                "growthRateFlagForDailyVolume": "profit",
                "monthlyTransactionVolume": "1805",
                "previousMonthTransactionVolume": "631",
                "percentageForMonthlyVolume": "186.053882725832%",
                "growthRateFlagForMonthlyVolume": "profit",
                "weeklyTransactionVolume": "650",
                "previousWeekTransactionVolume": "650",
                "percentageForWeeklyVolume": "0%",
                "growthRateFlagForWeeklyVolume": "",
                "totalNumberOfTransactionOfCurrentMonth": "8",
                "totalNumberOfTransactionOfPreviousMonth": "2",
                "percentageForTransactions": "300%",
                "growthRateFlagForTransactions": "profit"
            }
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
    "data": {
        "businessOverview": {
            "status": "400",
            "message": "Fail: No Records",
            "respcode": 1002,
            "businessOverview": null
        }
    }
}
*/

// -------------------------------------------GET Channel Listing/Transactions --------------------------------------------------
/**
 * @api {post} /merchantdashboard 2. Get Channel Listing/Transactions
 * @apiName transactions
 * @apiGroup Dashboard
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

  {
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
    "app_version" : "1.45"
  }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:

  {"query":"{transactions(ma_user_id:28641,userid:1249,type:DAILY){status,message,respcode,channels{transactionType,transactionTypeLabel,amount,previousAmount,percentageForamount,growthRateFlagForamount,noOfTransactions,previousNoOfTransactions,percentageForNoOfTransactions,growthRateFlagForNoOfTransactions},totalTransactionAmount,percentageForTotalAmount,growthForTotalAmount,todaysDate,totalNoOfTransaction,percentageForTotalNoOfTransactions,growthForTotalNoOfTransactions,todaysDate}}"}

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to give channelwise data to RT</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {Number} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Number} channels  channel list for retailers
* @apiSuccess {Number} totalTransactionAmount  totalTransactionAmount
* @apiSuccess {Number} percentageForTotalAmount  percentageForTotalAmount
* @apiSuccess {Number} growthForTotalAmount  growthForTotalAmount
* @apiSuccess {Number} todaysDate  totalNoOfTransaction
* @apiSuccess {Number} todaysDate  percentageForTotalNoOfTransactions
* @apiSuccess {Number} todaysDate  growthForTotalNoOfTransactions
* @apiSuccess {Number} todaysDate  todaysDate

HTTP/1.1 200 OK
{
  "data": {
    "transactions": {
      "status": "200",
      "message": "Success",
      "respcode": "1000",
      "channels": [
        {
          "transactionType": "5",
          "transactionTypeLabel": "AEPS",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "6",
          "transactionTypeLabel": "BBPS",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "24",
          "transactionTypeLabel": "CMS",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "10",
          "transactionTypeLabel": "CollectMoney",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "2",
          "transactionTypeLabel": "transfers",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "31,32",
          "transactionTypeLabel": "FASTag",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "28",
          "transactionTypeLabel": "GOLD",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "15",
          "transactionTypeLabel": "Insurance",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "17",
          "transactionTypeLabel": "Recharge",
          "amount": "109",
          "previousAmount": "10",
          "percentageForamount": "990.00%",
          "growthRateFlagForamount": "profit",
          "noOfTransactions": "2",
          "previousNoOfTransactions": "1",
          "percentageForNoOfTransactions": "100.00%",
          "growthRateFlagForNoOfTransactions": "profit"
        },
        {
          "transactionType": "34",
          "transactionTypeLabel": "Shopping transaction",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "23",
          "transactionTypeLabel": "Human ATM",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        },
        {
          "transactionType": "20",
          "transactionTypeLabel": "POS",
          "amount": "0",
          "previousAmount": "0",
          "percentageForamount": "0.00%",
          "growthRateFlagForamount": "",
          "noOfTransactions": "0",
          "previousNoOfTransactions": "0",
          "percentageForNoOfTransactions": "0.00%",
          "growthRateFlagForNoOfTransactions": ""
        }
      ],
      "totalTransactionAmount": "109",
      "percentageForTotalAmount": "990.00%",
      "growthForTotalAmount": "profit",
      "todaysDate": "TODAY - Mon Jul 11 2022",
      "totalNoOfTransaction": "2",
      "percentageForTotalNoOfTransactions": "100.00%",
      "growthForTotalNoOfTransactions": "profit"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response Fail: User Type not define:
HTTP/1.1 200 OK
{
    "data": {
        "transactions": {
            "status": "400",
            "message": "Fail : user type not define",
            "respcode": "1028",
            "channels": null,
            "totalTransactionAmount": null,
            "percentageForTotalAmount": null,
            "growthForTotalAmount": null,
            "todaysDate": null,
            "totalNoOfTransaction": null,
            "percentageForTotalNoOfTransactions": null,
            "growthForTotalNoOfTransactions": null
        }
    }
}

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
    "data": {
        "transactions": {
            "status": "400",
            "message": "Fail: No Records",
            "respcode": "1002",
            "channels": null,
            "totalTransactionAmount": null,
            "percentageForTotalAmount": null,
            "growthForTotalAmount": null,
            "todaysDate": null,
            "totalNoOfTransaction": null,
            "percentageForTotalNoOfTransactions": null,
            "growthForTotalNoOfTransactions": null
        }
    }
}
*/

// ----------------------------------------- GET EARNINGS FOR RT/DT/SDT--------------------------------------------------
/**
 * @api {post} /merchantdashboard 3. Get Earnings for RT/DT/SDT
 * @apiName earnings
 * @apiGroup Dashboard
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:

 {"query":"{earnings(ma_user_id:28641,userid:1249){status,message,respcode,dailyEarnings,monthlyEarnings,lastMonthEarnings,percentage_earnings,growthRateFlagForEarnings,monthwiseEarningData{month,transactionAmount}}}"}

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API gives earning details to the retailers/DT/SDT</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} dailyEarnings  API dailyEarnings.
* @apiSuccess {String} monthlyEarnings  API  monthlyEarnings.
* @apiSuccess {String} lastMonthEarnings  API  lastMonthEarnings.
* @apiSuccess {String} percentage_earnings  API percentage_earnings.
* @apiSuccess {String} growthRateFlagForEarnings  API growthRateFlagForEarnings.
* @apiSuccess {String} monthwiseEarningData  API monthwiseEarningData object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "earnings": {
            "status": "200",
            "message": "Success",
            "respcode": "1000",
            "dailyEarnings": "13.85",
            "monthlyEarnings": "13.85",
            "lastMonthEarnings": "0.07",
            "percentage_earnings": "20395.85%",
            "growthRateFlagForEarnings": "profit",
            "monthwiseEarningData": [
                {
                    "month": "July 2022",
                    "transactionAmount": "1.38"
                },
                {
                    "month": "June 2022",
                    "transactionAmount": "0.07"
                }
            ]
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.
* @apiError {String} dailyEarnings API dailyEarnings.
* @apiError {String} monthlyEarnings  API  monthlyEarnings.
* @apiError {String} lastMonthEarnings  API  lastMonthEarnings.
* @apiError {String} percentage_earnings  API percentage_earnings.
* @apiError {String} growthRateFlagForEarnings  API growthRateFlagForEarnings.
* @apiError {String} monthwiseEarningData  API monthwiseEarningData object.

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
    "data": {
        "earnings": {
            "status": "400",
            "message": "Fail: No Records",
            "respcode": "1002",
            "dailyEarnings": null,
            "monthlyEarnings": null,
            "lastMonthEarnings": null,
            "percentage_earnings": null,
            "growthRateFlagForEarnings": null,
            "monthwiseEarningData": null
        }
    }
}
*/

// ----------------------------------------- GET CASHIN DETAILS FOR RT/DT/SDT--------------------------------------------------
/**
 * @api {post} /merchantdashboard 4. Get CashIn details for RT/DT/SDT
 * @apiName  cashIn
 * @apiGroup Dashboard
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:

{"query":"{cashIn(ma_user_id:29138,userid:2079){status,message,respcode,cashIn{cashInThisMonth,cashInLastMonth,percentageForCashIn,growthRateFlagForCashIn,cashThisMonth,cashLastMonth,percentageForCash,growthRateFlagForCash,bankTransferThisMonth,bankTransferLastMonth,percentageForBankTransfer,growthRateFlagForBankTransfer,merchantTransfersThisMonth,merchantTransfersLastMonth,percentageForMerchantTransfer,growthRateFlagForMerchantTransfer}}}"}

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API gives CashIn details to the retailers/DT/SDT</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} cashIn  API cashIn Data Object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "cashIn": {
            "status": "200",
            "message": "Success",
            "respcode": "1000",
            "cashIn": {
                "cashInThisMonth": "0.2K",
                "cashInLastMonth": "1K",
                "percentageForCashIn": "80%",
                "growthRateFlagForCashIn": "loss",
                "cashThisMonth": "0.25K",
                "cashLastMonth": "50",
                "percentageForCash": "4000%",
                "growthRateFlagForCash": "profit",
                "bankTransferThisMonth": "4K",
                "bankTransferLastMonth": "10k",
                "percentageForBankTransfer": "60%",
                "growthRateFlagForBankTransfer": "loss",
                "merchantTransfersThisMonth": "0.5k",
                "merchantTransfersLastMonth": "0.5k",
                "percentageForMerchantTransfer": "0.00%",
                "growthRateFlagForMerchantTransfer": ""
            }
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.
* @apiError {String} cashIn  API cashIn Data Object.

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
    "data": {
        "cashIn": {
            "status": "400",
            "message": "Fail: No Records",
            "respcode": "1002",
            "cashIn": null
        }
    }
}
*/

// ----------------------------------------- GET MERCHANT DETAILS FOR DT/SDT--------------------------------------------------
/**
 * @api {post} /merchantdashboard 5. Get Merchants details for DT/SDT
 * @apiName  merchants
 * @apiGroup Dashboard
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:

{"query":"{merchants(ma_user_id:100000041,userid:1247){status,message,respcode,merchants{totalMerchantBase,newMerchantsThisMonth,newMerchantsLastMonth,newMerchantsBusinessMonthly,newMerchantsBusinessLastMonth,percentageForMerchants,growthRateFlagForMerchants}}}"}

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API gives Merchants details to the DT/SDT</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} merchants  API merchants Data Object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "merchants": {
            "status": "200",
            "message": "Success",
            "respcode": "1000",
            "merchants": {
                "totalMerchantBase": "5",
                "newMerchantsThisMonth": "1",
                "newMerchantsLastMonth": "2",
                "newMerchantsBusinessMonthly": "5K",
                "newMerchantsBusinessLastMonth": "2K",
                "percentageForMerchants": "150%",
                "growthRateFlagForMerchants": "profit"
            }
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.
* @apiError {String} merchants  API merchants Data Object.

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
    "data": {
        "merchants": {
            "status": "400",
            "message": "Fail: No Records",
            "respcode": "1002",
            "merchants": null
        }
    }
}
*/
// ----------------------------------------- GET TOP MERCHANT DETAILS FOR DT/SDT--------------------------------------------------
/**
 * @api {post} /merchantdashboard 6. Get Top 5 Merchants details for DT/SDT
 * @apiName  topMerchants
 * @apiGroup Dashboard
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeader {String} app_version App Version.
 * @apiHeaderExample {json} Header-Example:

 {
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
 }

 * @apiParam {String} query Encrypted Query/Mutation.
 * @apiParamExample {json} Request-Example:

{"query":"{topMerchants(ma_user_id:*********,userid:1260){status,message,respcode,topMerchantsList{companyName,merchantName,transactionAmount}}}"}

 * @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API gives Top five Merchant details for DT/SDT</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} topMerchantsList  API topMerchantsList Data Object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "topMerchants": {
            "status": "200",
            "message": "Success",
            "respcode": "1000",
            "topMerchantsList": [
                {
                    "companyName": "sinalkar pvt ltd",
                    "merchantName": "Backend3  retailer",
                    "transactionAmount": "3.45 K"
                },
                {
                    "companyName": "vipul Pvt Ltd",
                    "merchantName": "vipul sakpal",
                    "transactionAmount": "57.56"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} states details array.
* @apiError {String} message  API response message.
* @apiError {String} topMerchantsList  API topMerchantsList Data Object.

* @apiErrorExample Error-Response Fail: No Records:
HTTP/1.1 200 OK
{
    "data": {
        "topMerchants": {
            "status": "400",
            "message": "Fail: No Records",
            "respcode": "1002",
            "topMerchantsList": null
        }
    }
}
*/
