// -------------- CASH WITHDRAWAL SEND OTP Version Two-----------------

/**
* @api {post} /cashwithdrawal 1) Cash Withdrawal Payment Modes
* @apiName cashWithdrawalPaymentModes
* @apiGroup CASH WITHDRAWAL VERSION 2 POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawalPaymentModes(ma_user_id:245,userid:1155,handler:PAYMENTMODE){status,message,paymentModesVal{ma_withdrawal_bankmode_config_id, payment_mode, payment_type, ma_withdrawal_bank_config_id, charge_type, transaction_type, charge_value, min_amount, max_amount, priority, mode_status, addedon, updatedon}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to get Payment Modes for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} paymentModesVal  Payment Modes array.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"cashWithdrawalPaymentModes": {"status": 200,"message": "Success","paymentModesVal": [{"ma_withdrawal_bankmode_config_id": "1","payment_mode": "NEFT","payment_type": 2,"ma_withdrawal_bank_config_id": "1","charge_type": 1,"transaction_type": 1,"charge_value": "10.00", "min_amount": "1.00", "max_amount": "200000","priority": "TRUE","mode_status": "Y","addedon": "*************","updatedon": "*************"},{"ma_withdrawal_bankmode_config_id": "2","payment_mode": "IMPS","payment_type": 1,"ma_withdrawal_bank_config_id": "1","charge_type": 2,"transaction_type": 1,"charge_value": "1.00", "min_amount": "1.00", "max_amount": "200000","priority": "TRUE","mode_status": "Y","addedon": "*************","updatedon": "*************"}]}}}

* @apiError {String} status API status code.
* @apiError {String} paymentModesVal  Payment Modes array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":400,"message":"Something went wrong","paymentModesVal":null}}}
*/

/**
* @api {post} /cashwithdrawal 2) Send OTP
* @apiName cashWithdrawalSendOtps
* @apiGroup CASH WITHDRAWAL VERSION 2 POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawal(ma_user_id:28552,userid:7638,amount:100,orderid:"MAWEB54031594806719",bankid:67,maWithdrawalBankmodeConfigId:1,handler:SEND){status,message,otpOrderid,transactionCharges}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to send OTP for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} otpOrderid  OTP order Id.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} transactionCharges API Payment Mode Charges.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Success: OTP Sent to customer","otpOrderid":"MACW73111594806728","transactionCharges": "10"}}}

* @apiError {String} status API status code.
* @apiError {String} otpOrderid  OTP order Id.
* @apiError {String} message  API response message.
* @apiError {String} transactionCharges API Payment Mode Charges.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":400,"message":"Something went wrong","otpOrderid":null,"transactionCharges":null}}}
*/

// -------------- CASH WITHDRAWAL RE SEND OTP -----------------
/**
* @api {post} /cashwithdrawal 3) ReSend OTP
* @apiName cashWithdrawalReSendOtp
* @apiGroup CASH WITHDRAWAL VERSION 2 POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawal(ma_user_id:28552,userid:7638,amount:100,orderid:"MAWEB54031594806719",bankid:67,handler:RESEND,otpOrderid:"MACW73111594806728",otp:"",maWithdrawalBankmodeConfigId:1){status,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Re-send OTP for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Success"}}}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Something went wrong"}}}
*/

// -------------- Verfiy OTP & CashWithdrawal POINTS TO CASH -----------------
/**
* @api {post} /cashwithdrawal 4) Verfiy OTP & CashWithdrawal
* @apiName cashWithdrawalVerifyOtp
* @apiGroup CASH WITHDRAWAL VERSION 2 POINTS TO CASH

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{cashWithdrawal(ma_user_id:28552,userid:7638,amount:100,orderid:"MAWEB54031594806719",bankid:67,handler:VERIFY,otpOrderid:"MACW73111594806728",otp:"123456",maWithdrawalBankmodeConfigId:1){status,message}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to Verify the OTP and process points to cash for Cash withdrawal</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Success"}}}

* @apiError {String} status API status code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"cashWithdrawal":{"status":200,"message":"Something went wrong"}}}
*/
