// -------------- AEPS Transaction -----------------
/**
* @api {post} /aepsmerchantauth 1) AEPS 2 Factor Authentications
* @apiName registerMerchantForAepsAuth
* @apiGroup AEPSMERCHANTAUTHS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    registerMerchantForAepsAuth(
        ma_user_id:29138,
        userid:2079,
        merchant_aadhaar_no:"************",
        aggregator_order_id:"MAWEB245245245909",
        mobile_number:"**********",
        captureResponse:"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",
        cardnumberORUID:"************************************************************************************************************************************************************************************",
        languageCode: "en",
        latitude:"9.9352576",
        longitude:"76.3068416",
        paymentType:"B",
        timestamp:"**********",
        transactionType:"AUTH",
        call_type: "aeps",
        privatekey:"edec9a11e3d2e7254b8fcbf6789323df617161d005859a992b33f15f76f861a6",
        mer_dom:"Imh0dHBzOi8vYXBtZXJjaGFudGFwcC5ub3dwYXkuY28uaW4i"
        ){status,message,respcode}}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{registerMerchantForAepsAuth(ma_user_id:29138,userid:2079,merchant_aadhaar_no:"************",aggregator_order_id:"MAWEB245245245909",mobile_number:"**********",captureResponse:"eyJQaWREYXRhdHlwZSI6Ilg2R0b1hIcmdyN2FnU0czTGVlZ0xRV3VaOXFrOE9xbi9seGpEWWo1QXRHaEFKTnV3VXJuMjZaaHhJOExZbXVtbGVKbTNseENScE1zaS8rd3pjL1ZpVkZCU3FtMUtyV3JSTGZjMThQWXI4WVNHcm9uQ0dVK09Ga3FWYkNhQ0w3cmFWVVFSREY2SUZjRGxVUTROQklGZkZ1UlEvQngrYlEzNVFCd3htSDh6Zm1jayt1L2Q2ZU4rdEtsOThybkttTmVDczlFRXM3VDMzcWZHaUpNOVFTTHBBV2FVSHNXTEZiRk4waENJUmZ1QWJKUXRlL1BnOXpMTDlYYkJXQ05MVkM0cU9WbW5LMTdQKzE4TlBHd2toNFdYZDJxOGd0VHBteFg1U1JYUEhzLzBCREpHdVRMOVRSZnl3dnZHUzgvTmVjT0JYbkV0d3BmakgrbmVPczVLU1FCSkx0K3dhZUJGMWRDZk9HdXl0MTJ5WCt5aXMrYVRIVkJXb0RONGp4NzZTUkZxWmdlTmlwSE9TQWkxL2l1enhLN3BMci9jbjA4ZVB3NHBIMjEwMm9XaGZXQWFFTnI0V0kyMDR1dnV5d3FQSGRhM3JtYTVwL21iM0hwVTl4Q1BRaEZqaEtCR2pYdjJYbklOY0liOG02LzdtRHBVZFJ4dUhHbXdJZTRjMkxMUUROYUJ2T3NKY3R1S1JFaG1qMHB5WTdqNDVSalZhRFZRT1BYS3YwK3NWVUQzZGhSZEZncnJ5NkhkV01lR2E2bmJPY1lHK1NXWnJ5WFNrWTNkZkNRMGttd01hbWFtZG9GeDRWVFAydGNReGUvekU4dmY2ZVFUWS9ZVmdLR09tRTdMWjN0cnNjbWtkdWpTYk56L3hCdXkxTGl2bTh0LzI4UVRhb2F3K1BQUGpobVZHM3g4MFZiNmpuMm9rMWdYTXcrNWFvN1QyV1dQRmxlcUxHbjg5Qi8iLCJjaSI6IjIwMjIxMDIxIiwiZGMiOiJiNDQ3NzgwMC1mMzVjLTExZWEtOTk2Ni05OGYyYjNlNjgzZTAiLCJkcElEIjoiU0VDVUdFTi5TR0kiLCJlcnJDb2RlIjoiMCIsImVyckluZm8iOiJTVUNDRVNTIiwiZkNvdW50IjoiMSIsImZUeXBlIjoiMCIsImhtYWMiOiJEOVBOTTRuMVFVcHcycmlLYUwwVXY0dUNqSmYzYkVDOHhYSGdscjR6NDZFZDN3UkExdzFyYkNHL0dnRVFEMDhuYVlFdWhrdHlkRHR5WG55eEEydWROYmNGY3J6MDVPdW9xMTVsYTdScCt2WGphTmFvODY2WTI4c3ZtUU55R2xtandLUkJFOEY5NzR6Wmd4T1lyNlc0UXdxN1k2WlNINHhnRjFoM0VDd1BEK0lsQ3E1T3o3NzZsdXFJOFFzUjR4bnNkRWcrdGY5ZW1JYi9MZ2xTOEVJcWI5c0cyQlZXOUxUY095TFJ3bWNnT2oyc0FyV29jNlhBMWdOQT09In0=", cardnumberORUID:"************************************************************************************************************************************************************************************", languageCode: "en",latitude:"9.9352576", longitude:"76.3068416", paymentType:"B",timestamp:"**********",transactionType:"AUTH",call_type: "aeps",privatekey:"edec9a11e3d2e7254b8fcbf6789323df617161d005859a992b33f15f76f861a6",mer_dom:"Imh0dHBzOi8vYXBtZXJjaGFudGFwcC5ub3dwYXkuY28uaW4i"){status,message,respcode,authentication_required}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
"data": {
    "registerMerchantForAepsAuth": {
    "status": 200,
    "message": "Two Factor Verification Success",
    "respcode": 1000,
    authentication_required: 'N'
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
"data": {
  "registerMerchantForAepsAuth": {
    "status": 400,
    "respcode": 1001,
    "message": "Something went wrong"
  }
}
}
*/

// -------------- Get AEPS Merchant Auth -----------------
/**
* @api {post} /aepsmerchantauth 2) Get AEPS Merchants Authentication
* @apiName getAepsMerchantAuth
* @apiGroup AEPSMERCHANTAUTHS

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    getAepsMerchantAuth(
        ma_user_id:29138,
        userid:2079
    ){status,respcode,message,authentication_required}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getAepsMerchantAuth(ma_user_id:29138, userid:2079) {status,respcode,message,authentication_required}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getAepsMerchantAuth": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "authentication_required": "N"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getMiniStatement": {
      "status": 400,
      "message": "Fail: Something Went Wrong!",
      "respcode": 1001
    }
  }
}
*/
