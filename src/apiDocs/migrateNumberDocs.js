// -------------- SEND OTP FOR MIGRATE NUMBER -----------------
/**
 * @api {post} /migratenumber 1) Send OTP
 * @apiName kycOtpHandler
 * @apiGroup MIGRATE NUMBER
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeaderExample {json} Header-Example:
 {
 *       "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
*     }
*
* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  kycOtpHandler(
    uic:"997512387492589",
    ma_user_id:28552,
    userid: 7638,
    country_code: "IN",
    mobile_number:"9996663334",
    old_mobile_number:"9975123874",
    handler:SEND,
    otp_type:MIGRATE
  ){status,message,orderid,respcode}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation {kycOtpHandler(uic:"997512387492589",ma_user_id:28552,userid: 7638,country_code: "IN",mobile_number:"9996663334",old_mobile_number:"9975123874",handler:SEND,otp_type:MIGRATE){status,message,orderid,respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "kycOtpHandler":{
      "status":200,
      "message":"Success: OTP Sent to customer",
      "orderid":"MAMN09111593423990",
      "respcode":2002
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data":{
    "kycOtpHandler":{
      "status":400,
      "message":"Something went wrong",
      "orderid":"",
      "respcode":1001
    }
  }
}
*/

// -------------- VERIFY OTP AND MIGRATE NUMBER -----------------
/**
 * @api {post} /migratenumber 2) Verify OTP & Change Mobile
 * @apiName changeMobile
 * @apiGroup MIGRATE NUMBER
 *
 * @apiHeader {String} Authorization JWT Token.
 * @apiHeaderExample {json} Header-Example:
 {
 *       "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
*     }
*
* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  changeMobile(
    uic:"997512387492589",
    ma_user_id: 28552,
    userid:7638,
    country_code:"IN",
    old_mobile_number:"9975123874",
    new_mobile_number:"9996663366",
    orderid:"MAMN64531593425379",
    otp:"123456",
    otp_type:MIGRATE,
    documents:[{type_of_document:PAN,document_number:""},{type_of_document:AADHAR,document_number:""}]
  ){status,message,respcode}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{changeMobile(uic:"997512387492589",ma_user_id: 28552,userid:7638,country_code:"IN",old_mobile_number:"9975123874",new_mobile_number:"9996663366",orderid:"MAMN64531593425379",otp:"123456",otp_type:MIGRATE,documents:[{type_of_document:PAN,document_number:""},{type_of_document:AADHAR,document_number:""}]){status,message,respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "changeMobile":{
      "status":200,
      "message":"Success",
      "respcode":1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data":{
    "changeMobile":{
      "status":400,
      "message":"Success",
      "respcode":1001
    }
  }
}
*/

