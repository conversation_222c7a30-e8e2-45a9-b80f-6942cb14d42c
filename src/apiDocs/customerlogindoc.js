// -------------- PreCustomerlogin -----------------
/**
* @api {post} /custlogin 1) precustomerlogin
* @apiName preCustomerLogin
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } userid USERID

* @apiParamExample {json} Request-Example:
{
    "query": "{preCustomerLogin(ma_user_id:28479 , userid:7556 , mobile_number:\"**********\"){status,message,respcode,customFields}}"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } userid USERID
* @apiParamExample {json} Request-Custom-Field-Required-Example:
{
    "query": "{preCustomerLogin(ma_user_id:28479 , userid:7556 , mobile_number:\"**********\"){status,message,respcode,customFields}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{preCustomerLogin(ma_user_id:28479 , userid:7556 , mobile_number:\"**********\"){status,message,respcode,customFields}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} customFields  From Fields Array

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "preCustomerLogin": {
            "status": 200,
            "message": "Success: PreCustomLogin Required",
            "respcode": 1235,
            "customFields": [
                {
                    "field": "firstName",
                    "label": "First Name",
                    "rules": {
                        "max": 49,
                        "min": 3,
                        "regex": "/^[a-zA-Z ']{1,50}$/",
                        "required": true
                    },
                    "inputype": "text"
                },
                {
                    "field": "lastName",
                    "label": "Last Name",
                    "rules": {
                        "max": 49,
                        "min": 3,
                        "regex": "/^[a-zA-Z ']{1,50}$/",
                        "required": true
                    },
                    "inputype": "text"
                }
            ]
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} customFields  From Fields Array

* @apiSuccessExample Request-Custom-Field-Not-Required-Example:
HTTP/1.1 200 OK
{
    "data": {
        "preCustomerLogin": {
            "status": 200,
            "message": "Success: PreCustomLogin not required",
            "respcode": 1234,
            "customFields": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} customFields  From Fields Array

* @apiSuccessExample Request-Remitter-Already-Exists-Example:
{
    "data": {
        "preCustomerLogin": {
            "status": 200,
            "message": "Success: Customer Already Registered",
            "respcode": 1234,
            "customFields": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} customFields  From Fields Array

* @apiSuccessExample Request-PreCustomLogin-Required-Example:
{
    "data": {
        "preCustomerLogin": {
            "status": 200,
            "message": "Success: PreCustomLogin Required",
            "respcode": 1235,
            "customFields": [
                {
                    "field": "firstName",
                    "label": "First Name",
                    "rules": {
                        "max": 49,
                        "min": 3,
                        "regex": "/^[a-zA-Z ']{1,50}$/",
                        "required": true
                    },
                    "inputype": "text"
                },
                {
                    "field": "lastName",
                    "label": "Last Name",
                    "rules": {
                        "max": 49,
                        "min": 3,
                        "regex": "/^[a-zA-Z ']{1,50}$/",
                        "required": true
                    },
                    "inputype": "text"
                },
                {
                    "field": "dob",
                    "label": "DOB",
                    "rules": {
                        "max": 49,
                        "min": 3,
                        "regex": "/^[a-zA-Z ']{1,50}$/",
                        "required": true
                    },
                    "inputype": "text"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "preCustomerLogin": {
            "status": 400,
            "message": "Fail: Something went wrong",
            "respcode": 1001,
            "customFields": []
        }
    }
}
*/

// -------------- Customer register/login -----------------
/**
* @api {post} /custlogin 2) customer register/login
* @apiName customerLogin
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { EnumValue } benificiary_list BENIFICIARY LIST
* @apiParam { IntValue } ma_user_device_id MA USER DEVICE ID
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } userid USERID
* @apiParam { IntValue } offset OFFSET
* @apiParam { IntValue } limit LIMIT
* @apiParam { EnumValue } bankListLimits BANKLISTLIMITS
* @apiParam { StringValue } customFields Optional 64Encode String

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{customerLogin(benificiary_list:YES, ma_user_device_id:1, ma_user_id:28479, mobile_number:\"**********\", userid:7556,offset :0 , limit : 2, bankListLimits:YES){status,message,respcode,orderid,kyc_status,remitter_name,isBeneMobileMandatory,uic,nextFlag,beneficiarylist{ma_beneficiaries_id,ma_user_id,userid,ma_bank_master_id,beneficiary_status,beneficiary_name,bank_name,account_number,mobile_number,ben_mobile_number,ifsc_code,relationship,bene_verify_status,boarding_bank_name,bank_status},customerFieldJson,bene_verification,sessionRQ,bankListLimits{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus},unRegisterBankList{bank_name,bank_logo,monthlyLimit,bankRegisterStatus,bank_otp,ma_bank_on_boarding_id}}}"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { EnumValue } benificiary_list BENIFICIARY LIST
* @apiParam { IntValue } ma_user_device_id MA USER DEVICE ID
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } userid USERID
* @apiParam { IntValue } offset OFFSET
* @apiParam { IntValue } limit LIMIT
* @apiParam { EnumValue } bankListLimits BANKLISTLIMITS
* @apiParamExample {json} Request-Without-Bene-WithoutLimit-Example:
{
    "query": "mutation{customerLogin(benificiary_list:YES, ma_user_device_id:1, ma_user_id:28479, mobile_number:\"**********\", userid:7556,offset :0 , limit : 2, bankListLimits:NO){status,message,respcode,orderid,kyc_status,remitter_name,isBeneMobileMandatory,uic,nextFlag,beneficiarylist{ma_beneficiaries_id,ma_user_id,userid,ma_bank_master_id,beneficiary_status,beneficiary_name,bank_name,account_number,mobile_number,ben_mobile_number,ifsc_code,relationship,bene_verify_status,boarding_bank_name,bank_status},customerFieldJson,bene_verification,sessionRQ,bankListLimits{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus},unRegisterBankList{bank_name,bank_logo,monthlyLimit,bankRegisterStatus,bank_otp,ma_bank_on_boarding_id}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{customerLogin(benificiary_list:NO, ma_user_device_id:1, ma_user_id:28479, mobile_number:\"**********\", userid:7556,offset :0 , limit : 10, bankListLimits:NO,customFields : "eyAiZmlyc3ROYW1lIiA6ICJzd2FydXAiICwgImxhc3ROYW1lIjogImFpcnRlbCIgfSA" ){status,message,respcode,orderid,kyc_status,remitter_name,isBeneMobileMandatory,uic,beneficiarylist{ma_beneficiaries_id,ma_user_id,userid,ma_bank_master_id,beneficiary_status,beneficiary_name,bank_name,account_number,mobile_number,ben_mobile_number,ifsc_code,relationship,bene_verify_status,boarding_bank_name,bank_status},customerFieldJson,bene_verification,sessionRQ,bankListLimits{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus},unRegisterBankList{bank_name,bank_logo,monthlyLimit,bankRegisterStatus,bank_otp,ma_bank_on_boarding_id}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {String} kyc_status  Customer KYC Status
* @apiSuccess {String} remitter_name  Customer remitter name
* @apiSuccess {Boolean} isBeneMobileMandatory  Beneficiary mobile no flag
* @apiSuccess {Boolean} nextFlag  Beneficiary Pagination Flag
* @apiSuccess {Array} beneficiarylist  Beneficiary Array
* @apiSuccess {Array} customerFieldJson  Beneficiary Array
* @apiSuccess {Boolean} bene_verification  Enable Verify and Add Bene Option
* @apiSuccess {String} sessionRQ  Session request id which is referecne for current dmt session with bank priorites
* @apiSuccess {Array} bankListLimits  List of bank available to get avaiable or possible to increase the limit
* @apiSuccess {Array} unRegisterBankList  List of bank in which current remitter is not register

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerLogin": {
            "status": 200,
            "message": "Success: Remitter logged in successfully",
            "respcode": 2001,
            "orderid": null,
            "kyc_status": "PENDING",
            "remitter_name": "Sanjay Sinalkar",
            "uic": "**********19995",
            "nextFlag": true,
            "isBeneMobileMandatory":false
            "beneficiarylist": [
                {
                    "ma_beneficiaries_id": "514",
                    "ma_user_id": 28479,
                    "userid": 7556,
                    "ma_bank_master_id": 75,
                    "beneficiary_status": "YES",
                    "beneficiary_name": "Rajkumar Changedd",
                    "bank_name": "The Dhanalakshmi Bank Ltd, ITC CENTRE,CHENNAI",
                    "account_number": "*************",
                    "mobile_number": "**********",
                    "ben_mobile_number": "**********",
                    "ifsc_code": "HDFC0000004",
                    "relationship": "Brother",
                    "bene_verify_status": "UNVERIFIED",
                    "boarding_bank_name": "NSDL",
                    "bank_status": "REGISTERED"
                },
                {
                    "ma_beneficiaries_id": "511",
                    "ma_user_id": 28479,
                    "userid": 7556,
                    "ma_bank_master_id": 75,
                    "beneficiary_status": "YES",
                    "beneficiary_name": "Rajkumar Changedd",
                    "bank_name": "The Dhanalakshmi Bank Ltd, THRISSUR MAIN",
                    "account_number": "************",
                    "mobile_number": "**********",
                    "ben_mobile_number": "**********",
                    "ifsc_code": "DLXB0000001",
                    "relationship": "Brother",
                    "bene_verify_status": "VERIFIED",
                    "boarding_bank_name": "NSDL",
                    "bank_status": "UNREGISTERED"
                }
            ],
            "customFields": {
                "firstName": "swarup",
                "lastName": "airtel"
            },
            "customerFieldJson": null,
            "bene_verification": true,
            "sessionRQ": "dc4ed9b551bb6a61aa059780cb769bda",
            "bankListLimits": [
                {
                    "bank_name": "NSDL",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 25000,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "REGISTERED"
                },
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 25000,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "REGISTERED"
                }
            ],
            "unRegisterBankList": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {String} kyc_status  Customer KYC Status
* @apiSuccess {String} remitter_name  Customer remitter name
* @apiSuccess {Boolean} nextFlag  Beneficiary Pagination Flag
* @apiSuccess {Array} beneficiarylist  Beneficiary Array
* @apiSuccess {Array} customerFieldJson  Beneficiary Array
* @apiSuccess {Boolean} bene_verification  Enable Verify and Add Bene Option
* @apiSuccess {String} sessionRQ  Session request id which is referecne for current dmt session with bank priorites
* @apiSuccess {Array} bankListLimits  List of bank available to get avaiable or possible to increase the limit
* @apiSuccess {Array} unRegisterBankList  List of bank in which current remitter is not register

* @apiSuccessExample Success-WithoutBeneList-WithOutLimit-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerLogin": {
            "status": 200,
            "message": "Success: Remitter logged in successfully",
            "respcode": 2001,
            "orderid": null,
            "kyc_status": "PENDING",
            "remitter_name": "Sanjay AWS",
            "isBeneMobileMandatory":false
            "uic": "***************",
            "nextFlag": false,
            "beneficiarylist": [],
            "customerFieldJson": null,
            "bene_verification": false,
            "sessionRQ": "879ed5a3976ca200eb36b11f8c59229f",
            "bankListLimits": [],
            "customFields": {
                "firstName": "swarup",
                "lastName": "airtel"
            },
            "unRegisterBankList": [
                {
                    "bank_name": "NSDL",
                    "bank_logo": null,
                    "monthlyLimit": 25000,
                    "bankRegisterStatus": "UNREGISTERED",
                    "bank_otp": "NO",
                    "ma_bank_on_boarding_id": 5
                }
            ]
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.
* @apiSuccess {String} kyc_status  Customer KYC Status
* @apiSuccess {String} remitter_name  Customer remitter name
* @apiSuccess {Boolean} nextFlag  Beneficiary Pagination Flag
* @apiSuccess {Array} beneficiarylist  Beneficiary Array
* @apiSuccess {Array} customerFieldJson  Beneficiary Array
* @apiSuccess {Boolean} bene_verification  Enable Verify and Add Bene Option
* @apiSuccess {String} sessionRQ  Session request id which is referecne for current dmt session with bank priorites
* @apiSuccess {Array} bankListLimits  List of bank available to get avaiable or possible to increase the limit
* @apiSuccess {Array} unRegisterBankList  List of bank in which current remitter is not register

* @apiSuccessExample New-Remitter-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerLogin": {
            "status": 200,
            "message": "Success: OTP Sent to customer",
            "respcode": 2002,
            "orderid": "MACOPAYT1610456632",
            "kyc_status": null,
            "remitter_name": null,
            "uic": null,
            "nextFlag": false,
            "beneficiarylist": null,
            "isBeneMobileMandatory":false
            "customerFieldJson": "[{\"field\": \"firstName\", \"label\": \"First Name\", \"rules\": {\"max\": 100, \"min\": 5, \"regrex\": \"/^[a-zA-Z. ']{1,100}$/\", \"required\": true}, \"inputype\": \"text\"}, {\"field\": \"lastName\", \"label\": \"Last Name\", \"rules\": {\"max\": 100, \"min\": 5, \"regrex\": \"/^[a-zA-Z. ']{1,100}$/\", \"required\": true}, \"inputype\": \"text\"}]",
            "bene_verification": true,
            "consumedLimit": null,
            "remainingLimit": null,
            "limitForBank": null,
            "dailyLimit": null,
            "monthlyLimit": null,
            "sessionRQ": "cc0bfa3beff0b1e5213fabe4e2560462",
            "customFields": {
                "firstName": "swarup",
                "lastName": "airtel"
            },
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerLogin": {
            "status": 400,
            "message": "Fail: Mobile number not valid ",
            "respcode": 1033,
            "orderid": null,
            "kyc_status": null,
            "remitter_name": null,
            "isBeneMobileMandatory":null
            "uic": null,
            "beneficiarylist": null,
            "customerFieldJson": null,
            "bene_verification": null,
            "sessionRQ": null,
            "bankListLimits": null,
            "unRegisterBankList": null,
            "customFields": {},
        }
    }
}
*/

// -------------- Verify Customer OTP of Register -----------------
/**
* @api {post} /custlogin 3) Verify Customer OTP of Register
* @apiName verifyOtp
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } userid USERID
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } ma_user_device_id MA USER DEVICE ID
* @apiParam { EnumValue } benificiary_list BENIFICIARY LIST
* @apiParam { StringValue } otp OTP
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } customerFieldJson CUSTOMERFIELDJSON
* @apiParam { StringValue } firstName FIRSTNAME
* @apiParam { StringValue } lastName LASTNAME
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{"query":"mutation{verifyOtp(userid:7556,ma_user_id:28479,mobile_number:\"**********\",ma_user_device_id:1,benificiary_list:NO,otp:\"121255\",orderid:\"MACOPAYT1614344396\",customerFieldJson:\"\",firstName:\"Ritesh\",lastName:\"Nayak\" ,sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status,message,respcode,uic,beneExists}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{verifyOtp(userid:7556,ma_user_id:28479,mobile_number:\"**********\",ma_user_device_id:1,benificiary_list:NO,otp:\"121255\",orderid:\"MACOPAYT1614344396\",customerFieldJson:\"\",firstName:\"Ritesh\",lastName:\"Nayak\" ,sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status,message,respcode,uic,beneExists}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uic  Customer unique code.
* @apiSuccess {String} beneExists  Beneficiary List Exist

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyOtp": {
            "status": 200,
            "message": "Success: Remitter registered successfully",
            "respcode": 2004,
            "uic": "**********31401",
            "beneExists": true
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uic  Customer unique code.
* @apiSuccess {String} beneExists  Beneficiary List Exist

* @apiSuccessExample Success~already~Verified-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyOtp": {
            "status": 200,
            "message": "Customer account already Verified",
            "respcode": 2004,
            "uic": "**********31401",
            "beneExists": true
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyOtp": {
            "status": 400,
            "message": "Fail: OTP has expired",
            "respcode": 1008,
            "uic": null,
            "beneExists": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiSuccess {String} beneExists  Beneficiary List Exist

* @apiErrorExample Bank-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyOtp": {
            "status": 400,
            "message": "Fail: PAYTM API Failure ::: OTP verification failed",
            "respcode": 10005,
            "uic": null,
            "beneExists": null
        }
    }
}
*/

// -------------- Customer Register to Other Bank -----------------
/**
* @api {post} /custlogin 4) customer register to other bank
* @apiName customerLoginBankOtp
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { EnumValue } benificiary_list BENIFICIARY LIST
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } uic UIC
* @apiParam { IntValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID
* @apiParam { IntValue } ma_user_device_id MA USER DEVICE ID
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{"query":"mutation{customerLoginBankOtp(benificiary_list:NO, ma_user_id:28479, mobile_number:\"**********\", userid:7556,uic:\"**********31401\",ma_bank_on_boarding_id:5,ma_user_device_id:1,sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status,message,respcode,orderid,uic,customerFieldJson}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{customerLoginBankOtp(benificiary_list:NO, ma_user_id:28479, mobile_number:\"**********\", userid:7556,uic:\"**********31401\",ma_bank_on_boarding_id:5,ma_user_device_id:1,sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\"){status,message,respcode,orderid,uic,customerFieldJson}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  Order id
* @apiSuccess {String} uic  Unique customer code
* @apiSuccess {String} customerFieldJson  Json string

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerLoginBankOtp": {
            "status": 200,
            "message": "Success: OTP Sent to customer",
            "respcode": 2002,
            "orderid": "MACO19961614346751",
            "uic": "**********31401",
            "customerFieldJson": "[{\"field\": \"statename\", \"label\": \"State\", \"rules\": {\"max\": 49, \"min\": 5, \"required\": true}, \"inputype\": \"text\"}, {\"field\": \"cityname\", \"label\": \"City\", \"rules\": {\"max\": 49, \"min\": 5, \"required\": true}, \"inputype\": \"text\"}, {\"field\": \"pincode\", \"label\": \"Pincode\", \"rules\": {\"max\": 10, \"min\": 1, \"required\": true}, \"inputype\": \"number\"}, {\"field\": \"senderaddress1\", \"label\": \"Address\", \"rules\": {\"max\": 199, \"min\": 5, \"regex\": \"/[^a-z0-9,+]+/gi\", \"required\": true}, \"inputype\": \"text\"}]"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} orderid  order id

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerLoginBankOtp": {
            "status": 400,
            "message": "Fail: Invalid session Id [GET_SESSION]~null",
            "respcode": 1161,
            "orderid": null,
            "uic": null,
            "customerFieldJson": null
        }
    }
}
*/

// -------------- Verify OTP Customer Register to Other Bank -----------------
/**
* @api {post} /custlogin 5) Verify OTP Customer Register to Other Bank
* @apiName verifyCustomerBankOtp
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } userid USERID
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { IntValue } ma_user_device_id MA USER DEVICE ID
* @apiParam { StringValue } otp OTP
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } customerFieldJson CUSTOMERFIELDJSON
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_on_boarding_id MA BANK ON BOARDING ID

* @apiParamExample {json} Request-Example:
{"query":"mutation{verifyCustomerBankOtp(userid:7556,ma_user_id:28479,mobile_number:\"**********\",ma_user_device_id:1,otp:\"123456\",orderid:\"MACO78021614348310\",customerFieldJson:\"\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\",ma_bank_on_boarding_id:5){status,message,respcode,uic,beneExists}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{verifyCustomerBankOtp(userid:7556,ma_user_id:28479,mobile_number:\"**********\",ma_user_device_id:1,otp:\"123456\",orderid:\"MACO78021614348310\",customerFieldJson:\"\",sessionRQ: \"54d735a33fb38c3ca27441ceaa7f2b7f\",ma_bank_on_boarding_id:5){status,message,respcode,uic,beneExists}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uic  Unique customer code.
* @apiSuccess {String} beneExists  Beneficiary List Exist

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyCustomerBankOtp": {
            "status": 200,
            "message": "Success: Remitter registered successfully",
            "respcode": 2004,
            "uic": "**********31401",
            "beneExists": true
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} uic  Unique customer code.
* @apiSuccess {String} beneExists  Beneficiary List Exist

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyCustomerBankOtp": {
            "status": 400,
            "message": "Fail: OTP has expired",
            "respcode": 1008,
            "uic": null,
            "beneExists": null
        }
    }
}
*/

// -------------- Customer Login/Register Resent OTP -----------------
/**
* @api {post} /custlogin 6) Customer Login/Register Resent OTP
* @apiName resentOtp
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } orderid ORDERID
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{"query":"mutation{resentOtp(ma_user_id:28479,userid:7556,mobile_number:\"9821191280\",orderid:\"MACOPAYT1614343055\",sessionRQ:\"10f94340d2c6d9f586e163e5bb10347b\"){status,message,respcode}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation{resentOtp(ma_user_id:28479,userid:7556,mobile_number:\"9821191280\",orderid:\"MACOPAYT1614343055\",sessionRQ:\"10f94340d2c6d9f586e163e5bb10347b\"){status,message,respcode}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentOtp": {
            "status": 200,
            "message": "Success",
            "respcode": 1000
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentOtp": {
            "status": 400,
            "message": "Fail: Invalid order id",
            "respcode": 1104
        }
    }
}
*/

// -------------- GET REMITTER LIMIT -----------------
/**
* @api {post} /custlogin 7) GET REMITTER LIMIT
* @apiName getRemitterLimit
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ

* @apiParamExample {json} Request-Example:
{
    "query": "{getRemitterLimit(ma_user_id:28479,userid:7556,mobile_number:\"**********\",uic:\"**********19995\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status,respcode,message,bankListLimits{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus,ma_bank_on_boarding_id,bankKycStatus,isKycAvailable},unRegisterBankList{bank_name,bank_logo,monthlyLimit,bankRegisterStatus,bank_otp,ma_bank_on_boarding_id}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{getRemitterLimit(ma_user_id:28479,userid:7556,mobile_number:\"**********\",uic:\"**********19995\",sessionRQ: \"26e0f4f0aac6703376d978d7fe91061d\"){status,respcode,message,bankListLimits{bank_name,bank_logo,dailyLimit,monthlyLimit,remainingLimit,consumedLimit,bankRegisterStatus,ma_bank_on_boarding_id,bankKycStatus,isKycAvailable},unRegisterBankList{bank_name,bank_logo,monthlyLimit,bankRegisterStatus,bank_otp,ma_bank_on_boarding_id}}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} bankListLimits  List of bank available to get avaiable or possible to increase the limit
* @apiSuccess {Array} unRegisterBankList  List of bank in which current remitter is not register

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getRemitterLimit": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "bankListLimits": [
                {
                    "bank_name": "NSDL",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 25000,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "REGISTERED",
                    "ma_bank_on_boarding_id": 5,
                    "bankKycStatus": "REGISTERED",
                    "isKycAvailable": true
                },
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 25000,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "REGISTERED",
                    "ma_bank_on_boarding_id": 9,
                    "bankKycStatus": "UNREGISTERED",
                    "isKycAvailable": false
                }
            ],
            "unRegisterBankList": []
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} bankListLimits  List of bank available to get avaiable or possible to increase the limit
* @apiSuccessExample Success-new-Registred~-Unregistred-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getRemitterLimit": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "bankListLimits": [
                {
                    "bank_name": "NSDL",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 25000,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "REGISTERED",
                    "ma_bank_on_boarding_id": 5,
                    "bankKycStatus": "REGISTERED",
                    "isKycAvailable": true
                },
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "dailyLimit": 25000,
                    "monthlyLimit": 25000,
                    "remainingLimit": 0,
                    "consumedLimit": 0,
                    "bankRegisterStatus": "UNREGISTERED",
                    "ma_bank_on_boarding_id": 9,
                    "bankKycStatus": "UNREGISTERED",
                    "isKycAvailable": false
                }
            ],
            "unRegisterBankList": [
                {
                    "bank_name": "PAYTM",
                    "bank_logo": null,
                    "monthlyLimit": 25000,
                    "bankRegisterStatus": "UNREGISTERED",
                    "bank_otp": "YES",
                    "ma_bank_on_boarding_id": 9
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} bankListLimits Array of bank list
* @apiSuccess {Array} unRegisterBankList  List of bank in which current remitter is not register

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getRemitterLimit": {
            "status": 400,
            "respcode": 1161,
            "message": "Fail: Invalid session Id [GET_SESSION]~^**********71857",
            "bankListLimits": null,
            "unRegisterBankList": null
        }
    }
}
*/

// -------------- PRE KYC Registration  -----------------
/**
* @api {post} /custlogin 8) PRE KYC Registration
* @apiName preKycRegistration
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_on_boarding_id BANK ID

* @apiParamExample {json} Request-Example:
{
    "query": "{preKycRegistration(ma_user_id:28479 , userid:7556 , mobile_number : \"**********\",uic:\"**********75971\",sessionRQ:\"95b72f903a6833f511ca60bcf91f60a3\",ma_bank_on_boarding_id:5){status,message,respcode,customFields}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "{preKycRegistration(ma_user_id:28479 , userid:7556 , mobile_number : \"**********\",uic:\"**********75971\",sessionRQ:\"95b72f903a6833f511ca60bcf91f60a3\",ma_bank_on_boarding_id:5){status,message,respcode,customFields}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} customFields  dynamic required fields as per kyc type
* @apiSuccessExample Success-Response KYC Required:
HTTP/1.1 200 OK
{
    "data": {
        "preKycRegistration": {
            "status": 200,
            "message": "Success: Kyc Registered not required",
            "respcode": 12348,
            "customFields": [
                {
                    "kycType": "PAN",
                    "fields": [
                        {
                            "field": "Pancard",
                            "label": "Pancard",
                            "rules": {
                                "max": 10,
                                "min": 10,
                                "regex": "^([A-Z]){5}([0-9]){4}([A-Z]){1}$",
                                "required": true
                            },
                            "inputype": "text"
                        }
                    ]
                },
                {
                    "kycType": "BIO",
                    "fields": []
                }
            ]
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} customFields  dynamic required fields as per kyc type
* @apiSuccessExample Success-Response KYC NOT Required:
HTTP/1.1 200 OK
{
    "data": {
        "preKycRegistration": {
            "status": 200,
            "message": "Success: Kyc Registered not required",
            "respcode": 12349,
            "customFields": []
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiSuccess {Array} customFields  dynamic required fields as per kyc type

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "preKycRegistration": {
            "status": 200,
            "message": "Success: Kyc Registered not required",
            "respcode": 12349,
            "customFields": []
        }
    }
}
*/

// -------------- KYC Registration  -----------------
/**
* @api {post} /custlogin 9) KYC Registration
* @apiName customerKycRegistration
* @apiGroup DMT_STEP_1_REMITTER

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam { IntValue } ma_user_id MA USER ID
* @apiParam { IntValue } userid USERID
* @apiParam { StringValue } mobile_number MOBILE NUMBER
* @apiParam { StringValue } uic UIC
* @apiParam { StringValue } sessionRQ SESSIONRQ
* @apiParam { IntValue } ma_bank_on_boarding_id BANK ID
* @apiParam { EnumValue } kyc_type  PAN/BIO
* @apiParam { StringValue } customFields dyamic kyc fields

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{customerKycRegistration(ma_user_id:28479 , userid:7556 , mobile_number : \"**********\",uic:\"**********75971\",sessionRQ:\"95b72f903a6833f511ca60bcf91f60a3\",ma_bank_on_boarding_id:5,kyc_type:PAN,customFields:\"eyJwYW5udW1iZXIiOiJlZnNocDQ0MzQ4OGYifQ==\"){status,message,respcode}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{
    "query": "mutation{customerKycRegistration(ma_user_id:28479 , userid:7556 , mobile_number : \"**********\",uic:\"**********75971\",sessionRQ:\"95b72f903a6833f511ca60bcf91f60a3\",ma_bank_on_boarding_id:5,kyc_type:PAN,customFields:\"eyJwYW5udW1iZXIiOiJlZnNocDQ0MzQ4OGYifQ==\"){status,message,respcode}}"
}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerKycRegistration": {
            "status": 200,
            "message": "Success: KYC Verified Successfully",
            "respcode": 12350
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "preKycRegistration": {
            "status": 400,
            "message": "Fail: Kyc Verification Failed",
            "respcode": 12350
        }
    }
}
*/