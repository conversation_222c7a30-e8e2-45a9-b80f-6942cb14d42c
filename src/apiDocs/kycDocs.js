// -------------- SEND OTP KYC -----------------
/**
* @api {post} /kyc 1) Send OTP
* @apiName kycOtpHandler-Send
* @apiGroup KYC

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  kycOtpHandler(
    uic: "996937619755625",
    ma_user_id: 28552,
    userid: 7638,
    country_code: "IN",
    mobile_number: "9820586859",
    handler: "SEND",
    otp_type: "KYC
  ){status,message,respcode,orderid}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation {kycOtpHandler(uic:"996937619755625",ma_user_id: 28552,userid: 7638,country_code: "IN",mobile_number: "9820586859",handler:SEND,otp_type:KYC){status,message,respcode,orderid}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} orderid  OTP order id.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "kycOtpHandler": {
      "status": 200,
      "message": "Success: OTP Sent to customer",
      "respcode": 2002,
      "orderid": "MAKYC85631593426371"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} orderid  OTP order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "kycOtpHandler": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "orderid": ""
    }
  }
}
*/

// -------------- RESEND OTP KYC -----------------
/**
* @api {post} /kyc 2) Re-Send OTP
* @apiName kycOtpHandler-Resend
* @apiGroup KYC

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  kycOtpHandler(
    uic:"996937619755625",
    ma_user_id: 28552,
    userid: 7638,
    country_code: "IN",
    mobile_number: "9820586859",
    orderid:"MAKYC09311582699059",
    handler:RESEND,
    otp_type:KYC
  ){status,message,respcode,orderid}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation {kycOtpHandler(uic:"996937619755625",ma_user_id: 28552,userid: 7638,country_code: "IN",mobile_number: "9820586859",orderid:"MAKYC09311582699059",handler:RESEND,otp_type:KYC){status,message,respcode,orderid}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "kycOtpHandler":{
      "status":200,
      "respcode":1000,
      "message":"Success"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "kycOtpHandler": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001
    }
  }
}
*/

// -------------- VERIFY OTP KYC -----------------
/**
* @api {post} /kyc 3) Verify OTP
* @apiName verifyOtpKyc
* @apiGroup KYC

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  verifyOtpKyc(
    uic:"982058685929124",
    ma_user_id:28643,
    userid:1251,
    country_code:"IN",
    mobile_number:"9820586859",
    orderid:"MAKYC75831604398632",
    otp:"123456",
    handler:VERIFY,
    otp_type:KYC
  ){status,message,respcode,kyc_master{ma_kyc_document_master_id,document_type,mime_type,size_limit,mandatory_flag}}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation {verifyOtpKyc(uic:"982058685929124",ma_user_id:28643,userid:1251,country_code:"IN",mobile_number:"9820586859",orderid:"MAKYC75831604398632",otp:"123456",handler:VERIFY,otp_type:KYC){status,message,respcode,kyc_master{ma_kyc_document_master_id,document_type,mime_type,size_limit,mandatory_flag}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} kyc_master  Array of kyc master.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "verifyOtpKyc":{
      "status":200,
      "message":"Success",
      "respcode":1000,
      "kyc_master":[
        {
          "ma_kyc_document_master_id":1,
          "document_type":"PAN",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":1
        },
        {
          "ma_kyc_document_master_id":2,
          "document_type":"AADHAR",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":1
        },
        {
          "ma_kyc_document_master_id":3,
          "document_type":"PHOTO",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":1
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} kyc_master  Array of kyc master.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtpKyc": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "kyc_master": []
    }
  }
}
*/

// -------------- GET KYC MASTER DETAILS -----------------
/**
* @api {post} /kyc 4) Get KYC Master details
* @apiName getKycDetails
* @apiGroup KYC

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
  getKycDetails(
    userid:1251,
    uic:"982058685929124"
  ){status,message,respcode,kyc_status,kyc_master{ma_kyc_document_master_id,document_type,mime_type,size_limit,mandatory_flag},documents{ma_kyc_document_master_id,document_type,document_number,document_path}}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getKycDetails(userid:1251,uic:"982058685929124"){status,message,respcode,kyc_status,kyc_master{ma_kyc_document_master_id,document_type,mime_type,size_limit,mandatory_flag},documents{ma_kyc_document_master_id,document_type,document_number,document_path}}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} kyc_master  Array of kyc master.
* @apiSuccess {String} kyc_status  KYC status of customer.
* @apiSuccess {Array} documents  Array of uploaded documents.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "getKycDetails":{
      "status":200,
      "message":"Success",
      "respcode":1000,
      "kyc_status":"PENDING",
      "kyc_master":[
        {
          "ma_kyc_document_master_id":1,
          "document_type":"PAN",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":1
        },
        {
          "ma_kyc_document_master_id":2,
          "document_type":"AADHAR",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":1
        },
        {
          "ma_kyc_document_master_id":3,
          "document_type":"PHOTO",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":1
        },
        {
          "ma_kyc_document_master_id":4,
          "document_type":"DL",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":0
        },
        {
          "ma_kyc_document_master_id":5,
          "document_type":"BILL",
          "mime_type":"jpg,jpeg,png,pdf",
          "size_limit":"1024",
          "mandatory_flag":0
        }
      ],
      "documents":null
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {Array} kyc_master  Array of kyc master.
* @apiError {String} kyc_status  KYC status of customer.
* @apiError {Array} documents  Array of uploaded documents.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getKycDetails": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "kyc_master": [],
      "documents": [],
      "kyc_status": null
    }
  }
}
*/

// -------------- GET KYC STATUS -----------------
/**
* @api {post} /kyc 5) Get KYC Status
* @apiName getKycStatus
* @apiGroup KYC

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
  getKycStatus(
    uic:"720806938053666"
  ){status,respcode,message,approval_flag,kyc_status}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"{getKycStatus(uic:"720806938053666"){status,respcode,message,approval_flag,kyc_status}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} approval_flag State of approval.
* @apiSuccess {String} kyc_status  KYC status of customer.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getKycStatus": {
      "status": 200,
      "respcode": 1000,
      "message": "Success",
      "approval_flag": "APPROVED",
      "kyc_status": "APPROVED"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {String} approval_flag State of approval.
* @apiError {String} kyc_status  KYC status of customer.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getKycStatus": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "approval_flag": null,
      "kyc_status": null
    }
  }
}
*/

// -------------- UPDATE KYC -----------------
/**
* @api {post} /kyc 6) Update KYC
* @apiName updateKyc
* @apiGroup KYC

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
mutation {
  updateKyc (
    ma_user_id: 245,
    userid: 1155,
    uic: "720806938053666",
    action: 1,
    documents:[
      {
        type_of_document:PAN,
        document_number: "**********",
        document_path:"s3.bucket.path.pan.jpg"
      },
      {
        type_of_document:AADHAR,
        document_number: "123456789123",
        document_path:"s3.bucket.path.aadhar.jpg"
      },
      {
        type_of_document:PHOTO,
        document_number: "",
        document_path:"s3.bucket.path.photo.jpg"
      }
    ]
  ){status,message,respcode}
}

* @apiDescription <span class="type" style="background-color: #e535ab">GraphQL Request</span>
<pre><code>{"query":"mutation {updateKyc (ma_user_id: 245,userid: 1155,uic: "720806938053666",action: 1,documents:[{type_of_document:PAN,document_number: "**********",document_path:"s3.bucket.path.pan.jpg"},{type_of_document:AADHAR,document_number: "123456789123",document_path:"s3.bucket.path.aadhar.jpg"},{type_of_document:PHOTO,document_number: "",document_path:"s3.bucket.path.photo.jpg"}]){status,message}}"}</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "updateKyc": {
      "status": 200,
      "message": "Success"
      "respcode": "1000"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.
* @apiError {String} approval_flag State of approval.
* @apiError {String} kyc_status  KYC status of customer.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "updateKyc": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001
    }
  }
}
*/
