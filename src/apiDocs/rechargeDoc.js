// -------------- Get Cricle -----------------

/**
* @api {post} /recharge 1) Get Cricle
* @apiName getCircle
* @apiGroup RECHARGE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getCircle(
      ma_user_id: 28479,
      userid: 7556
    ){
      status,
      message,
      respcode,
      circles{
        circle_id,
        circle_name
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
  "query": "{getCircle(ma_user_id: 28479, userid: 7556){status, message, respcode, circles{circle_id, circle_name}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getCircle": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "circles": [
        {
          "circle_id": 34,
          "circle_name": "ASSAM"
        },
        {
          "circle_id": 22,
          "circle_name": "BIHAR JHARKHAND"
        },
        {
          "circle_id": 7,
          "circle_name": "CHENNAI"
        },
        {
          "circle_id": 12,
          "circle_name": "GUJARAT"
        },
        {
          "circle_id": 20,
          "circle_name": "HARYANA"
        },
        {
          "circle_id": 21,
          "circle_name": "HIMACHAL PRADESH"
        },
        {
          "circle_id": 25,
          "circle_name": "JAMMU KASHMIR"
        },
        {
          "circle_id": 9,
          "circle_name": "KARNATAKA"
        },
        {
          "circle_id": 14,
          "circle_name": "KERALA"
        },
        {
          "circle_id": 6,
          "circle_name": "KOLKATA"
        },
        {
          "circle_id": 4,
          "circle_name": "MAHARASHTRA"
        },
        {
          "circle_id": 16,
          "circle_name": "MADHYA PRADESH "
        },
        {
          "circle_id": 16,
          "circle_name": " CHHATTISGARH"
        },
        {
          "circle_id": 3,
          "circle_name": " MUMBAI "
        },
        {
          "circle_id": 26,
          "circle_name": " NORTH EAST "
        },
        {
          "circle_id": 23,
          "circle_name": "ORISSA"
        },
        {
          "circle_id": 1,
          "circle_name": "PUNJAB"
        },
        {
          "circle_id": 18,
          "circle_name": "RAJASTHAN"
        },
        {
          "circle_id": 8,
          "circle_name": "TAMIL NADU"
        },
        {
          "circle_id": 10,
          "circle_name": "UP EAST"
        },
        {
          "circle_id": 8,
          "circle_name": "UP WEST AND UTTARAKHAND"
        },
        {
          "circle_id": 2,
          "circle_name": "WEST BENGAL"
        },
        {
          "circle_id": 5,
          "circle_name": "DELHI NCR"
        },
        {
          "circle_id": 35,
          "circle_name": "ANDHRA PRADESH"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} data  User Details array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data":{"getCircle":{"status":400,"respcode":1001,"message":"Fail: Something went wrong","circles": null}}}
*/

// -------------- Get Operator Details -----------------
/**
* @api {post} /recharge 2) Get Operator Details
* @apiName getOperatorDetails
* @apiGroup RECHARGE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    getOperatorDetails(
      ma_user_id: 18999,
      userid: 5912,
      type: "Mobile",
      mobile: "**********"
    ){
      status,
      message,
      respcode,
      operatorDetails{
        status_id,
        message,
        mobile,
        opname,
        opid,
        circle,
        circleid
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
  "query": "{getOperatorDetails(ma_user_id: 18999, userid: 5912, type: \"Mobile\", mobile: \"**********\"){status, message, respcode, operatorDetails{status_id, message, mobile, opname, opid, circle, circleid}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data API User details array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getOperatorDetails": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "operatorDetails": [
        {
          "status_id": 1,
          "message": "Success",
          "mobile": "**********",
          "opname": "Vodafone",
          "opid": 3,
          "circle": "Mumbai",
          "circleid": 3
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} data API User details array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getOperatorDetails": {
      "status": 400,
      "message": "Error: [~MA~] No data found",
      "respcode": 1028,
      "operatorDetails": null
    }
  }
}
*/

// -------------- Get Recharge Plans -----------------
/**
* @api {post} /recharge 3) Get Recharge Plans
* @apiName getRechargePlans
* @apiGroup RECHARGE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example - TYPE MOBILE:
  {
    getRechargePlans(
      ma_user_id: 28479,
      userid: 7556,
      type: "Mobile",
      mobile: "**********",
      opid: 1,
      circleid: 1
    ){
      status,
      message,
      respcode,
      rechargePlans
    }
  }

* @apiParamExample {json} Request-Example - TYPE DTH:
  {
    getRechargePlans(
      ma_user_id: 28479,
      userid: 7556,
      type: "DTH",
      mobile: "197641021",
      opid: 15
    ){
      status,
      message,
      respcode,
      rechargePlans
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
TYPE MOBILE
{
  "query": "{getRechargePlans(ma_user_id: 28479, userid: 7556, type: \"Mobile\", mobile: \"**********\", opid: 1, circleid: 1){status, message, respcode, rechargePlans}}"
}

TYPE DTH
{
  "query": "{getRechargePlans(ma_user_id: 28479, userid: 7556, type: \"DTH\", mobile: \"197641021\", opid: 15){status, message, respcode, rechargePlans}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getRechargePlans": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "rechargePlans": {
        "Plan": [
          {
            "rs": {
              "1 MONTHS": "109.32"
            },
            "desc": "Total channels 197",
            "plan_name": "INTRODUCTORY VALUE COMBO OFFER PACK @129",
            "last_update": "06-07-2020"
          },
          ...
          {
            "rs": {
              "1 MONTHS": "95"
            },
            "desc": "Total channels 23",
            "plan_name": "STAR HINDI TELUGU PREMIUM 2",
            "last_update": "06-07-2020"
          }
        ]
      }
    }
  }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getRechargePlans": {
      "status": 400,
      "message": "Error: [~MA~] No data found",
      "respcode": 1028,
      "rechargePlans": null
    }
  }
}
*/

// -------------- Get Biller Id -----------------
/**
* @api {post} /recharge 4) Get Biller Id
* @apiName getBillerId
* @apiGroup RECHARGE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example TYPE DTH:
  {
    getBillerId(
      ma_user_id: 28479,
      userid: 7556,
      type: "DTH",
    ){
      status,
      message,
      respcode,
      billers{
        BILLER_MASTER_ID,
        BILLER_CATEGORY,
        BILLER_SUB_CATEGORY,
        BILLER_NAME,
        BILLER_LOGO,
        BILLER_BILL_COPY,
        BBPS_GATEWAY_ID,
        STATUS,
        register{
          flag,
          FIELDNAMES,
          VALIDATION
        },
        instant{
          flag,
          FIELDNAMES,
          VALIDATION
        },
        recharge{
          flag,
          FIELDNAMES,
          VALIDATION
        },
        PAY_AFTER_DUEDATE,
        ISBILLERBBPS,
        ONLINE_VALIDATION,
        PAYMENTAMOUNT_VALIDATION,
        PARTIAL_PAY,
        BILLER_TYPE,
        PAYMENT_METHODS,
        BILLER_RECHARGE_TYPE
      }
    }
  }
* @apiParamExample {json} Request-Example TYPE MOBILE:
  {
    getBillerId(
      ma_user_id: 28479,
      userid: 7556,
      type: "Mobile",
    ){
      status,
      message,
      respcode,
      billers{
        BILLER_MASTER_ID,
        BILLER_CATEGORY,
        BILLER_SUB_CATEGORY,
        BILLER_NAME,
        BILLER_LOGO,
        BILLER_BILL_COPY,
        BBPS_GATEWAY_ID,
        STATUS,
        register{
          flag,
          FIELDNAMES,
          VALIDATION
        },
        instant{
          flag,
          FIELDNAMES,
          VALIDATION
        },
        recharge{
          flag,
          FIELDNAMES,
          VALIDATION
        },
        PAY_AFTER_DUEDATE,
        ISBILLERBBPS,
        ONLINE_VALIDATION,
        PAYMENTAMOUNT_VALIDATION,
        PARTIAL_PAY,
        BILLER_TYPE,
        PAYMENT_METHODS,
        BILLER_RECHARGE_TYPE
      }
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
  "query": "{getBillerId(ma_user_id: 28479, userid: 7556){status, message, respcode, billers{BILLER_MASTER_ID,BILLER_CATEGORY,BILLER_SUB_CATEGORY,BILLER_NAME,BILLER_LOGO,BILLER_BILL_COPY,BBPS_GATEWAY_ID,STATUS,register{flag,FIELDNAMES,VALIDATION},instant{flag,FIELDNAMES,VALIDATION},recharge{flag,FIELDNAMES,VALIDATION},PAY_AFTER_DUEDATE,ISBILLERBBPS,ONLINE_VALIDATION,PAYMENTAMOUNT_VALIDATION,PARTIAL_PAY,BILLER_TYPE,PAYMENT_METHODS,BILLER_RECHARGE_TYPE}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getBillerId": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "billers": [
        {
          "BILLER_MASTER_ID": 420,
          "BILLER_CATEGORY": "Recharge",
          "BILLER_SUB_CATEGORY": "Prepaid",
          "BILLER_NAME": "BSNL",
          "BILLER_LOGO": null,
          "BILLER_BILL_COPY": null,
          "BBPS_GATEWAY_ID": 2,
          "STATUS": "Y",
          "register": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "instant": [
            {
              "flag": 1,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "recharge": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "PAY_AFTER_DUEDATE": "N",
          "ISBILLERBBPS": "N",
          "ONLINE_VALIDATION": "N",
          "PAYMENTAMOUNT_VALIDATION": "N",
          "PARTIAL_PAY": "N",
          "BILLER_TYPE": null,
          "PAYMENT_METHODS": null,
          "BILLER_RECHARGE_TYPE": "TELECOM"
        },
        {
          "BILLER_MASTER_ID": 415,
          "BILLER_CATEGORY": "Recharge",
          "BILLER_SUB_CATEGORY": "Prepaid",
          "BILLER_NAME": "IDEA",
          "BILLER_LOGO": null,
          "BILLER_BILL_COPY": null,
          "BBPS_GATEWAY_ID": 2,
          "STATUS": "Y",
          "register": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "instant": [
            {
              "flag": 1,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "recharge": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "PAY_AFTER_DUEDATE": "N",
          "ISBILLERBBPS": "N",
          "ONLINE_VALIDATION": "N",
          "PAYMENTAMOUNT_VALIDATION": "N",
          "PARTIAL_PAY": "N",
          "BILLER_TYPE": null,
          "PAYMENT_METHODS": null,
          "BILLER_RECHARGE_TYPE": "TELECOM"
        },
        {
          "BILLER_MASTER_ID": 413,
          "BILLER_CATEGORY": "Recharge",
          "BILLER_SUB_CATEGORY": "Prepaid",
          "BILLER_NAME": "AIRTEL",
          "BILLER_LOGO": null,
          "BILLER_BILL_COPY": null,
          "BBPS_GATEWAY_ID": 2,
          "STATUS": "Y",
          "register": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "instant": [
            {
              "flag": 1,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "recharge": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "PAY_AFTER_DUEDATE": "N",
          "ISBILLERBBPS": "N",
          "ONLINE_VALIDATION": "N",
          "PAYMENTAMOUNT_VALIDATION": "N",
          "PARTIAL_PAY": "N",
          "BILLER_TYPE": null,
          "PAYMENT_METHODS": null,
          "BILLER_RECHARGE_TYPE": "TELECOM"
        },
        {
          "BILLER_MASTER_ID": 432,
          "BILLER_CATEGORY": "Recharge",
          "BILLER_SUB_CATEGORY": "Prepaid",
          "BILLER_NAME": "BSNL STV",
          "BILLER_LOGO": null,
          "BILLER_BILL_COPY": null,
          "BBPS_GATEWAY_ID": 2,
          "STATUS": "Y",
          "register": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "instant": [
            {
              "flag": 1,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "recharge": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "PAY_AFTER_DUEDATE": "N",
          "ISBILLERBBPS": "N",
          "ONLINE_VALIDATION": "N",
          "PAYMENTAMOUNT_VALIDATION": "N",
          "PARTIAL_PAY": "N",
          "BILLER_TYPE": null,
          "PAYMENT_METHODS": null,
          "BILLER_RECHARGE_TYPE": "TELECOM"
        },
        {
          "BILLER_MASTER_ID": 414,
          "BILLER_CATEGORY": "Recharge",
          "BILLER_SUB_CATEGORY": "Prepaid",
          "BILLER_NAME": "VODAFONE",
          "BILLER_LOGO": null,
          "BILLER_BILL_COPY": null,
          "BBPS_GATEWAY_ID": 2,
          "STATUS": "Y",
          "register": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "instant": [
            {
              "flag": 1,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "recharge": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "PAY_AFTER_DUEDATE": "N",
          "ISBILLERBBPS": "N",
          "ONLINE_VALIDATION": "N",
          "PAYMENTAMOUNT_VALIDATION": "N",
          "PARTIAL_PAY": "N",
          "BILLER_TYPE": null,
          "PAYMENT_METHODS": null,
          "BILLER_RECHARGE_TYPE": "TELECOM"
        },
        {
          "BILLER_MASTER_ID": 424,
          "BILLER_CATEGORY": "Recharge",
          "BILLER_SUB_CATEGORY": "Prepaid",
          "BILLER_NAME": "RELIANCE JIO",
          "BILLER_LOGO": null,
          "BILLER_BILL_COPY": null,
          "BBPS_GATEWAY_ID": 2,
          "STATUS": "Y",
          "register": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "instant": [
            {
              "flag": 1,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "recharge": [
            {
              "flag": 0,
              "FIELDNAMES": "Mobile Number",
              "VALIDATION": "^[0-9]{10}$"
            }
          ],
          "PAY_AFTER_DUEDATE": "N",
          "ISBILLERBBPS": "N",
          "ONLINE_VALIDATION": "N",
          "PAYMENTAMOUNT_VALIDATION": "N",
          "PARTIAL_PAY": "N",
          "BILLER_TYPE": null,
          "PAYMENT_METHODS": null,
          "BILLER_RECHARGE_TYPE": "TELECOM"
        }
      ]
    }
  }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{ "data": { "getBillerId": { "status": 400,"respcode":1001,"message": "Fail: Something went wrong", "billers": null } } }
*/

// -------------- Do Recharge -----------------
/**
* @api {post} /recharge 5) Do Recharge
* @apiName doRecharge
* @apiGroup RECHARGE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvcmV0YWlsYS5haXJwYXkubmluamFcL2FwaVwvbG9naW4iLCJpYXQiOjE2MDEyNjQ2OTEsImV4cCI6MTYwMTM1MTA5MSwibmJmIjoxNjAxMjY0NjkxLCJqdGkiOiJoUDNvMWdrM2lUN2g4VTlQIiwicHVibGlja2V5IjoiMTI1MSIsInByb2ZpbGVpZCI6IjI4NjQzIn0.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    doRecharge(
      ma_user_id: 28479,
      userid: 7556,
      amount: "10",
      pin: "1234",
      mobile: "**********",
      PAYMENTAMOUNT_VALIDATION: "Y",
      BILLER_MASTER_ID: 414,
      BILLER_NAME: "VODAFONE",
      aggregator_order_id: "MARCH1000042",
      type: "Mobile"
    ){
      status,
      message,
      respcode,
      customer_name,
      customer_mobile,
      amount,
      aggregator_order_id,
      merchant_name,
      merchant_mobile,
      transaction_time,
      transaction_status,
      provider_name,
      utility_name,
      transaction_charges,
      transaction_amount,
      bill_response,
      transaction_id
    }
  }

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
  "query": "mutation{doRecharge(ma_user_id: 28479, userid: 7556, amount: \"10\", pin: \"1234\", mobile: \"**********\", PAYMENTAMOUNT_VALIDATION: \"Y\", BILLER_MASTER_ID: 414, BILLER_NAME: \"VODAFONE\", aggregator_order_id: \"MARCH1000042\"){status, message, respcode, customer_name, customer_mobile, amount, aggregator_order_id, merchant_name, merchant_mobile, transaction_time, transaction_status, provider_name, utility_name, transaction_charges, transaction_amount, bill_response, transaction_id}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "doRecharge": {
      "status": 200,
      "message": "Transaction in process",
      "respcode": 1028,
      "customer_name": null,
      "customer_mobile": "**********",
      "amount": "10.00",
      "aggregator_order_id": "MAWEB0000001021",
      "merchant_name": "Backend3  retailer",
      "merchant_mobile": "**********",
      "transaction_time": "30-08-2021 01:14:18",
      "transaction_status": "PENDING",
      "provider_name": "VODAFONE",
      "utility_name": "Recharge",
      "transaction_charges": "0",
      "transaction_amount": "10",
      "bill_response": "W10=",
      "transaction_id": "MAWEB0000001021"
    }
  }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "doRecharge": {
      "status": 400,
      "message": "Error: [~MA~] No data found",
      "respcode": 1028
    }
  }
}
*/
