// -------------- Get Kotak Term Plan List -----------------
/**
* @api {post} /insurance 1) getPolicyPlans
* @apiName getPolicyPlans
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{getPolicyPlans(ma_user_id:28479,userid:7556){status,respcode,message,policyPlans{policy_id,policy_code,policy_title,policy_desc,policy_amount,policy_provider,policy_duration,policy_duration_type}}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This is api use to get list of plans of Kotak term insurance</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Array} policyPlans  policy plan list.

* @apiSuccessExample {json} Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getPolicyPlans": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "policyPlans": [
                {
                    "policy_id": "1",
                    "policy_code": "KOTAK_1L_1Y",
                    "policy_title": "Term Insurance For Rupees 1 Lakh",
                    "policy_desc": "Nominee gets 1 lakhs in case of death of insured",
                    "policy_amount": "100000.00",
                    "policy_provider": "KOTAK",
                    "policy_duration": 1,
                    "policy_duration_type": "YEAR"
                },
                {
                    "policy_id": "2",
                    "policy_code": "KOTAK_2L_1Y",
                    "policy_title": "Term Insurance For Rupees 2 Lakh",
                    "policy_desc": "Nominee gets 2 lakhs in case of death of insured",
                    "policy_amount": "200000.00",
                    "policy_provider": "KOTAK",
                    "policy_duration": 1,
                    "policy_duration_type": "YEAR"
                },
                {
                    "policy_id": "3",
                    "policy_code": "KOTAK_50K_1M",
                    "policy_title": "Term Insurance For Rupees 50 Thousands",
                    "policy_desc": "Nominee gets 50 thousand in case of death of insured",
                    "policy_amount": "50000.00",
                    "policy_provider": "KOTAK",
                    "policy_duration": 1,
                    "policy_duration_type": "MONTHS"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample {json} No-Record-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getPolicyPlans": {
            "status": 400,
            "respcode": 1002,
            "message": "Fail: No Records policy plans",
            "policyPlans": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiError {String} message  API response message.

* @apiErrorExample {json} Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getPolicyPlans": {
            "status": 400,
            "respcode": 1001,
            "message": "Somthing went wrong",
            "policyPlans": null
        }
    }
}
*/

// -------------- Check Active Policy Exist for Mobile Number -----------------
/**
* @api {post} /insurance 2) checkActivePolicy
* @apiName checkActivePolicy
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{checkActivePolicy(mobile_number:\"9876543210\",policy_code:\"KOTAK_1L_1Y\",ma_user_id:28479,userid:7556){status,respcode,message,old_policy_holder,customer_details{ma_user_policy_detail_id,customer_salutation_desc,customer_first_name,customer_last_name,customer_gender,customer_dob,current_policy_expiry_date,next_policy_commence_date,customer_email_id,customer_address_one,customer_address_two,customer_state_code,customer_pincode,customer_nominee_list{nominee_first_name,nominee_last_name,nominee_dob,nominee_gender,nominee_relationship}},policy_state_data{state_name,state_code}}}"}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
This api used to get details of given mobile number against selected term plans, if details
already exist with active policy then it will return old policy details in response , else null
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Boolean} old_policy_holder  System having already one active policy or not (true/false).
* @apiSuccess {Object} customer_details If old_policy_holder param true then old customer detail will return in this object.
* @apiSuccess {Array} policy_state_data State List for policy

* @apiSuccessExample Success-Response-old-policy-holder:
HTTP/1.1 200 OK
{
    "data": {
        "checkActivePolicy": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "old_policy_holder": true,
            "customer_details": {
                "ma_user_policy_detail_id": "10",
                "customer_salutation_desc": "Mrs",
                "customer_first_name": "Sanjay",
                "customer_last_name": "Sinalkar",
                "customer_gender": "FEMALE",
                "customer_dob": "12-07-1989",
                "current_policy_expiry_date": "23-11-2020",
                "next_policy_commence_date": "19-11-2020",
                "customer_email_id": null,
                "customer_address_one": null,
                "customer_address_two": null,
                "customer_state_code": null,
                "customer_pincode": null,
                "customer_nominee_list": [
                    {
                        "nominee_first_name": "Sanjay",
                        "nominee_last_name": "Sinalkar",
                        "nominee_dob": "01-02-1991",
                        "nominee_gender": "MALE",
                        "nominee_relationship": "BROTHER"
                    }
                ]
            },
            "policy_state_data": [
                {
                    "state_name": "Andaman and Nicobar Islands",
                    "state_code": "AN"
                },
                {
                    "state_name": "Andhra Pradesh",
                    "state_code": "AP"
                },
                {
                    "state_name": "Andhra Pradesh (New)",
                    "state_code": "AD"
                },
                {
                    "state_name": "Arunachal Pradesh",
                    "state_code": "AR"
                },
                {
                    "state_name": "Assam",
                    "state_code": "AS"
                },
                {
                    "state_name": "Bihar",
                    "state_code": "BH"
                },
                {
                    "state_name": "Chandigarh",
                    "state_code": "CH"
                },
                {
                    "state_name": "Chhattisgarh",
                    "state_code": "CT"
                },
                {
                    "state_name": "Dadra and Nagar Haveli",
                    "state_code": "DN"
                },
                {
                    "state_name": "Daman and Diu",
                    "state_code": "DD"
                },
                {
                    "state_name": "Delhi",
                    "state_code": "DL"
                },
                {
                    "state_name": "Goa",
                    "state_code": "GA"
                },
                {
                    "state_name": "Gujarat",
                    "state_code": "GJ"
                },
                {
                    "state_name": "Haryana",
                    "state_code": "HR"
                },
                {
                    "state_name": "Himachal Pradesh",
                    "state_code": "HP"
                },
                {
                    "state_name": "Jammu and Kashmir",
                    "state_code": "JK"
                },
                {
                    "state_name": "Jharkhand",
                    "state_code": "JH"
                },
                {
                    "state_name": "Karnataka",
                    "state_code": "KA"
                },
                {
                    "state_name": "Kerala",
                    "state_code": "KL"
                },
                {
                    "state_name": "Lakshadweep",
                    "state_code": "LD"
                },
                {
                    "state_name": "Madhya Pradesh",
                    "state_code": "MP"
                },
                {
                    "state_name": "Maharashtra",
                    "state_code": "MH"
                },
                {
                    "state_name": "Manipur",
                    "state_code": "MN"
                },
                {
                    "state_name": "Meghalaya",
                    "state_code": "ME"
                },
                {
                    "state_name": "Mizoram",
                    "state_code": "MI"
                },
                {
                    "state_name": "Nagaland",
                    "state_code": "NL"
                },
                {
                    "state_name": "Odisha",
                    "state_code": "OR"
                },
                {
                    "state_name": "Puducherry",
                    "state_code": "PY"
                },
                {
                    "state_name": "Punjab",
                    "state_code": "PB"
                },
                {
                    "state_name": "Rajasthan",
                    "state_code": "RJ"
                },
                {
                    "state_name": "Sikkim",
                    "state_code": "SK"
                },
                {
                    "state_name": "Tamil Nadu",
                    "state_code": "TN"
                },
                {
                    "state_name": "Telangana",
                    "state_code": "TS"
                },
                {
                    "state_name": "Tripura",
                    "state_code": "TR"
                },
                {
                    "state_name": "Uttar Pradesh",
                    "state_code": "UP"
                },
                {
                    "state_name": "Uttarakhand",
                    "state_code": "UT"
                },
                {
                    "state_name": "West Bengal",
                    "state_code": "WB"
                }
            ]
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Boolean} old_policy_holder  System having already one active policy or not (true/false).
* @apiSuccess {Object} customer_details If old_policy_holder param true then old customer detail will return in this object.
* @apiSuccess {Array} policy_state_data State List for policy

* @apiSuccessExample {json} Success-Response-new-policy-holder:
HTTP/1.1 200 OK
{
    "data": {
        "checkActivePolicy": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "old_policy_holder": false,
            "customer_details": {
                "ma_user_policy_detail_id": null,
                "customer_salutation_desc": null,
                "customer_first_name": null,
                "customer_last_name": null,
                "customer_gender": null,
                "customer_dob": null,
                "current_policy_expiry_date": null,
                "next_policy_commence_date": null,
                "customer_email_id": null,
                "customer_address_one": null,
                "customer_address_two": null,
                "customer_state_code": null,
                "customer_pincode": null,
                "customer_nominee_list": null
            },
            "policy_state_data": [
                {
                    "state_name": "Andaman and Nicobar Islands",
                    "state_code": "AN"
                },
                {
                    "state_name": "Andhra Pradesh",
                    "state_code": "AP"
                },
                {
                    "state_name": "Andhra Pradesh (New)",
                    "state_code": "AD"
                },
                {
                    "state_name": "Arunachal Pradesh",
                    "state_code": "AR"
                },
                {
                    "state_name": "Assam",
                    "state_code": "AS"
                },
                {
                    "state_name": "Bihar",
                    "state_code": "BH"
                },
                {
                    "state_name": "Chandigarh",
                    "state_code": "CH"
                },
                {
                    "state_name": "Chhattisgarh",
                    "state_code": "CT"
                },
                {
                    "state_name": "Dadra and Nagar Haveli",
                    "state_code": "DN"
                },
                {
                    "state_name": "Daman and Diu",
                    "state_code": "DD"
                },
                {
                    "state_name": "Delhi",
                    "state_code": "DL"
                },
                {
                    "state_name": "Goa",
                    "state_code": "GA"
                },
                {
                    "state_name": "Gujarat",
                    "state_code": "GJ"
                },
                {
                    "state_name": "Haryana",
                    "state_code": "HR"
                },
                {
                    "state_name": "Himachal Pradesh",
                    "state_code": "HP"
                },
                {
                    "state_name": "Jammu and Kashmir",
                    "state_code": "JK"
                },
                {
                    "state_name": "Jharkhand",
                    "state_code": "JH"
                },
                {
                    "state_name": "Karnataka",
                    "state_code": "KA"
                },
                {
                    "state_name": "Kerala",
                    "state_code": "KL"
                },
                {
                    "state_name": "Lakshadweep",
                    "state_code": "LD"
                },
                {
                    "state_name": "Madhya Pradesh",
                    "state_code": "MP"
                },
                {
                    "state_name": "Maharashtra",
                    "state_code": "MH"
                },
                {
                    "state_name": "Manipur",
                    "state_code": "MN"
                },
                {
                    "state_name": "Meghalaya",
                    "state_code": "ME"
                },
                {
                    "state_name": "Mizoram",
                    "state_code": "MI"
                },
                {
                    "state_name": "Nagaland",
                    "state_code": "NL"
                },
                {
                    "state_name": "Odisha",
                    "state_code": "OR"
                },
                {
                    "state_name": "Puducherry",
                    "state_code": "PY"
                },
                {
                    "state_name": "Punjab",
                    "state_code": "PB"
                },
                {
                    "state_name": "Rajasthan",
                    "state_code": "RJ"
                },
                {
                    "state_name": "Sikkim",
                    "state_code": "SK"
                },
                {
                    "state_name": "Tamil Nadu",
                    "state_code": "TN"
                },
                {
                    "state_name": "Telangana",
                    "state_code": "TS"
                },
                {
                    "state_name": "Tripura",
                    "state_code": "TR"
                },
                {
                    "state_name": "Uttar Pradesh",
                    "state_code": "UP"
                },
                {
                    "state_name": "Uttarakhand",
                    "state_code": "UT"
                },
                {
                    "state_name": "West Bengal",
                    "state_code": "WB"
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Active-Insurance-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkActivePolicy": {
            "status": 400,
            "respcode": 1003,
            "message": "Fail: User has Active Insurance already",
            "old_policy_holder": null,
            "customer_details": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkActivePolicy": {
            "status": 400,
            "respcode": 1001,
            "message": "Something Went wrong",
            "old_policy_holder": null,
            "customer_details": null
        }
    }
}
*/

// -------------- Calculate Premium -----------------
/**
* @api {post} /insurance 3) calculatePremium
* @apiName calculatePremium
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParam {string} query.aggregator_order_id 20 characters required including suffix MAWEB/MAAPP.

* @apiParamExample {json} Request-Example:
{
    "query": "mutation{calculatePremium(mobile_number:\"9876543210\",policy_code:\"KOTAK_2L_1Y\",ma_user_id:28479,userid:7556,aggregator_order_id:\"MAWEB123455432112370\",customer_first_name:\"Sanjay\",customer_last_name:\"Sinalkar\",customer_gender:MALE,customer_dob:\"12-07-1989\",state_code:\"MH\"){status,respcode,message,total_premium,ma_user_policy_detail_id,aggregator_order_id,age}}"
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
This api used to get premium amount of selected plan against given user details with mobile number against selected state. This details will post to api call of Merchant Solution API (https://insurance.airpay.ninja/api/ins/create_order) to fetch premium amount and age against given customer dob
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Decimal} total_premium  Total premium  from MS.
* @apiSuccess {Integer} ma_user_policy_detail_id  Request id against given orderid.
* @apiSuccess {String} aggregator_order_id  Unique Order id.
* @apiSuccess {Integer} age calculated age from given dob from MS.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "calculatePremium": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "total_premium": "293.00",
            "ma_user_policy_detail_id": 61,
            "aggregator_order_id": "MAWEB123455432112370",
            "age": 31
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Invalid-Order-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "calculatePremium": {
            "status": 400,
            "respcode": 1001,
            "message": "invalid order format",
            "total_premium": null,
            "ma_user_policy_detail_id": null,
            "aggregator_order_id": null,
            "age": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "calculatePremium": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001,
      "total_premium": null,
      "ma_user_policy_detail_id": null,
      "aggregator_order_id": null,
      "age": null
    }
  }
}
*/

// -------------- Create Policy Transaction -----------------
/**
* @api {post} /insurance 4) doPolicyTransaction
* @apiName doPolicyTransaction
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{doPolicyTransaction(ma_user_policy_detail_id:61,mobile_number:\"9876543210\",policy_code:\"KOTAK_2L_1Y\",ma_user_id:28479,userid:7556,aggregator_order_id:\"MAWEB123455432112370\",,customer_first_name:\"Sanjay\",customer_last_name:\"Sinalkar\",customer_gender:MALE,customer_dob:\"12-07-1989\",state_code:\"MH\",customer_email_id:\"<EMAIL>\",address_one:\"Test Address 1\",address_two:\"Test Address 2 \",pincode:\"400708\",security_pin:\"0011\",nominee_list: [{nominee_first_name:\"Sanjay\",nominee_last_name:\"Sinalkar\",nominee_dob:\"01-02-1991\",nominee_gender:MALE,nominee_relationship:\"Father\"},{nominee_first_name:\"Sanjay\",nominee_last_name:\"Sinalkar\",nominee_dob:\"28-02-1991\",nominee_gender:FEMALE,nominee_relationship:\"SISTER\"}]){status,respcode,message,ma_user_policy_detail_id,dogh}}"
}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
This api will used to create transaction with selected plan against given user details, internally this api will call Merchant Solution api (https://insurance.airpay.ninja/api/ins/sendapp_data) with given details, if succeed then it will sent one otp sms to user mobile. Also 
send success response with policy details
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {Integer} ma_user_policy_detail_id  API response uq.
* @apiSuccess {String} dogh  API dogh document pdf url.

* @apiSuccessExample {json} Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "doPolicyTransaction": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "ma_user_policy_detail_id": 61,
            "dogh": "https://insurance.airpay.ninja/api/file/b87b27657e7f14bd2aa03aa3a39fb5e9.pdf"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Already-Exist-Order-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "doPolicyTransaction": {
            "status": 400,
            "respcode": 1018,
            "message": "Fail: Previous transaction [MAEB4222291589532739] already in process",
            "ma_user_policy_detail_id": null,
            "dogh": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "doPolicyTransaction": {
            "status": 400,
            "respcode": 1001,
            "message": "Something Went wrong",
            "ma_user_policy_detail_id": null,
            "dogh": null
        }
    }
}
*/

// -------------- Verify Kotak OTP -----------------
/**
* @api {post} /insurance 5) verifyKotakOtp
* @apiName verifyKotakOtp
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{verifyKotakOtp(mobile_number:\"9664332419\",otp:\"716213\",ma_user_id:28479,userid:7556,aggregator_order_id:\"MAEB4222291589532740\",ma_user_policy_detail_id:55){status,respcode,message,coi}}"
}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api will call merchant solution api(https://insurance.airpay.ninja/api/ins/otp_check) internally with given otp details which has received , if succeed then response gives
pdf files direct links in response and also sent policy document short url on customer mobile 
number
on user mobile from kotak</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} coi Certificate Of Insurance PDF URL.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyKotakOtp": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "coi": "https://insurance.airpay.ninja/api/file/408c9dea8493dd9c2e87e362075d6371.pdf"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Same-Reference-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyKotakOtp": {
            "status": 400,
            "respcode": 1001,
            "message": "Policy is already insured for this reference id"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyKotakOtp": {
      "status": 400,
      "message": "Something Went wrong",
      "respcode": 1001
    }
  }
}
*/

// -------------- Resent Kotak OTP -----------------
/**
* @api {post} /insurance 6) resentKotakOtp
* @apiName resentKotakOtp
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{resentKotakOtp(mobile_number:\"9664332419\",ma_user_id:28479,userid:7556,aggregator_order_id:\"MAEB4222291589532740\",ma_user_policy_detail_id:55){status,respcode,message}}"
}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to resent kotak otp if verify otp expires, this api internally call merchant solution api(https://insurance.airpay.ninja/api/ins/resendotp_send), if succeed then return 200 else 400 , same response return to response </code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentKotakOtp": {
            "status": 200,
            "respcode": 1000,
            "message": "OTP has been sent successfully"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentKotakOtp": {
            "status": 400,
            "respcode": 1011,
            "message": "Fail: Mobile number not valid as"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} No-Record-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentKotakOtp": {
            "status": 400,
            "respcode": 1002,
            "message": "Fail: No Records policy request"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentKotakOtp": {
            "status": 400,
            "respcode": 1001,
            "message": "Something went wrong"
        }
    }
}
*/

// -------------- Resent Kotak Policy Document -----------------
/**
* @api {post} /insurance 7) resentSuccessSMS
* @apiName resentSuccessSMS
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "mutation{resentSuccessSMS(mobile_number:\"9664332419\",ma_user_id:28479,userid:7556,aggregator_order_id:\"MAEB4222291589532737\",ma_user_policy_detail_id:52){status,respcode,message}}"
}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to resent kotak issued document sms to customer mobile, this will internally call merchant solution api (https://insurance.airpay.ninja/api/ins/otp_sendsms), if policy issued then it return sucess as well it trigger message to customer regardling policy document </code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentSuccessSMS": {
            "status": 200,
            "respcode": 1000,
            "message": "success"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} No-Record-Response-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentSuccessSMS": {
            "status": 400,
            "respcode": 1002,
            "message": "Fail: No Records policy request"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} Process-not-Completed-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentSuccessSMS": {
            "status": 400,
            "respcode": 1001,
            "message": "Process not completed"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "resentSuccessSMS": {
            "status": 400,
            "respcode": 1001,
            "message": "Something went wrong"
        }
    }
}
*/

// -------------- Receipt Kotak Policy API -----------------
/**
* @api {post} /transaction 8) getInsuranceTransactionDetails
* @apiName getInsuranceTransactionDetails
* @apiGroup INSURANCE

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    "query": "{getInsuranceTransactionDetails(ma_transaction_master_id : 5360,ma_user_id:28479,userid:7556){status,respcode,message,transaction_status,coi}}"
}
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This api is used to get successful policy issued response receipt </code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getInsuranceTransactionDetails": {
            "status": 200,
            "respcode": 1000,
            "message": "Success",
            "transaction_status": "SUCCESS",
            "coi": "https://insurance.airpay.ninja/api/file/8544bbbb43491bf1bfedfee587d777b3.pdf"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample {json} No-Record-Response-Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getInsuranceTransactionDetails": {
            "status": 400,
            "respcode": 1002,
            "message": "Receipt not found",
            "transaction_status": null,
            "coi": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getInsuranceTransactionDetails": {
            "status": 400,
            "respcode": 1001,
            "message": "Something went wrong",
            "transaction_status": null,
            "coi": null
        }
    }
}
*/
