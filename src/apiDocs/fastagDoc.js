
// -------------- Customer Onboard -ADD-----------------
// -------------- Request OTP -----------------
/**
* @api {post} /Fastag 1) Request Otp
* @apiName requestOtp
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    requestOTP(
      mobile_number:"**********",
      custid:"",
      ma_user_id:28776,
      userid:1460
      ) {
         status,
         message,
         respcode,
         txn_number,
         request_number
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Request Otp for cutomer-onboard</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "requestOTP": {
            "status": 200,
            "message": "Success: OTP sent successfully",
            "respcode": 2007,
            "txn_number": "18189",
            "request_number": "25351614577477484"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "requestOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "txn_number": null,
      "request_number": null
    }
  }
}
*/

// -------------- Verify OTP -----------------
/**
* @api {post} /Fastag 2) Verify OTP
* @apiName verifyOtp
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation{
    verifyOTP(
      otp:"902622",
      txn_number: "17736",
      request_number:"25351614577477484",
      ma_user_id:28776,
      userid:1460
    ){
       status,
       message,
       respcode
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for verify otp with same request_number</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyOTP": {
            "status": 200,
            "message": "Success",
            "respcode": 1000
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// -------------- Customer Onboard -----------------
/**
* @api {post} /Fastag 3) Customer Onboard - ADD
* @apiName customer
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation{
    customerOnboard(
      ma_user_id:28776,
      userid:1460,
      requestId:"25351614577477484",
      customerId:"",
      action:"ADD",
      customerType:"KYC",
      firstName:"Libin",
      middleName:"",
      lastName:"KK",
      gender:"Male",
      dob:"01/02/1992",
      gstNo:"",
      mobileNo:"**********",
      phoneNo:"",
      emailId:"<EMAIL>",
      usrConsent:"Y",
      address1:"Kochi",
      address2:"Kerala",
      address3:"TESTADD",
      district:"Ernakulam",
      city:"Kochi",
      country:"india",
      state:"kerala",
      pincode:"123456",
      uploadType:"CU",
      docType:"",
      docName:"",
      docNo:"",
      uploadFile:""
      ){
        status,
        respcode,
        message,
        customers{
          ma_ft_customer_id,
          first_name,
          dob,
          pincode,
          mobile_number,
          email
        }
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Add Customer-onboard (customer_type - KYC)</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerOnboard": {
            "status": "200",
            "respcode": "1000",
            "message": "Success",
            "customers": [
                {
                    "ma_ft_customer_id": 208430,
                    "first_name": "Libin",
                    "dob": "01/02/1992",
                    "pincode": "123456",
                    "mobile_number": "**********",
                    "email": "<EMAIL>"
                }
            ]
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "customers": null

    }
  }
}
*/

/** */

// -------------- Customer Onboard -UPDATE-----------------
// -------------- Request OTP for Customer- Update-----------------
/**
* @api {post} /Fastag 4) Request Otp for Customer - Update
* @apiName requestOtp - Update Customer
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    requestOTP(
      mobile_number:"**********",
      custid:"208430",
      ma_user_id:28776,
      userid:1460
      ) {
         status,
         message,
         respcode,
         txn_number,
         request_number
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Request Otp for cutomer-onboard</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "requestOTP": {
            "status": 200,
            "message": "Success: OTP sent successfully",
            "respcode": 2007,
            "txn_number": "18189",
            "request_number": "25351614577477484"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "requestOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "txn_number": null,
      "request_number": null
    }
  }
}
*/

// -------------- Verify OTP for Customer- Update -----------------
/**
* @api {post} /Fastag 5) Verify OTP for Customer - Update
* @apiName verifyOtp- Update Customer
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation{
    verifyOTP(
      otp:"902622",
      txn_number: "17736",
      request_number:"25351614577477484",
      ma_user_id:28776,
      userid:1460
    ){
       status,
       message,
       respcode
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for verify otp with same request_number</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "verifyOTP": {
            "status": 200,
            "message": "Success",
            "respcode": 1000
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong"
    }
  }
}
*/

// -------------- Customer Onboard -----------------
/**
* @api {post} /Fastag 6) Customer Onboard - UPDATE
* @apiName Update Customer
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Verify and Complete transaction:
  mutation{
    customerOnboard(
      ma_user_id:28776,
      userid:1460,
      requestId:"25351614577477484",
      customerId:"208430",
      action:"UPDATE",
      customerType:"KYC",
      firstName:"Libin",
      middleName:"",
      lastName:"KK",
      gender:"Male",
      dob:"01/02/1992",
      gstNo:"",
      mobileNo:"**********",
      phoneNo:"",
      emailId:"<EMAIL>",
      usrConsent:"Y",
      address1:"Kochi",
      address2:"Kerala",
      address3:"TESTUPDATE",
      district:"Ernakulam",
      city:"Kochi",
      country:"india",
      state:"kerala",
      pincode:"123456",
      uploadType:"CU",
      docType:"",
      docName:"",
      docNo:"",
      uploadFile:""
      ){
        status,
        respcode,
        message,
        customers{
          ma_ft_customer_id,
          first_name,
          dob,
          pincode,
          mobile_number,
          email
        }
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Update Customer-onboard (customer_type - KYC)</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "customerOnboard": {
            "status": "200",
            "respcode": "1000",
            "message": "Success",
            "customers": [
                {
                    "ma_ft_customer_id": 208430,
                    "first_name": "Libin",
                    "dob": "01/02/1992",
                    "pincode": "123456",
                    "mobile_number": "**********",
                    "email": "<EMAIL>"
                }
            ]
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "customerOnboard": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "customers": null

    }
  }
}
*/

// -------------- Check Customer -----------------
/**
* @api {post} /Fastag 7) check Customer
* @apiName checkCustomer
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  checkCustomer(ma_user_id:28776,
    userid:1460,
    vrn:"kl341234",
    mobileNo:"**********"
    ){
      status,
      message,
      respcode,
      customers{
        first_name,
        middle_name,
        last_name,
        gender,
        dob,
        ma_ft_customer_id,
        email,
        address_1,
        pincode,
        address_2,
        state,
        customer_type
      },
      request_number,
      txn_number
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for check the customer is onboarded or not. If customer is not onboarded then call the RenquestOtp api</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkCustomer": {
            "status": "200",
            "message": "Customer Already Exist",
            "respcode": "1182",
            "customers": [
                {
                    "first_name": "Libin",
                    "middle_name": "",
                    "last_name": "KK",
                    "gender": "Male",
                    "dob": "01/02/1992",
                    "ma_ft_customer_id": 208430,
                    "email": "<EMAIL>",
                    "address_1": "Kochi",
                    "pincode": "123456",
                    "address_2": "Kerala",
                    "state": "kerala",
                    "customer_type": "NKYC"
                }
            ],
            "request_number": null,
            "txn_number": null
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "checkCustomer": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "customers": null,
      "request_number": null,
      "txn_number": null

    }
  }
}
*/

// -------------- Check Tag Status -----------------
/**
* @api {post} /Fastag 8) Check Tag Status
* @apiName checkTagStatus
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  checkTagStatus(
    ma_user_id:28776,
    userid:1460,
    vrn:"MK98AS09876k"
    ){
      status,
      message,
      respcode,
      reg_date,
      ma_ft_customer_master_id,
      cust_id,
      mobile_number,
      vehicle_status,
      available_bal,
      vrn,
      tagId,
      veh_class,
      veh_desc,
      customer_type,
      cust_id,
      customer_name
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for check the tag is activated for the recharge</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "checkTagStatus":{
      "status":"200",
      "message":"Success",
      "respcode":"1000",
      "reg_date":"0000-00-00",
      "ma_ft_customer_master_id":"196661",
      "cust_id":"196661",
      "mobile_number":"**********",
      "vehicle_status":"Activated",
      "available_bal":"110",
      "vrn":"MK98AS09876",
      "tagId":"34161FA820327FA404CC5520",
      "veh_class":"VC4",
      "veh_desc":null,
      "customer_type":"NKYC",
      "customer_name":"Prajaktab"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "checkTagStatus": {
            "status": "400",
            "message": "Tag not issued against VRN",
            "respcode": "1186",
            "reg_date": null,
            "ma_ft_customer_master_id": null,
            "cust_id": null,
            "mobile_number": null,
            "vehicle_status": null,
            "available_bal": null,
            "vrn": null,
            "tagId": null,
            "veh_class": null,
            "veh_desc": null,
            "customer_type": null,
            "customer_name": null
        }
    }
}
*/

// -------------- fastag Recharge -----------------
/**
* @api {post} /Fastag 9) Recharge
* @apiName fastagRecharge
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    fastagRecharge(
      ma_user_id:28479,
      userid: 7556,
      ma_ft_customer_master_id:303,
      aggregator_order_id:"MAWEB63652311147488",
      tagid:"34161FA820327FA404CC5900",
      amount:"1000",
      mobile_number:"**********",
      vrn_number:"APITEST987",
      security_pin:"123456"
      ){
        status,
        message,
        respcode,
        customer_name,
        customer_mobile,
        bank_name,
        aggregator_order_id,
        initial_bal,
        tagId,
        transaction_time,
        transaction_reason,
        transaction_status
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for reacharge actiavated tag</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "fastagRecharge": {
    "status": 200,
    "message": "Success",
    "respcode": 1000,
    "customer_name": "Libin Kurian",
    "customer_mobile": "**********",
    "bank_name": "IDFC FIRST BANK",
    "aggregator_order_id": "MAWEB63652311147488",
    "initial_bal": "1000.00",
    "tagId": "34161FA820327FA404CC5900",
    "transaction_time": "26-02-2021 01:51:30",
    "transaction_reason": "Success",
    "transaction_status": "SUCCESS"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "fastagRecharge": {
    "status": 200,
    "message": "Success",
    "respcode": 1000,
    "customer_name": "Libin Kurian",
    "customer_mobile": "**********",
    "bank_name": "IDFC FIRST BANK",
    "aggregator_order_id": "MAWEB63652311147488",
    "initial_bal": "1000.00",
    "tagId": "34161FA820327FA404CC5900",
    "transaction_time": "26-02-2021 01:51:30",
    "transaction_reason": "Failure",
    "transaction_status": "FAILURE"
    }
  }
}
*/
// -------------- Get Fastag Recharge Receipt Details -----------------
/**
* @api {post} /Fastag 10) Get Fastag Recharge Receipt Details
* @apiName getFastagRechargeReceiptDetails
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  getFastagRechargeReceiptDetails(
    ma_user_id:28479,
    userid:7556,
    ma_transaction_master_id:5543
    ){
      status,
      message,
      respcode,
      customer_name,
      customer_mobile,
      bank_name,
      aggregator_order_id,
      initial_bal,
      tagId,
      transaction_time,
      transaction_reason,
      transaction_status
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for get reacharge details</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getFastagRechargeReceiptDetails": {
    "status": 200,
    "message": "Success",
    "respcode": 1000,
    "customer_name": "Libin Kurian",
    "customer_mobile": "**********",
    "bank_name": "IDFC FIRST BANK",
    "aggregator_order_id": "MAWEB63652311147488",
    "initial_bal": "1000.00",
    "tagId": "34161FA820327FA404CC5900",
    "transaction_time": "26-02-2021 01:51:30",
    "transaction_reason": "Success",
    "transaction_status": "SUCCESS"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getFastagRechargeReceiptDetails": {
    "status": 200,
    "message": "Success",
    "respcode": 1000,
    "customer_name": "Libin Kurian",
    "customer_mobile": "**********",
    "bank_name": "IDFC FIRST BANK",
    "aggregator_order_id": "MAWEB63652311147488",
    "initial_bal": "1000.00",
    "tagId": "34161FA820327FA404CC5900",
    "transaction_time": "26-02-2021 01:51:30",
    "transaction_reason": "Failure",
    "transaction_status": "FAILURE"
    }
  }
}
*/
// -------------- Issue Tag -----------------
/**
* @api {post} /Fastag 11) Issue Tag
* @apiName issueTag
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation{
    issueTag(
      ma_user_id:28619,
      userid:1144,
      custid:196661,
      ma_ft_customer_master_id:196661,
      vrn_number:"MH04AT1002",
      request_number:"73411613401069676",
      aggregator_order_id:"MAWEB78791613401106",
      product:"880000000002",
      tvc:"4",
      cch:"VC4",
      tagid:"34161FA820328EE80B916800",
      barcode:"100002",
      chasis_number:"",
      issue_amount:"100.00",
      security_amount:"200.00",
      min_balance:"109.00",
      vehicle_category:RETAIL,
      vehicle_make_model:"",
      vehicle_color:"",
      reg_date:"02/02/2021",
      mobile_number:"**********",
      docNo:"7686876",
      attachment:"cc6b18bbae1cc7266dcdfc2d23d76ceebd57e023/****************.pdf",
      uploadid:464,
      engineNo:"6786876",
      security_pin:"3434"
      ){
        status,
        message,
        respcode,
        customer_name,
        customer_mobile,
        bank_name,
        aggregator_order_id,
        vrn,
        issue_fee,
        chasis_no,
        security_fee,
        min_bal,
        initial_bal,
        tagId,
        transaction_time,
        transaction_reason,
        cch,
        product_name,
        transaction_status
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Issue Tag</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "issueTag":{
      "status":200,
      "message":"Success",
      "respcode":1000,
      "customer_name":null,
      "customer_mobile":null,
      "bank_name":"IDFC FIRST BANK",
      "aggregator_order_id":"MAWEB78791613401106",
      "vrn":"MH04AT1002",
      "issue_fee":"100.00",
      "chasis_no":"",
      "security_fee":"200.00",
      "min_bal":"109.00",
      "initial_bal":"409.00",
      "tagId":"34161FA820328EE80B916800",
      "transaction_time":"15-02-2021 08:29:44",
      "transaction_reason":"",
      "cch":"VC4",
      "product_name":"Normal KYC Car/Jeep/Van/Mini Light Commercial Vehicle",
      "transaction_status":"SUCCESS"
    }
  }
}
* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOtp": {
      "status": 400,
      "respcode": 1001,
      "message": "Fail: Something went wrong",
      "customer_name":null,
      "customer_mobile":null,
      "bank_name":null,
      "aggregator_order_id":null,
      "vrn":null,
      "issue_fee":null,
      "chasis_no":null,
      "security_fee":null,
      "min_bal":null,
      "initial_bal":null,
      "tagId":null,
      "transaction_time":null,
      "transaction_reason":null,
      "cch":null,
      "product_name":null,
      "transaction_status":"FAILURE"

    }
  }
}
*/

// -------------- fetch Txn Status Cron -----------------
/**
* @api {post} /Fastag 12) Fetch transaction status
* @apiName fetchTxnStatusCron
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
 mutation{
   fetchTxnStatusCron(
     agencyId:"62740",
     txnId:"78401613109097542"
     ){
       status,
        message,
        respcode
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is running cron for activate the pending fastag transaction and activate vehicle.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "fetchTxnStatusCron": {
            "status": "200",
            "message": "Success",
            "respcode": "1000"
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "fetchTxnStatusCron": {
            "status": "400",
            "message": "Fail: Service Not Available",
            "respcode": "1147"
        }
    }
}
*/

// -------------- Update Customer Kyc -----------------
/**
* @api {post} /fasttagcustomer 13) Update Customer Kyc
* @apiName updateCustomerKyc
* @apiGroup FASTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwdWJsaWNrZXkiOjEyNDksInByb2ZpbGVpZCI6Mjg2NDEsImlhdCI6MTYyMDYyOTg0MSwiZXhwIjoxNjIwNzE2MjQxfQ.dku2t2d9Qm72SNwAz9mjn2ITtUnhUAeg5CiGVeU9Cmg"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  mutation {
      updateCustomerKyc
      (
        ma_user_id:28641,
        userid:1249,
        requestId:"*****************",
        customerId:"206740",
        customerType:"KYC",
        firstName:"vipul",
        gender:"Male",
        dob:"17/02/2003",
        gstNo:"",
        mobileNo:"9833424669",
        emailId:"<EMAIL>",
        usrConsent:"Y",
        address1:"malad",
        district:"Ernakulam",
        pincode:"400097",
        docType:"IDN",
        docName:"Aadhaar Card",
        docNo:"************",
        uploadFile:"0e821a76c8b42b1fc2bae01a07865ac570268864/2047321615374594.pdf"
      )
      {
        status, respcode, message
      }
    }
* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This Api is used for Update Customer KYC</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode  API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":
    {
      "updateCustomerKyc":
        {
          "status":"200","respcode":"1000","message":"Success"
        }
    }
}
* @apiError {String} status API status code.
* @apiError {String} message  API response code.
* @apiError  {String} respcode  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "data":
  {
    "updateCustomerKyc":
      {
        "status":"400","respcode":"1001","message":"Fail: Something went wrong"
      }
  }
}
*/