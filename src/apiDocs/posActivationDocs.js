// -------------- POS ACTIVATION API-----------------

/***
* @api {post} /posactivation 1) Check Pos Serial No.
* @apiName checkSerialNo
* @apiGroup POS ACTIVATION API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{checkSerialNo(ma_user_id:28479,userid:7556,serial_no:"123456"){status,respcode,message,device_type,serial_no}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used check Serial No.</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} device_type  API device type name.
* @apiSuccess {String} serial_no  API serial no.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"checkSerialNo": {"status": 200,"message": "Device Already Activated","respcode": 1000,"device_type": "mob","serial_no": "123456","device_status": null}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} orderid  API order id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"checkSerialNo": {"status": 400,"message": "Fail: Invalid Serial No.","respcode": 1028,"device_type": null,"serial_no": null,"device_status": null}}}
*/

/**
* @api {post} /posactivation 2) Get Pos Detail For Merchant
* @apiName getPosDetailForMerchant
* @apiGroup POS ACTIVATION API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"{getPosDetailForMerchant(userid:2294,ma_user_id:29253){status,respcode,message,posMerchantDetails{device_type,pos_serial_no,airpay_mid,airpay_tid,activation_date,enable_status}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to all pos device details of merchant</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} posMerchantDetails API pos details.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
<<<<<<< HEAD
{"data": {"getPosDetailForMerchant": {"status": 200,"message": "success","respcode": 1000,"posMerchantDetails": [{"device_type": "mob","pos_serial_no": "123456","airpay_mid": "100046","airpay_tid": "173992","activation_date": "27-05-2021 08:32:23","enable_status": "false"}, { "device_type": "VM30", "pos_serial_no": "1491154822", "airpay_mid": "29253", "airpay_tid": "62605998", "activation_date": "07-11-2022 11:17:26", "enable_status": "true"}]}}}
=======
{"data": {"getPosDetailForMerchant": {"status": 200,"message": "success","respcode": 1000,"posMerchantDetails": [{"device_type": "mob","pos_serial_no": "123456","airpay_mid": "100046","airpay_tid": "173992","activation_date": "27-05-2021 08:32:23",pos_enable: "true"}]}}}
>>>>>>> 5442ead609a323a56f87e0fa372f2ca6ba8f473d

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} posMerchantDetails  API pos details.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getPosDetailForMerchant": {"status": 400,"message": "Fail: Invalid Merchant Id.","respcode": 1028,"device_type": null,"serial_no": null,"posMerchantDetails": null}}}
*/

/**
* @api {post} /posactivation 3) Activate POS Device
* @apiName posActivate
* @apiGroup POS ACTIVATION API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{posActivate(ma_user_id:100046,userid:2386,serial_no:\"123456\"){status,message,respcode,terminalid,device_type,serial_no,merchant_id,file_path,steps_description,pos_enable}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to activate POS device</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} terminalid  API terminal id.
* @apiSuccess {String} device_type  API device type.
* @apiSuccess {String} serial_no  API device serial no.
* @apiSuccess {String} merchant_id  API ma user id.
* @apiSuccess {String} file_path  API download file path.
* @apiSuccess {String} steps_description  API steps to activate pos.
* @apiSuccess {String} pos_enable  API steps to enable pos via App.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data": {"posActivate": {"status": 200,"message": "success","respcode": 1000,"terminalid":"1234","device_type": "A200","serial_no": "123456","merchant_id":29138,"file_path":"**********.pdf","steps_description":"Step 1: Open settings.|Step 2: Enter your merchant ID: <mid>|Step 3: Enter Terminal ID: <tid>|Step 4: Please Activate to activate your POS device" , pos_enable: "true"}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} terminalid  API terminal id.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"posActivate": {"status": 108,"message": "Fail: Authentication/Authorization Failed - Merchant Key Authentication Failed. ","respcode": 108,"terminalid": null}}}
*/

/**
* @api {post} /posactivation 4) Get Device Manual For Download
* @apiName getDeviceTypeManual
* @apiGroup POS ACTIVATION API

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{getDeviceTypeManual(ma_user_id:100046,userid:2386){status,message,respcode,deviceManualUrl{device_type,file_path}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to download Device Type Manual</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} device_type  API device type.
* @apiSuccess {String} file_path  API download file path.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"getDeviceTypeManual":{"status":200,"message":"success","respcode":1000,"deviceManualUrl":[{"device_type":"D200","file_path":"**********.pdf"},{"device_type":"A910","file_path":"https://retailappdocs.s3.ap-south-1.amazonaws.com/bank_logos/1620644331_flowerPink.jpg"}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} device type  API device type.
* @apiError {String} file path  API download file path.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"getDeviceTypeManual": {"status": 408,"message": "Fail: No records found ","respcode": 108,"deviceManualUrl": null}}}
*/

/**
* @api {post} /posactivation 4) Enable POs from App
* @apiName enablePos
* @apiGroup POS ENABLE API - APP

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{"query":"mutation{enablePos(ma_user_id:100046,userid:2386, ,serial_no:"123456",isMobile: true){status,message,respcode}}}"}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>This API is used to enable POS via App</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{"data":{"enablePos":{"status":200,"message":"success","respcode":1000}]}}}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{"data": {"enablePos": {"status": 200,"message": "Fail: No records found ","respcode": 1000}}}
*/
