// -------------- Get Insurance List -----------------

/**
* @api {post} /bbpsinsurance 1) Get Insurance Provider List
* @apiName getInsuranceProviders
* @apiGroup LIC Insurance

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    getInsuranceProviders(ma_user_id:18999,userid:5912){
        status,message,respcode,providers {provider_id,provider_name, logo_url, fields {label,value,type,validation{max,min,regex},isEditable,isVisible,postKey,isRequired,subLabel}}
    }
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
    "query": "{getInsuranceProviders(ma_user_id:18999,userid:5912){status,message,respcode,providers {provider_id,provider_name, logo_url, fields {label,value,type,validation{max,min,regex},isEditable,isVisible,postKey,isRequired,subLabel}}}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} providers  insurance providers array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getInsuranceProviders": {
      "status": 200,
      "message": "Success",
      "respcode": 1000,
      "providers": [
                {
                    "provider_id": 201,
                    "provider_name": "LIC",
                    "logo_url": "https://5.imimg.com/data5/JO/WG/MY-10234721/life-insurance-agent-lic-500x500.jpg",
                    "fields": [
                        {
                            "label": "Policy Number",
                            "value": "",
                            "type": "Number",
                            "validation": {
                                "max": 20,
                                "min": 10,
                                "regex": "^[0-9]{1,20}$"
                            },
                            "isEditable": true,
                            "isVisible": true,
                            "postKey": "policy_number",
                            "isRequired": true,
                            "subLabel": ""
                        },
                        {
                            "label": "Mobile Number",
                            "value": "",
                            "type": "Number",
                            "validation": {
                                "max": 10,
                                "min": 10,
                                "regex": "^[0-9]{1,10}$"
                            },
                            "isEditable": true,
                            "isVisible": true,
                            "postKey": "mobile_number",
                            "isRequired": true,
                            "subLabel": ""
                        }
                        {
                            "label": "Customer Email",
                            "value": "",
                            "type": "String",
                            "validation": {
                                "max": 50,
                                "min": 10,
                                "regex": "^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$"
                            },
                            "isEditable": true,
                            "isVisible": true,
                            "postKey": "email",
                            "isRequired": true,
                            "subLabel": ""
                        }
                    ]
                }
            ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} providers  Insurance Providers array.
* @apiError {String} message  API response message.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "getInsuranceProviders": {
            "status": 400,
            "message": "Fail: Something Went wrong",
            "respcode": 1001,
            "providers": null
        }
    }
}
*/

// -------------- Get Insurance Details -----------------
/**
* @api {post} /bbpsinsurance 2) Get Insurance Details
* @apiName fetchInsuranceDetails
* @apiGroup LIC Insurance

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
{
    fetchInsuranceDetails(ma_user_id:18999,userid:5912,provider_id: 201,form_data:\"************************************************************************************\", order_id: \"MAWEB00192910212\"){
        status,message,respcode,insuranceDetails {label,value,type,validation{max,min,regex},isEditable,isVisible,postKey,isRequired,subLabel,placeHolder}
    }
}

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
    "query": "{
        fetchInsuranceDetails(ma_user_id:18999,userid:5912,provider_id: 201,form_data:\"************************************************************************************\", order_id: \"MAWEB00192910212\"){
            status,message,respcode,insuranceDetails {label,value,type,validation{max,min,regex},isEditable,isVisible,postKey,isRequired,subLabel,placeHolder}
        }
    }"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} insuranceDetails API insuranceDetails array.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "fetchInsuranceDetails": {
            "status": 200,
            "message": "Success",
            "respcode": 1000,
            "insuranceDetails": [
                {
                    "label": "Customer Name",
                    "value": "",
                    "type": "String",
                    "validation": {
                        "max": 30,
                        "min": 1,
                        "regex": "^[a-zA-Z ]{1,30}$"
                    },
                    "isEditable": false,
                    "isVisible": true,
                    "postKey": "customer_name",
                    "isRequired": true,
                    "subLabel": "Ra******* La***",
                    "placeHolder": "Ra******* La***"
                },
                {
                    "label": "Net Amount",
                    "value": "6239.46",
                    "type": "Number",
                    "validation": {
                        "max": 10,
                        "min": 1,
                        "regex": "^[0-9.]{1,10}$"
                    },
                    "isEditable": false,
                    "isVisible": true,
                    "postKey": "net_amount",
                    "isRequired": true,
                    "subLabel": "",
                    "placeHolder": null
                },
                {
                    "label": "Due Date - From To",
                    "value": "12/09/2021 - 12/09/2021",
                    "type": "String",
                    "validation": {
                        "max": 30,
                        "min": 1,
                        "regex": "^[0-9/- ]{1,30}$"
                    },
                    "isEditable": false,
                    "isVisible": true,
                    "postKey": "duedatefromto",
                    "isRequired": true,
                    "subLabel": "",
                    "placeHolder": null
                }
            ]
        }
    }
}

* @apiError {String} status API status code.
* @apiError {String} respcode API response code.
* @apiError {String} message  API response message.
* @apiError {String} insuranceDetails API insuranceDetails array.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
    "data": {
        "fetchInsuranceDetails": {
            "status": 400,
            "message": "Error::: No data found",
            "respcode": 1028,
            "insuranceDetails": null
        }
    }
}
*/

// -------------- Do Payment -----------------
/**
* @api {post} /bbpsinsurance 3) Do Payment
* @apiName doInsurancePayment
* @apiGroup LIC Insurance

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} app_version App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "app_version" : "1.45"
}

* @apiParam {String} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:

  mutation {
    doInsurancePayment(
        ma_user_id:28641,
        userid:1249,
        pin:"1515",
        aggregator_order_id: "MAWEB98221636962831",
        provider_id: 201,
        form_data: "********************************************************************************************************************************************************************************************************************************************"
        ){
            status,
            message,
            respcode,
            customer_mobile,
            customer_name,
            amount,
            aggregator_order_id,
            merchant_name,
            merchant_mobile,
            transaction_time,
            transaction_status,
            provider_name,
            utility_name,
            transaction_charges,
            transaction_amount,
            bill_response,
            transaction_id,
            logo_url
        }
    }

* @apiDescription <span class="type" style="background-color: #e535ab">Description</span>
<pre><code>
{
    "query": "mutation{doInsurancePayment(ma_user_id:28641,userid:1249, pin:\"1515\", aggregator_order_id: \"MAWEB98221636962831\",provider_id: 201,form_data: \"********************************************************************************************************************************************************************************************************************************************\" ){status,message,respcode, customer_mobile, customer_name, amount, aggregator_order_id, merchant_name, merchant_mobile, transaction_time, transaction_status, provider_name, utility_name, transaction_charges, transaction_amount, bill_response, transaction_id}}"
}
</code></pre>

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
    "data": {
        "doInsurancePayment": {
            "status":200,
            "message":"Success",
            "respcode":1000,
            "customer_mobile":"**********",
            "customer_name":"Deepak Gupta",
            "amount":"6125.00",
            "aggregator_order_id":"MAWEB98221636962831",
            "merchant_name":"Backend3  retailer",
            "merchant_mobile":"**********",
            "transaction_time":"26-11-2021 12:09:49",
            "transaction_status":"SUCCESS",
            "provider_name":"LIC",
            "utility_name":"LIC Insurance",
            "transaction_charges":"0",
            "transaction_amount":"6125",
            "bill_response":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            "transaction_id":"MAWEB98221636962831",
            "logo_url": "https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png"
        }
    }
}

* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.
* @apiErrorExample Error-Response: Transaction details not available
HTTP/1.1 200 OK
{
    "data": {
        "doInsurancePayment": {
            "status": 200,
            "message": "Fail : Transaction details not available",
            "respcode": 1028,
            "customer_name": null,
            "customer_mobile": "**********",
            "amount": "4500.00",
            "aggregator_order_id": "MAWEB54561635759619",
            "merchant_name": "AirpayRT6  ",
            "merchant_mobile": "**********",
            "transaction_time": "15-11-2021 02:58:08",
            "transaction_status": "INITIATED",
            "provider_name": "LIC",
            "utility_name": "LIC Insurance",
            "transaction_charges": "0",
            "transaction_amount": "4500",
            "bill_response": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            "transaction_id": "MAWEB54561635759619",
            "logo_url": "https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png"
        }
    }
}
* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.
* @apiErrorExample Error-Response: Previous transaction already in process
HTTP/1.1 200 OK
{
    "data": {
        "doInsurancePayment": {
            "status": 400,
            "message": "Fail: Previous transaction already in process",
            "respcode": 1133,
            "customer_name": null,
            "customer_mobile": null,
            "amount": null,
            "aggregator_order_id": null,
            "merchant_name": null,
            "merchant_mobile": null,
            "transaction_time": null,
            "transaction_status": null,
            "provider_name": null,
            "utility_name": null,
            "transaction_charges": null,
            "transaction_amount": null,
            "bill_response": null,
            "transaction_id": null,
            "logo_url": null
        }
    }
}
* @apiSuccess {String} status API status code.
* @apiSuccess {String} respcode API response code.
* @apiSuccess {String} message  API response message.
* @apiSuccess {String} data  User Details Object.
* @apiErrorExample Error-Response: Error in point bank transaction
HTTP/1.1 200 OK
{
    "data": {
        "doInsurancePayment": {
            "status": 200,
            "message": "Fail : Error in point bank transaction",
            "respcode": 1028,
            "customer_name": null,
            "customer_mobile": "**********",
            "amount": "6125.00",
            "aggregator_order_id": "MAWEB98221636962810",
            "merchant_name": "AirpayRT6  ",
            "merchant_mobile": "**********",
            "transaction_time": "16-11-2021 11:56:52",
            "transaction_status": "INITIATED",
            "provider_name": "LIC",
            "utility_name": "LIC Insurance",
            "transaction_charges": "0",
            "transaction_amount": "6125",
            "bill_response": "****************************************************************************",
            "transaction_id": "MAWEB98221636962810",
            "logo_url": "https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png"
        }
    }
}
*/
