// ------------------------ Check Customer ----------------------------
/**
* @api {post} /inittransaction 01) Check Customer
* @apiName inittransactioncheckCustomer
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagcustomer",
    "data": {
      "functionname": "checkCustomer",
      "format": "query",
      "ext_mobileNo": "**********"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_customers", "ext_request_number", "ext_txn_number"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Checks for customer in our database through mobile number or vrn</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_customers
* @apiSuccess {String} ext_request_number
* @apiSuccess {String} ext_txn_number

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "checkCustomer": {
      "ext_status": "200",
      "ext_message": "Success: OTP sent successfully",
      "ext_respcode": "2007",
      "ext_customers": null,
      "ext_request_number": "61451616685933408",
      "ext_txn_number": "21494"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// -------------------------- Request OTP -----------------------------
/**
* @api {post} /inittransaction 02) Request OTP
* @apiName inittransactionrequestOTP
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "requestOTP",
      "format": "mutation",
      "ext_mobile_number": "9947123735",
      "ext_custid": ""
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_txn_number", "ext_request_number"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_txn_number
* @apiSuccess {String} ext_request_number

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "requestOTP": {
      "ext_status": 200,
      "ext_message": "Success: OTP sent successfully",
      "ext_respcode": 2007,
      "ext_txn_number": "18189",
      "ext_request_number": "25351614577477484"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// --------------------------- Verify OTP -----------------------------
/**
* @api {post} /inittransaction 03) Verify OTP
* @apiName inittransactionverifyOTP
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "verifyOTP",
      "format": "mutation",
      "ext_otp": "902622",
      "ext_txn_number":  "17736",
      "ext_request_number": "25351614577477484"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOTP": {
      "ext_status": 200,
      "ext_message": "Success",
      "ext_respcode": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// --------------------- Customer Onboard - ADD -----------------------
/**
* @api {post} /inittransaction 04) Customer Onboard - ADD
* @apiName inittransactioncustomerOnboard
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagcustomer",
    "data": {
      "functionname": "customerOnboard",
      "format": "mutation",
      "ext_requestId": "57001616687240816",
      "ext_customerId": "",
      "ext_action": "ADD",
      "ext_customerType": "KYC",
      "ext_firstName": "Elton",
      "ext_middleName": "",
      "ext_lastName": "Andrew",
      "ext_gender": "Male",
      "ext_dob": "14/01/1999",
      "ext_gstNo": "",
      "ext_mobileNo": "**********",
      "ext_phoneNo": "",
      "ext_emailId": "<EMAIL>",
      "ext_usrConsent":"Y",
      "ext_address1": "Mumbai",
      "ext_address2": "Maharashtra",
      "ext_address3": "TESTADD2",
      "ext_district": "Mumbai",
      "ext_city": "Mumbai",
      "ext_country": "india",
      "ext_state": "Maharashtra",
      "ext_pincode": "400067",
      "ext_uploadType": "CU",
      "ext_docType": "",
      "ext_docName": "",
      "ext_docNo": "",
      "ext_uploadFile": ""
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_customers"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Adds Customer details</p>
<pre><code>{"query":"ada3c14e7288df08HWbS9DnAqWGx9CiBJwd9rk4ZFX6XZm6UdSnZK5qdzcqqMW5TQZ+hCjmp2MK5Z+WPv1i56ltcLZYGbVW3vg3oA6uhChG3zuTdZeLyEmu1qU14kVjeXkstTPJsxJlClMagc+iH+liggyvqtwGiAzXr5JJmgIuxL7AusUH/r3Mv/yMpep+TGOS9n6QxEv+ZuMTMMQraxRbF/C6aLOnKKGeizYPN6sUhaAluFEFDZ/NgFY6od47p/CXPVuN5tNaQB+b/KXHOxWqWf0J9i5yzydw843yCzgVaCsZOUV4tNA0OKXE4t+C5WX8hA5nI6ZD2SECmmJN09Tccn9EntX9NY5gozG1CCHhb/r0qBu1iEZm2TJynCpYfgVR3fR8T2niH2WpZH4Fjls6N9ujMG/UGHVM08NJ+dZVsHJmpQzSguoLCfDCEK3ON707AQathLwzCXIiYNh74og4N/tlogonYhoyY7Sjo/DanPUB1lgkDNwtFohRp5Td/o6nGv2S1razSatU9viP9+wXsS4vWrzkMSysFF7oy9a3tboFDfL8DVWnmXR62Att8kjT5IEC3g7h8GciHH5iRoH8/HevsWUuZsogPuE9Z7Pb5DIOztphKsYqfVw4ZRu8qOEtqjmnMCmg0TqchXfw2T36VBorJte2/9TwagBFrWqYd5opCeMSmaBZdQRDf01pYNIoMIOWNdMhEoGJLf45dbNzH0d9tdUCebm56fRnG+bxXs6mxOFxMlR8Mgv2kSSoznzP9yq7wyTCy1TuhgO2JgkeDmcHq1PlBh24y1Ph+QdB7/HKQfG3igk4dI+Drp1NbxMc7QrQZ99sNT4SDJ80iT2eXadFfDEOhnh8KSy/11qFtcZn20KD0TLGSucuoXBXCQia/y7QAQgrIZcfAdgxivoZL5D1Et9WswkXNXT3RUS3Bb1dZR0rYKddtwS68yxHn+eTvXzBz8+v601mAaisZyEvih1XdMSGth3JzA2Tp7RfUbQheQgDk8fd7GOsI8AbBiGE7Sho/4IpYiftURsuRce0kuH1lkpyrW6yP/nB+Hk2Snf64XB7/FJqbyo1zYEfyhGkpREd9ALua/tA4eLDZhneRBGyOxT7odIk2/Poh2SWI8bgA/l3tOepDsbelv9N17KFsTeJCD0Uc3mSPnEznDA8lG2Yusi1Ht7Ir1czs5IQybCyvIlCWcjfs/k/V+UooYxX/HBdEVqU1Ef71uO4ZM7tdgGNO/JDm1HVcI6GCLfcS13Ze7uPzjI7M788W8vbRfdFIiS3ZTLQtr82xtBebbFoWExjwxpF6LhhrBg=="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {Array} ext_customers

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "customerOnboard": {
      "ext_status": "200",
      "ext_respcode": "1000",
      "ext_message": "Success",
      "ext_customers": [
        {
          "ext_ma_ft_customer_id": 208430,
          "ext_first_name": "Elton",
          "ext_dob": "14/01/1999",
          "ext_pincode": "400067",
          "ext_mobile_number": "**********",
          "ext_email": "<EMAIL>"
          "ext_ma_user_id": 28707,
          "ext_userid": 1353,
          "ext_ma_ft_customer_master_id": 1,
          "ext_customer_type": "KYC",
          "ext_middle_name": "",
          "ext_last_name": "Andrew",
          "ext_gender": "Male",
          "ext_gst_number": null,
          "ext_status": null,
          "ext_phone_number": null,
          "ext_usr_consent": "Yes",
          "ext_address_1": "Mumbai",
          "ext_address_2": "Maharashtra",
          "ext_address_3": "TESTADD2",
          "ext_district": "Mumbai",
          "ext_city": "Mumbai",
          "ext_country": "India",
          "ext_state": "Maharashtra",
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// ------------------- Customer Onboard - UPDATE ----------------------
/**
* @api {post} /inittransaction 05) Customer Onboard - UPDATE
* @apiName inittransactioncustomerOnboardupdate
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagcustomer",
    "data": {
      "functionname": "customerOnboard",
      "format": "mutation",
      "ext_requestId": "57001616687240816",
      "ext_customerId": "",
      "ext_action": "UPDATE",
      "ext_customerType": "KYC",
      "ext_firstName": "Elton",
      "ext_middleName": "",
      "ext_lastName": "Andrew",
      "ext_gender": "Male",
      "ext_dob": "14/01/1999",
      "ext_gstNo": "",
      "ext_mobileNo": "**********",
      "ext_phoneNo": "",
      "ext_emailId": "<EMAIL>",
      "ext_usrConsent":"Y",
      "ext_address1": "Mumbai",
      "ext_address2": "Maharashtra",
      "ext_address3": "TESTADD2",
      "ext_district": "Mumbai",
      "ext_city": "Mumbai",
      "ext_country": "india",
      "ext_state": "Maharashtra",
      "ext_pincode": "400067",
      "ext_uploadType": "CU",
      "ext_docType": "",
      "ext_docName": "",
      "ext_docNo": "",
      "ext_uploadFile": ""
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_customers"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Updates Customer details</p>
<pre><code>{"query":"ada3c14e7288df08HWbS9DnAqWGx9CiBJwd9rk4ZFX6XZm6UdSnZK5qdzcqqMW5TQZ+hCjmp2MK5Z+WPv1i56ltcLZYGbVW3vg3oA6uhChG3zuTdZeLyEmu1qU14kVjeXkstTPJsxJlClMagc+iH+liggyvqtwGiAzXr5JJmgIuxL7AusUH/r3Mv/yMpep+TGOS9n6QxEv+ZuMTMMQraxRbF/C6aLOnKKGeizYPN6sUhaAluFEFDZ/NgFY6od47p/CXPVuN5tNaQB+b/KXHOxWqWf0J9i5yzydw843yCzgVaCsZOUV4tNA0OKXE4t+C5WX8hA5nI6ZD2SECmmJN09Tccn9EntX9NY5gozG1CCHhb/r0qBu1iEZm2TJynCpYfgVR3fR8T2niH2WpZH4Fjls6N9ujMG/UGHVM08NJ+dZVsHJmpQzSguoLCfDCEK3ON707AQathLwzCXIiYNh74og4N/tlogonYhoyY7Sjo/DanPUB1lgkDNwtFohRp5Td/o6nGv2S1razSatU9viP9+wXsS4vWrzkMSysFF7oy9a3tboFDfL8DVWnmXR62Att8kjT5IEC3g7h8GciHH5iRoH8/HevsWUuZsogPuE9Z7Pb5DIOztphKsYqfVw4ZRu8qOEtqjmnMCmg0TqchXfw2T36VBorJte2/9TwagBFrWqYd5opCeMSmaBZdQRDf01pYNIoMIOWNdMhEoGJLf45dbNzH0d9tdUCebm56fRnG+bxXs6mxOFxMlR8Mgv2kSSoznzP9yq7wyTCy1TuhgO2JgkeDmcHq1PlBh24y1Ph+QdB7/HKQfG3igk4dI+Drp1NbxMc7QrQZ99sNT4SDJ80iT2eXadFfDEOhnh8KSy/11qFtcZn20KD0TLGSucuoXBXCQia/y7QAQgrIZcfAdgxivoZL5D1Et9WswkXNXT3RUS3Bb1dZR0rYKddtwS68yxHn+eTvXzBz8+v601mAaisZyEvih1XdMSGth3JzA2Tp7RfUbQheQgDk8fd7GOsI8AbBiGE7Sho/4IpYiftURsuRce0kuH1lkpyrW6yP/nB+Hk2Snf64XB7/FJqbyo1zYEfyhGkpREd9ALua/tA4eLDZhneRBGyOxT7odIk2/Poh2SWI8bgA/l3tOepDsbelv9N17KFsTeJCD0Uc3mSPnEznDA8lG2Yusi1Ht7Ir1czs5IQybCyvIlCWcjfs/k/V+UooYxX/HBdEVqU1Ef71uO4ZM7tdgGNO/JDm1HVcI6GCLfcS13Ze7uPzjI7M788W8vbRfdFIiS3ZTLQtr82xtBebbFoWExjwxpF6LhhrBg=="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {Array} ext_customers
w
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "customerOnboard": {
      "ext_status": "200",
      "ext_respcode": "1000",
      "ext_message": "Success",
      "ext_customers": [
        {
          "ext_ma_ft_customer_id": 208430,
          "ext_first_name": "Elton",
          "ext_dob": "14/01/1999",
          "ext_pincode": "400067",
          "ext_mobile_number": "**********",
          "ext_email": "<EMAIL>"
          "ext_ma_user_id": 28707,
          "ext_userid": 1353,
          "ext_ma_ft_customer_master_id": 1,
          "ext_customer_type": "KYC",
          "ext_middle_name": "",
          "ext_last_name": "Andrew",
          "ext_gender": "Male",
          "ext_gst_number": null,
          "ext_status": null,
          "ext_phone_number": null,
          "ext_usr_consent": "Yes",
          "ext_address_1": "Mumbai",
          "ext_address_2": "Maharashtra",
          "ext_address_3": "TESTADD2",
          "ext_district": "Mumbai",
          "ext_city": "Mumbai",
          "ext_country": "India",
          "ext_state": "Maharashtra",
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// ------------------------- Check Barcode ----------------------------
/**
* @api {post} /inittransaction 06) Check Barcode
* @apiName inittransactioncheckBarcode
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "checkBarcode",
      "format": "query",
      "ext_filter_type": {
        "enum": "BARCODE"
      },
      "ext_filter_value": "123456-006-789652",
      "ext_vrn": "MH09FB1236",
      "ext_ma_ft_customer_master_id": 888
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_tagid", "ext_barcode", "ext_tvc", "ext_customer_type"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Validates Barcode</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_tagid
* @apiSuccess {String} ext_barcode
* @apiSuccess {String} ext_tvc
* @apiSuccess {String} ext_customer_type

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "checkBarcode": {
      "ext_status": 200,
      "ext_message": "Success",
      "ext_respcode": 2007,
      "ext_tagid": "34161FA820328EE80B916800",
      "ext_barcode": "25351614577477484",
      "ext_tvc": "4",
      "ext_customer_type": "NKYC"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// -------------------------- Product List ----------------------------
/**
* @api {post} /inittransaction 07) Product List
* @apiName inittransactionproductList
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "productList",
      "format": "mutation",
      "ext_tvc": "4",
      "ext_customer_type": "NKYC"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_vehicle_list", "ext_product_list", "ext_category_list"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_vehicle_list
* @apiSuccess {String} ext_product_list
* @apiSuccess {String} ext_category_list

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "productList": {
      "ext_status": 200,
      "ext_message": "Success",
      "ext_respcode": 2007,
      "ext_vehicle_list": [
        {
          "ext_ma_ft_vehicle_master_id": "8",
          "ext_cch":"VC4",
          "ext_vehicle_class":"4",
          "ext_vehicle_name":"Car / Jeep / Van"
        },
        {
          "ext_ma_ft_vehicle_master_id":"24",
          "ext_cch":"VC20",
          "ext_vehicle_class":"4",
          "ext_vehicle_name":"Tata Ace and Similar mini Light Commercial Vehicle"
        }
      ],
      "ext_product_list": [
        {
          "ext_ma_ft_product_master_id":"2",
          "ext_vehicle_class":"4",
          "ext_product_name":"Nilsec NKYC Car/Jeep/Van/Mini Light Commercial Vehicle",
          "ext_product_id":"880000001144",
          "ext_cch":"VC4"
        },
        {
          "ext_ma_ft_product_master_id":"3",
          "ext_vehicle_class":"4",
          "ext_product_name":"Nilsec NKYC Car/Jeep/Van/Mini Light Commercial Vehicle",
          "ext_product_id":"880000001144",
          "ext_cch":"VC20"
        }
      ],
      "ext_category_list": [
        {
          "category_name":"COMMERCIAL"
        },
        {
          "category_name":"NON_COMMERCIAL"
        }
      ]
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// ------------------------- Fetch Amount -----------------------------
/**
* @api {post} /inittransaction 08) Fetch Amount
* @apiName inittransactionfetchAmount
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "fetchAmount",
      "format": "mutation",
      "ext_ma_ft_product_master_id": 2,
      "ext_custid": ""
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_issue_amount", "ext_security_amount", "ext_min_balance"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Gets Amount</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_issue_amount
* @apiSuccess {String} ext_security_amount
* @apiSuccess {String} ext_min_balance

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "fetchAmount": {
      "ext_status": 200,
      "ext_message": "Success",
      "ext_respcode": 2007,
      "ext_issue_amount": "100.00",
      "ext_security_amount": "200.00",
      "ext_min_balance": "109.00"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// --------------------------- Issue Tag ------------------------------
/**
* @api {post} /inittransaction 09) Issue Tag
* @apiName inittransactionissueTag
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagissue",
    "data": {
      "functionname": "issueTag",
      "format": "mutation",
      "ext_custid": 196661,
      "ext_ma_ft_customer_master_id": 196661,
      "ext_vrn_number": "MH04AT1002",
      "ext_request_number": "73411613401069676",
      "ext_aggregator_order_id": "MAWEB78791613401106",
      "ext_product": "880000000002",
      "ext_tvc": "4",
      "ext_cch": "VC4",
      "ext_tagid": "34161FA820328EE80B916800",
      "ext_barcode": "100002",
      "ext_chasis_number": "",
      "ext_issue_amount": "100.00",
      "ext_security_amount": "200.00",
      "ext_min_balance": "109.00",
      "ext_vehicle_make_model": "",
      "ext_vehicle_color": "",
      "ext_reg_date": "02/02/2021",
      "ext_mobile_number": "**********",
      "ext_docNo": "7686876",
      "ext_attachment": "cc6b18bbae1cc7266dcdfc2d23d76ceebd57e023/****************.pdf",
      "ext_uploadid": 464,
      "ext_engineNo": "6786876",
      "ext_security_pin": "3434"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_customer_name", "ext_customer_mobile", "ext_bank_name", "ext_aggregator_order_id", "ext_vrn", "ext_issue_fee", "ext_chasis_no", "ext_security_fee", "ext_min_bal", "ext_initial_bal", "ext_tagId", "ext_transaction_time", "ext_transaction_reason", "ext_cch", "ext_product_name", "ext_transaction_status"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Issues tag for vehicle</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_customer_name
* @apiSuccess {String} ext_customer_mobile
* @apiSuccess {String} ext_bank_name
* @apiSuccess {String} ext_aggregator_order_id
* @apiSuccess {String} ext_vrn
* @apiSuccess {String} ext_issue_fee
* @apiSuccess {String} ext_chasis_no
* @apiSuccess {String} ext_security_fee
* @apiSuccess {String} ext_min_bal
* @apiSuccess {String} ext_initial_bal
* @apiSuccess {String} ext_tagId
* @apiSuccess {String} ext_transaction_time
* @apiSuccess {String} ext_transaction_reason
* @apiSuccess {String} ext_cch
* @apiSuccess {String} ext_product_name
* @apiSuccess {String} ext_transaction_status
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "issueTag":{
      "ext_status": 200,
      "ext_message": "Success",
      "ext_respcode": 1000,
      "ext_customer_name": null,
      "ext_customer_mobile": null,
      "ext_bank_name": "IDFC FIRST BANK",
      "ext_aggregator_order_id": "MAWEB78791613401106",
      "ext_vrn": "MH04AT1002",
      "ext_issue_fee": "100.00",
      "ext_chasis_no": "",
      "ext_security_fee": "200.00",
      "ext_min_bal": "109.00",
      "ext_initial_bal": "409.00",
      "ext_tagId": "34161FA820328EE80B916800",
      "ext_transaction_time": "15-02-2021 08:29:44",
      "ext_transaction_reason": "",
      "ext_cch": "VC4",
      "ext_product_name": "Normal KYC Car/Jeep/Van/Mini Light Commercial Vehicle",
      "ext_transaction_status": "SUCCESS"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// ------------------------ Check Tag Status --------------------------
/**
* @api {post} /inittransaction 10) Check Tag Status
* @apiName inittransactioncheckTagStatus
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagcustomer",
    "data": {
      "functionname": "checkTagStatus",
      "format": "query",
      "ext_vrn":"MK98AS09876k"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_reg_date", "ext_ma_ft_customer_master_id", "ext_cust_id", "ext_mobile_number", "ext_vehicle_status", "ext_available_bal", "ext_vrn", "ext_tagId", "ext_veh_class", "ext_veh_desc", "ext_customer_type", "ext_cust_id", "ext_customer_name"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Checks whether the tag is activated for the recharge</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_reg_date
* @apiSuccess {String} ext_ma_ft_customer_master_id
* @apiSuccess {String} ext_cust_id
* @apiSuccess {String} ext_mobile_number
* @apiSuccess {String} ext_vehicle_status
* @apiSuccess {String} ext_available_bal
* @apiSuccess {String} ext_vrn
* @apiSuccess {String} ext_tagId
* @apiSuccess {String} ext_veh_class
* @apiSuccess {String} ext_veh_desc
* @apiSuccess {String} ext_customer_type
* @apiSuccess {String} ext_cust_id
* @apiSuccess {String} ext_customer_name
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data":{
    "checkTagStatus":{
      "ext_status": "200",
      "ext_message": "Success",
      "ext_respcode": "1000",
      "ext_reg_date": "0000-00-00",
      "ext_ma_ft_customer_master_id": "196661",
      "ext_cust_id": "196661",
      "ext_mobile_number": "**********",
      "ext_vehicle_status": "Activated",
      "ext_available_bal": "110",
      "ext_vrn": "MK98AS09876",
      "ext_tagId": "34161FA820327FA404CC5520",
      "ext_veh_class": "VC4",
      "ext_veh_desc": null,
      "ext_customer_type": "NKYC",
      "ext_customer_name": "Prajaktab"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// ------------------------ Fastag Recharge ---------------------------
/**
* @api {post} /inittransaction 11) Fastag Recharge
* @apiName inittransactionfastagRecharge
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagissue",
    "data": {
      "functionname": "fastagRecharge",
      "format": "mutation",
      "ext_ma_ft_customer_master_id": 303,
      "ext_aggregator_order_id": "MAWEB63652311147488",
      "ext_tagid": "34161FA820327FA404CC5900",
      "ext_amount": "1000",
      "ext_mobile_number": "**********",
      "ext_vrn_number": "APITEST987",
      "ext_security_pin": "123456"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_customer_name", "ext_customer_mobile", "ext_bank_name", "ext_aggregator_order_id", "ext_initial_bal", "ext_tagId", "ext_transaction_time", "ext_transaction_reason", "ext_transaction_status"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Reacharge actiavated tag</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_customer_name
* @apiSuccess {String} ext_customer_mobile
* @apiSuccess {String} ext_bank_name
* @apiSuccess {String} ext_aggregator_order_id
* @apiSuccess {String} ext_initial_bal
* @apiSuccess {String} ext_tagId
* @apiSuccess {String} ext_transaction_time
* @apiSuccess {String} ext_transaction_reason
* @apiSuccess {String} ext_transaction_status
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "fastagRecharge": {
    "ext_status": 200,
    "ext_message": "Success",
    "ext_respcode": 1000,
    "ext_customer_name": "Elton Andrew",
    "ext_customer_mobile": "**********",
    "ext_bank_name": "IDFC FIRST BANK",
    "ext_aggregator_order_id": "MAWEB63652311147488",
    "ext_initial_bal": "1000.00",
    "ext_tagId": "34161FA820327FA404CC5900",
    "ext_transaction_time": "26-02-2021 01:51:30",
    "ext_transaction_reason": "Success",
    "ext_transaction_status": "SUCCESS"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// -------------- Get Fastag Recharge Receipt Details -----------------
/**
* @api {post} /inittransaction 12) Get Fastag Recharge Receipt Details
* @apiName inittransactiongetFastagRechargeReceiptDetails
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagissue",
    "data": {
      "functionname": "getFastagRechargeReceiptDetails",
      "format": "query",
      "ext_ma_transaction_master_id": 5543
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_customer_name", "ext_customer_mobile", "ext_bank_name", "ext_aggregator_order_id", "ext_initial_bal", "ext_tagId", "ext_transaction_time", "ext_transaction_reason", "ext_transaction_status"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_customer_name
* @apiSuccess {String} ext_customer_mobile
* @apiSuccess {String} ext_bank_name
* @apiSuccess {String} ext_aggregator_order_id
* @apiSuccess {String} ext_initial_bal
* @apiSuccess {String} ext_tagId
* @apiSuccess {String} ext_transaction_time
* @apiSuccess {String} ext_transaction_reason
* @apiSuccess {String} ext_transaction_status
* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "getFastagRechargeReceiptDetails": {
    "ext_status": 200,
    "ext_message": "Success",
    "ext_respcode": 1000,
    "ext_customer_name": "Libin Kurian",
    "ext_customer_mobile": "**********",
    "ext_bank_name": "IDFC FIRST BANK",
    "ext_aggregator_order_id": "MAWEB63652311147488",
    "ext_initial_bal": "1000.00",
    "ext_tagId": "34161FA820327FA404CC5900",
    "ext_transaction_time": "26-02-2021 01:51:30",
    "ext_transaction_reason": "Success",
    "ext_transaction_status": "SUCCESS"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// --------------- Request OTP for Customer - Update ------------------
/**
* @api {post} /inittransaction 13) Request OTP for Customer - Update
* @apiName inittransactionrequestOTPUpdate
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "requestOTP",
      "format": "mutation",
      "ext_mobile_number": "9947123735",
      "ext_custid": "208430",
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode", "ext_txn_number", "ext_request_number"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard update</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode
* @apiSuccess {String} ext_txn_number
* @apiSuccess {String} ext_request_number

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "requestOTP": {
      "ext_status": 200,
      "ext_message": "Success: OTP sent successfully",
      "ext_respcode": 2007,
      "ext_txn_number": "18189",
      "ext_request_number": "25351614577477484"
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// --------------- Verify OTP for Customer - Update -------------------
/**
* @api {post} /inittransaction 14) Verify OTP for Customer - Update
* @apiName inittransactionverifyOTPUpdate
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "ma_user_id": 28707,
    "user_id": 1353,
    "purpose": "MERCHANTUR",
    "core_function": "fasttagproduct",
    "data": {
      "functionname": "verifyOTP",
      "format": "mutation",
      "ext_otp": "902622",
      "ext_txn_number":  "17736",
      "ext_request_number": "25351614577477484"
    },
    "responseParams": ["ext_status", "ext_message", "ext_respcode"],
    "checksum": "aa4a5ef588cbac9597eaf647e229ddff"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  "data": {
    "verifyOTP": {
      "ext_status": 200,
      "ext_message": "Success",
      "ext_respcode": 1000
    }
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  "ext_status": 400,
  "ext_message": "Request Failed"
}
*/

// -------------------------- File Upload -----------------------------
/**
* @api {post} /inittransaction 15) File Upload
* @apiName inittransactionfileupload
* @apiGroup External API - FASTTAG

* @apiHeader {String} Authorization JWT Token.
* @apiHeader {String} X-Api-Key API Key.
* @apiHeader {String} App Version.
* @apiHeaderExample {json} Header-Example:
{
  "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************.cz5FKFW7R5WtCP2V4sVM1jVKDWhlT_U9Kt9s7m-FuOY"
  "X-Api-Key" : "jry4WpwDkok6lfr2"
  "app_version" : "1.45"
}

* @apiParam {string} query Encrypted Query/Mutation.
* @apiParamExample {json} Request-Example:
  {
    "base64String": "iVBORw0KGgoAAAANSUhEUgAABVYAAAMACAYAAADPPjzCAAAAAXNSR0IArs4cL7X2FEsKQ72NZRX7KxBpu/rDDN/6njUmi9WjTfeeOPFfwPeQJunDA3EYwAAAABJRU5ErkJggg==",
    "ma_user_id": "28707",
    "userid": "1353",
    "module": "fasttag"
  }

* @apiDescription <span class="type" style="background-color: #e535ab">RESTFUL Request</span>
<p>Request Otp for cutomer-onboard</p>
<pre><code>{"query":"23428244a328495bppXxnBHb6knqPC+qfSZJyL3qcWmSHmDLYTzHt1TMrvUAX2T2u25sq/rD9kxG2wmVzfcS0sTWdxU0yaPWpPHBo1/1/LfYd0cSLm+O8Bz6U+7eh+y1dIESDMRMSo3Ve1y1p1icPCFz2KGQSCchKiZZ1nMS1r/0WJsA2gEGZWzK9MRLIj1EEC8DfD/Ae2j7X8Qv77NiosTIpZMJGqcQJRc4J1jp9eBTq/QAIDzquYrqucE20jHMi98OhaICIRfls6WTxhQW47xD/XuYhMbtWxBLucp5OMq8Ny/lA3lUaTeDkrJtTjpAdoG9STgx78j+TucQ+xV1Sk85adO9BJbqEJ6GO8TlFmnyMiRFyw6YwV1rvSQQYMgA1s/54Se5rKRZm0/5hqDMuuEhAJRqHoS4YNJu2t8asC4D2KIi9bjrQNHIFWX8M4yEhHzqLviS6lqRcyWnXnwui878b8C2x00qF5fWcPXcMyu7oUsUX/KRv2utyfU="}</code></pre>

* @apiSuccess {String} ext_status API status code.
* @apiSuccess {String} ext_message
* @apiSuccess {String} ext_respcode

* @apiSuccessExample Success-Response:
HTTP/1.1 200 OK
{
  {
    "status":200,
    "message":"Success",
    "uploadid":10,
    "basepath":"1a0141d472fb2d0a0be41810f7cd2250812b7830/1606282702.png",
    "fullpath":"https://retailappdocs.s3.ap-south-1.amazonaws.com/1a0141d472fb2d0a0be41810f7cd2250812b7830/1606282702.png"
  }
}

* @apiError {String} status API status code.
* @apiError {String} message  API response code.

* @apiErrorExample Error-Response:
HTTP/1.1 200 OK
{
  {
  "status":400,
  "message":"Error In File Upload"
  }
}
*/
