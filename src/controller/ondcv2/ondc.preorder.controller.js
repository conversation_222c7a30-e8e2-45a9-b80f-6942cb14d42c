// Package Imports
const { basename } = require('path')
const Joi = require('joi')
const _ = require('lodash')

// Code Imports
const logs = require('../../util/log')
const errorEmail = require('../../util/errorHandler')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const ValidationError = require('../../util/errors/ValidationError')
const common = require('../../util/common')
const errorMsg = require('./config/errors')
const OndcMariaRepository = require('./repository/ondc.maria.repository')
const OndcPreOrderIntegration = require('./integration/ondc.preorder.integration')
const OndcPreOrderHelper = require('./helper/ondc.preorder.helper')
const OndcPreOrderRepository = require('./repository/ondc.preorder.repository')

module.exports = class OndcPreOrderController {
  static get LOGGER () {
    return logs.logger
  }

  /** API
   * ondcSearchItems description - Returns min max limit of amount
   * @typedef {{ ma_user_id: number, userid: number, search_string: String, delivery_location: String, storeLimit: number, storeOffset: number, ondc_transaction_id: String, itemsLimit: number, itemsOffset: number, search_type?: 'PRODUCT'|'CATEGORY'|'PROVIDER', sort_object: { store_distance: 'ASC'|'DESC', price: 'ASC'|'DESC', store_name: 'ASC'|'DESC'} }} requestData
   * @param {requestData} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, ondc_transaction_id: String, store_next_flag: Boolean, search_data: Object }>}
   */
  static async ondcSearchItems (__, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcSearchItems', type: 'request', fields: fields })

    // Validate Fields *****
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      search_string: Joi.string().trim().min(1).required(),
      delivery_location: Joi.string().trim().min(1).required(),
      storeLimit: Joi.number().min(1).default(4),
      storeOffset: Joi.number().min(0).default(0),
      ondc_transaction_id: Joi.string().allow(null).empty('').default(null),
      itemsLimit: Joi.number().min(1).default(4),
      itemsOffset: Joi.number().min(0).default(0),
      search_type: Joi.string().label('Search Type').valid('PRODUCT', 'CATEGORY', 'PROVIDER').required(),
      sort_object: Joi.object({
        price: Joi.string().valid('ASC', 'DESC', null).label('Price filter'),
        store_name: Joi.string().valid('ASC', 'DESC', null).label('Store Name filter'),
        store_distance: Joi.string().valid('ASC', 'DESC', null).label('Store Distance filter')
      }).default(null)
    })

    console.time(`${fields.ma_user_id} - ${fields.search_string} - ondcSearchItems API Time Log`)
    try {
      /** @type {requestData} */
      const request = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })
      const mariaRepository = new OndcMariaRepository(logger)

      logger({ pagename: basename(__filename), action: 'ondcSearchItems', type: 'request Data', fields: request })
      // Check ondc_transaction_id parameter is there or not *****
      if (request.ondc_transaction_id) {
        // Check data as per ondc_transaction_id.
        const formatSearchData = await mariaRepository.getSearchResults({
          ma_user_id: request.ma_user_id,
          userid: request.userid,
          ondc_transaction_id: request.ondc_transaction_id,
          storeLimit: request.storeLimit,
          storeOffset: request.storeOffset,
          itemsLimit: request.itemsLimit,
          itemsOffset: request.itemsOffset,
          sort_object: request.sort_object
        })

        const next_flag = formatSearchData.totalStoreCount - (fields.storeLimit + fields.storeOffset)
        console.timeEnd(`${fields.ma_user_id} - ${fields.search_string} - ondcSearchItems API Time Log`)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, search_data: formatSearchData.formatedData, ondc_transaction_id: fields.ondc_transaction_id, store_next_flag: next_flag > 0 }
      }

      const preOrderIntegration = new OndcPreOrderIntegration(logger)
      const preOrderHelper = new OndcPreOrderHelper(logger)
      const preOrderRepository = new OndcPreOrderRepository(logger)
      // Category list for serach API. As per this we make request
      const { fetchOndcResult, ondc_message_id_for_request, ondc_transaction_id_for_request, decryptedSearchRequestData } = await preOrderIntegration.search(request)

      // Save data in maria db table to fetch records for View Details & View All button
      // Format search API data as per frontend *****
      const [___, formatSearchData] = await Promise.all([
        mariaRepository.saveSearchResults({
          response: fetchOndcResult.getResultsResponse,
          ma_user_id: request.ma_user_id,
          userid: request.userid,
          sessionid: ondc_transaction_id_for_request,
          search_request_id: fetchOndcResult.RequestId,
          search_txn_id: ondc_transaction_id_for_request,
          search_msg_id: ondc_message_id_for_request,
          delivery_location: request.delivery_location,
          fetchOndcResult
        }),
        preOrderHelper.formatOndcSearchApiData({ searchApiData: fetchOndcResult.getResultsResponse, fields: request }),
        preOrderRepository.insertSearchRequest({ fields: request, decryptedSearchRequestData, ondc_message_id_for_request, ondc_transaction_id_for_request })
      ])

      logger({ pagename: basename(__filename), action: 'ondcSearchItems', type: 'formatSearchData', fields: formatSearchData.totalStoreCount })
      const next_flag = formatSearchData.totalStoreCount - (fields.storeLimit + 0)
      logger({ pagename: basename(__filename), action: 'ondcSearchItems', type: 'next_flag', fields: next_flag })
      console.timeEnd(`${fields.ma_user_id} - ${fields.search_string} - ondcSearchItems API Time Log`)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, search_data: formatSearchData.formatedData, ondc_transaction_id: ondc_transaction_id_for_request, store_next_flag: next_flag > 0 }
      /* */
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcSearchItems', type: 'err', fields: err })
      if (common.isCustomError(err)) return common.getErrorResponse(err)
      errorEmail.notifyCatchErrorEmail({
        function: 'ondcSearchItems',
        data: { ...{ } },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }

  /** API ++++++++++
   * ondcCartCheckout description - add/update product in cart table
   * @typedef {{ ma_user_id: number, userid: number, cart_session_id: String, cart_product_list: {
   *    product_id: string,
   *    provider_id: string,
   *    ondc_transaction_id: string,
   *    quantity: number
   *  }[] }} requestData
   * @param {requestData} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, cart_session_id: String, cart_list: Object }>}
   * 1. valiate the request
   * 2. call select API provider_id and ondc_transaction_id wise
   * 3. validate every select API response
   * 4. insert or upadte product data in cart table
  */
  static async ondcCartCheckout (__, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'request - fields', fields: fields })

    // 1. Validate fields *****
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      cart_session_id: Joi.string().required(),
      cart_product_list: Joi.array().items(Joi.object({
        product_id: Joi.string().required(),
        provider_id: Joi.string().required(),
        quantity: Joi.number().min(1).required(),
        ondc_transaction_id: Joi.string().trim().required()
      })).required()
    })

    try {
      /** @type {requestData} */
      const request = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })
      logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'request - request', fields: request })
      // Call Maria Repository for search product data
      const mariaRepository = new OndcMariaRepository(logger)
      const preOrderRepository = new OndcPreOrderRepository(logger)
      const preOrderIntegration = new OndcPreOrderIntegration(logger)
      const preOrderHelper = new OndcPreOrderHelper(logger)

      // 2. Call the select API as per provider_id(seller wise) and ondc_transaction_id *****
      // Create selectResponse variable to store select API(MS) response
      const selectResponse = []
      const selectResponse1 = []

      // Sort request.cart_product_list data based on 'provider_id' and 'ondc_transaction_id'
      // https://stackoverflow.com/questions/23600897/using-lodash-groupby-how-to-add-your-own-keys-for-grouped-output - groupBy code logic
      // https://gist.github.com/progging/1159726f2704ca06641ab2a41f408584 - groupBy code logic = currently using this
      const fetchCartData = Object.values(_.groupBy(request.cart_product_list, item => `${item.provider_id}-${item.ondc_transaction_id}`))
      logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'fetchCartData', fields: JSON.stringify(fetchCartData) })

      const formatSearchData = await Promise.all(Object.values(fetchCartData).map(val => this.ondcCartCheckoutHelper(val, { preOrderRepository, preOrderIntegration, mariaRepository, uniqueIdForLog: `${val[0].provider_id}-${val[0].ondc_transaction_id}` })))
      logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'formatSearchData ata log', fields: JSON.stringify(formatSearchData) })

      // 3. Insert or Update the cart data based on select API(MS) response(selectResponse) *****
      const createCartDetails = await preOrderHelper.createCartDetailsForInsertOrUpate(formatSearchData, request)
      logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'createCartDetailsForInsertOrUpate', fields: JSON.stringify(createCartDetails) })
      if (createCartDetails.status != 200) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, cart_session_id: request.cart_session_id }

      if (_.get(createCartDetails, 'insertCartData', []).length > 0) {
        const saveProductDetails = await preOrderRepository.saveProductDetails({ cartData: createCartDetails.insertCartData })
        logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'saveProductDetails', fields: saveProductDetails })
      }
      if (_.get(createCartDetails, 'updateCartData', []).length > 0 || _.get(createCartDetails, 'deleteCartData', []).length > 0) {
        for (const updateCart in createCartDetails.updateCartData) {
          const upateCartDataResponse = await preOrderRepository.upateCartData({ ...createCartDetails.updateCartData[updateCart] })
          logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: `upateCartDataResponse = ${updateCart}`, fields: upateCartDataResponse })
        }
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, cart_session_id: fields.cart_session_id, selectData: { breakout: createCartDetails.breakout, cart_details: createCartDetails.finalResponse } }
      // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, cart_session_id: fields.cart_session_id }
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'err', fields: err })
      if (common.isCustomError(err)) return { ...common.getErrorResponse(err), cart_session_id: fields.cart_session_id || null, ondc_transaction_id: fields.ondc_transaction_id || null }
      errorEmail.notifyCatchErrorEmail({
        function: 'ondcAddToCart',
        data: { ...{ } },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, mobile_id: fields.mobile_id || null }
    }
  }

  /** API ++++++++++
   * ondcCartCheckout description - add/update product in cart table
   * @typedef {{ ma_user_id: number, userid: number, mobile_id: String }} requestData
   * @param {requestData} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, mobile_id: String, cart_list: Object }>}
   */
  static async ondcCartCheckoutHelper (data, { preOrderRepository, preOrderIntegration, mariaRepository, uniqueIdForLog }) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'request - fields' + `- ${uniqueIdForLog}`, fields: data })
    try {
      // 2. Call the select API as per provider_id(seller wise) and ondc_transaction_id *****
      // Create selectResponse variable to store select API(MS) response
      let selectResponse = []
      const noSelectResponse = []
      const selectResponse1 = []

      let providerDatas = data

      // Check product data already in cart table or not = IF yes then update the records, For that we mark the updateCart as true for update ELSE insert the new records.
      const productIds = _.map(providerDatas, 'product_id')
      const fetchMultipleProductCartData = await preOrderRepository.fetchMultipleProductsCartData({ productIds, provider_id: providerDatas[0].provider_id, ondc_transaction_id: providerDatas[0].ondc_transaction_id, uniqueIdForLog })
      logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'fetchMultipleProductCartData' + `- ${uniqueIdForLog}`, fields: fetchMultipleProductCartData })

      // Check product data already in cart table or not = IF yes then update the records, For that we mark the updateCart as true for update ELSE insert the new records.
      const fetchMultipleProviderCartData = await preOrderRepository.fetchMultipleProviderCartData({ provider_id: providerDatas[0].provider_id, ondc_transaction_id: providerDatas[0].ondc_transaction_id, uniqueIdForLog })
      logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'fetchMultipleProviderCartData' + `- ${uniqueIdForLog}`, fields: fetchMultipleProviderCartData })

      // For additional data we fetch the records from the search result table
      const fetchMultipleProductsSearchData = await mariaRepository.fetchMultipleProductsSearchData({ productIds, provider_id: providerDatas[0].provider_id, ondc_transaction_id: providerDatas[0].ondc_transaction_id, uniqueIdForLog })
      logger({ pagename: basename(__filename), action: 'ondcAddToCart', type: 'fetchMultipleProductsSearchData' + `- ${uniqueIdForLog}`, fields: fetchMultipleProductsSearchData })

      // Add additional parameters based on fetchMultipleProductCartData and fetchMultipleProductsSearchData
      providerDatas = await providerDatas.map(val => ({
        ...val,
        searchData: [_.find(fetchMultipleProductsSearchData, { prov_items_id: val.product_id }) || {}], // fetch data from fetchMultipleProductsSearchData as per prov_items_id
        updateCart: !!_.find(fetchMultipleProductCartData, { product_id: val.product_id }) // IF product data present in the table then pass the value as true ELSE false
      }))

      let selectflag = false
      if (fetchMultipleProductCartData.length != fetchMultipleProviderCartData.length || fetchMultipleProductCartData.length != providerDatas.length || fetchMultipleProviderCartData.length != providerDatas.length || (fetchMultipleProductCartData.length <= 0 && fetchMultipleProviderCartData.length <= 0)) {
        selectflag = true
      } else {
        providerDatas.map((val) => {
          const data = _.find(fetchMultipleProductCartData, { product_id: val.product_id })
          if (data && data.product_quantity != val.quantity) selectflag = true
        })
      }
      logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'selectflag' + `- ${uniqueIdForLog}`, fields: selectflag })

      // Call Select API(MS) and store the response in selectResponse
      if (selectflag) {
        // Delete prouct data from the table.
        const deleteCartData = await preOrderRepository.deleteCartData({ provider_id: providerDatas[0].provider_id, ondc_transaction_id: providerDatas[0].ondc_transaction_id, uniqueIdForLog })
        logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'deleteCartData' + `- ${uniqueIdForLog}`, fields: deleteCartData })

        selectResponse = await preOrderIntegration.select(providerDatas, uniqueIdForLog)
      } else selectResponse = { provier_wise_data: providerDatas, fetchMultipleProductCartData }

      logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'selectResponse' + `- ${uniqueIdForLog}`, fields: JSON.stringify(selectResponse) })
      logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'selectResponse' + `- ${uniqueIdForLog}`, fields: selectResponse })

      return selectResponse
      // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, mobile_id: fields.mobile_id }
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcCartCheckoutHelper', type: 'err' + `- ${uniqueIdForLog}`, fields: err })
      if (common.isCustomError(err)) return { ...common.getErrorResponse(err) }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }
}
