// Package Import
const { basename } = require('path')
const _ = require('lodash')
const moment = require('moment')
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const util = require('../../../util/util')

// Code Import
const HelperError = require('../../../util/errors/HelperError')
const common = require('../../../util/common')
const errorMsg = require('../config/errors')
const ValidationError = require('../../../util/errors/ValidationError')
const Joi = require('joi')

module.exports = class OndcTransactionHelper {
  constructor (logger) {
    this.logger = logger || console.log
  }

  /**
   * Creates a new Connection if connection passed in params is undefined.
   * @param {mysqlConnection} con
   * @returns {Promise<mySQLWrapper>}
   */
  async getConnection (con) {
    return [!con, con || await mySQLWrapper.getConnectionFromPool()]
  }

  /** saveCartProductDetails ++++++++++
   * @typedef {{ selectResponse: Object, request: Object }}
   * @returns {Promise<{ fetchOndcResult: { status: 200, respcode: number, message: string, action_code: 1000 } }>}
   */
  async createCartDetailsForInsertOrUpate (selectResponse, request) {
    this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'request Data', fields: JSON.stringify({ selectResponse, request }) })
    try {
      let finalResponse = []
      const insertCartData = []
      const updateCartData = []
      const deleteCartData = []
      const insertCartDataAgain = []
      const breakout = {
        baseprice: 0.00,
        price_without_charges: 0.00,
        shipping_charges: 0.00,
        final_amount: 0.00,
        price_currency: 'INR'
      }
      for (const selectResp in selectResponse) {
        // Add parameters in final response
        finalResponse = [...finalResponse, ...(await selectResponse[selectResp].provier_wise_data.map(val => ({
          status: selectResponse[selectResp].fetchOndcResult ? 400 : 200,
          message: selectResponse[selectResp].fetchOndcResult ? 'Fail: Product Not Serviceable!' : 'Success: Product Serviceable!',
          product_id: val.product_id,
          provider_id: val.provider_id,
          ondc_transaction_id: val.ondc_transaction_id,
          product_quantity: val.quantity,
          product_image: JSON.parse(_.get(val, 'searchData[0].prov_items_desc_images', null)) || null,
          product_name: _.unescape(_.get(val, 'searchData[0].prov_items_desc_name', '')),
          baseamount: parseFloat(_.get(val, 'searchData[0].prov_items_price_value', 0)).toFixed(2),
          proucttTotalPriceWithoutCharges: (parseFloat(_.get(val, 'searchData[0].prov_items_price_value', 0)) * _.toNumber(val.quantity)).toFixed(2)
        })))]

        if (selectResponse[selectResp].fetchOndcResult && selectResponse[selectResp].fetchOndcResult.status == 200) {
          // Valiation
          // 1. error
          if (_.get(selectResponse[selectResp], 'fetchOndcResult.getResultsResponse[0].error', null)) {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : error', fields: null })
            continue
          }
          // 2. order.fulfillments[0].state.descriptor.name
          if (_.get(selectResponse[selectResp], 'fetchOndcResult.getResultsResponse[0].order.fulfillments[0].state.descriptor.name', null) != 'Serviceable') {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : descriptor.name', fields: null })
            continue
          }
          // 3. order data validation
          const getOrderResponse = _.get(selectResponse[selectResp], 'fetchOndcResult.getResultsResponse[0].order', null)
          if (!getOrderResponse) {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : order', fields: null })
            continue
          }

          // Fetch total price provider(seller) wise
          const totalSellerWiseProductPrice = _.get(getOrderResponse, 'quote.price.value', null)
          const totalSellerWiseProductCurrency = _.get(getOrderResponse, 'quote.price.currency', null)
          const totalSellerWiseProductBreakup = _.get(getOrderResponse, 'quote.breakup', null)
          if (!totalSellerWiseProductPrice || !totalSellerWiseProductCurrency || (!totalSellerWiseProductBreakup || totalSellerWiseProductBreakup.length <= 0)) {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : totalSellerWiseProductBreakup', fields: null })
            continue
          }

          // Fetch total delivery charges provider(seller) wise
          const commonDeliveryChargeBreakup = _.find(totalSellerWiseProductBreakup, { '@ondc/org/title_type': 'delivery' }) || {}
          const commonDeliveryChargePrice = _.get(commonDeliveryChargeBreakup, 'price.value', 0)
          const commonDeliveryChargeCurrency = _.get(commonDeliveryChargeBreakup, 'price.currency', 'INR')
          const provierWiseData = _.get(selectResponse[selectResp], 'provier_wise_data', null)

          // Calculate breakout value
          breakout.price_without_charges = (parseFloat(breakout.price_without_charges) + parseFloat(totalSellerWiseProductPrice) - parseFloat(commonDeliveryChargePrice || 0)).toFixed(2)
          breakout.shipping_charges = (parseFloat(breakout.shipping_charges) + parseFloat(commonDeliveryChargePrice || 0)).toFixed(2)
          breakout.final_amount = (parseFloat(breakout.final_amount) + parseFloat(totalSellerWiseProductPrice)).toFixed(2)

          // Loop for product
          for (const providerWise in provierWiseData) {
            // Validation *****
            // 1. prouctWiseBreakup valiation
            const prouctWiseBreakup = _.find(totalSellerWiseProductBreakup, { '@ondc/org/item_id': provierWiseData[providerWise].product_id, '@ondc/org/title_type': 'item' })
            if (!prouctWiseBreakup) {
              this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : prouctWiseBreakup', fields: null })
              break
            }

            // Fetch product price details
            const productWisePrice = _.get(prouctWiseBreakup, 'price.value', null)
            const prductWiseCurrency = _.get(prouctWiseBreakup, 'price.currency', null)
            const prductWiseCount = _.get(prouctWiseBreakup, '@ondc/org/item_quantity.count', null)
            if (prductWiseCount != provierWiseData[providerWise].quantity) {
              this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : quantity', fields: null })
              break
            }

            // If product data already present in cart table then push the data in updateCartData array
            insertCartData.push([
              request.ma_user_id,
              provierWiseData[providerWise].ondc_transaction_id,
              provierWiseData[providerWise].provider_id,
              _.get(provierWiseData[providerWise], 'searchData[0].prov_items_desc_name', null),
              provierWiseData[providerWise].product_id,
              provierWiseData[providerWise].quantity,
              _.get(provierWiseData[providerWise], 'searchData[0].prov_items_price_value', null),
              productWisePrice,
              commonDeliveryChargePrice,
              'Incart',
              request.cart_session_id
            ])

            finalResponse[_.toNumber(finalResponse.length) - _.toNumber(provierWiseData.length) + _.toNumber(providerWise)] = { ...finalResponse[_.toNumber(finalResponse.length) - _.toNumber(provierWiseData.length) + _.toNumber(providerWise)], status: 200, message: 'Product Serviceable!' }
          }
        } else if (selectResponse[selectResp].fetchMultipleProductCartData && selectResponse[selectResp].fetchMultipleProductCartData.length > 0) {
          const productWisePriceWithoutCharges = _.sumBy(selectResponse[selectResp].fetchMultipleProductCartData, 'product_total_price')
          const product_shipping_charges = _.get(selectResponse[selectResp], 'fetchMultipleProductCartData[0].delivery_charges', 0)
          // Calculate breakout value
          breakout.price_without_charges = (parseFloat(breakout.price_without_charges) + parseFloat(productWisePriceWithoutCharges)).toFixed(2)
          breakout.shipping_charges = (parseFloat(breakout.shipping_charges) + parseFloat(product_shipping_charges || 0)).toFixed(2)
          breakout.final_amount = (parseFloat(breakout.final_amount) + parseFloat(productWisePriceWithoutCharges) + parseFloat(product_shipping_charges)).toFixed(2)
        }
      }

      return { status: 200, finalResponse: _.orderBy(finalResponse, ['status'], ['asc']), insertCartData, updateCartData, breakout }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'err', fields: err })
      if (common.isCustomError(err)) return { ...common.getErrorResponse(err) }
      return { status: 400 }
    }
  }

  /**
   * Verify Pin
   * @param {Object} fields
   * @param {number} fields.ma_user_id
   * @param {number} fields.userid
   * @param {string} fields.security_pin
   * @param {Object|null} con connection
   */
  async verifyPin (fields) {
    this.logger({ pagename: basename(__filename), action: 'verifyPin', type: 'request', fields })
    // Validate Fields *****
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      pin: Joi.string().trim().min(1).required()
    })
    await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })

    try {
      const securePinCtrl = require('../../securityPin/securityPinController')
      const securePinData = await securePinCtrl.verifySecurePin(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        security_pin: fields.pin
      })
      console.log('securePinData', securePinData)
      return securePinData
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'verifyPin', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
    }
  }

  /** API ++++++++++
   * ondcInitApiProcess description - INIT Api process
   * @typedef {{ data: object, { mariaRepository: object, transactionRepository: object, preTransactionIntegration: object, uniqueIdForLog: string, request: object, connection: object } }} data
   * @param {data} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, cartData: object, searchData: object, customerData: object }>}
   */
  async ondcInitApiProcess (data, { mariaRepository, transactionRepository, preTransactionIntegration, uniqueIdForLog = '', request, connection }) {
    this.logger({ pagename: basename(__filename), action: 'ondcInitApiProcess', type: 'request - fields' + `- ${uniqueIdForLog}`, fields: data })
    try {
      const ondcTransactionIds = data[0].ondc_transaction_id
      const providerIds = data[0].provider_id
      const productIds = _.map(data, 'product_id')

      // Fetch Serach Data
      const fetchMultipleProductsSearchData = await mariaRepository.fetchSearchData({ whereClause: { sessionid: ondcTransactionIds, prov_id: providerIds }, whereInClause: { prov_items_id: productIds }, uniqueIdForLog })
      this.logger({ pagename: basename(__filename), action: 'ondcInitApiProcess', type: 'fetchMultipleProductsSearchData' + `- ${uniqueIdForLog}`, fields: 'fetchMultipleProductsSearchData' })

      // Fetch customer details data
      const fetchCustomerDetails = await transactionRepository.fetchCustomerDetails(request, { connection })
      this.logger({ pagename: basename(__filename), action: 'ondcInitApiProcess', type: 'fetchCustomerDetails' + `- ${uniqueIdForLog}`, fields: fetchCustomerDetails })

      const initResponse = await preTransactionIntegration.init(data, fetchMultipleProductsSearchData, fetchCustomerDetails, uniqueIdForLog, request)
      return { ...initResponse, cartData: data, searchData: fetchMultipleProductsSearchData, customerData: fetchCustomerDetails }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'ondcInitApiProcess', type: 'err' + `- ${uniqueIdForLog}`, fields: err })
      if (common.isCustomError(err)) return null
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }

  /** API ++++++++++
   * ondcCartCheckout description - add/update product in cart table
   * @typedef {{ ma_user_id: number, userid: number, mobile_id: String }} requestData
   * @param {requestData} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, mobile_id: String, cart_list: Object }>}
   */
  async upateCartData (data, { preOrderRepository, uniqueIdForLog }) {
    this.logger({ pagename: basename(__filename), action: 'upateCartData', type: 'request - fields' + `- ${uniqueIdForLog}`, fields: data })
    try {
      const ondc_transaction_id = data.cartData[0].ondc_transaction_id
      const provider_id = data.cartData[0].provider_id
      const updateCartSellerWise = await preOrderRepository.upateCartData({ whereClause: { provider_id, ondc_transaction_id }, setData: { order_status: data.status != 200 ? 'Failed' : 'Ordered' } })
      this.logger({ pagename: basename(__filename), action: 'upateCartData', type: 'updateCartSellerWise' + `- ${uniqueIdForLog}`, fields: updateCartSellerWise })

      if (data.status != 200) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, data }
      else return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, data }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'upateCartData', type: 'err' + `- ${uniqueIdForLog}`, fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, data: data || null }
    }
  }

  /** Internal Function
   * initiateOndcTransaction description - Initiate ONDC Transaction
   * @param {{ ma_user_id: number, userid: number, store_id: String, product_id: String, ondc_transaction_id: String, pin: String, amount: String, mailing_id: String, mailing_type?: 'CUSTOMER'|'AGENT', aggregator_order_id: String, mobile_id }} fields
   * @return {{ status: number, message: string, respcode:number, action_code: number }}
   */
  async initiateOndcTransaction (fields, { connection, cartData = {}, customerData = {} }) {
    this.logger({ pagename: basename(__filename), action: 'initiateOndcTransaction', type: 'request', fields: fields })
    try {
      // Create form data for ma_transaction_details table *****
      const transactionDetailsFormData = {
        cart_session_id: fields.cart_session_id,
        mobile_id: cartData.mobile_number,
        customer_name: customerData[0].customer_name,
        email_id: customerData[0].email_id
      }

      // Initiate data for transaction Table *****
      const transactionFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.aggregator_order_id,
        transaction_id: fields.aggregator_order_id,
        amount: fields.amount,
        recon_amount: 0,
        transaction_type: 44,
        transaction_status: 'I',
        remarks: fields.remark || 'ONDC Transaction',
        mobile_number: cartData.mobile_number,
        customer_name: customerData[0].customer_name,
        customer_email: customerData[0].email_id,
        customer_mobile: cartData.mobile_number,
        cms_unique_id: fields.cart_session_id,
        form_data: JSON.parse(JSON.stringify(transactionDetailsFormData)),
        connection
      }

      const transactionController = require('../../transaction/transactionController')
      const transactionResponse = await transactionController.initiateTransaction('_', transactionFields)
      this.logger({ pagename: basename(__filename), action: 'initiateOndcTransaction', type: 'transactionResponse', fields: transactionResponse })

      if (transactionResponse.status != 200) return { ...transactionResponse, action_code: 1001 }
      return { ...transactionResponse, action_code: 1000 }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'initiateOndcTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
    }
  }

  /** Internal Function
   * saveOrderDetails description - INSERT data in ma_ondc_order_details and ma_ondc_product_details table
   * @param {{ ma_user_id: number, userid: number, store_id: String, product_id: String, ondc_transaction_id: String, pin: String, amount: String, mailing_id: String, mailing_type?: 'CUSTOMER'|'AGENT', aggregator_order_id: String, mobile_id: String, initApiRes: Object }} fields
   * @return {{ status: number, message: string, respcode:number, action_code: number }}
   */
  async saveOrderDetails (fieldsData, { connection: con, request, transactionRepository, allSellerWiseAmt }) {
    this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'request', fields: fieldsData })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      // Insert in data in order details table *****
      await mySQLWrapper.beginTransaction(connection)
      const saveOrderDetails = await transactionRepository.saveOrderDetails({
        connection,
        orederData: [[
          request.ma_user_id,
          request.cart_session_id,
          request.aggregator_order_id,
          allSellerWiseAmt,
          request.mailing_id,
          request.mailing_type,
          'PENDING',
          fieldsData[0].cartData[0].mobile_number,
          _.sumBy(fieldsData, fields => { return fields.status == 200 ? parseFloat(fields.sellerWiseCharges) : 0 }) || 0,
          0
        ]]
      })
      this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'saveOrderDetails response', fields: saveOrderDetails })
      if (!saveOrderDetails || saveOrderDetails.affectedRows <= 0) {
        await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'Insertion Fail (ma_ondc_order_details)', respcode: 1001, action_code: 1001 }
      }

      const saveSellerDetails = await Promise.all(Object.values(fieldsData).map(val => this.saveSellerDetails(val, { connection, uniqueIdForLog: `${val.cartData[0].provider_id}-${val.cartData[0].ondc_transaction_id}`, request, insertOrderDetailsData: saveOrderDetails, transactionRepository })))

      await mySQLWrapper.commit(connection)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
      if (isTempConnection) connection.release()
    }
  }

  /** Internal Function
   * saveSellerDetails description - INSERT data in ma_ondc_order_details and ma_ondc_product_details table
   * @param {{ ma_user_id: number, userid: number, store_id: String, product_id: String, ondc_transaction_id: String, pin: String, amount: String, mailing_id: String, mailing_type?: 'CUSTOMER'|'AGENT', aggregator_order_id: String, mobile_id: String, initApiRes: Object }} fields
   * @return {{ status: number, message: string, respcode:number, action_code: number }}
   */
  async saveSellerDetails (sellerData, { connection, request, uniqueIdForLog, insertOrderDetailsData, transactionRepository }) {
    this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'request' + ` - ${uniqueIdForLog}`, fields: sellerData })
    try {
      if (sellerData.status != 200) return null
      sellerData.ondc_order_id = `${await this.generateRamdomId(8)}-${await this.generateRamdomId(4)}-${await this.generateRamdomId(4)}-${await this.generateRamdomId(4)}`

      // Data initialize for ma_ondc_product_details Table *****
      const additionalDetails = sellerData.searchData
      // delete additionalDetails[0].addedon
      let state_descriptor_name_value = 'Serviceable'
      let ondc_org_category = ''
      let ondc_org_TAT = ''
      let ondc_org_provider_name = ''
      let buyer_app_finder_fee_type = ''
      let buyer_app_finder_fee_amount = ''
      let ondc_withholding_amount = '0'
      let unique_key_id = ''

      if (_.get(sellerData, 'getResultsResponse[0].order.fulfillments[0]', null)) {
        const fulfillmentsData = _.get(sellerData, 'getResultsResponse[0].order.fulfillments[0]', null)
        state_descriptor_name_value = _.get(fulfillmentsData, 'state.descriptor.name', 'Serviceable')
        ondc_org_category = _.get(fulfillmentsData, '@ondc/org/category', '')

        this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'ondc_org_category with slash' + ` - ${uniqueIdForLog}`, fields: (fulfillmentsData['@ondc/org/TAT']) ? fulfillmentsData['@ondc/org/category'] : '' })
        this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'inserOrderDetailsData without slash' + ` - ${uniqueIdForLog}`, fields: (fulfillmentsData['@ondc/org/TAT']) ? fulfillmentsData['@ondc/org/category'] : '' })
        ondc_org_TAT = _.get(fulfillmentsData, '@ondc/org/TAT', '')
        ondc_org_provider_name = _.get(fulfillmentsData, '@ondc/org/provider_name', '')
      }
      if (sellerData.getResultsResponse[0].order.fulfillments && sellerData.getResultsResponse[0].order.payment) {
        const payment = sellerData.getResultsResponse[0].order.payment
        buyer_app_finder_fee_type = _.get(payment, '@ondc/org/buyer_app_finder_fee_type', '')
        buyer_app_finder_fee_amount = _.get(payment, '@ondc/org/buyer_app_finder_fee_amount', '')
        ondc_withholding_amount = _.get(payment, '@ondc/org/ondc-withholding_amount', '0')
      }
      if (sellerData.getResultsResponse[0]) unique_key_id = _.get(sellerData, 'getResultsResponse[0].unique_key_id', '')

      const saveSellerDetails = await transactionRepository.saveSellerDetails({
        connection,
        sellerData: [[
          insertOrderDetailsData.insertId,
          sellerData.cartData[0].ondc_transaction_id,
          sellerData.searchData[0].search_bpp_id,
          sellerData.sellerWiseQnt || 0,
          sellerData.sellerWiseAmt || 0,
          sellerData.sellerWiseCharges || 0,
          (sellerData.sellerWiseAmt || 0) + (sellerData.sellerWiseCharges || 0) || 0,
          JSON.stringify(sellerData.getResultsResponse),
          state_descriptor_name_value,
          ondc_org_category,
          ondc_org_TAT,
          ondc_org_provider_name,
          buyer_app_finder_fee_type,
          buyer_app_finder_fee_amount,
          ondc_withholding_amount,
          unique_key_id,
          sellerData.ondc_order_id,
          sellerData.cartData[0].provider_id
        ]]
      })
      this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'saveSellerDetails - response' + ` - ${uniqueIdForLog}`, fields: saveSellerDetails })
      if (saveSellerDetails.affectedRows <= 0) {
        // await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'Insertion Fail (ma_ondc_seller_details)', respcode: 1001, action_code: 1001 }
      }

      const productData = await Promise.all(Object.values(sellerData.cartData).map(val => {
        const searchData = _.filter(additionalDetails, function (search_data) { return val.provider_id == search_data.prov_id && val.product_id == search_data.prov_items_id && val.ondc_transaction_id == search_data.sessionid })
        this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'searchData - searchData' + ` - ${uniqueIdForLog}` + ` - ${val.product_id}`, fields: searchData })
        delete searchData.addedon

        return [
          insertOrderDetailsData.insertId,
          saveSellerDetails.insertId,
          request.ma_user_id,
          val.ondc_transaction_id,
          val.provider_id,
          val.product_id,
          val.product_quantity || 0,
          val.product_price || 0,
          val.product_total_price || 0,
          'Pending',
          val.product_name || '',
          searchData[0].prov_items_desc_images,
          JSON.stringify(searchData),
          searchData[0].prov_items_ondc_cancel == 1 ? 'Y' : 'N',
          searchData[0].prov_items_ondc_return == 1 ? 'Y' : 'N'
        ]
      }))
      const saveProductDetails = await transactionRepository.saveProductDetails({
        connection,
        productData
      })
      this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'saveProductDetails - response' + ` - ${uniqueIdForLog}`, fields: saveProductDetails })
      if (saveProductDetails.affectedRows <= 0) {
        // await mySQLWrapper.rollback(connection)
        return { status: 400, message: 'Insertion Fail (ma_ondc_product_details)', respcode: 1001, action_code: 1001 }
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000 }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'catcherror' + ` - ${uniqueIdForLog}`, fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
    //   if (tempConnection) connection.release()
    //   mariaDBConnection.release()
    }
  }

  /** Internal Function
   * generateRamdomId description
   * @param {{ requestData: String }} fields
   * @returns {Promise < ecryptedData: String >}
   */
  async generateRamdomId (length) {
    this.logger({ pagename: basename(__filename), action: 'generateRamdomId', type: 'length', fields: length })
    try {
      let id = ''
      const characters = '0123456789abcdefghijklmnopqrstuvwxyz'
      for (let i = 0; i < length; i++) id += characters.charAt(Math.floor(Math.random() * characters.length))
      this.logger({ pagename: basename(__filename), action: 'generateRamdomId', type: 'id', fields: id })
      return id
    } catch (axiosErr) {
      this.logger({ pagename: basename(__filename), action: 'generateRamdomId', type: 'axios catcherror', fields: axiosErr })
      return ''
    }
  }

  /**
   * ondcLedgerEntries description - ONDC Ledger Entries
   * @param {{ ma_user_id: number, userid: number, amount: String, aggregator_order_id: String, mobile_id }} fields
   * @return {Promise<{ status: number, message: string, respcode:number, action_code: number }>}
   */
  async ondcLedgerEntries (fields, con) {
    this.logger({ pagename: basename(__filename), action: 'ondcedgerEntries', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const ledgers = require('../../ledgerEntries/ledgerEntriesController')
      const incentives = require('../../incentive/ondcDistributionController')
      const getDistributionFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_id,
        amount: fields.amount,
        aggregator_order_id: fields.aggregator_order_id
      }
      const configuration = await incentives.getDistribution(getDistributionFields, connection)
      this.logger({ pagename: basename(__filename), action: 'ondcedgerEntries', type: 'configuration', fields: configuration })

      // Ledger Entry data *****
      const ledgerFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        airpayMaUserId: util.airpayUserId,
        airpayUserId: fields.userid,
        amount: fields.amount,
        orderid: fields.aggregator_order_id,
        customer_charge: (configuration[0] || {}).customer_charges || 0,
        inclusive_flag: fields.inclusive_flag
      }

      // Begin Transaction
      await mySQLWrapper.beginTransaction(connection)

      const ledgersResponse = await ledgers.ondcLedgerEntries(ledgerFields, connection)
      this.logger({ pagename: basename(__filename), action: 'ondcedgerEntries', type: 'ledgersResponse', fields: ledgersResponse })
      if (ledgersResponse.status != 200) {
        await mySQLWrapper.rollback(connection)
        return { ...ledgersResponse, action_code: 1001 }
      }

      // Commit Transaction
      await mySQLWrapper.commit(connection)

      return ledgersResponse
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'ondcLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /** Internal Function
   * ondcConfirmApi description - Call MS side Confirm API for ondcVerifyPin function
   * @param {{ store_id: String, product_id: String, ondc_transaction_id: String, mailing_id: String, mailing_type?: 'CUSTOMER'|'AGENT', store_id: String, product_id: String }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number }>}
   */
  async ondcConfirmApi (fields, { connection = null, transactionRepository, preTransactionIntegration, uniqueIdForLog, request }) {
    this.logger({ pagename: basename(__filename), action: 'ondcConfirmApi', type: 'request' + ` - ${uniqueIdForLog}`, fields })
    try {
      if (fields.searchData.length <= 0) return { status: 400, respcode: 1001, message: 'Search details not found', action_code: 1001, confirmDataSellerWise: fields }
      const searchData = fields.searchData

      if (fields.cartData.length <= 0) return { status: 400, respcode: 1001, message: 'Cart details not found', action_code: 1001, confirmDataSellerWise: fields }
      const cartData = fields.cartData

      // Fetch customer data for CONFIRM API request *****
      if (fields.customerData.length <= 0) return { status: 400, respcode: 1001, message: 'Cart details not found', action_code: 1001, confirmDataSellerWise: fields }
      const customerData = fields.customerData

      const quoteData = _.get(fields, 'getResultsResponse[0].order.quote', false)
      const paymentData = _.get(fields, 'getResultsResponse[0].order.payment', false)
      if (!quoteData || !paymentData) return { status: 400, respcode: 1001, message: 'Invalid INIT API response paymentData', action_code: 1001 }

      const confirmResponse = await preTransactionIntegration.confirm({ fields, searchData, cartData, customerData, uniqueIdForLog, request, transactionRepository, connection })
      this.logger({ pagename: basename(__filename), action: 'ondcConfirmApi', type: 'confirmResponse' + ` - ${uniqueIdForLog}`, fields: confirmResponse })

      let whereClause = { ondc_transaction_id: cartData[0].ondc_transaction_id, provider_id: cartData[0].provider_id }
      let setData = { product_order_status: (confirmResponse.status != 200 && confirmResponse.respcode == 1028) ? 'Cancelled' : (confirmResponse.status != 200 && confirmResponse.respcode != 1028) ? 'Pending' : 'Success' }
      let upateTableData = await transactionRepository.upateTableData({ connection, whereClause, setData, tableName: 'ma_ondc_product_details' })
      this.logger({ pagename: basename(__filename), action: 'ondcConfirmApi', type: 'upateTableData - ma_ondc_product_details ' + ` - ${uniqueIdForLog}`, fields: upateTableData })

      if (confirmResponse.status != 200 && confirmResponse.respcode == 1028) {
        whereClause = { ondc_transaction_id: cartData[0].ondc_transaction_id, search_bpp_id: searchData[0].search_bpp_id, provider_id: cartData[0].provider_id }
        setData = { shipping_status: 'Cancelled' }
        upateTableData = await transactionRepository.upateTableData({ connection, whereClause, setData, tableName: 'ma_ondc_seller_details' })
        this.logger({ pagename: basename(__filename), action: 'ondcConfirmApi', type: 'upateTableData - ma_ondc_seller_details ' + ` - ${uniqueIdForLog}`, fields: upateTableData })
      }

      return { ...confirmResponse, confirmDataSellerWise: fields }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'ondcConfirmApi', type: 'err' + ` - ${uniqueIdForLog}`, fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, confirmDataSellerWise: fields }
    } finally {
      // Release the connection
      // if (tempConnection) connection.release()
      // mariaDBconnection.release()
    }
  }
}
