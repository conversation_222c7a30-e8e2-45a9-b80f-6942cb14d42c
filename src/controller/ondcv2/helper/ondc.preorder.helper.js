// Package Import
const { basename } = require('path')
const _ = require('lodash')
const moment = require('moment')

// Code Import
const HelperError = require('../../../util/errors/HelperError')
const common = require('../../../util/common')
const errorMsg = require('../config/errors')

module.exports = class OndcPreOrderHelper {
  constructor (logger) {
    this.logger = logger || console.log
  }

  async formatShopDetailsAndProductList (item) {
    let product_image = item.prov_items_desc_images
    let shop_image = item.prov_image
    let product_thumbnail = null
    try {
      shop_image = JSON.parse(shop_image || null)
      product_image = JSON.parse(product_image || null)
      shop_image = shop_image[0]
      product_thumbnail = product_image[0]
    } catch (e) {
      this.logger({ pagename: basename(__filename), action: 'formatShopDetailsAndProductList', type: 'json parse error', fields: e })
    }

    // Set Shop Details if empty
    const shopDetails = {
      shop_id: _.unescape(item.prov_id),
      shop_name: _.unescape(item.prov_name),
      short_desc: _.unescape(item.prov_short_desc),
      long_desc: _.unescape(item.prov_long_desc),
      shop_image: _.unescape(shop_image || null),
      locations: [{
        id: _.unescape(item.prov_loc_id),
        gps: _.unescape(item.prov_loc_gps),
        address: {
          door: _.unescape(item.prov_loc_addr_door),
          name: _.unescape(item.prov_loc_addr_name),
          street: _.unescape(item.prov_loc_addr_street),
          building: _.unescape(item.prov_loc_addr_building),
          locality: _.unescape(item.prov_loc_addr_locality),
          ward: _.unescape(item.prov_loc_addr_ward),
          city: _.unescape(item.prov_loc_addr_city),
          area_code: _.unescape(item.prov_loc_addr_area_code),
          state: _.unescape(item.prov_loc_addr_state)
        }
      }]
    }

    // Product List
    const productList = {
      product_id: _.unescape(item.prov_items_id),
      product_name: _.unescape(item.prov_items_desc_name),
      product_image,
      product_thumbnail,
      product_short_desc: _.unescape(item.prov_items_desc_short_desc),
      product_long_desc: _.unescape(item.prov_items_desc_long_desc),
      product_price: {
        currency: _.unescape(item.prov_items_price_curr),
        value: '' + (item.prov_items_price_value.toFixed(2))
      }
    }
    return { shopDetails, productList }
  }

  async fetchData (dataObjs) {
    const insertBulkData = []
    const storeDistanceObj = {}
    await Promise.all(dataObjs.response.map(async (ondcData, catalog_index) => {
      catalog_index++
      if (_.get(ondcData, 'catalog.bpp/providers', []).length == 0) return
      await Promise.all(_.get(ondcData, 'catalog.bpp/providers', []).map(async (bbpProviders, bpp_provider_index) => {
        bpp_provider_index++
        const prov_ondc_fssai_license_no = _.escape(_.get(bbpProviders, '@ondc/org/fssai_license_no')) || null
        const expiry_dt = _.get(bbpProviders, 'exp', '')

        // create storeDistanceObj with store id and distance
        if (!storeDistanceObj[_.get(bbpProviders, 'id')]) {
          storeDistanceObj[_.get(bbpProviders, 'id')] = _.get(bbpProviders, 'locations[0].gps') ? await this.calcDistance(dataObjs.delivery_location, _.get(bbpProviders, 'locations[0].gps')) : null
        }

        const insertData = [
          dataObjs.ma_user_id || null,
          dataObjs.userid || null,
          dataObjs.search_txn_id || null, // sessionid
          ondcData.REQUEST_ID || null,
          dataObjs.search_txn_id || null,
          dataObjs.search_msg_id || null,
          ondcData.bpp_id, // search_bpp_id
          ondcData.unique_key_id, // search_unq_id
          ondcData.subscriber_id, // search_subs_id
          _.escape(_.get(bbpProviders, 'id')) || null, // prov_id
          _.escape(_.get(bbpProviders, 'descriptor.name')) || null, // prov_name
          _.escape(_.get(bbpProviders, 'descriptor.symbol')) || null, // prov_symbol
          _.escape(_.get(bbpProviders, 'descriptor.short_desc')) || null, // prov_short_desc
          _.escape(_.get(bbpProviders, 'descriptor.long_desc')) || null, // prov_long_desc
          JSON.stringify(_.get(bbpProviders, 'descriptor.images') || []), // prov_image
          !_.isEmpty(prov_ondc_fssai_license_no) ? prov_ondc_fssai_license_no : null, // prov_ondc_fssai_license_no
          _.escape(_.get(bbpProviders, 'ttl')) || null, // prov_ttl
          _.escape(_.get(bbpProviders, 'locations[0].id')) || null, // prov_loc_id
          _.escape(_.get(bbpProviders, 'locations[0].gps')) || null, // prov_loc_gps
          _.escape(_.get(bbpProviders, 'locations[0].address.door')) || null, // prov_loc_addr_door
          _.escape(_.get(bbpProviders, 'locations[0].address.name')) || null, // prov_loc_addr_name
          _.escape(_.get(bbpProviders, 'locations[0].address.building')) || null, // prov_loc_addr_building
          _.escape(_.get(bbpProviders, 'locations[0].address.street')) || null, // prov_loc_addr_street
          _.escape(_.get(bbpProviders, 'locations[0].address.locality')) || null, // prov_loc_addr_locality
          _.escape(_.get(bbpProviders, 'locations[0].address.ward')) || null, // prov_loc_addr_ward
          _.escape(_.get(bbpProviders, 'locations[0].address.city')) || null, // prov_loc_addr_city
          _.escape(_.get(bbpProviders, 'locations[0].address.state')) || null, // prov_loc_addr_state
          _.escape(_.get(bbpProviders, 'locations[0].address.country')) || null, // prov_loc_addr_country
          _.toInteger(_.get(bbpProviders, 'locations[0].address.area_code')) || null, // prov_loc_addr_area_code
          moment(expiry_dt, 'YYYY-MM-DDTH:m:s:a').isValid() ? moment(expiry_dt, 'YYYY-MM-DDTH:m:s:a').format('YYYY-MM-DD H:m:s') : null,
          // 'asdd',
          _.toInteger(_.get(bbpProviders, 'rating')) || null, // prov_rateable
          JSON.stringify(_.get(ondcData, 'catalog.bpp/fulfillments', [])), // fulfilment_type
          storeDistanceObj[_.get(bbpProviders, 'id')] || null // store_distance
        ]

        const itemsData = []
        await Promise.all(_.get(bbpProviders, 'items', []).map(async (itemData, item_index) => _.toInteger(_.get(itemData, 'price.value')) > 0 && itemsData.push([
          ...insertData,
          +`${catalog_index}${bpp_provider_index}${item_index}`, // bpp_index_order
          itemData.id, // prov_items_id
          _.escape(_.get(itemData, 'descriptor.name')) || null, // prov_items_desc_name
          _.escape(_.get(itemData, 'descriptor.symbol')) || null, // prov_items_desc_symbol
          _.escape(_.get(itemData, 'descriptor.short_desc')) || null, // prov_items_desc_short_desc
          _.escape(_.get(itemData, 'descriptor.long_desc')) || null, // prov_items_desc_long_desc
          JSON.stringify(_.get(itemData, 'descriptor.images') || []), // prov_items_desc_images
          _.escape(_.get(itemData, 'price.currency')) || null, // prov_items_price_curr
          !isNaN(_.get(itemData, 'price.value')) ? _.get(itemData, 'price.value') : null, // prov_items_price_value
          !isNaN(_.get(itemData, 'price.est_value')) ? _.get(itemData, 'price.est_value') : null, // prov_items_price_est_value
          !isNaN(_.get(itemData, 'price.comp_val')) ? _.get(itemData, 'price.comp_val') : null, // prov_items_price_comp_val
          !isNaN(_.get(itemData, 'price.list_val')) ? _.get(itemData, 'price.list_val') : null, // prov_items_price_list_val
          !isNaN(_.get(itemData, 'price.off_val')) ? _.get(itemData, 'price.off_val') : null, // prov_items_price_off_val
          !isNaN(_.get(itemData, 'price.minimum_value')) ? _.get(itemData, 'price.minimum_value') : null, // prov_items_price_min_val
          !isNaN(_.get(itemData, 'price.maximum_value')) ? _.get(itemData, 'price.maximum_value') : null, // prov_items_price_max_val
          _.escape(_.get(itemData, 'category_id')) || null, // prov_items_cat_id
          _.escape(_.get(itemData, 'fulfillment_id')) || null, // prov_items_ful_id
          _.escape(_.get(itemData, 'location_id')) || null, // prov_items_loc_id
          _.get(itemData, 'matched') ? 1 : 0, // prov_items_matched
          _.get(itemData, 'related') ? 1 : 0, // prov_items_related
          _.get(itemData, 'rateable') ? 1 : 0, // prov_items_rateable
          _.get(itemData, 'recommended') ? 1 : 0, // prov_items_recomm
          _.get(itemData, '@ondc/org/returnable') ? 1 : 0, // prov_items_ondc_return
          _.get(itemData, '@ondc/org/cancellable') ? 1 : 0, // prov_items_ondc_cancel
          _.get(itemData, '@ondc/org/seller_pickup_return') ? 1 : 0, // prov_items_ondc_sell_pick_return
          _.escape(_.get(itemData, '@ondc/org/time_to_ship')) || null, // prov_items_ondc_time_to_ship
          _.get(itemData, '@ondc/org/available_on_cod') ? 1 : 0, // prov_items_ondc_cod
          _.escape(_.get(itemData, '@ondc/org/contact_details_consumer_care')) || null, // prov_items_ondc_customer_care
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.description')) || null, // prov_items_ondc_statutory_req_desc
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.manufacturer_or_packer_name')) || null, // prov_items_ondc_statutory_req_maf_name
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.manufacturer_or_packer_address')) || null, // prov_items_ondc_statutory_req_addr
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.common_or_generic_name_of_commodity')) || null, // prov_items_ondc_statutory_req_generic_name
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.net_quantity_or_measure_of_commodity_in_pkg')) || null, // prov_items_ondc_statutory_req_net_qua
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.month_year_of_manufacture_packing_import')) || null, // prov_items_ondc_statutory_req_maf_year
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_packaged_commodities.imported_product_country_of_origin')) || null, // prov_items_ondc_statutory_req_origin_country
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_prepackaged_food.nutritional_info')) || null, // prov_items_ondc_statutory_req_nutri_info
          _.escape(_.get(itemData, '@ondc/org/statutory_reqs_prepackaged_food.additives_info')) || null, // prov_items_ondc_statutory_req_add_info
          _.escape(_.get(itemData, '@ondc/org/mandatory_reqs_veggies_fruits.net_quantity')) || null, // prov_items_ondc_statutory_req_net_quantity_display
          _.escape(_.get(itemData, '@ondc/org/return_window')) || null, // prov_items_ondc_return_window
          null, // prov_items_ondc_statutory_req_tag_veg
          null // prov_items_ondc_statutory_req_tag_non_veg
        ])))

        insertBulkData.push(...itemsData)
      }))
    }))

    return insertBulkData
  }

  // calculate distance in KM base on the lat and lon
  async calcDistance (delivery_location, store_location = '') {
    console.log('calcDistance request =====', { delivery_location, store_location })
    try {
      const delivery_location_array = delivery_location.split(',')
      const store_location_array = store_location.split(',')
      let lat1 = parseFloat(delivery_location_array[0].trim())
      const lon1 = parseFloat(delivery_location_array[1].trim())
      let lat2 = parseFloat(store_location_array[0].trim())
      const lon2 = parseFloat(store_location_array[1].trim())

      const R = 6371
      const dLat = await this.toRad(lat2 - lat1)
      const dLon = await this.toRad(lon2 - lon1)

      lat1 = await this.toRad(lat1)
      lat2 = await this.toRad(lat2)

      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
      const distance = R * c

      return parseFloat(distance).toFixed(2)
    } catch (err) {
      console.log('calcDistance catch error =====', err)
      return null
    }
  }

  async toRad (value) {
    return value * Math.PI / 180
  }

  /** Internal Function
   * formatOndcSearchApiData description - Format Search API data to send this data to frontend
   * @param {{ searchApiData: String }} fields
   * @returns {Promise <{ formatedData: { formatedData: *, totalStoreItemsCount: number, totalStoreCount: number }, totalStoreItemsCount: number, totalStoreCount: number }>}
   */
  async formatOndcSearchApiData ({ searchApiData, fields }) {
    this.logger({ pagename: basename(__filename), action: 'formatOndcSearchApiData', type: 'request', fields: JSON.stringify(searchApiData) })
    try {
      if (searchApiData.length <= 0) throw new HelperError({ status: 400, respcode: 2003, message: errorMsg.responseCode[2003], action_code: 1001 })
      let formatedData = {}
      let totalStoreItemsCount = 0
      let totalStoreCount = 0
      searchApiData.forEach((ondcData) => {
        if (_.get(ondcData, 'catalog.bpp/providers.length', 0) <= 0) return

        // Loop for bpp/providers
        _.get(ondcData, 'catalog.bpp/providers', []).forEach((store) => {
          // Check items count greater than 0 or not in bpp/providers obj
          if (_.get(store, 'items.length', 0) <= 0) return
          // Check store id already present in Obj or not
          if (!_.get(formatedData, store.id) && Object.keys(formatedData).length < fields.storeLimit) {
            // This is for Store Pagination limit
            if (Object.keys(formatedData).length < fields.storeLimit) {
              formatedData[store.id] = {
                shop_details: {
                  shop_id: store.id,
                  shop_name: _.get(store, 'descriptor.name', '').replace(/\s{2,}/g, ' ').trim(),
                  short_desc: _.get(store, 'descriptor.short_desc', '').replace(/\s{2,}/g, ' ').trim(),
                  long_desc: _.get(store, 'descriptor.long_desc', '').replace(/\s{2,}/g, ' ').trim(),
                  shop_image: _.get(store, 'descriptor.images[0]', ''),
                  locations: _.get(store, 'descriptor.locations', [])
                },
                storewise_items_count: 0
              }
            }
            totalStoreCount++
          }

          // Loop for itesm *****
          store.items.forEach((item) => {
            if (_.toNumber(_.get(item, 'price.value')) <= 0) return

            if (store.id in formatedData) {
              if (!formatedData[store.id].product_list || (formatedData[store.id].product_list && Object.keys(formatedData[store.id].product_list).length < fields.itemsLimit)) { // This is for Items Pagination Limit
                const prodData = {
                  product_id: _.get(item, 'id', ''),
                  product_name: _.get(item, 'descriptor.name', ''),
                  product_image: _.get(item, 'descriptor.images', []),
                  product_thumbnail: _.get(item, 'descriptor.images[0]', null),
                  product_short_desc: _.get(item, 'descriptor.short_desc', ''),
                  product_long_desc: _.get(item, 'descriptor.long_desc', ''),
                  product_price: {
                    currency: _.get(item, 'price.currency', ''),
                    value: (+_.get(item, 'price.value', '0')).toFixed(2)
                  }
                }
                if (!('product_list' in formatedData[store.id])) formatedData[store.id] = { ...formatedData[store.id], product_list: [] } // If product_list key not present then add this field(product_list).
                formatedData[store.id].product_list.push(prodData)
              }
              formatedData[store.id].storewise_items_count++
            }
            totalStoreItemsCount++
          })
        })
      })

      // Conevert Object into Array of Object *****
      formatedData = Object.values(formatedData)
      if (formatedData.length <= 0) throw new HelperError({ status: 400, respcode: 2003, message: errorMsg.responseCode[2003], action_code: 1001 })
      this.logger({ pagename: basename(__filename), action: 'formatOndcSearchApiData', type: 'formatedData 2', fields: JSON.stringify({ formatedData, totalStoreItemsCount, totalStoreCount }) })
      const formatedData1 = []
      formatedData.map((key) => {
        if (key.product_list && key.product_list.length > 0) formatedData1.push(key)
        else if (key.storewise_items_count > 0 && (!key.product_list || key.product_list.length <= 0)) {
          totalStoreItemsCount = _.toNumber(totalStoreItemsCount) - _.toNumber(key.storewise_items_count)
          totalStoreCount--
        } else totalStoreCount--
      })
      formatedData = formatedData1
      this.logger({ pagename: basename(__filename), action: 'formatOndcSearchApiData', type: 'formatedData 3', fields: JSON.stringify({ formatedData, totalStoreItemsCount, totalStoreCount }) })

      formatedData = { formatedData, totalStoreItemsCount, totalStoreCount }
      // Final response *****
      return { formatedData, totalStoreItemsCount, totalStoreCount }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'formatOndcSearchApiData', type: 'err', fields: err })
      if (common.isCustomError(err)) throw err
      throw new HelperError({ status: 400, message: errorMsg.responseCode[1001], action_code: 1001 })
    } finally {
      // connection.release()
    }
  }

  /** saveCartProductDetails ++++++++++
   * @typedef {{ selectResponse: Object, request: Object }}
   * @returns {Promise<{ fetchOndcResult: { status: 200, respcode: number, message: string, action_code: 1000 } }>}
   */
  async createCartDetailsForInsertOrUpate (selectResponse, request) {
    this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'request Data', fields: JSON.stringify({ selectResponse, request }) })
    try {
      let finalResponse = []
      const insertCartData = []
      const updateCartData = []
      const deleteCartData = []
      const insertCartDataAgain = []
      const breakout = {
        baseprice: 0.00,
        price_without_charges: 0.00,
        shipping_charges: 0.00,
        final_amount: 0.00,
        price_currency: 'INR'
      }
      for (const selectResp in selectResponse) {
        // Add parameters in final response
        finalResponse = [...finalResponse, ...(await selectResponse[selectResp].provier_wise_data.map(val => ({
          status: selectResponse[selectResp].fetchOndcResult ? 400 : 200,
          message: selectResponse[selectResp].fetchOndcResult ? 'Fail: Product Not Serviceable!' : 'Success: Product Serviceable!',
          product_id: val.product_id,
          provider_id: val.provider_id,
          ondc_transaction_id: val.ondc_transaction_id,
          product_quantity: val.quantity,
          product_image: JSON.parse(_.get(val, 'searchData[0].prov_items_desc_images', null)) || null,
          product_name: _.unescape(_.get(val, 'searchData[0].prov_items_desc_name', '')),
          baseamount: parseFloat(_.get(val, 'searchData[0].prov_items_price_value', 0)).toFixed(2),
          proucttTotalPriceWithoutCharges: (parseFloat(_.get(val, 'searchData[0].prov_items_price_value', 0)) * _.toNumber(val.quantity)).toFixed(2)
        })))]

        if (selectResponse[selectResp].fetchOndcResult && selectResponse[selectResp].fetchOndcResult.status == 200) {
          // Valiation
          // 1. error
          if (_.get(selectResponse[selectResp], 'fetchOndcResult.getResultsResponse[0].error', null)) {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : error', fields: null })
            continue
          }
          // 2. order.fulfillments[0].state.descriptor.name
          if (_.get(selectResponse[selectResp], 'fetchOndcResult.getResultsResponse[0].order.fulfillments[0].state.descriptor.name', null) != 'Serviceable') {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : descriptor.name', fields: null })
            continue
          }
          // 3. order data validation
          const getOrderResponse = _.get(selectResponse[selectResp], 'fetchOndcResult.getResultsResponse[0].order', null)
          if (!getOrderResponse) {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : order', fields: null })
            continue
          }

          // Fetch total price provider(seller) wise
          const totalSellerWiseProductPrice = _.get(getOrderResponse, 'quote.price.value', null)
          const totalSellerWiseProductCurrency = _.get(getOrderResponse, 'quote.price.currency', null)
          const totalSellerWiseProductBreakup = _.get(getOrderResponse, 'quote.breakup', null)
          if (!totalSellerWiseProductPrice || !totalSellerWiseProductCurrency || (!totalSellerWiseProductBreakup || totalSellerWiseProductBreakup.length <= 0)) {
            this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : totalSellerWiseProductBreakup', fields: null })
            continue
          }

          // Fetch total delivery charges provider(seller) wise
          const commonDeliveryChargeBreakup = _.find(totalSellerWiseProductBreakup, { '@ondc/org/title_type': 'delivery' }) || {}
          const commonDeliveryChargePrice = _.get(commonDeliveryChargeBreakup, 'price.value', 0)
          const commonDeliveryChargeCurrency = _.get(commonDeliveryChargeBreakup, 'price.currency', 'INR')
          const provierWiseData = _.get(selectResponse[selectResp], 'provier_wise_data', null)

          // Calculate breakout value
          breakout.price_without_charges = (parseFloat(breakout.price_without_charges) + parseFloat(totalSellerWiseProductPrice) - parseFloat(commonDeliveryChargePrice || 0)).toFixed(2)
          breakout.shipping_charges = (parseFloat(breakout.shipping_charges) + parseFloat(commonDeliveryChargePrice || 0)).toFixed(2)
          breakout.final_amount = (parseFloat(breakout.final_amount) + parseFloat(totalSellerWiseProductPrice)).toFixed(2)

          // Loop for product
          for (const providerWise in provierWiseData) {
            // Validation *****
            // 1. prouctWiseBreakup valiation
            const prouctWiseBreakup = _.find(totalSellerWiseProductBreakup, { '@ondc/org/item_id': provierWiseData[providerWise].product_id, '@ondc/org/title_type': 'item' })
            if (!prouctWiseBreakup) {
              this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : prouctWiseBreakup', fields: null })
              break
            }

            // Fetch product price details
            const productWisePrice = _.get(prouctWiseBreakup, 'price.value', null)
            const prductWiseCurrency = _.get(prouctWiseBreakup, 'price.currency', null)
            const prductWiseCount = _.get(prouctWiseBreakup, '@ondc/org/item_quantity.count', null)
            if (prductWiseCount != provierWiseData[providerWise].quantity) {
              this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'Valiation Error : quantity', fields: null })
              break
            }

            // If product data already present in cart table then push the data in updateCartData array
            insertCartData.push([
              request.ma_user_id,
              provierWiseData[providerWise].ondc_transaction_id,
              provierWiseData[providerWise].provider_id,
              _.get(provierWiseData[providerWise], 'searchData[0].prov_items_desc_name', null),
              provierWiseData[providerWise].product_id,
              provierWiseData[providerWise].quantity,
              _.get(provierWiseData[providerWise], 'searchData[0].prov_items_price_value', null),
              productWisePrice,
              commonDeliveryChargePrice,
              'Incart',
              request.cart_session_id
            ])

            finalResponse[_.toNumber(finalResponse.length) - _.toNumber(provierWiseData.length) + _.toNumber(providerWise)] = { ...finalResponse[_.toNumber(finalResponse.length) - _.toNumber(provierWiseData.length) + _.toNumber(providerWise)], status: 200, message: 'Product Serviceable!' }
          }
        } else if (selectResponse[selectResp].fetchMultipleProductCartData && selectResponse[selectResp].fetchMultipleProductCartData.length > 0) {
          const productWisePriceWithoutCharges = _.sumBy(selectResponse[selectResp].fetchMultipleProductCartData, 'product_total_price')
          const product_shipping_charges = _.get(selectResponse[selectResp], 'fetchMultipleProductCartData[0].delivery_charges', 0)
          // Calculate breakout value
          breakout.price_without_charges = (parseFloat(breakout.price_without_charges) + parseFloat(productWisePriceWithoutCharges)).toFixed(2)
          breakout.shipping_charges = (parseFloat(breakout.shipping_charges) + parseFloat(product_shipping_charges || 0)).toFixed(2)
          breakout.final_amount = (parseFloat(breakout.final_amount) + parseFloat(productWisePriceWithoutCharges) + parseFloat(product_shipping_charges)).toFixed(2)
        }
      }

      return { status: 200, finalResponse: _.orderBy(finalResponse, ['status'], ['asc']), insertCartData, updateCartData, breakout }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveCartProductDetails', type: 'err', fields: err })
      if (common.isCustomError(err)) return { ...common.getErrorResponse(err) }
      return { status: 400 }
    }
  }
}
