// Package Import
const { basename } = require('path')
const _ = require('lodash')

// Code Import
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const errorMsg = require('../config/errors')
const DAO = require('../../../lib/daov2')
const OndcPreOrderHelper = require('../helper/ondc.preorder.helper')
const RepositoryError = require('../../../util/errors/RepositoryError')

module.exports = class OndcMariaRepository extends DAO {
  constructor (logger) {
    super(logger)
    this.logger = logger
    this.preOrderHelper = new OndcPreOrderHelper(logger)
  }

  /**
   * Creates a new Connection if connection passed in params is undefined.
   * @param {mysqlConnection} con
   * @returns
   */
  async getConnection (con) {
    return [!con, con || await mySQLWrapper.getConnectionFromMariaDb()]
  }

  /**
   * getSearchResults description - Fetches results from db based on ondc_transaction_id
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, ondc_transaction_id: string, storeLimit: number, storeOffset: number, itemsLimit: number, itemsOffset: number, connection: any, sort_object: { price: 'ASC'|'DESC', store_distance: 'ASC'|'DESC', store_name: 'ASC'|'DESC' } }} fields
   * @returns {Promise <{ formatedData: { formatedData, totalStoreItemsCount, totalStoreCount }, totalStoreItemsCount, totalStoreCount }>}
   */
  async getSearchResults (fields) {
    this.logger({ pagename: basename(__filename), action: 'getSearchResults', type: 'request', fields })

    const [isTempConnection, connection] = await this.getConnection(fields.connection)
    try {
      // Sort logic
      let orderByCondition = `ORDER by ISNULL(store_distance) ASC, store_distance ${_.get(fields, 'sort_object.store_distance') || 'ASC'}`
      if (fields.sort_object) {
        if (_.get(fields, 'sort_object.price')) orderByCondition += ` , prov_items_price_value ${_.get(fields, 'sort_object.price')}`
        if (_.get(fields, 'sort_object.store_name')) orderByCondition = `ORDER by prov_name ${_.get(fields, 'sort_object.store_name')}`
      }

      // Fetch Result Logic
      const storeQuery = `SELECT DISTINCT(prov_id) FROM (SELECT prov_id, bpp_index_order FROM ma_ondc_search_results mosr WHERE search_txn_id = ? AND ma_user_id = ? group by prov_id ${orderByCondition}) as temp LIMIT ?`
      const storeCountQuery = 'SELECT COUNT(DISTINCT(prov_id)) as store_count FROM ma_ondc_search_results WHERE search_txn_id = ? AND ma_user_id = ?'
      const itemCountQuery = 'SELECT COUNT(prov_id) as item_count FROM ma_ondc_search_results WHERE search_txn_id = ? AND ma_user_id = ?'

      const [storeResult, storeCount, itemCount] = await Promise.all([
        this.secureRawQuery(storeQuery, {
          connection,
          params: [fields.ondc_transaction_id, fields.ma_user_id, [fields.storeOffset || 0, fields.storeLimit || 4]],
          key: 'storeResult'
        }),
        this.secureRawQuery(storeCountQuery, { connection, params: [fields.ondc_transaction_id, fields.ma_user_id], key: 'storeCount' }),
        this.secureRawQuery(itemCountQuery, { connection, params: [fields.ondc_transaction_id, fields.ma_user_id], key: 'itemCount' })
      ])

      if (_.get(itemCount, '[0].item_count', 0) <= 0) throw new RepositoryError({ status: 400, respcode: 2002, message: errorMsg.responseCode[2002] })
      if (storeResult.length <= 0) throw new RepositoryError({ status: 400, respcode: 2001, message: errorMsg.responseCode[2001] })

      const itemsQuery = `SELECT prov_id, prov_name, prov_short_desc, prov_long_desc, prov_image, prov_loc_id, prov_loc_gps, prov_loc_addr_door, prov_loc_addr_name, prov_loc_addr_street, prov_loc_addr_building, prov_loc_addr_locality, prov_loc_addr_ward, prov_loc_addr_city, prov_loc_addr_area_code, prov_loc_addr_state, prov_items_id, prov_items_desc_name, prov_items_desc_images, prov_items_desc_short_desc, prov_items_desc_long_desc, prov_items_price_curr, prov_items_price_value FROM ma_ondc_search_results mosr WHERE search_txn_id = ? AND ma_user_id = ? AND prov_id = ? ${orderByCondition} LIMIT ?`
      const storeItemCountQuery = 'SELECT count(prov_items_id) as storewise_items_count FROM ma_ondc_search_results WHERE search_txn_id = ? AND ma_user_id = ? and prov_id = ?'

      const formatedData = await Promise.all(storeResult.map(async (store) => {
        const itemsResult = await this.secureRawQuery(itemsQuery, { connection, params: [fields.ondc_transaction_id, fields.ma_user_id, store.prov_id, [fields.itemsOffset || 0, fields.itemsLimit || 4]], key: 'itemsQuery ' + store.prov_id })
        const searchData = {
          storewise_items_count: 0,
          product_list: []
        }

        const [storewise_items_count] = await Promise.all([
          await this.secureRawQuery(storeItemCountQuery, { connection, params: [fields.ondc_transaction_id, fields.ma_user_id, store.prov_id], key: 'storewise_items_count ' + store.prov_id }),
          ...itemsResult.map(async (item) => {
            const { shopDetails, productList } = await this.preOrderHelper.formatShopDetailsAndProductList(item)
            searchData.shop_details = searchData.shop_details || { ...shopDetails }
            searchData.product_list.push(productList)
          })
        ])

        searchData.storewise_items_count = storewise_items_count[0].storewise_items_count

        return searchData
      }))

      this.logger({ pagename: basename(__filename), action: 'getSearchResults', type: 'searchResult', fields: { searchResult: storeResult.length, storeCount, itemCount } })

      const totalStoreItemsCount = +(itemCount[0].item_count) || 0
      const totalStoreCount = +(storeCount[0].store_count) || 0
      return { formatedData: { formatedData, totalStoreItemsCount, totalStoreCount }, totalStoreItemsCount, totalStoreCount }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'getSearchResults', type: 'err', fields: err })
      throw new RepositoryError({ status: 400, respcode: 2002, message: errorMsg.responseCode[2002] })
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /**
   * saveSearchResults description - save search result in mongo db table
   * @param {{ searchResults: String, trasaction_id: String, message_id: String }}
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async saveSearchResults (dataObjs) {
    this.logger({ pagename: basename(__filename), action: 'saveSearchResults', type: 'request', fields: JSON.stringify({ dataObjs }) })

    if (_.isEmpty(dataObjs, 'fetchOndcResult.getResultsResponse')) return false

    try {
      const ondcInsertQuery = 'INSERT INTO ma_ondc_search_results (ma_user_id, userid,sessionid,search_req_id,search_txn_id,search_msg_id,search_bpp_id, search_unq_id, search_subs_id, prov_id, prov_name, prov_symbol, prov_short_desc, prov_long_desc, prov_image, prov_ondc_fssai_license_no, prov_ttl, prov_loc_id, prov_loc_gps, prov_loc_addr_door, prov_loc_addr_name, prov_loc_addr_building, prov_loc_addr_street, prov_loc_addr_locality, prov_loc_addr_ward, prov_loc_addr_city, prov_loc_addr_state, prov_loc_addr_country, prov_loc_addr_area_code, prov_expiry, prov_rateable,fulfilment_type, store_distance, bpp_index_order, prov_items_id, prov_items_desc_name, prov_items_desc_symbol, prov_items_desc_short_desc, prov_items_desc_long_desc, prov_items_desc_images, prov_items_price_curr, prov_items_price_value, prov_items_price_est_value, prov_items_price_comp_val, prov_items_price_list_val, prov_items_price_off_val, prov_items_price_min_val, prov_items_price_max_val, prov_items_cat_id, prov_items_ful_id, prov_items_loc_id, prov_items_matched, prov_items_related, prov_items_rateable, prov_items_recomm, prov_items_ondc_return, prov_items_ondc_cancel, prov_items_ondc_sell_pick_return, prov_items_ondc_time_to_ship, prov_items_ondc_cod, prov_items_ondc_customer_care, prov_items_ondc_statutory_req_desc, prov_items_ondc_statutory_req_maf_name, prov_items_ondc_statutory_req_addr, prov_items_ondc_statutory_req_generic_name, prov_items_ondc_statutory_req_net_qua, prov_items_ondc_statutory_req_maf_year, prov_items_ondc_statutory_req_origin_country, prov_items_ondc_statutory_req_nutri_info, prov_items_ondc_statutory_req_add_info, prov_items_ondc_statutory_req_net_quantity_display, prov_items_ondc_return_window, prov_items_ondc_statutory_req_tag_veg, prov_items_ondc_statutory_req_tag_non_veg) VALUES ?'
      console.time(`${dataObjs.ma_user_id} - saveSearchResults Function Time Log`)

      await Promise.all(_.chunk((await this.preOrderHelper.fetchData(dataObjs)), 50).map(async (chunkedBulkInsert) => {
        const [__, connection] = await this.getConnection()
        try {
          this.secureRawQuery(ondcInsertQuery, { connection, params: [chunkedBulkInsert] })
        } catch (err) {
          this.logger({ pagename: basename(__filename), action: 'saveSearchResults', type: 'err', fields: err })
        } finally {
          connection.release()
        }
      }))

      console.timeEnd(`${dataObjs.ma_user_id} - saveSearchResults Function Time Log`)
      // Final response
      return true
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveSearchResults', type: 'err', fields: err })
      return false
    }
  }

  /** ++++++++++
   * @private
   * fetchMultipleProductsSearchData - fetch multiple search data base on product ids
   * @param {{ ondc_transaction_id: string, provider_id: string, productIds: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchMultipleProductsSearchData ({ connection: con, productIds = [], provider_id = '', ondc_transaction_id = '', uniqueIdForLog = null }) {
    this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductsSearchData', type: 'request' + ` - ${uniqueIdForLog}`, fields: { productIds, provider_id, ondc_transaction_id } })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const searchSql = 'select * from ma_ondc_search_results where sessionid = ? and prov_id = ? and prov_items_id in ?'
      const searchSqlResp = await this.secureRawQuery(searchSql, { connection, params: [ondc_transaction_id, provider_id, [productIds]], key: 'fetchMultipleProductCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductsSearchData', type: 'searchSqlResp' + ` - ${uniqueIdForLog}`, fields: searchSqlResp })
      return searchSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductsSearchData', type: 'catch err' + ` - ${uniqueIdForLog}`, fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** Use###########
   * fetchCartData - save product details like id, name, base price, quantity & delivery charges
   * @param {{ ma_user_id: number, ondc_transaction_id: string, provider_id: string, product_name: string, product_id: string,product_quantity: number,product_price: string,product_total_price: string,delivery_charges: string, order_status: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchProuctCartData ({ connection: con, whereClause = {}, whereInClause = {} }) {
    this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'request', fields: { whereClause, whereInClause } })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let sqlConitions = ''
      const conditionsValArr = []
      Object.keys(whereClause).forEach(function (key, index) {
        if (index === 0) sqlConitions = ` WHERE ${key} = ? `
        else sqlConitions += ` AND ${key} = ? `
        conditionsValArr.push(whereClause[key])
      })

      Object.keys(whereInClause).forEach(function (key, index) {
        if (index === 0) sqlConitions += `${(sqlConitions && conditionsValArr) ? 'AND' : 'WHERE'} ${key} IN ? `
        else sqlConitions += ` AND ${key} IN ? `
        conditionsValArr.push([whereInClause[key]])
      })

      const searchSql = `select * from ma_ondc_product_cart ${sqlConitions || 'LIMIT 1'}`
      this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'cartSql', fields: searchSql })
      this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'conditionsValArr', fields: conditionsValArr })
      const searchSqlResp = await this.secureRawQuery(searchSql, { connection, params: conditionsValArr, key: 'fetchCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'searchSqlResp', fields: searchSqlResp })

      if (searchSqlResp.length <= 0) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      const parsedResult = searchSqlResp
      if (_.isEmpty(parsedResult)) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })

      return searchSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'catch err', fields: err })
      throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** Use###########
   * fetchSearchData - Fetch Search Data
   * @param {{ whereClause: object, whereInClause: object, uniqueIdForLog: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchSearchData ({ connection: con, whereClause = {}, whereInClause = {}, uniqueIdForLog = '' }) {
    this.logger({ pagename: basename(__filename), action: 'fetchSearchData', type: 'request' + `- ${uniqueIdForLog}`, fields: { whereClause, whereInClause } })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let sqlConitions = ''
      const conditionsValArr = []
      Object.keys(whereClause).forEach(function (key, index) {
        if (index === 0) sqlConitions = ` WHERE ${key} = ? `
        else sqlConitions += ` AND ${key} = ? `
        conditionsValArr.push(whereClause[key])
      })

      Object.keys(whereInClause).forEach(function (key, index) {
        if (index === 0) sqlConitions += `${(sqlConitions && conditionsValArr) ? 'AND' : 'WHERE'} ${key} IN ? `
        else sqlConitions += ` AND ${key} IN ? `
        conditionsValArr.push([whereInClause[key]])
      })

      const searchSql = `select * from ma_ondc_search_results ${sqlConitions || 'LIMIT 1'}`
      const searchSqlResp = await this.secureRawQuery(searchSql, { connection, params: conditionsValArr, key: 'fetchCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchSearchData', type: 'searchSqlResp' + `- ${uniqueIdForLog}`, fields: 'searchSqlResp' })

      if (searchSqlResp.length <= 0) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      const parsedResult = searchSqlResp
      if (_.isEmpty(parsedResult)) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })

      return searchSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchSearchData', type: 'catch err' + `- ${uniqueIdForLog}`, fields: err })
      throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
    } finally {
      if (isTempConnection) connection.release()
    }
  }
}
