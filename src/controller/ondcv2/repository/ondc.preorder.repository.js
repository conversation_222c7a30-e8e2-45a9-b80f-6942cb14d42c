// Package Imports
const { basename } = require('path')
const _ = require('lodash')

// Code Imports
const DAO = require('../../../lib/daov2')
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const RepositoryError = require('../../../util/errors/RepositoryError')
const errorMsg = require('../config/errors')

module.exports = class OndcPreOrderRepository extends DAO {
  constructor (logger) {
    super(logger)
    this.logger = logger
  }

  /**
   * Creates a new Connection if connection passed in params is undefined.
   * @param {mysqlConnection} con
   * @returns {Promise<mySQLWrapper>}
   */
  async getConnection (con) {
    return [!con, con || await mySQLWrapper.getConnectionFromPool()]
  }

  async insertSearchRequest ({ connection: con, decryptedSearchRequestData, fields, ondc_transaction_id_for_request, ondc_message_id_for_request }) {
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const insertSearchRequestSql = 'INSERT INTO ma_ondc_search_request (ma_user_id, ms_search_api_request, search_type, search_string, delivery_location, ondc_transaction_id, ondc_message_id) VALUES ?'
      await this.secureRawQuery(insertSearchRequestSql, { connection, params: [[[fields.ma_user_id, JSON.stringify(decryptedSearchRequestData), fields.search_type, fields.search_string, fields.delivery_location, ondc_transaction_id_for_request, ondc_message_id_for_request]]], key: 'insertSearchRequest' })
    } catch (err) {
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * saveProductDetails - save product details like id, name, base price, quantity & delivery charges
   * @param {{ cartData: object [] }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async saveProductDetails ({ connection: con, cartData = [] }) {
    this.logger({ pagename: basename(__filename), action: 'saveProductDetails', type: 'request', fields: cartData })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      // Insert in seller details table *****
      const saveCartDetailInsertSql = 'INSERT INTO ma_ondc_product_cart (ma_user_id, ondc_transaction_id, provider_id, product_name, product_id,product_quantity,product_price, product_total_price, delivery_charges, order_status, cart_session_id) VALUES ?'
      const insertCartRequestSqlRes = await this.secureRawQuery(saveCartDetailInsertSql, { connection, params: [cartData] })
      this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'insertCartRequestSqlRes', fields: insertCartRequestSqlRes })
      return insertCartRequestSqlRes
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveProductDetails', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** Use###########
   * @private
   * fetchCartData - save product details like id, name, base price, quantity & delivery charges
   * @param {{ ma_user_id: number, ondc_transaction_id: string, provider_id: string, product_name: string, product_id: string,product_quantity: number,product_price: string,product_total_price: string,delivery_charges: string, order_status: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchCartData ({ connection: con, whereClause = {} }) {
    this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'request', fields: whereClause })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let sqlConitions = ''
      const conditionsValArr = []
      Object.keys(whereClause).forEach(function (key, index) {
        if (index === 0) sqlConitions = ` WHERE ${key} = '${whereClause[key]}' `
        else sqlConitions += ` AND ${key} = '${whereClause[key]}' `
        conditionsValArr.push(whereClause[key])
      })

      const cartSql = `select product_id, provider_id, ondc_transaction_id, product_quantity as quantity from ma_ondc_product_cart ${sqlConitions || 'LIMIT 1'}`
      this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'cartSql', fields: cartSql })
      this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'conditionsValArr', fields: conditionsValArr })
      const cartSqlResp = await this.secureRawQuery(cartSql, { connection, params: [conditionsValArr], key: 'fetchCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'cartSqlResp', fields: cartSqlResp })

      return cartSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * upateCartData - update cart details
   * @param {{ whereClause: Object, setData: Object }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async upateCartData ({ connection: con, whereClause = {}, setData = {} }) {
    this.logger({ pagename: basename(__filename), action: 'upateCartData', type: 'request', fields: whereClause })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let sqlConitions = ''
      const conditionsValArr = []
      Object.keys(whereClause).forEach(function (key, index) {
        if (index === 0) sqlConitions = ` WHERE ${key} = '${whereClause[key]}' `
        else sqlConitions += ` AND ${key} = '${whereClause[key]}' `
        conditionsValArr.push(whereClause[key])
      })

      let setDataVal = ''
      Object.keys(setData).forEach(function (key, index) {
        if (index === 0) setDataVal = ` SET ${key} = '${setData[key]}' `
        else setDataVal += ` , ${key} = '${setData[key]}' `
      })

      const cartUpdateSql = `UPDATE ma_ondc_product_cart ${setDataVal} ${sqlConitions}`
      const cartUpdateSqlResp = await this.secureRawQuery(cartUpdateSql, { connection, params: conditionsValArr, key: 'cartUpdateSqlResp Response' })

      if (cartUpdateSqlResp.affectedRows <= 0) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      return cartUpdateSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'upateCartData', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * fetchMultipleProductCartData - fetch multiple cart data base on product ids
   * @param {{ ondc_transaction_id: string, provider_id: string, productIds: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchMultipleProductsCartData ({ connection: con, productIds = [], provider_id = '', ondc_transaction_id = '', uniqueIdForLog }) {
    this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductCartData', type: 'request' + ` - ${uniqueIdForLog}`, fields: { productIds, provider_id, ondc_transaction_id } })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const cartSql = 'select * from ma_ondc_product_cart where ondc_transaction_id = ? and provider_id = ? and product_id in ? and order_status = "Incart"'
      const cartSqlResp = await this.secureRawQuery(cartSql, { connection, params: [ondc_transaction_id, provider_id, [productIds]], key: 'fetchMultipleProductCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductCartData', type: 'cartSqlResp' + ` - ${uniqueIdForLog}`, fields: cartSqlResp })
      return cartSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductCartData', type: 'catch err' + ` - ${uniqueIdForLog}`, fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * @private
   * fetchMultipleProductCartData - fetch multiple cart data base on product ids
   * @param {{ ondc_transaction_id: string, provider_id: string, productIds: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchMultipleProviderCartData ({ connection: con, provider_id = '', ondc_transaction_id = '', uniqueIdForLog = null }) {
    this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductCartData', type: 'request' + ` - ${uniqueIdForLog}`, fields: { provider_id, ondc_transaction_id } })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const cartSql = 'select * from ma_ondc_product_cart where ondc_transaction_id = ? and provider_id = ? and order_status = "Incart"'
      const cartSqlResp = await this.secureRawQuery(cartSql, { connection, params: [ondc_transaction_id, provider_id], key: 'fetchMultipleProductCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductCartData', type: 'cartSqlResp' + ` - ${uniqueIdForLog}`, fields: cartSqlResp })
      return cartSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchMultipleProductCartData', type: 'catch err' + ` - ${uniqueIdForLog}`, fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * @private
   * deleteCartData - permanently delete cart data.
   * @param {{ ondc_transaction_id: string, provider_id: string, rondomIdForCart: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async deleteCartData ({ connection: con, provider_id = '', ondc_transaction_id = '', rondomIdForCart = '', uniqueIdForLog = null }) {
    this.logger({ pagename: basename(__filename), action: 'deleteCartData', type: 'request' + ` - ${uniqueIdForLog}`, fields: { rondomIdForCart, provider_id, ondc_transaction_id } })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const deleteSql = 'DELETE FROM ma_ondc_product_cart WHERE ondc_transaction_id = ? and provider_id = ?'
      const deleteResp = await this.secureRawQuery(deleteSql, { connection, params: [ondc_transaction_id, provider_id], key: 'fetchMultipleProductCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'deleteCartData', type: 'deleteSql' + ` - ${uniqueIdForLog}`, fields: deleteResp })
      return deleteResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'deleteCartData', type: 'catch err' + ` - ${uniqueIdForLog}`, fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** Use###########
   * @private
   * fetchCartData - save product details like id, name, base price, quantity & delivery charges
   * @param {{ ma_user_id: number, ondc_transaction_id: string, provider_id: string, product_name: string, product_id: string,product_quantity: number,product_price: string,product_total_price: string,delivery_charges: string, order_status: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchProuctCartData ({ connection: con, whereClause = {} }) {
    this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'request', fields: whereClause })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let sqlConitions = ''
      const conditionsValArr = []
      Object.keys(whereClause).forEach(function (key, index) {
        if (index === 0) sqlConitions = ` WHERE ${key} = '${whereClause[key]}' `
        else sqlConitions += ` AND ${key} = '${whereClause[key]}' `
        conditionsValArr.push(whereClause[key])
      })

      const cartSql = `select * from ma_ondc_product_cart ${sqlConitions || 'LIMIT 1'}`
      const cartSqlResp = await this.secureRawQuery(cartSql, { connection, params: [conditionsValArr], key: 'fetchCartData Response' })
      this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'cartSqlResp', fields: cartSqlResp })

      if (cartSqlResp.length <= 0) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      const parsedResult = cartSqlResp
      if (_.isEmpty(parsedResult)) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })

      return cartSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchProuctCartData', type: 'catch err', fields: err })
      throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
    } finally {
      if (isTempConnection) connection.release()
    }
  }
}
