// Package Imports
const { basename } = require('path')
const _ = require('lodash')

// Code Imports
const DAO = require('../../../lib/daov2')
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const RepositoryError = require('../../../util/errors/RepositoryError')
const errorMsg = require('../config/errors')

module.exports = class OndcTransactionRepository extends DAO {
  constructor (logger) {
    super(logger)
    this.logger = logger
  }

  /**
   * Creates a new Connection if connection passed in params is undefined.
   * @param {mysqlConnection} con
   * @returns {Promise<mySQLWrapper>}
   */
  async getConnection (con) {
    return [!con, con || await mySQLWrapper.getConnectionFromPool()]
  }

  async updateCartMobileID ({ connection: con, mobile_id, ondc_transaction_id }) {
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      await this.secureRawQuery('UPDATE ma_ondc_product_cart SET mobile_number = ? WHERE ondc_transaction_id = ?', {
        connection,
        params: [mobile_id, ondc_transaction_id],
        key: 'updateCartMobileID'
      })
      return true
    } catch (err) {
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  async getCustomerAddresses ({ connection: con, mobile_id, address_id }) {
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const ondcCustomerSql = `SELECT
        ma_ondc_customer_details_id as address_id, mobile_id, customer_name as billing_name, email_id, address1, address2, city, state, pincode, country, address_type
      FROM
        ma_ondc_customer_details
      WHERE
        mobile_id = ? AND address_status = 'A' ${address_id ? 'AND ma_ondc_customer_details_id = ?' : ''}`

      await this.secureRawQuery(ondcCustomerSql, {
        connection,
        params: [mobile_id, address_id],
        key: 'getCustomerAddress'
      })
      return true
    } catch (err) {
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  async getMerchantAddresses ({ connection: con, ma_user_id, userid }) {
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      const merchantSql = `SELECT
            u.ma_user_master_id as address_id, u.mobile_id, u.email_id, CONCAT(u.firstname, ' ', u.lastname) as billing_name, COALESCE(u.address, '') as address1, '' as address2, c.name as city, s.name as state, u.pincode, u.country, 'Office' as address_type
          FROM
            ma_user_master u
          JOIN
            ma_cities_master c
          ON
            u.city = c.id
          JOIN
            ma_states_master s
          ON
            u.state = s.id 
          WHERE
            u.profileid = ? AND u.userid = ?
          LIMIT 1`

      await this.secureRawQuery(merchantSql, {
        connection,
        params: [ma_user_id, userid],
        key: 'getMerchantAddresses'
      })
      return true
    } catch (err) {
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  async insertAndUpdateAddress ({ connection: con, insertData }) {
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      // Insert Query
      const addressInsertQuery = `INSERT INTO ma_ondc_customer_details (${_.keys(insertData)}) VALUES ?`
      const addressInsertResult = await this.secureRawQuery(addressInsertQuery, { connection, params: [[_.values(insertData)]], key: 'insertAddress' })

      const addressUpdateQuery = 'UPDATE ma_ondc_customer_details SET address_status = "I" WHERE ma_ondc_customer_details_id != ? AND mobile_id = ? AND address_type = ?'
      const addressUpdateResult = await this.secureRawQuery(addressUpdateQuery, { connection, params: [addressInsertResult.insertId, insertData.mobile_id, insertData.address_type], key: 'updateAddress' })
      return +addressInsertResult.insertId
    } catch (err) {
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** Use###########
   * fetchCustomerDetails - fetch customer details data from ma_ondc_customer_details IF fields.mailing_type == 'CUSTOMER' else from ma_user_master table
   * @param {{ ma_user_id: number, ondc_transaction_id: string, provider_id: string, product_name: string, product_id: string,product_quantity: number,product_price: string,product_total_price: string,delivery_charges: string, order_status: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async fetchCustomerDetails (fields, { connection: con }) {
    this.logger({ pagename: basename(__filename), action: 'fetchCustomerDetails', type: 'request', fields })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let ondcCustomerSqlData
      // IF fields.mailing_type == 'CUSTOMER' then fetch customer Data from ma_ondc_customer_details table
      if (fields.mailing_type == 'CUSTOMER') {
        const ondcCustomerSql = `select address1, customer_name, address2, locality, city, state, country,  pincode, address_type, mobile_id, email_id
                  from ma_ondc_customer_details
                  where ma_ondc_customer_details_id = ? limit 1`
        this.logger({ pagename: basename(__filename), action: 'fetchCustomerDetails', type: 'ondcCustomerSql', fields: ondcCustomerSql })
        ondcCustomerSqlData = await this.secureRawQuery(ondcCustomerSql, { connection, params: [fields.mailing_id], key: 'ondcCustomerSqlData' })
        this.logger({ pagename: basename(__filename), action: 'fetchCustomerDetails', type: 'ondcCustomerSqlData', fields: ondcCustomerSqlData })
      } else { // else fetch customer Data from ma_user_master table
        const merchantSql = `select u.mobile_id, u.email_id, CONCAT(u.firstname, ' ', u.lastname) as customer_name, u.pincode, c.name as city, s.name as state, 'Test address1' as address1, 'Test address2' as address2, 'Test locality' AS locality, 'India' AS country, 'Office' as address_type
                  from ma_user_master u
                  JOIN ma_cities_master c ON u.city = c.id
                  JOIN ma_states_master s on u.state = s.id 
                  where u.profileid = ?
                  and u.userid = ?
                  limit 1`
        this.logger({ pagename: basename(__filename), action: 'fetchCustomerDetails', type: 'merchantSql', fields: merchantSql })
        ondcCustomerSqlData = await this.secureRawQuery(merchantSql, { connection, params: [fields.ma_user_id, fields.userid], key: 'ondcCustomerSqlData' })
        this.logger({ pagename: basename(__filename), action: 'fetchCustomerDetails', type: 'ondcCustomerSqlData', fields: ondcCustomerSqlData })
      }
      if (ondcCustomerSqlData.length <= 0) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      const parsedResult = ondcCustomerSqlData
      if (_.isEmpty(parsedResult)) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      return ondcCustomerSqlData
    } catch (err) {
      throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * saveOrderDetails - save order details
   * @param {{ cartData: object [] }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async saveOrderDetails ({ connection: con, orederData = [] }) {
    this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'request', fields: orederData })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      // Insert in seller details table *****
      const saveOrderDetailInsertSql = 'INSERT INTO ma_ondc_order_details (ma_user_id, cart_session_id, aggregator_order_id, amount, customer_mailing_id, mailing_type, order_status, mobile_id, delivery_charges, other_charges) VALUES ?'
      const insertOrderDataSqlRes = await this.secureRawQuery(saveOrderDetailInsertSql, { connection, params: [orederData] })
      this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'insertCartRequestSqlRes', fields: insertOrderDataSqlRes })
      return insertOrderDataSqlRes
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveOrderDetails', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * saveSellerDetails - save order details
   * @param {{ cartData: object [] }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async saveSellerDetails ({ connection: con, sellerData = [] }) {
    this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'request', fields: sellerData })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      // Insert in seller details table *****
      const saveOrderDetailInsertSql = 'INSERT INTO ma_ondc_seller_details (ma_ondc_order_details_id, ondc_transaction_id, search_bpp_id, seller_product_quantity, seller_product_price, seller_delivery_charges,  seller_product_total_price, init_api_response, state_descriptor_name_value, ondc_org_category, ondc_org_TAT, ondc_org_provider_name, buyer_app_finder_fee_type, buyer_app_finder_fee_amount, ondc_withholding_amount, unique_key_id, ondc_order_id, provider_id) VALUES ?'
      const insertOrderDataSqlRes = await this.secureRawQuery(saveOrderDetailInsertSql, { connection, params: [sellerData] })
      this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'insertCartRequestSqlRes', fields: insertOrderDataSqlRes })
      return insertOrderDataSqlRes
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveSellerDetails', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * saveProductDetails - save order details
   * @param {{ cartData: object [] }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async saveProductDetails ({ connection: con, productData = [] }) {
    this.logger({ pagename: basename(__filename), action: 'saveProductDetails', type: 'request', fields: productData })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      // Insert in seller details table *****
      const saveOrderDetailInsertSql = 'INSERT INTO ma_ondc_product_details (ma_ondc_order_details_id, ma_ondc_seller_details_id, ma_user_id, ondc_transaction_id, provider_id, product_id, product_quantity, product_price,  product_total_price, product_order_status, product_name, product_image, product_additional_details, cancellable, returnable) VALUES ?'
      const insertOrderDataSqlRes = await this.secureRawQuery(saveOrderDetailInsertSql, { connection, params: [productData] })
      this.logger({ pagename: basename(__filename), action: 'saveProductDetails', type: 'insertCartRequestSqlRes', fields: insertOrderDataSqlRes })
      return insertOrderDataSqlRes
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'saveProductDetails', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /** ++++++++++
   * upateSellerDetails - save product details like id, name, base price, quantity & delivery charges
   * @param {{ ma_user_id: number, ondc_transaction_id: string, provider_id: string, product_name: string, product_id: string,product_quantity: number,product_price: string,product_total_price: string,delivery_charges: string, order_status: string }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async upateTableData ({ connection: con, whereClause = {}, setData = {}, tableName = '' }) {
    this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'request', fields: whereClause })
    const [isTempConnection, connection] = await this.getConnection(con)
    try {
      let sqlConitions = ''
      const conditionsValArr = []
      Object.keys(whereClause).forEach(function (key, index) {
        if (index === 0) sqlConitions = ` WHERE ${key} = '${whereClause[key]}' `
        else sqlConitions += ` AND ${key} = '${whereClause[key]}' `
        conditionsValArr.push(whereClause[key])
      })

      let setDataVal = ''
      Object.keys(setData).forEach(function (key, index) {
        if (index === 0) setDataVal = ` SET ${key} = '${setData[key]}' `
        else setDataVal += ` , ${key} = '${setData[key]}' `
      })

      const cartUpdateSql = `UPDATE ${tableName} ${setDataVal} ${sqlConitions}`
      const cartUpdateSqlResp = await this.secureRawQuery(cartUpdateSql, { connection, params: conditionsValArr, key: 'cartUpdateSqlResp Response' })

      if (cartUpdateSqlResp.length <= 0) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      const parsedResult = cartUpdateSqlResp
      if (_.isEmpty(parsedResult)) throw new RepositoryError({ status: 400, message: errorMsg.responseCode[1002], respcode: 1002, action_code: 1001 })
      return cartUpdateSqlResp
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchCartData', type: 'catch err', fields: err })
      return null
    } finally {
      if (isTempConnection) connection.release()
    }
  }
}
