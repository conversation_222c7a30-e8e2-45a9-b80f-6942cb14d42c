// Package Imports
const { basename } = require('path')
const Joi = require('joi')
const _ = require('lodash')

// Code Imports
const logs = require('../../util/log')
const errorEmail = require('../../util/errorHandler')
const common = require('../../util/common')
const errorMsg = require('./config/errors')
const constants = require('./config/constants')

const mySQLWrapper = require('../../lib/mysqlWrapper')
const ValidationError = require('../../util/errors/ValidationError')
const OndcTransactionRepository = require('./repository/ondc.transaction.repository')
const OndcTransactionHelper = require('./helper/ondc.transaction.helper')
const OndcTransactionIntegration = require('./integration/ondc.transaction.integration')
const otpController = require('../otp/otpController')
const { min } = require('lodash')
const OndcMariaRepository = require('./repository/ondc.maria.repository')
const OndcPreOrderIntegration = require('./integration/ondc.preorder.integration')
const OndcPreOrderRepository = require('./repository/ondc.preorder.repository')

module.exports = class transactionController {
  static get LOGGER () {
    return logs.logger
  }

  /**
   * Creates a new Connection if connection passed in params is undefined.
   * @param {mysqlConnection} con
   * @returns {Promise<mySQLWrapper>}
   */
  static async getConnection (con) {
    return [!con, con || await mySQLWrapper.getConnectionFromPool()]
  }

  /** API
   * ondcSendOtp description - Send Otp on customer mobile number
   * @param {{ ma_user_id: number, userid: number, mobile_id: String, ondc_transaction_id: String, otp_for: "PURCHASE"|"HISTORY"|"CANCEL"|"RETURN" }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, ondc_transaction_id: String, otpOrderid: string }>}
   */
  static async ondcSendOtp (_, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcSendOtp', type: 'request', fields })

    // Validate request
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      mobile_id: Joi.string().length(10).pattern(constants.validations.mobile_id_regex).required(),
      otp_for: Joi.string().allow(null).empty(null).valid('PURCHASE', 'HISTORY', 'CANCEL', 'RETURN').default('PURCHASE'),
      ondc_transaction_id: Joi.string().allow(null).empty(null).default('')
    })

    try {
      const { ma_user_id, userid, mobile_id, otp_for, ondc_transaction_id } = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })

      // Send OTP
      const otpResponse = await otpController.sentOtp(ma_user_id, userid, mobile_id, constants.ondc.otpTypes[otp_for])
      if (!otpResponse.status) return { status: 400, respcode: 1022, message: otpResponse.message, action_code: 1001 }

      return { status: 200, respcode: 2007, message: errorMsg.responseCode[2007], action_code: 1000, otpOrderid: otpResponse.data.aggregator_order_id, ondc_transaction_id }
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcSendOtp', type: 'err', fields: err })
      if (common.isCustomError(err)) return common.getErrorResponse(err)

      errorEmail.notifyCatchErrorEmail({ function: 'ondcSendOtp', data: { ...fields }, error: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  /** API
   * ondcResendOtp description - Re-send Otp on customer mobile number
   * @param {{ ma_user_id: number, userid: number, mobile_id: string, otpOrderid: string, ondc_transaction_id?: string, otp_for?: "PURCHASE"|"HISTORY"|"CANCEL"|"RETURN" }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, ondc_transaction_id?: string, otpOrderid: string }>}
   */
  static async ondcResendOtp (_, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcResendOtp', type: 'request', fields })

    // Validate request
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      mobile_id: Joi.string().length(10).pattern(constants.validations.mobile_id_regex).required(),
      otp_for: Joi.string().allow(null).empty(null).valid('PURCHASE', 'HISTORY', 'CANCEL', 'RETURN').default('PURCHASE'),
      ondc_transaction_id: Joi.string().allow(null).empty(null).default(''),
      otpOrderid: Joi.when('otp_for', [
        { is: 'PURCHASE', then: Joi.string().pattern(new RegExp(constants.validations.purchase_otp_regex)) },
        { is: 'HISTORY', then: Joi.string().pattern(new RegExp(constants.validations.history_otp_regex)) },
        { is: 'CANCEL', then: Joi.string().pattern(new RegExp(constants.validations.cancel_otp_regex)) },
        { is: 'RETURN', then: Joi.string().pattern(new RegExp(constants.validations.return_otp_regex)) }
      ]).messages(constants.validationErrors.otp_orderid)
    })

    try {
      const { mobile_id, otp_for, ondc_transaction_id, otpOrderid } = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })

      // Re-send OTP
      const otpResponse = await otpController.resentOtp(mobile_id, otpOrderid, constants.ondc.otpTypes[otp_for])
      logger({ pagename: basename(__filename), action: 'ondcResendOtp', type: 'otpResponse', fields: otpResponse })

      if (!otpResponse.status) return { status: 400, respcode: 1022, message: otpResponse.message, action_code: 1001, ondc_transaction_id, otpOrderid }
      return { status: 200, respcode: 2007, message: errorMsg.responseCode[2007], action_code: 1000, ondc_transaction_id, otpOrderid }
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcResendOtp', type: 'err', fields: err })
      if (common.isCustomError(err)) return common.getErrorResponse(err)

      errorEmail.notifyCatchErrorEmail({ function: 'ondcResendOtp', data: { ...fields }, error: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  /** API
   * ondcFetchMailingDetails description - First verify Otp (optional)and then fetch customer details
   * @typedef {{ address_id: number, mobile_id: string, billing_name: string, email_id: string, address1: string, address2: string, city: string, state: string, country: string, address_type: string }} mailingDetails
   * @typedef {{ ma_user_id: number, userid: number, mobile_id: string, otp?: string, aggregator_order_id?: string, ondc_transaction_id: string, address_id?: number }} ondcFetchMailingDetailsRequestParams
   * @param {ondcFetchMailingDetailsRequestParams} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, customerMailingDetails: mailingDetails, agentMailingDetails: mailingDetails, ondc_transaction_id: String }>}
   */
  static async ondcFetchMailingDetails (__, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcFetchMailingDetails', type: 'request', fields })

    // Validate request
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      mobile_id: Joi.string().length(10).pattern(new RegExp(constants.validations.mobile_id_regex)).required(),
      otp: Joi.string().pattern(new RegExp(constants.validations.otp_regex)),
      aggregator_order_id: Joi.string().pattern(new RegExp(constants.validations.purchase_otp_regex)),
      ondc_transaction_id: Joi.string().min(10).required(),
      address_id: Joi.number().min(1).optional()
    }).and('otp', 'aggregator_order_id')

    try {
      /** @type {ondcFetchMailingDetailsRequestParams} */
      const { ma_user_id, userid, mobile_id, address_id, ondc_transaction_id, aggregator_order_id, otp } = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })
      const ondcTransactionRepository = new OndcTransactionRepository(logger)

      let updatePromise = null
      // Update Cart after verification success
      if (aggregator_order_id && otp) {
        const verifyResponse = await otpController.verifyOtp(aggregator_order_id, 'ONDC', otp)
        logger({ pagename: basename(__filename), action: 'ondcFetchMailingDetails', type: 'verifyResponse', fields: verifyResponse })
        if (verifyResponse.status != true) return { status: 400, respcode: 1001, message: verifyResponse.message || errorMsg.responseCode[1001], action_code: 1001 }

        updatePromise = ondcTransactionRepository.updateCartMobileID({ mobile_id, ondc_transaction_id })
      }

      // get Addresses
      const [ondcCustomerSqlData, merchantSqlData] = await Promise.all([
        ondcTransactionRepository.getCustomerAddresses({ mobile_id, address_id }),
        ondcTransactionRepository.getMerchantAddresses({ ma_user_id, userid }),
        updatePromise
      ])

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, customerMailingDetails: ondcCustomerSqlData, agentMailingDetails: merchantSqlData, ondc_transaction_id, otpOrderid: aggregator_order_id }
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcFetchMailingDetails', type: 'err', fields: err })
      if (common.isCustomError(err)) return common.getErrorResponse(err)

      errorEmail.notifyCatchErrorEmail({ function: 'ondcFetchMailingDetails', data: { ...fields }, error: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  /**
   * ondcAddCustomerMailingAddress description - Adds new Mailing Address and disables previous addresses
   * @param {null} _
   * @param {{
     *  ma_user_id: number,
     *  userid: number,
     *  ondc_transaction_id: string,
     *  mobile_id: string,
     *  billing_name: string,
     *  email_id: string,
     *  address1: string,
     *  address2: string,
     *  city: string,
     *  state: string,
     *  country: string,
     *  pincode: string,
     *  address_type: string
     * }} fields
     * @returns {Promise <{ status: number, message: string, respcode: number, action_code: 1000|1001, customerMailingDetails: mailingDetails, ondc_transaction_id: string }>}
     */
  static async ondcAddCustomerMailingAddress (__, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcAddCustomerMailingAddress', type: 'request', fields })

    // Validate request
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      mobile_id: Joi.string().length(10).pattern(new RegExp(constants.validations.mobile_id_regex)).required(),
      customer_name: Joi.string().required(),
      address1: Joi.string().min(5).required(),
      locality: Joi.string().min(5).required(),
      city: Joi.string().min(5).required(),
      state: Joi.string().min(5).required(),
      country: Joi.string().min(5).required(),
      pincode: Joi.string().length(6).required(),
      address_type: Joi.string().valid('Home', 'Agent', 'Shop').insensitive().required(),
      email_id: Joi.string().email().optional(),
      ondc_transaction_id: Joi.string().trim().allow('').optional()
    }).rename('address2', 'locality').rename('billing_name', 'customer_name')

    try {
      const { ma_user_id, userid, ondc_transaction_id, ...insertData } = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })
      const ondcTransactionRepository = new OndcTransactionRepository(logger)

      const address_id = await ondcTransactionRepository.insertAndUpdateAddress({ insertData })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, customerMailingDetails: [{ address_id, ...insertData }], ondc_transaction_id: ondc_transaction_id }
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcAddCustomerMailingAddress', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({ function: 'ondcAddCustomerMailingAddress', data: { ...fields }, error: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }

  /** API
   * ondcVerifyPin description
   * @param {{ ma_user_id: number, userid: number, pin: String, mailing_id: String, mailing_type?: 'CUSTOMER'|'AGENT', aggregator_order_id: String, mobile_id: String, customer_delivery_location: String, cart_session_id: String }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, ondc_transaction_id: String, itemsData: Object }>}
   */
  static async ondcVerifyPin (__, fields) {
    const logger = this.LOGGER
    logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'request', fields: fields })
    const [isTempConnection, connection] = await this.getConnection()
    // Validate Fields *****
    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      pin: Joi.string().trim().min(1).required(),
      mailing_id: Joi.string().trim().min(1).required(),
      mailing_type: Joi.string().valid('CUSTOMER', 'AGENT').required(),
      aggregator_order_id: Joi.string().min(0).required(),
      mobile_id: Joi.number().min(0).required(),
      customer_delivery_location: Joi.string().min(0).required(),
      cart_session_id: Joi.string().min(0).required()
    })

    console.time(`${fields.ma_user_id} - ${fields.search_string} - ondcSearchItems API Time Log`)
    try {
      /** @type {requestData} */
      const request = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })
      logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'request Data', fields: request })
      const transactionHelper = new OndcTransactionHelper(logger)

      // Verify Pin
      const pinResponse = await transactionHelper.verifyPin(request)
      logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'pinResponse', fields: pinResponse })
      if (pinResponse.status != 200) return pinResponse

      const mariaRepository = new OndcMariaRepository(logger)
      const preOrderIntegration = new OndcPreOrderIntegration(logger)
      const preTransactionIntegration = new OndcTransactionIntegration(logger)
      const preOrderRepository = new OndcPreOrderRepository(logger)
      const transactionRepository = new OndcTransactionRepository(logger)

      // Fetch Data for Init API
      const fetchMultipleProviderCartData = await preOrderRepository.fetchProuctCartData({ whereClause: { cart_session_id: request.cart_session_id, order_status: 'Incart' }, connection })
      logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'fetchMultipleProviderCartData', fields: fetchMultipleProviderCartData })
      const cartDataSellerWise = Object.values(_.groupBy(fetchMultipleProviderCartData, item => `${item.provider_id}-${item.ondc_transaction_id}`))
      logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'cartDataSellerWise', fields: JSON.stringify(cartDataSellerWise) })
      request.mobile_id = fetchMultipleProviderCartData[0].mobile_number

      // Call Init API Asyn *****
      const initApiProcess = await Promise.all(Object.values(cartDataSellerWise).map(val => transactionHelper.ondcInitApiProcess(val, {
        mariaRepository,
        transactionRepository,
        preTransactionIntegration,
        uniqueIdForLog: `${val[0].provider_id}-${val[0].ondc_transaction_id}`,
        request,
        connection
      })))
      logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'initApiProcess data log', fields: JSON.stringify(initApiProcess) })
      let allSellerWiseAmt = _.sumBy(initApiProcess, fields => { return fields.status == 200 ? fields.sellerWiseAmt : 0 }) || 0
      if (allSellerWiseAmt <= 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      allSellerWiseAmt += _.sumBy(initApiProcess, fields => { return fields.status == 200 ? fields.sellerWiseCharges : 0 }) || 0

      // Check Merchant balance *****
      const balanceController = require('../balance/balanceController')
      const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: request.ma_user_id,
        amount: allSellerWiseAmt,
        transactionType: '1'
      })
      logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'getWalletBalanceDirect', fields: pointsDetailsEntries })
      if (pointsDetailsEntries.status != 200) return { ...pointsDetailsEntries }

      const upateCartData = await Promise.all(Object.values(initApiProcess).map(val => transactionHelper.upateCartData(val, { preOrderRepository, uniqueIdForLog: `${val.cartData[0].provider_id}-${val.cartData[0].ondc_transaction_id}` })))
      logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'upateCartData data log', fields: JSON.stringify(upateCartData) })

      // Initiate ONDC Transaction with transaction_status 'I' *****
      const initiateOndcTransaction = await transactionHelper.initiateOndcTransaction({ ...request, amount: allSellerWiseAmt }, { connection, cartData: initApiProcess[0].cartData[0], customerData: initApiProcess[0].customerData })
      logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'initiateOndcTransaction', fields: initiateOndcTransaction })
      if (initiateOndcTransaction.status != 200) return { ...initiateOndcTransaction, ondc_transaction_id: fields.ondc_transaction_id }

      // Initiate ONDC Transaction with transaction_status 'I' *****
      const saveOrderDetails = await transactionHelper.saveOrderDetails(initApiProcess, { connection, request, transactionRepository, mySQLWrapper, allSellerWiseAmt })
      logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'saveOrderDetails', fields: saveOrderDetails })
      if (saveOrderDetails.status != 200) return { ...saveOrderDetails, ondc_transaction_id: fields.ondc_transaction_id, aggregator_order_id: fields.aggregator_order_id, transaction_status: 'I' }

      // ONDC Ledger Entries *****
      const ondcLedgerEntries = await transactionHelper.ondcLedgerEntries({ ...request, amount: allSellerWiseAmt }, connection)
      logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'ondcLedgerEntries', fields: ondcLedgerEntries })
      if (ondcLedgerEntries.status != 200) return { ...ondcLedgerEntries, ondc_transaction_id: fields.ondc_transaction_id, aggregator_order_id: fields.aggregator_order_id, transaction_status: 'I' }

      // Call Confirm Api (MS Side) *****
      const ondcConfirmApiResponse = await Promise.all(Object.values(initApiProcess).map(val => transactionHelper.ondcConfirmApi(val, { connection, transactionRepository, preTransactionIntegration, uniqueIdForLog: `${val.cartData[0].provider_id}-${val.cartData[0].ondc_transaction_id}`, request })))
      logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'ondcConfirmApiResponse', fields: ondcConfirmApiResponse })

      let successFlag = false
      let finalSuccessfulOrderAmount = 0
      let finalFailureOrderAmount = 0
      Object.values(ondcConfirmApiResponse).map(val => {
        if (val.status == 200 && val.confirmDataSellerWise.status == 200) {
          successFlag = true
          finalSuccessfulOrderAmount += parseFloat(val.confirmDataSellerWise.sellerWiseAmt) + parseFloat(val.confirmDataSellerWise.sellerWiseCharges)
        } else finalFailureOrderAmount += parseFloat(val.confirmDataSellerWise.sellerWiseAmt) + parseFloat(val.confirmDataSellerWise.sellerWiseCharges)
        return val
      })

      if (!successFlag) {
        if (ondcConfirmApiResponse.respcode == 1028) {
          const failedTransaction = await transactionHelper.failedTransaction({ ...fields, ondc_error_msg: ondcConfirmApiResponse.error_msg || null }, connection)
          failedTransaction.message = ondcConfirmApiResponse.message
          logs.logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'failedTransaction', fields: failedTransaction })
          return { ...failedTransaction, message: 'Fail: CONFIRM API failure', ondc_transaction_id: fields.ondc_transaction_id, aggregator_order_id: fields.aggregator_order_id, transaction_status: 'F' }
        }
        return { status: 200, respcode: 1000, message: ondcConfirmApiResponse.message, action_code: 1000, ondc_transaction_id: fields.ondc_transaction_id, aggregator_order_id: fields.aggregator_order_id, transaction_status: 'P' }
      }
      // if (1 == 1) return { status: 400, message: 'test', respcode: 1001, action_code: 1001 }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
      /* */
    } catch (err) {
      logger({ pagename: basename(__filename), action: 'ondcVerifyPin', type: 'err', fields: err })
      if (common.isCustomError(err)) return common.getErrorResponse(err)
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      if (isTempConnection) connection.release()
    }
  }

  /**
   * ondcViewCartItems description - Fetch Product Cart details
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, mobile_no: String, cart_session_id: String }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async ondcViewCartItems (__, fields) {
    const logger = this.LOGGER
    logs.logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'request', fields: fields })

    const validationSchema = Joi.object({
      ma_user_id: Joi.number().min(1).required(),
      userid: Joi.number().min(1).required(),
      mobile_no: Joi.number().min(0).required(),
      cart_session_id: Joi.string().min(0).required()
    })

    try {
      /** @type {requestData} */
      const request = await validationSchema.validateAsync(fields).catch(err => { throw new ValidationError(err) })
      logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'request Data', fields: request })

      const mariaRepository = new OndcMariaRepository(logger)
      const preOrderRepository = new OndcPreOrderRepository(logger)

      // Fetch Data for Init API
      const fetchMultipleProviderCartData = await preOrderRepository.fetchProuctCartData({ whereClause: { cart_session_id: request.cart_session_id, order_status: 'Incart', mobile_number: request.mobile_no } })
      logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'fetchMultipleProviderCartData', fields: fetchMultipleProviderCartData })

      const cartDataSellerWise = Object.values(_.groupBy(fetchMultipleProviderCartData, item => `${item.provider_id}-${item.ondc_transaction_id}`))
      logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'cartDataSellerWise', fields: JSON.stringify(cartDataSellerWise) })
      request.mobile_id = fetchMultipleProviderCartData[0].mobile_number

      if (fetchMultipleProviderCartData.length > 0) {
        // Fetch Serach Data
        let shipping_charges = 0
        let searchData = await Promise.all(cartDataSellerWise.map(val => {
          shipping_charges += parseFloat(val[0].delivery_charges)
          const ondcTransactionIds = val[0].ondc_transaction_id
          const providerIds = val[0].provider_id
          const productIds = _.map(val, 'product_id')
          return mariaRepository.fetchSearchData({ whereClause: { sessionid: ondcTransactionIds, prov_id: providerIds }, whereInClause: { prov_items_id: productIds }, uniqueIdForLog: ondcTransactionIds + providerIds })
        }))
        searchData = _.flatten(searchData)
        if (searchData.length <= 0) return { status: 400, message: errorMsg.responseCode[1002], respcode: 1028, action_code: 1001 }
        logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'searchData', fields: searchData })

        const product_quantity = parseFloat(_.sumBy(fetchMultipleProviderCartData, function (val) { return val.product_quantity }))
        const baseprice = parseFloat(_.sumBy(fetchMultipleProviderCartData, function (val) { return val.product_total_price }))
        const final_amount = (baseprice + shipping_charges).toFixed(2)
        const viewCartDataResult = {
          cart_details: [
            {
              baseprice: baseprice.toFixed(2),
              product_quantity,
              shipping_charges,
              final_amount
            }
          ],
          product_details: await fetchMultipleProviderCartData.map(element => {
            return {
              shop_id: element.provider_id,
              product_id: element.product_id,
              product_image: searchData[_.findKey(searchData, { sessionid: element.ondc_transaction_id, prov_id: element.provider_id, prov_items_id: element.product_id })].prov_items_desc_images || '',
              product_name: element.product_name,
              baseamount: element.product_price,
              product_quantity: element.product_quantity
            }
          })
        }

        logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'viewCartDataResult', fields: viewCartDataResult })

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, ondc_transaction_id: fields.ondc_transaction_id, viewCartData: viewCartDataResult }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': Product details not found. Please try again.', respcode: 1028, action_code: 1001 }
      }
    } catch (err) {
      logs.logger({ pagename: basename(__filename), action: 'ondcViewCartItems', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      //
    }
  }
}
