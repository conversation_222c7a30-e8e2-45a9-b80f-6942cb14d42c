// Package Import
const { basename } = require('path')
const axios = require('axios')
const _ = require('lodash')

// Code Import
const errorMsg = require('../config/errors')
const checksum = require('../../../util/checksum')
const constants = require('../config/constants')
const mySQLWrapper = require('../../../lib/mysqlWrapper')

module.exports = class OndcBaseIntegration {
  constructor (logger) {
    this.logger = logger || console.log
  }

  get CONFIG () {
    const config = {
      headers: {
        'Content-Type': 'application/json',
        AFFILIATE: constants.api.AFFILIATE_Header_value
      },
      timeout: 120000
    }
    console.log('config', config)
    return config
  }

  /** Internal Function
   * fetchOndcResult description - Pass request data to this function and this function will execute entire process of API (till get_result API of MS side)
   * Common function for all API to fetch get_result API data
   * @param {{ apiRequest: *, apiEndpoint: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, getResultsResponse: * }>}
   */
  async fetchOndcResult ({ apiRequest, apiEndpoint, uniqueIdForLog = null }) {
    try {
      const ondc_transaction_id_for_request = _.get(apiRequest, 'context.transaction_id', _.get(apiRequest, '[0].context.transaction_id', ''))
      const ondc_message_id_for_request = _.get(apiRequest, 'context.message_id', _.get(apiRequest, '[0].context.message_id', ''))
      apiRequest = JSON.stringify(apiRequest)
      this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: 'request' + ` - ${uniqueIdForLog}`, fields: JSON.stringify({ apiRequest, apiEndpoint }) })

      // Encrypt request data for API *****
      const encryptedSearchRequestData = await this.encryptMaAPIRequest({ requestData: apiRequest, uniqueIdForLog })
      this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: `${apiEndpoint}(${apiEndpoint} API) - encryptedSearchRequestData  - ${uniqueIdForLog}`, fields: encryptedSearchRequestData })
      if (encryptedSearchRequestData.status != 200) return encryptedSearchRequestData

      // Call API *****
      const searchApiResponse = await this.maSideApiAxiosCall({ requestData: encryptedSearchRequestData.data, endpoint: apiEndpoint, uniqueIdForLog })
      this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: `${apiEndpoint}(${apiEndpoint} API) - Search API response (searchApiResponse) - ${uniqueIdForLog}`, fields: searchApiResponse })
      if (searchApiResponse.status != 200) return searchApiResponse

      // Call get_results API function *****
      const getResultApiResponse = await this.ondcGetResultsAPI({
        ondc_transaction_id: ondc_transaction_id_for_request,
        ondc_message_id: ondc_message_id_for_request,
        request_id: searchApiResponse.axiosResponse.RequestId,
        uniqueIdForLog
      })
      this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: `${apiEndpoint}(getResult API) - getResultApiResponse - ${uniqueIdForLog}`, fields: getResultApiResponse })
      try {
        this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: `${apiEndpoint}(getResult API) - getResultApiResponse JSON.stringify - ${uniqueIdForLog}`, fields: JSON.stringify(getResultApiResponse) })
      } catch (err) {
        this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: `${apiEndpoint}(getResult API) - JSON.stringify err - ${uniqueIdForLog}`, fields: err })
      }

      if (getResultApiResponse.status != 200) return getResultApiResponse
      return { ...getResultApiResponse, RequestId: searchApiResponse.axiosResponse.RequestId }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'fetchOndcResult', type: 'err' + ` - ${uniqueIdForLog}`, fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1000 }
    }
  }

  /** Internal Function
   * sleepTransaction
   * @param { Integer } seconds
   * This function is do the set timout for give seconds
   */
  async sleepTransaction (seconds) {
    await new Promise(resolve => setTimeout(resolve, seconds))
  }

  /** Internal Function
   * ondcGetResults description - Returns min max limit of amount
   * @param {{ ondc_transaction_id: string, ondc_message_id: string, request_id: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, getResultsResponse: string }>}
   */
  async ondcGetResultsAPI ({ ondc_transaction_id = '', ondc_message_id = '', request_id = '', uniqueIdForLog = null }) {
    this.logger({ pagename: basename(__filename), action: 'ondcGetResultsAPI', type: 'request' + ` - ${uniqueIdForLog}`, fields: { ondc_transaction_id, ondc_message_id, request_id } })
    // Validate Fields *****

    try {
      // Request Data for get_results API (MS) *****
      const decryptedRequestData = `{
        "transaction_id": "${ondc_transaction_id}",
        "message_id": "${ondc_message_id}",
        "request_id": "${request_id}"
      }`
      // Encrypt request data for get_results API *****
      const encryptedSearchRequestData = await this.encryptMaAPIRequest({ requestData: decryptedRequestData, uniqueIdForLog })
      this.logger({ pagename: basename(__filename), action: 'ondcGetResultsAPI', type: 'encryptedSearchRequestData' + ` - ${uniqueIdForLog}`, fields: encryptedSearchRequestData })
      if (encryptedSearchRequestData.status != 200) return encryptedSearchRequestData

      // Call get_results API *****
      await this.sleepTransaction(5000)
      const getResultsApiResponse = await this.maSideApiAxiosCall({ requestData: encryptedSearchRequestData.data, endpoint: 'get_results', uniqueIdForLog })
      // this.logger({ pagename: basename(__filename), action: 'ondcGetResultsAPI', type: 'Search API response (getResultsApiResponse)', fields: getResultsApiResponse })
      this.logger({ pagename: basename(__filename), action: 'ondcGetResultsAPI', type: 'Search API response (getResultsApiResponse)' + ` - ${uniqueIdForLog}`, fields: ((getResultsApiResponse || {}).axiosResponse || {}).Data })
      if (getResultsApiResponse.status != 200) return getResultsApiResponse

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], getResultsResponse: getResultsApiResponse.axiosResponse.Data }
    } catch (err) {
      this.logger({ pagename: basename(__filename), action: 'ondcGetResultsAPI', type: 'err' + ` - ${uniqueIdForLog}`, fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1000 }
    } finally {
      // readconnection.release()
    }
  }

  /** Internal Function
   * encryptMaAPIRequest description
   * @param {{ requestData: string }} fields
   * @returns {Promise < ecryptedData: string >}
   */
  async encryptMaAPIRequest ({ requestData, uniqueIdForLog = null }) {
    this.logger({ pagename: basename(__filename), action: 'encryptMaAPIRequest', type: 'request' + ` - ${uniqueIdForLog}`, fields: requestData })
    try {
      let iv = ''
      const characters = '0123456789abcdefghijklmnopqrstuvwxyz'
      for (let i = 0; i < 16; i++) iv += characters.charAt(Math.floor(Math.random() * characters.length))
      this.logger({ pagename: basename(__filename), action: 'encryptMaAPIRequest', type: 'iv', fields: iv })

      const ecryptRequest = await checksum.ondcRequestEncrypt({ data: requestData, secrete_key: constants.api.ma_side_api_constants.secrete_key, iv })
      this.logger({ pagename: basename(__filename), action: 'encryptMaAPIRequest', type: 'ecryptRequest' + ` - ${uniqueIdForLog}`, fields: ecryptRequest })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, data: iv + ecryptRequest }
    } catch (axiosErr) {
      this.logger({ pagename: basename(__filename), action: 'encryptMaAPIRequest', type: 'axios catcherror' + ` - ${uniqueIdForLog}`, fields: axiosErr })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }

  /** Internal Function
   * maSideApiAxiosCall description - Common function MS side Api call
   * @param {{ requestData: string, endpoint: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: number, axiosResponse: Object }>}
   */
  async maSideApiAxiosCall ({ requestData, endpoint, uniqueIdForLog = null }) {
    this.logger({ pagename: basename(__filename), action: 'maSideApiAxiosCall', type: 'request' + ` - ${uniqueIdForLog}`, fields: { requestData, endpoint } })
    try {
      let response = {}
      try {
        response = await axios({
          method: 'POST',
          data: requestData,
          url: `${constants.api.ma_side_api_url.basePath}${constants.api.ma_side_api_url[endpoint]}`,
          ...this.CONFIG
        })
      } catch (axiosErr) {
        this.logger({ pagename: basename(__filename), action: 'maSideApiAxiosCall', type: `${endpoint}(${endpoint} API) - axios catcherror - ${uniqueIdForLog}`, fields: axiosErr })
        this.logger({ pagename: basename(__filename), action: 'maSideApiAxiosCall', type: `${endpoint}(${endpoint} API) - axios searchApiResponse - ${uniqueIdForLog}`, fields: response })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1001], action_code: 1001 }
      }
      this.logger({ pagename: basename(__filename), action: 'maSideApiAxiosCall', type: `${endpoint}(${endpoint} API) - axios response - ${uniqueIdForLog}`, fields: response })

      if (response && response.data && response.data.Result && response.data.Result == 'Fail') {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, Result: response.data.Result }
      } else if (response && response.data && !response.data.Result) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, axiosResponse: response.data, Result: null }
      } else if (!response || !response.data) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001, axiosResponse: response.data, Result: null }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, axiosResponse: response.data }
    } catch (axiosErr) {
      this.logger({ pagename: basename(__filename), action: 'maSideApiAxiosCall', type: `${endpoint}(${endpoint} API) - axios catcherror - ${uniqueIdForLog}`, fields: axiosErr })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }

  /** Internal Function
   * createOndcTransactionId description
   * @param {{ requestData: String }} fields
   * @returns {Promise < ecryptedData: String >}
   */
  async createOndcTransactionId () {
    const md5 = require('md5')
    return await md5(new Date()) + await this.generateRamdomId(4) + await this.generateRamdomId(4)
  }

  /** Internal Function
     * generateRamdomId description
     * @param {{ requestData: String }} fields
     * @returns {Promise < ecryptedData: String >}
     */
  async generateRamdomId (length) {
    this.logger({ pagename: basename(__filename), action: 'generateRamdomId', type: 'length', fields: length })
    try {
      let id = ''
      const characters = '0123456789abcdefghijklmnopqrstuvwxyz'
      for (let i = 0; i < length; i++) id += characters.charAt(Math.floor(Math.random() * characters.length))
      this.logger({ pagename: basename(__filename), action: 'generateRamdomId', type: 'id', fields: id })
      return id
    } catch (axiosErr) {
      this.logger({ pagename: basename(__filename), action: 'generateRamdomId', type: 'axios catcherror', fields: axiosErr })
      return ''
    }
  }
}
