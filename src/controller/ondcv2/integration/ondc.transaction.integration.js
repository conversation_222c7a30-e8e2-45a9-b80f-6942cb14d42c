// Package Imports
const { basename } = require('path')
const _ = require('lodash')
const md5 = require('md5')
const constants = require('../config/constants')

// Code Imports
const OndcBaseIntegration = require('./ondc.base.integration')
const IntegrationError = require('../../../util/errors/IntegrationError')

module.exports = class OndcPreOrderIntegration extends OndcBaseIntegration {
  constructor (logger) {
    super(logger)
    this.logger = logger
  }

  /** ++++++++++
   * @typedef {{ provier_wise_data: Object }} requestData
   * @param {requestData} fields
   * @returns {Promise<{ fetchOndcResult: { status: 200, respcode: number, message: string, action_code: 1000 } }>}
   */
  async init (cartData, searchData, customerData, uniqueIdForLog, request) {
    this.logger({ pagename: basename(__filename), action: 'init', type: 'request Data' + ` - ${uniqueIdForLog}`, fields: JSON.stringify({ cartData, searchData, customerData, uniqueIdForLog, request }) })

    // Craete request data for search API *****
    const ondc_transaction_id_for_request = _.get(cartData, '[0].ondc_transaction_id', await this.createOndcTransactionId()) || await this.createOndcTransactionId()
    const ondc_message_id_for_request = await this.createOndcTransactionId()

    // Create INIT API request *****
    const decryptedSearchRequestData = [
      {
        context: {
          transaction_id: ondc_transaction_id_for_request,
          message_id: ondc_message_id_for_request
        },
        message: {
          items: await searchData.map(element => {
            return {
              id: element.prov_items_id,
              quantity: {
                count: parseInt(_.find(cartData, { product_id: element.prov_items_id }).product_quantity || 0)
              },
              bpp_id: element.search_bpp_id,
              provider: {
                id: element.prov_id,
                locations: [
                  element.prov_loc_id
                ]
              },
              fulfilment_id: _.get(_.find(JSON.parse(element.fulfilment_type), fulfillment => fulfillment.type == 'Delivery'), 'id', null)
            }
          }),
          billing_info: {
            address: {
              door: customerData[0].address1,
              name: customerData[0].customer_name,
              building: '',
              street: customerData[0].address2,
              locality: customerData[0].locality,
              ward: '',
              city: customerData[0].city,
              state: customerData[0].state,
              country: customerData[0].country,
              area_code: customerData[0].pincode
            },
            phone: cartData[0].mobile_number,
            name: customerData[0].customer_name,
            email: customerData[0].email_id
          },
          delivery_info: {
            type: 'Delivery',
            name: customerData[0].customer_name,
            email: customerData[0].email_id,
            phone: cartData[0].mobile_number,
            location: {
              gps: request.customer_delivery_location,
              address: {
                door: customerData[0].address1,
                name: customerData[0].customer_name,
                building: '',
                street: customerData[0].address2,
                locality: customerData[0].locality,
                ward: '',
                city: customerData[0].city,
                state: customerData[0].state,
                country: customerData[0].country,
                area_code: customerData[0].pincode
              }
            }
          },
          payment: {
            type: 'POST-FULFILLMENT'
          }
        }
      }
    ]
    this.logger({ pagename: basename(__filename), action: 'init', type: 'INIT API decrypted request Data' + ` - ${uniqueIdForLog}`, fields: { ondc_transaction_id_for_request, ondc_message_id_for_request, decryptedSearchRequestData } })
    // Fetch API Result *****
    const fetchOndcResult = await this.fetchOndcResult({ apiRequest: decryptedSearchRequestData, apiEndpoint: 'init', uniqueIdForLog })
    this.logger({ pagename: basename(__filename), action: 'init', type: 'fetchOndcResult' + ` - ${uniqueIdForLog}`, fields: fetchOndcResult })

    let sellerWiseAmt = 0
    let sellerWiseQnt = 0
    let sellerWiseCharges = 0
    // INIT API response validation if status 200 *****
    if (fetchOndcResult.status == 200) {
      if (!_.get(fetchOndcResult, 'getResultsResponse.[0].order', null)) return { status: 400, respcode: 1019, message: 'We are unable to proceed with your request due to a technical issue(#3-1). Please try again later.', action_code: 1001, sellerWiseQnt: 0, sellerWiseAmt: 0, sellerWiseCharges: 0 }

      if (_.get(fetchOndcResult, 'getResultsResponse.[0].error', null)) return { status: 400, respcode: 1019, message: 'We are unable to proceed with your request due to a technical issue(#3-2). Please try again later.', action_code: 1001, sellerWiseQnt: 0, sellerWiseAmt: 0, sellerWiseCharges: 0 }

      // Product quantity validation
      if (!_.get(fetchOndcResult, 'getResultsResponse[0].order.quote.price.value', null) || _.get(fetchOndcResult, 'getResultsResponse[0].order.quote.breakup', []).length <= 0) {
        return { status: 400, respcode: 1019, message: 'We are unable to proceed with your request due to a technical issue(#3-3). Please try again later.', action_code: 1001, sellerWiseQnt: 0, sellerWiseAmt: 0, sellerWiseCharges: 0 }
      }

      sellerWiseAmt = _.sumBy(fetchOndcResult.getResultsResponse[0].order.quote.breakup, obj => {
        if (obj['@ondc/org/title_type'] == 'item') return parseFloat(_.get(obj, 'price.value', 0))
        return 0
      })

      sellerWiseQnt = _.sumBy(fetchOndcResult.getResultsResponse[0].order.quote.breakup, obj => {
        if (obj['@ondc/org/title_type'] == 'item') return parseFloat(_.get(obj, "['@ondc/org/item_quantity'].count", 0))
        return 0
      })

      sellerWiseCharges = _.sumBy(fetchOndcResult.getResultsResponse[0].order.quote.breakup, obj => {
        if (obj['@ondc/org/title_type'] == 'delivery') return parseFloat(_.get(obj, 'price.value', 0))
        return 0
      })

      const cart_product_quantity = _.sumBy(cartData, 'product_quantity')
      const cart_product_total_price = _.sumBy(cartData, 'product_total_price')
      const cart_delivery_charges = _.get(cartData, '[0].delivery_charges', 0)
      this.logger({ pagename: basename(__filename), action: 'init', type: 'seller breakout details' + ` - ${uniqueIdForLog}`, fields: { sellerWiseAmt, cart_product_total_price, sellerWiseQnt, cart_product_quantity, sellerWiseCharges, cart_delivery_charges } })

      if (sellerWiseAmt != cart_product_total_price || sellerWiseQnt != cart_product_quantity || sellerWiseCharges != cart_delivery_charges) {
        return { status: 400, respcode: 1019, message: 'We are unable to proceed with your request due to a technical issue(#3-4). Please try again later.', action_code: 1001, sellerWiseQnt: 0, sellerWiseAmt: 0, sellerWiseCharges: 0 }
      }
      return { ...fetchOndcResult, sellerWiseQnt, sellerWiseAmt, sellerWiseCharges }
    }
    return { ...fetchOndcResult, sellerWiseQnt: 0, sellerWiseAmt: 0, sellerWiseCharges: 0 }
  }

  /** ++++++++++
   * @typedef {{ provier_wise_data: Object }} requestData
   * @param {requestData} fields
   * @returns {Promise<{ fetchOndcResult: { status: 200, respcode: number, message: string, action_code: 1000 } }>}
   */
  async confirm ({ fields, searchData, cartData, customerData, uniqueIdForLog, request, transactionRepository, connection }) {
    this.logger({ pagename: basename(__filename), action: 'confirm' + ` - ${uniqueIdForLog}`, type: 'request Data', fields: JSON.stringify({ fields, searchData, cartData, customerData, uniqueIdForLog, request }) })

    if (fields.status && fields.status != 200) return []

    // Craete request data for search API *****
    const ondc_transaction_id_for_request = _.get(cartData, '[0].ondc_transaction_id', await this.createOndcTransactionId()) || await this.createOndcTransactionId()
    const ondc_message_id_for_request = await this.createOndcTransactionId()

    const fulfilment_id = _.get(_.find(JSON.parse(searchData[0].fulfilment_type), fulfillment => fulfillment.type == 'Delivery'), 'id')
    const quoteData = _.get(fields, 'getResultsResponse[0].order.quote', false)
    const paymentData = _.get(fields, 'getResultsResponse[0].order.payment', false)
    if (!quoteData || !paymentData) return { status: 400, respcode: 1001, message: 'Invalid INIT API response', action_code: 1001 }

    // Create INIT API request *****
    const decryptedSearchRequestData = [
      {
        context: {
          transaction_id: ondc_transaction_id_for_request,
          message_id: ondc_message_id_for_request,
          bpp_id: searchData[0].search_bpp_id
        },
        message: {
          id: fields.ondc_order_id,
          state: 'Created',
          provider: {
            id: searchData[0].prov_id,
            locations: [
              {
                id: searchData[0].prov_loc_id
              }
            ]
          },
          items: [
            {
              id: searchData[0].prov_items_id,
              quantity: {
                count: parseInt(cartData[0].product_quantity)
              },
              fulfilment_id: fulfilment_id
            }
          ],
          billing: {
            name: customerData[0].customer_name,
            address: {
              door: customerData[0].address1,
              name: customerData[0].customer_name,
              locality: customerData[0].locality,
              city: customerData[0].city,
              state: customerData[0].state,
              country: customerData[0].country,
              area_code: customerData[0].pincode
            },
            phone: request.mobile_id,
            email: customerData[0].email_id
          },
          fulfillments: [
            {
              id: 'Fulfillment1',
              type: 'Delivery',
              tracking: false,
              provider_id: 'slrp-1332211',
              end: {
                contact: {
                  email: customerData[0].email_id,
                  phone: request.mobile_id
                },
                location: {
                  gps: request.customer_delivery_location,
                  address: {
                    door: customerData[0].address1,
                    name: customerData[0].customer_name,
                    locality: customerData[0].locality,
                    city: customerData[0].city,
                    state: customerData[0].state,
                    country: customerData[0].country,
                    area_code: customerData[0].pincode
                  }
                }
              }
            }
          ],
          quote: quoteData,
          payment: {
            status: 'PAID',
            ...paymentData,
            uri: 'https://ondc.transaction.com/payment',
            tl_method: 'http/get',
            params: {
              currency: 'INR',
              transaction_id: fields.aggregator_order_id,
              amount: `${fields.amount}`
            }
          }
        }
      }
    ]
    this.logger({ pagename: basename(__filename), action: 'confirm', type: 'INIT API decrypted request Data' + ` - ${uniqueIdForLog}`, fields: { ondc_transaction_id_for_request, ondc_message_id_for_request, decryptedSearchRequestData } })
    // Fetch API Result *****
    const fetchOndcResult = await this.fetchOndcResult({ apiRequest: decryptedSearchRequestData, apiEndpoint: 'confirm', uniqueIdForLog })
    this.logger({ pagename: basename(__filename), action: 'confirm', type: 'fetchOndcResult' + ` - ${uniqueIdForLog}`, fields: fetchOndcResult })

    const orderState = constants.ondc.confirmAndStatusApiStateForSuccess
    const orderStateForPending = constants.ondc.confirmAndStatusApiStateForPending
    const orderStateForFailed = constants.ondc.confirmAndStatusApiStateForFailed
    this.logger({ pagename: basename(__filename), action: 'confirm', type: 'ondcConfirmApi response fetchOndcResult' + ` - ${uniqueIdForLog}`, fields: fetchOndcResult })

    // Error parameter validation
    if (fetchOndcResult.status == 200 && _.get(fetchOndcResult, 'getResultsResponse[0].error', null)) {
      const error = fetchOndcResult.getResultsResponse[0].error
      const error_code = error && error.code ? `${error.code} - ` : ''
      const error_message = error && error.message ? error.message : 'Something went wrong!'
      return { status: 400, respcode: 1028, message: 'We are unable to proceed with your request due to a technical issue(#4-4). Please try again later.', action_code: 1001, error_msg: `${error_code}${error_message}` }
    }

    // CONFIRM API response validation ****
    if (_.get(fetchOndcResult, 'getResultsResponse[0].order.state', null)) {
      if (orderState.includes(fetchOndcResult.getResultsResponse[0].order.state.toLowerCase())) {
        // Update ma_ondc_seller_details table *****
        const whereClause = { ondc_transaction_id: ondc_transaction_id_for_request, search_bpp_id: searchData[0].search_bpp_id }
        const setData = { confirm_api_response: JSON.stringify(fetchOndcResult.getResultsResponse), shipping_status: fetchOndcResult.getResultsResponse[0].order.state }
        const upateTableData = await transactionRepository.upateTableData({ connection, whereClause, setData, tableName: 'ma_ondc_seller_details' })
        this.logger({ pagename: basename(__filename), action: 'confirm', type: 'upateTableData' + ` - ${uniqueIdForLog}`, fields: upateTableData })

        // Return Success response
        return { ...fetchOndcResult, ondc_order_id: fetchOndcResult.getResultsResponse[0].order.id, ondc_state: fetchOndcResult.getResultsResponse[0].order.state }
      } else if (orderStateForPending.includes(fetchOndcResult.getResultsResponse[0].order.state.toLowerCase())) {
        // Confirm API state is not valid to proceed
        return { status: 400, respcode: 1001, message: 'We are unable to proceed with your request due to a technical issue(#4-1). Please try again later.', action_code: 1001 }
      } else if (orderStateForFailed.includes(fetchOndcResult.getResultsResponse[0].order.state.toLowerCase())) {
        // Confirm API is not valid to proceed
        return { status: 400, respcode: 1028, message: 'Confirm API is not valid to proceed', action_code: 1001 }
      } else return { status: 400, respcode: 1001, message: 'We are unable to proceed with your request due to a technical issue(#4-2). Please try again later.', action_code: 1001 } // Confirm API is not valid to proceed
    } else {
      // Confirm API is not valid to proceed
      return { status: 400, respcode: 1001, message: 'We are unable to proceed with your request due to a technical issue(#4-3). Please try again later.', action_code: 1001 }
    }
  }
}
