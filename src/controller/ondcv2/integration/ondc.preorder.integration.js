// Package Imports
const { basename } = require('path')
const _ = require('lodash')
const md5 = require('md5')

// Code Imports
const OndcBaseIntegration = require('./ondc.base.integration')
const IntegrationError = require('../../../util/errors/IntegrationError')

module.exports = class OndcPreOrderIntegration extends OndcBaseIntegration {
  constructor (logger) {
    super(logger)
    this.logger = logger
  }

  /**
   * @typedef {{ ma_user_id: number, userid: number, search_string: String, delivery_location: String, storeLimit: number, storeOffset: number, ondc_transaction_id: String, itemsLimit: number, itemsOffset: number, search_type?: 'PRODUCT'|'CATEGORY'|'PROVIDER', sort_object: { store_distance: 'ASC'|'DESC', price: 'ASC'|'DESC', store_name: 'ASC'|'DESC'} }} requestData
   * @param {requestData} fields
   * @returns {Promise<{ fetchOndcResult: { status: 200, respcode: number, message: string, action_code: 1000 }, ondc_transaction_id_for_request: string, ondc_message_id_for_request: string, decryptedSearchRequestData: { context: { transaction_id: string, message_id: string }, message: { criteria: { delivery_location: string, search_string?: string, category_id?: string, provider_id?: string}} } }>}
   */
  async search (fields) {
    this.logger({ pagename: basename(__filename), action: 'search', type: 'request Data', fields })
    const searchTypeKey = { PRODUCT: 'search_string', CATEGORY: 'category_id', PROVIDER: 'provider_id' }

    // Craete request data for search API *****
    const ondc_transaction_id_for_request = _.get(fields, 'ondc_transaction_id', await this.createOndcTransactionId()) || await this.createOndcTransactionId()
    const ondc_message_id_for_request = await this.createOndcTransactionId()

    const criteria = {}
    if (_.get(fields, 'search_type')) criteria[searchTypeKey[fields.search_type]] = fields.search_string
    else criteria[searchTypeKey.PRODUCT] = fields.search_string

    const decryptedSearchRequestData = {
      context: {
        transaction_id: ondc_transaction_id_for_request,
        message_id: ondc_message_id_for_request
      },
      message: {
        criteria: {
          ...criteria,
          delivery_location: fields.delivery_location
        }
      }
    }

    this.logger({ pagename: basename(__filename), action: 'search', type: 'Search API encrypted request Data', fields: { ondc_transaction_id_for_request, ondc_message_id_for_request, decryptedSearchRequestData } })

    // Fetch API Result *****
    const fetchOndcResult = await this.fetchOndcResult({ apiRequest: decryptedSearchRequestData, apiEndpoint: 'search' })
    this.logger({ pagename: basename(__filename), action: 'search', type: 'fetchOndcResult', fields: fetchOndcResult })
    if (fetchOndcResult.status !== 200) throw new IntegrationError(fetchOndcResult)
    return { fetchOndcResult, ondc_transaction_id_for_request, ondc_message_id_for_request, decryptedSearchRequestData }
  }

  /** ++++++++++
   * @typedef {{ provier_wise_data: Object }} requestData
   * @param {requestData} fields
   * @returns {Promise<{ fetchOndcResult: { status: 200, respcode: number, message: string, action_code: 1000 } }>}
   */
  async select (provier_wise_data, uniqueIdForLog) {
    this.logger({ pagename: basename(__filename), action: 'select' + ` - ${uniqueIdForLog}`, type: 'request Data', fields: JSON.stringify(provier_wise_data) })

    // Craete request data for search API *****
    const ondc_transaction_id_for_request = _.get(provier_wise_data, '[0].ondc_transaction_id', await this.createOndcTransactionId()) || await this.createOndcTransactionId()
    const ondc_message_id_for_request = await this.createOndcTransactionId()

    const decryptedSearchRequestData = [
      {
        context: {
          transaction_id: ondc_transaction_id_for_request,
          message_id: ondc_message_id_for_request
        },
        message: {
          cart: {
            items: await provier_wise_data.map(element => {
              return {
                id: _.toString(element.product_id),
                quantity: {
                  count: _.toNumber(element.quantity)
                },
                bpp_id: _.toString(_.get(element, 'searchData[0].search_bpp_id', null)),
                provider: {
                  id: _.toString(element.provider_id),
                  locations: [
                    _.toString(_.get(element, 'searchData[0].prov_loc_id', null))
                  ]
                }
              }
            })
          },
          fulfillments: [
            {
              end: {
                location: {
                  gps: _.toString(_.get(provier_wise_data[0], 'searchData[0].prov_loc_gps', null)),
                  address: {
                    area_code: _.toString(_.get(provier_wise_data[0], 'searchData[0].prov_loc_addr_area_code', null))
                  }
                }
              }
            }
          ]
        }
      }
    ]

    this.logger({ pagename: basename(__filename), action: 'select', type: 'Select API encrypted request Data' + ` - ${uniqueIdForLog}`, fields: { ondc_transaction_id_for_request, ondc_message_id_for_request, decryptedSearchRequestData } })

    // Fetch API Result *****
    const fetchOndcResult = await this.fetchOndcResult({ apiRequest: decryptedSearchRequestData, apiEndpoint: 'select', uniqueIdForLog })
    this.logger({ pagename: basename(__filename), action: 'select', type: 'fetchOndcResult' + ` - ${uniqueIdForLog}`, fields: fetchOndcResult })
    return { fetchOndcResult, provier_wise_data }
  }
}
