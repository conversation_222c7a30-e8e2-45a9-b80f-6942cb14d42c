module.exports = {
  api: {
    AFFILIATE_Header_value: 'AF0059AF1658123547',
    ma_side_api_url: {
      basePath: 'https://preprod-ondc.airpay.ninja',
      search: '/api/search',
      select: '/api/select',
      init: '/api/init',
      confirm: '/api/confirm',
      status: '/api/status',
      cancel: '/api/cancel',
      get_results: '/api/get_results',
      support: '/api/support',
      update: '/api/update'
    },
    ma_side_api_constants: {
      secrete_key: '5PbpCqeioHQ3MWj6dJVeluXDE7UttN8P'
    }
  },
  ondc: {
    confirmAndStatusApiStateForSuccess: ['created', 'packed', 'shipped', 'out for delivery', 'delivered', 'accepted'],
    confirmAndStatusApiStateForPending: ['rto initiated', 'rto delivered'],
    confirmAndStatusApiStateForFailed: ['cancelled'],
    forInvoiceCancellableButton: ['created', 'packed', 'accepted'],
    forInvoiceReturnableButton: ['delivered'],
    otpTypes: { PURCHASE: 'ONDC', HISTORY: 'ONDC', CANCEL: 'ONDCC', RETURN: 'ONDCC' }
  },
  validations: {
    orderid_regex: 'MAONDC[0-9]{13,14}',
    otp_orderid_regex: 'MAONDCC[0-9]{13,14}'
  },
  validationErrors: {
    orderid: {
      'string.base': 'Order ID must be a string.',
      'any.required': 'Order ID is required.',
      'string.pattern.base': 'Please enter a valid Order ID.'
    },
    otp_orderid: {
      'string.base': 'OTP Order ID must be a string.',
      'any.required': 'OTP Order ID is required.',
      'string.pattern.base': 'Please enter a valid OTP Order ID.'
    }
  }
}
