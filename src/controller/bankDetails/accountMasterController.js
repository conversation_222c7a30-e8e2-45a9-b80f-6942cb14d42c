const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
// const { lokiLogger } = require('../../util/lokiLogger')
const validator = require('../../util/validator')

class BankMaster extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_airpay_account_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_airpay_account_master_id'
  }

  static async getAccountMasterList (_, fields) {
    log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const sql = 'SELECT ma_airpay_account_master_id,account_number,account_type,bank_name,account_name,ifsc_code,bank_address,bank_city,bank_state,bank_pincode,bank_landline,bank_status,virtual_account_no,upi_address,payment_modes,va_account_flag,addedon FROM ma_airpay_account_master WHERE bank_status = \'Y\' ORDER BY bank_name ASC;'
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'sql', fields: sql })
      const account_list = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'response', fields: account_list })
      if (account_list === undefined && account_list === null && account_list.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      const commonKeyValue = require('../../util/commonKeyValue')
      const keyValuePaymentModes = commonKeyValue.payment_modes
      for (let index = 0; index < account_list.length; index++) {
        // manually set the Flag to N to show MODE OF PAYMENT in frontend
        account_list[index].va_account_flag = 'N'
        const { payment_modes } = account_list[index]
        let modeObject = {}
        try {
          modeObject = JSON.parse(payment_modes)
        } catch (error) {
          console.log('ErrorJsonParsePatmentMode>>', error, payment_modes)
        }
        const modeKeyValueArr = []
        if (modeObject != null && ('modes' in modeObject) && modeObject.modes.length > 0) {
          for (const key in modeObject.modes) {
            const currentMode = modeObject.modes[key]
            const keyValueObj = {}
            keyValueObj.key = currentMode
            if (currentMode in keyValuePaymentModes) {
              keyValueObj.value = keyValuePaymentModes[currentMode]
            } else {
              keyValueObj.value = currentMode
            }

            modeKeyValueArr.push(keyValueObj)
          }
        }

        account_list[index].payment_modes = modeKeyValueArr
      }
      // fetch user_type from ma_user_master (RT/DT/SDT). If DT/SDT-->show_ba_flag-Y else show_ba_flag-N (show_ba_flag--->Add Bank Account for DT/SDT)
      let show_ba_flag = 'N'
      const fetchUserDetails = `SELECT user_type from ma_user_master  where profileid = '${fields.ma_user_id}' and userid = '${fields.userid}' limit 1;`
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'fetchUserDetails', fields: fetchUserDetails })
      const fetchUserDetailsResp = await this.rawQuery(fetchUserDetails, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'fetchUserDetailsResp', fields: fetchUserDetailsResp })

      if (fetchUserDetailsResp.length > 0 && (fetchUserDetailsResp[0].user_type == 'DT' || fetchUserDetailsResp[0].user_type == 'SDT')) {
      // Fetch list of account no. added for userId (Only for DT and SDT)
        const fetchBankDetails = `SELECT ma_cashin_bank_details_id,ma_user_id,bank_name,account_holder_name,account_number from ma_cashin_bank_details where ma_user_id = '${fields.ma_user_id}';`
        log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'fetchBankDetails', fields: fetchBankDetails })
        const fetchBankDetailsResp = await this.rawQuery(fetchBankDetails, connection)
        log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'fetchBankDetailsResp', fields: fetchBankDetailsResp })
        // DT/SDT can't add more than 3 Accounts
        if (fetchBankDetailsResp.length < 3) {
          show_ba_flag = 'Y'
        }
      }
      console.log('account_list--->', account_list)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], account_list, show_ba_flag }
    } catch (error) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getVaAccountList (_, fields) {
    log.logger({ pagename: 'accountMasterController.js', action: 'getVaAccountList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const vaAccountList = `SELECT 
                      aam.bank_name,
                      aam.ma_airpay_account_master_id,
                      aam.account_number,
                      cvd.va_number,
                      aam.account_type,
                      aam.account_name,
                      aam.bank_city,
                      aam.bank_icon,
                      aam.bank_address,
                      aam.bank_state,
                      aam.bank_pincode,
                      aam.bank_landline,
                      aam.va_account_flag,
                      aam.ifsc_code
                  FROM 
                      ma_airpay_account_master AS aam
                  LEFT JOIN 
                      ma_cashin_va_details AS cvd 
                      ON aam.ma_airpay_account_master_id = cvd.ma_airpay_account_master_id                   
                  WHERE 
                      cvd.ma_user_id = '${fields.ma_user_id}'    
                      AND aam.bank_status = 'Y' 
                      AND aam.va_account_flag = 'Y'
                      AND cvd.va_status = 'A'
                  ORDER BY 
                      aam.bank_name ASC;`
      log.logger({ pagename: 'accountMasterController.js', action: 'getVaAccountList', type: 'vaAccountList', fields: vaAccountList })
      const vaAccountListResp = await this.rawQuery(vaAccountList, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getVaAccountList', type: 'vaAccountListResp', fields: vaAccountListResp })
      if (vaAccountListResp === undefined && vaAccountListResp === null && vaAccountListResp.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }

      for (let index = 0; index < vaAccountListResp.length; index++) {
        const virtualAccountNo = vaAccountListResp[index].va_number
        const vaAccountFlag = vaAccountListResp[index].va_account_flag
        // const accountNumber = vaAccountListResp[index].account_number
        // const bankName = vaAccountListResp[index].bank_name
        if (vaAccountFlag == 'Y' && virtualAccountNo != null) {
          vaAccountListResp[index].account_number = vaAccountListResp[index].va_number
          vaAccountListResp[index].va_account_flag = vaAccountFlag
        } else {
          vaAccountListResp[index].va_account_flag = 'N'
        }
      }

      console.log('vaAccountListResp--->', vaAccountListResp)
      // filter out the Object whose va_account_flag = 'N'
      // const filteredvaAccountListResp = vaAccountListResp.filter(item => item.va_account_flag !== 'N')

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], va_account_list: vaAccountListResp }
    } catch (error) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
  /*
  static async getAccountMasterList (_, fields) {
    log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const sql = `SELECT ma_airpay_account_master.bank_name, ma_airpay_account_master.ma_airpay_account_master_id,ma_airpay_account_master.account_number,ma_cashin_va_details.va_number, ma_airpay_account_master.* FROM ma_airpay_account_master LEFT JOIN ma_cashin_va_details ON ma_airpay_account_master.ma_airpay_account_master_id = ma_cashin_va_details.ma_airpay_account_master_id AND ma_cashin_va_details.ma_user_id = '${fields.ma_user_id}' AND ma_cashin_va_details.va_status = 'A' WHERE ma_airpay_account_master.bank_status = 'Y' ORDER BY bank_name ASC;`
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'sql', fields: sql })
      // lokiLogger.info({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'sql', fields: sql })
      const account_list = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'response', fields: account_list })
      // lokiLogger.info({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'response', fields: account_list })
      if (account_list === undefined && account_list === null && account_list.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }

      const commonKeyValue = require('../../util/commonKeyValue')
      const keyValuePaymentModes = commonKeyValue.payment_modes
      for (let index = 0; index < account_list.length; index++) {
        const virtualAccountNo = account_list[index].va_number
        const vaAccountFlag = account_list[index].va_account_flag
        const accountNumber = account_list[index].account_number
        const bankName = account_list[index].bank_name
        if (vaAccountFlag == 'Y' && virtualAccountNo != null) {
          // Change in previous flow
          if (bankName.toLowerCase().startsWith('icici')) {
            account_list[index].ifsc_code = 'ICIC0000106'
          }
          account_list[index].account_number = account_list[index].va_number
          account_list[index].va_account_flag = vaAccountFlag
        } else {
          account_list[index].va_account_flag = 'N'
        }
        const { payment_modes } = account_list[index]
        let modeObject = {}
        try {
          modeObject = JSON.parse(payment_modes)
        } catch (error) {
          console.log('ErrorJsonParsePatmentMode>>', error, payment_modes)
        }
        const modeKeyValueArr = []
        if (modeObject != null && ('modes' in modeObject) && modeObject.modes.length > 0) {
          for (const key in modeObject.modes) {
            const currentMode = modeObject.modes[key]
            const keyValueObj = {}
            keyValueObj.key = currentMode
            if (currentMode in keyValuePaymentModes) {
              keyValueObj.value = keyValuePaymentModes[currentMode]
            } else {
              keyValueObj.value = currentMode
            }

            modeKeyValueArr.push(keyValueObj)
          }
        }

        account_list[index].payment_modes = modeKeyValueArr
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], account_list }
    } catch (error) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
  */

  static async createCashinBankDetails (_, fields) {
    log.logger({ pagename: 'accountMasterController.js', action: 'createCashinBankDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'bank_name', 'account_holder_name', 'account_number', 'branch', 'ifsc_code'])
      if (validateTransferParamRes.status != 200) return validateTransferParamRes

      const fetchUserDetails = `SELECT user_type from ma_user_master  where profileid = '${fields.ma_user_id}' and userid = '${fields.userid}' limit 1;`
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'fetchUserDetails', fields: fetchUserDetails })
      const fetchUserDetailsResp = await this.rawQuery(fetchUserDetails, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getAccountMasterList', type: 'fetchUserDetailsResp', fields: fetchUserDetailsResp })

      // only DT & SDT can add Bank Account
      if (fetchUserDetailsResp.length > 0 && (fetchUserDetailsResp[0].user_type != 'DT' && fetchUserDetailsResp[0].user_type != 'SDT')) {
        return { status: 400, respcode: 1001, message: 'You are not authorised to add bank Account.' }
      }

      const checkSql = `SELECT ma_user_id FROM ma_cashin_bank_details WHERE ma_user_id = '${fields.ma_user_id}'`
      const checkResult = await this.rawQuery(checkSql, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'createCashinBankDetails', type: 'request', fields: checkResult })
      if (checkResult.length >= 3) {
        return { status: 400, respcode: 1001, message: 'User cannot add more than 3 bank accounts.' }
      }
      const user_type = fetchUserDetailsResp[0].user_type || ''
      const sql = `INSERT INTO ma_cashin_bank_details (ma_user_id, userid, bank_name, account_holder_name, account_number, branch, ifsc_code,remitter_relationship, account_status,merchant_type) VALUES ('${fields.ma_user_id}','${fields.userid}','${fields.bank_name}','${fields.account_holder_name}','${fields.account_number}','${fields.branch}','${fields.ifsc_code}','${fields.remitter_relationship}','P','${user_type}')`

      log.logger({ pagename: 'accountMasterController.js', action: 'createCashinBankDetails', type: 'request', fields: sql })

      var indiabankifsc = /^[A-Za-z]{4}0[A-Z0-9a-z]{6}$/
      const ifscCheck = indiabankifsc.test(fields.ifsc_code)
      if (!ifscCheck) {
        return { status: 400, respcode: 3001, message: errorMsg.responseCode[3001] }
      }

      const isValidReAccountNo = validator.validateAccountNo(fields.account_number)
      if (!isValidReAccountNo) {
        return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':re_account_number ' }
      }
      const result = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'createCashinBankDetails', type: 'response', fields: result })

      if (result.affectedRows === 1) {
        return { status: 200, respcode: 1000, message: 'Your remitter account request is under review.', messsageNote: 'Note: Once the account is added, please contact customer care for any changes.' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (error) {
      log.logger({ pagename: 'accountMasterController.js', action: 'createCashinBankDetails', type: 'error', error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getCashinBankDetailList (_, fields) {
    log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const sql = `Select
                     bank_name,
                     account_holder_name,
                     account_number,
                     branch,
                     ifsc_code,
                     remitter_relationship,
                     account_status
                   FROM
                     ma_cashin_bank_details
                   WHERE
                     ma_user_id = '${fields.ma_user_id}'
                   AND userid = '${fields.userid}' 
                   AND (account_status = 'A' OR account_status = 'P');`

      const bank_details_list = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'request', fields: bank_details_list })

      if (bank_details_list === undefined && bank_details_list === null && bank_details_list.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      let show_ba_flag = 'N'
      const fetchUserDetails = `SELECT user_type from ma_user_master  where profileid = '${fields.ma_user_id}' and userid = '${fields.userid}' limit 1;`
      log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'fetchUserDetails', fields: fetchUserDetails })
      const fetchUserDetailsResp = await this.rawQuery(fetchUserDetails, connection)
      log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'fetchUserDetailsResp', fields: fetchUserDetailsResp })

      if (fetchUserDetailsResp.length > 0 && (fetchUserDetailsResp[0].user_type == 'DT' || fetchUserDetailsResp[0].user_type == 'SDT')) {
      // Fetch list of account no. added for userId (Only for DT and SDT)
        const fetchBankDetails = `SELECT ma_cashin_bank_details_id,ma_user_id,bank_name,account_holder_name,account_number from ma_cashin_bank_details where ma_user_id = '${fields.ma_user_id}';`
        log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'fetchBankDetails', fields: fetchBankDetails })
        const fetchBankDetailsResp = await this.rawQuery(fetchBankDetails, connection)
        log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'fetchBankDetailsResp', fields: fetchBankDetailsResp })
        // DT/SDT can't add more than 3 Accounts
        if (fetchBankDetailsResp.length < 3) {
          show_ba_flag = 'Y'
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bank_details_list, show_ba_flag }
    } catch (error) {
      log.logger({ pagename: 'accountMasterController.js', action: 'getCashinBankDetailList', type: 'error', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = BankMaster
