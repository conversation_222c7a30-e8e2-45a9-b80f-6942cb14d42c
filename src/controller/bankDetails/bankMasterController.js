const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
// const { lokiLogger } = require('../../util/lokiLogger')

class BankMaster extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_bank_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_bank_master_id'
  }

  static async checkBank (maBankMasterId, con) {
    log.logger({ pagename: 'bankMasterController.js', action: 'checkBank', type: 'request', fields: { maBankMasterId: maBankMasterId } })
    // lokiLogger.info({ pagename: 'bankMasterController.js', action: 'checkBank', type: 'request', fields: { maBankMasterId: maBankMasterId } })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_bank_master_id,bank_name FROM ma_bank_master WHERE ma_bank_master_id =${maBankMasterId} AND bank_status = '1' limit 1`
      const checkbank = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'bankMasterController.js', action: 'checkBank', type: 'response', fields: checkbank })
      // lokiLogger.info({ pagename: 'bankMasterController.js', action: 'checkBank', type: 'response', fields: checkbank })
      if (checkbank.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: checkbank[0] }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'bankMasterController.js', action: 'checkBank', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: 'bankMasterController.js', action: 'checkBank', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [CHECK_BANK]' }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getBanks (_, fields, connRead) {
    log.logger({ pagename: 'bankMasterController.js', action: 'getBanks', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'bankMasterController.js', action: 'getBanks', type: 'request', fields: fields })
    const isSet = (connRead === null || connRead === undefined)
    const connection = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connRead
    // Co-operative Bank Merchant Onboarding Changes
    if (fields.campaign_flag) {
      const fetchCbList = `SELECT ma_bank_master_id,affiliate_id,bank_name FROM ma_cooperative_bank_mapping where campaign_flag = '${fields.campaign_flag}' and bank_status = 1 limit 1`
      let fetchCbListResult = await this.rawQuery(fetchCbList, connection)
      log.logger({ pagename: 'bankMasterController.js', action: 'getBanks', type: 'fetchCbListResult', fields: fetchCbListResult })
      if (fetchCbListResult.length > 0) {
        fetchCbListResult = fetchCbListResult.map(obj => ({
          ...obj,
          campaign_flag: fields.campaign_flag
        }))
        return { status: 200, respcode: 1000, total: fetchCbListResult.length, message: errorMsg.responseCode[1000], bankList: fetchCbListResult }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    }

    let totalFlag = 0
    var sql = `SELECT SQL_CALC_FOUND_ROWS 
                ma_bank_master.ma_bank_master_id,ma_bank_master.bank_name,
                IF(ma_bank_branch.branch IS NULL,'',ma_bank_branch.branch) AS defaultBranch,
                IF(ma_bank_branch.ifsc IS NULL,'',ma_bank_branch.ifsc) AS defaultIfsc
              FROM ma_bank_master 
              LEFT JOIN ma_bank_branch ON ma_bank_master.ma_bank_master_id = ma_bank_branch.ma_bank_master_id AND default_flag = '1'
              WHERE bank_status ='1'`
    const stringFormat = /^[-\w\s]+$/
    if (fields.bank_name && stringFormat.test(fields.bank_name)) {
      sql += ` AND bank_name like '%${fields.bank_name}%'`
    }
    if (fields.limit && (fields.offset !== null && fields.offset !== undefined)) {
      totalFlag = 1
      sql += ` LIMIT ${fields.offset},${fields.limit}`
    }
    // console.log(sql)
    const bankList = await this.rawQuery(sql, connection)

    const countSql = 'SELECT FOUND_ROWS() AS total'
    const countResult = await this.rawQuery(countSql, connection)
    let total = 0
    if (countResult.length > 0) {
      total = countResult[0].total
    }

    log.logger({ pagename: 'bankMasterController.js', action: 'getBanks', type: 'response', fields: bankList })
    // lokiLogger.info({ pagename: 'bankMasterController.js', action: 'getBanks', type: 'response', fields: bankList })
    if (isSet) connection.release()
    if (bankList !== undefined && bankList !== null && bankList.length > 0) {
      if (totalFlag) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], total, bankList }
      }
      return bankList
    }
    return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
  }
}

module.exports = BankMaster
