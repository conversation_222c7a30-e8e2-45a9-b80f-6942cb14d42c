const DAO = require('../../lib/dao')
const errorMsg = require('../../util/error')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
// const { lokiLogger } = require('../../util/lokiLogger')

class BankBranch extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_bank_branch'
  }

  static get PRIMARY_KEY () {
    return 'ma_bank_branch_id'
  }

  static async validateIfscCode (ifscCode, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateIfscCode', type: 'request', fields: { ifscCode: ifscCode } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'validateIfscCode', type: 'request', fields: { ifscCode: ifscCode } })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_bank_branch_id, branch, ma_bank_master_id FROM ma_bank_branch WHERE ifsc ='${ifscCode}' AND branch_status = '1' limit 1`
      const checkifsc = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'bankBranchController.js', action: 'validateIfscCode', type: 'response', fields: checkifsc })
      // lokiLogger.info({ pagename: 'bankBranchController.js', action: 'validateIfscCode', type: 'response', fields: checkifsc })
      if (checkifsc.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ...checkifsc[0] }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'bankBranchController.js', action: 'validateIfscCode', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: 'bankBranchController.js', action: 'validateIfscCode', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [CHECK_IFSC]' }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getBranches (_, fields) {
    log.logger({ pagename: 'bankBranchController.js', action: 'getBranches', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'bankBranchController.js', action: 'getBranches', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    if (fields === null || fields === undefined) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] }
    }
    var sql = `SELECT SQL_CALC_FOUND_ROWS branch,ifsc,centre,district,bm.ma_bank_master_id,address,micr_code,state 
                FROM ma_bank_branch bb 
                join ma_bank_master bm on bb.ma_bank_master_id = bm.ma_bank_master_id 
                WHERE bm.bank_status ='1' 
                AND branch_status='1' 
                AND bm.ma_bank_master_id=${fields.ma_bank_master_id} `
    const stringFormat = /^[-\w\s]+$/
    if (fields.search_value && stringFormat.test(fields.search_value)) {
    // if (!(fields.search_value === null || fields.search_value === undefined)) {
      sql += ` AND (branch like '%${fields.search_value}%' OR address like '%${fields.search_value}%' OR centre like '%${fields.search_value}%'  OR district like '%${fields.search_value}%' OR ifsc like '%${fields.search_value}%' )`
    }
    if (fields.limit && (fields.offset !== null && fields.offset !== undefined)) {
      sql += ` LIMIT ${fields.limit} OFFSET ${fields.offset}`
    }
    // console.log(sql)
    log.logger({ pagename: 'bankBranchController.js', action: 'getBranches', type: 'getBranchesSQL', fields: sql })
    // lokiLogger.info({ pagename: 'bankBranchController.js', action: 'getBranches', type: 'getBranchesSQL', fields: sql })
    console.time('TIMER_GET_BRANCH')
    const branchList = await this.rawQuery(sql, connection)
    console.timeEnd('TIMER_GET_BRANCH')

    const countSql = 'SELECT FOUND_ROWS() AS total'
    const countResult = await this.rawQuery(countSql, connection)
    let total = 0
    if (countResult.length > 0) {
      total = countResult[0].total
    }

    var defaultSql = `SELECT branch,ifsc,centre,district,address,micr_code,state 
                      FROM ma_bank_branch bb 
                      JOIN ma_bank_master bm ON bb.ma_bank_master_id = bm.ma_bank_master_id 
                      WHERE bm.bank_status ='1' 
                      AND branch_status='1' 
                      AND default_flag = '1'
                      AND bm.ma_bank_master_id=${fields.ma_bank_master_id}`
    console.time('TIMER_GET_DEFAULT_BRANCH')
    const defaultBranch = await this.rawQuery(defaultSql, connection)
    console.timeEnd('TIMER_GET_DEFAULT_BRANCH')
    const defaultVal = { branch: '', ifsc: '' }
    if (defaultBranch.length > 0) {
      defaultVal.branch = defaultBranch[0].branch
      defaultVal.ifsc = defaultBranch[0].ifsc
    }

    log.logger({ pagename: 'bankBranchController.js', action: 'getBranches', type: 'response', fields: branchList })
    // lokiLogger.info({ pagename: 'bankBranchController.js', action: 'getBranches', type: 'response', fields: branchList })
    connection.release()
    if (branchList.length > 0) {
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], defaultBranch: defaultVal.branch, defaultIfsc: defaultVal.ifsc, branchList, total }
    }
    return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
  }

  static async getBranchByIfsc (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT
                    bn.bank_name,
                    br.branch,
                    br.ifsc,
                    br.micr_code,
                    br.address,
                    br.centre,
                    br.state,
                    br.district,
                    br.ma_bank_master_id
                  FROM ma_bank_branch AS br
                  JOIN ma_bank_master AS bn ON br.ma_bank_master_id = bn.ma_bank_master_id
                  WHERE br.ifsc = '${fields.ifsc}'
                  AND bn.bank_status = 1
                  AND br.branch_status = 1`
      const queryResponse = await this.rawQuery(sql, connection)
      if (queryResponse.length > 0) {
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          bank_name: queryResponse[0].bank_name,
          branch: queryResponse[0].branch,
          ifsc: queryResponse[0].ifsc,
          micr_code: queryResponse[0].micr_code,
          address: queryResponse[0].address,
          centre: queryResponse[0].centre,
          state: queryResponse[0].state,
          district: queryResponse[0].district,
          ma_bank_master_id: queryResponse[0].ma_bank_master_id
        }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } catch (err) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getBranchByIfscForBank (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT
                    bn.ma_bank_master_id,
                    bn.bank_name,
                    br.branch,
                    br.ifsc,
                    br.micr_code,
                    br.address,
                    br.centre,
                    br.state,
                    br.district
                  FROM ma_bank_branch AS br
                  JOIN ma_bank_master AS bn ON br.ma_bank_master_id = bn.ma_bank_master_id
                  WHERE br.ifsc = '${fields.ifsc}'
                  AND bn.bank_status = 1
                  AND br.branch_status = 1`
      const queryResponse = await this.rawQuery(sql, connection)
      if (queryResponse.length > 0) {
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          ma_bank_master_id: queryResponse[0].ma_bank_master_id,
          bank_name: queryResponse[0].bank_name.trim(),
          branch: queryResponse[0].branch.trim(),
          ifsc: queryResponse[0].ifsc,
          micr_code: queryResponse[0].micr_code,
          address: queryResponse[0].address,
          centre: queryResponse[0].centre,
          state: queryResponse[0].state,
          district: queryResponse[0].district
        }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } catch (err) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getBranchByIfscWithAutoSearch (_, fields) {
    log.logger({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Made changes for Cooperative bank. Added campaign_flag condition
      if (fields.campaign_flag) {
        const fetchCbList = `SELECT ma_bank_master_id,affiliate_id,bank_name FROM ma_cooperative_bank_mapping where campaign_flag = '${fields.campaign_flag}' and bank_status = 1 limit 1`
        log.logger({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'fetchCbList', fields: fetchCbList })
        const fetchCbListResult = await this.rawQuery(fetchCbList, connection)
        log.logger({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'fetchCbListResult', fields: fetchCbListResult })
        if (fetchCbListResult.length > 0) {
          let sql = `SELECT CONCAT_WS('-',br.ifsc,br.branch,br.district,br.state,bn.bank_name) as ifscBranchBank,
                  bn.bank_name,
                  br.branch,
                  br.ifsc,
                  br.micr_code,
                  br.address,
                  br.centre,
                  br.state,
                  br.district,
                  br.ma_bank_master_id
                FROM ma_bank_branch AS br
                JOIN ma_bank_master AS bn ON br.ma_bank_master_id = bn.ma_bank_master_id
                WHERE br.ifsc = '${fields.search_value}'
                AND bn.ma_bank_master_id = ${fetchCbListResult[0].ma_bank_master_id}
                AND bn.bank_status = 1
                AND br.branch_status = 1`
          if (fields.limit !== undefined && fields.offset !== undefined) {
            sql += ` LIMIT ${fields.limit} OFFSET ${fields.offset}`
          } else {
            sql += ' LIMIT 10 '
          }
          log.logger({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'sql', fields: sql })
          const branchList = await this.rawQuery(sql, connection)
          log.logger({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'branchList', fields: branchList })

          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], branchList, total: branchList.length }
        }
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
      }
      let sql = `SELECT SQL_CALC_FOUND_ROWS 
                  CONCAT_WS('-',br.ifsc,br.branch,br.district,br.state,bn.bank_name) as ifscBranchBank,
                  bn.bank_name,
                  br.branch,
                  br.ifsc,
                  br.micr_code,
                  br.address,
                  br.centre,
                  br.state,
                  br.district,
                  br.ma_bank_master_id
                FROM ma_bank_branch AS br
                JOIN ma_bank_master AS bn ON br.ma_bank_master_id = bn.ma_bank_master_id
                WHERE br.ifsc = '${fields.search_value}'
                AND bn.bank_status = 1
                AND br.branch_status = 1`
      if (fields.limit !== undefined && fields.offset !== undefined) {
        sql += ` LIMIT ${fields.limit} OFFSET ${fields.offset}`
      } else {
        sql += ' LIMIT 10 '
      }
      // console.log(sql)
      log.logger({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'getBranchByIfscWithAutoSearchSQL', fields: sql })
      // lokiLogger.info({ pagename: 'bankBranchController.js', action: 'getBranchByIfscWithAutoSearch', type: 'getBranchByIfscWithAutoSearchSQL', fields: sql })
      console.time('TIMER_GET_BRANCH_SEARCH')
      const branchList = await this.rawQuery(sql, connection)
      console.timeEnd('TIMER_GET_BRANCH_SEARCH')

      const countSql = 'SELECT FOUND_ROWS() AS total'
      const countResult = await this.rawQuery(countSql, connection)
      let total = 0
      if (countResult.length > 0) {
        total = countResult[0].total
      }

      if (branchList.length > 0) {
        const branchListMap = branchList.map(branch => ({
          ...branch,
          ifscBranchBank: branch.ifscBranchBank.trim(),
          bank_name: branch.bank_name.trim(),
          branch: branch.branch.trim()
        }))
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], branchList: branchListMap, total }
      }
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
    } catch (err) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = BankBranch
