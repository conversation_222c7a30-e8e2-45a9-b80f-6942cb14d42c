const DAO = require('../../lib/dao')
const errorMsg = require('../../util/error')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const BillPay = require('../billpay/billpay')
const sms = require('../../util/sms')
const mailer = require('../../util/sendEmails')
const util = require('../../util/util')
const common = require('../../util/common')
// const { lokiLogger } = require('../../util/lokiLogger')

class BillpayProvider extends DAO {
  /**
       * Overrides TABLE_NAME with this class' backing table at MySQL
       */
  static get TABLE_NAME () {
    return 'ma_billpay_gateway_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_billpay_gateway_id'
  }

  static async checkOperatorBalance (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `
        SELECT gm.*,am.*
        FROM ma_billpay_gateway_master as gm
        JOIN ma_billpay_app_master as am on am.ma_billpay_appid = gm.ma_billpay_appid
        WHERE gm.gateway_status = 'Y' AND am.app_status = 'Y'
        AND gm.gateway_type = 'Prefunded'
        `
      const gatewayData = await this.rawQuery(sql, connection)
      if (gatewayData.length > 0) {
        for (let index = 0; index < gatewayData.length; index++) {
          const currentGateway = gatewayData[index]

          const shortname = currentGateway.shortname

          const checkBalanceObj = new BillPay(shortname, connection)

          const apiPayload = {
            api_token: currentGateway.api_token,
            gateway_user_id: currentGateway.gateway_user_id
          }
          const resultBalanceResponse = await checkBalanceObj.requestToProvider('GET_BALANCE', apiPayload)
          console.log('resultBalanceResponse', resultBalanceResponse)
          if (resultBalanceResponse.status === 200 && resultBalanceResponse.apiStatus === 'S') {
            const current_balance = resultBalanceResponse.apiData.balance

            const threshold_limit = await common.getSystemCodes(this, util.billpay.threshold_limit, connection)
            console.log('current_balance', current_balance)
            console.log('threshold_limit', threshold_limit)
            if (current_balance <= threshold_limit) {
              const notifyData = {
                app_name: currentGateway.app_name,
                threshold_limit: threshold_limit,
                gateway_user_id: currentGateway.gateway_user_id,
                current_balance: current_balance
              }

              await this.notifyBySms(notifyData, connection)
              await this.notifyByEmail(notifyData, connection)
            }
          }
        }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
      return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (error) {
      console.log('checkOperatorBalanceError', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkOperatorBalance', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'checkOperatorBalance', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async notifyBySms (notifyData, connection) {
    let balAlert = util.communication.BALBILLPAYALERT
    const mobilenumber = await common.getSystemCodes(this, util.billpay.informToOPsBySms, connection) // To do Dynamic Fro DB
    balAlert = balAlert.replace('<threshold limit>', notifyData.threshold_limit)
    balAlert = balAlert.replace('<app_name>', notifyData.app_name)
    balAlert = balAlert.replace('<client_id>', notifyData.gateway_user_id)
    balAlert = balAlert.replace('<Salutation>', util.communication.Signature)

    console.log('balAlertSMS', balAlert)
    await sms.sentSmsAsync(balAlert, mobilenumber, util.templateid.BALBILLPAYALERT)
  }

  static async notifyByEmail (notifyData, connection) {
    const serviceEmailStr = await common.getSystemCodes(this, util.billpay.informToOPsByEmail, connection)
    const serviceEmailArr = serviceEmailStr.split(',')
    const mailerData = {
      template: 'balanceAlert.ejs',
      content: {
        app_name: notifyData.app_name,
        gateway_user_id: notifyData.gateway_user_id,
        current_balance: notifyData.current_balance,
        threshold_limit: notifyData.threshold_limit
      },
      to: serviceEmailArr,
      subject: 'Account Balance Alert : ' + notifyData.app_name
    }

    console.log('mailerData', mailerData)
    const mailResponse = await mailer(mailerData)
    console.log('mailResponse', mailResponse)
  }
}

module.exports = BillpayProvider
