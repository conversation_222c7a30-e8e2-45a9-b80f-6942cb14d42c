const axios = require('axios')
const qs = require('qs')

module.exports = async (ma_cms_merchant_on_boarding_id, apiFields) => {

  // apiFields

  const postData = qs.stringify(postParams)
      const response = await axios({
        method: 'post',
        url: util.paymentsUrl + 'order/verify.php',
        data: postData,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })

      if (response.status !== 200) {
}
