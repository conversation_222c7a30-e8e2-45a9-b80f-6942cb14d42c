// const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
// const errorMsg = require('../../util/error')

const Routing = require('./routing.js')

/* const handler = Routing.getHandler(null, { transferId: '12345', orderId: 'abcd1234', transferMode: 'ADDBENEFICIARY' }).then((macson) => {
})
console.log(handler)
*/

const testfunction = async () => {
  const connection = await mySQLWrapper.getConnectionFromPool()
  const routingObj = new Routing(null, connection)
  const handler = await routingObj.getHandler(null, { transferId: null, transferMode: 'ADDBENEFICIARY', beneficiaryId: 1 })
  // const handler = await routingObj.getHandler(null, { transferId: '12345', orderId: 'abcd1234', transferMode: 'TRANSFER', beneficiaryId: 1 })
  // const handler = await routingObj.getHandler(null, { transferId: '12345', orderId: 'abcd1234', transferMode: 'AUTORETRY', beneficiaryId: 1 })
  // const handler = await routingObj.getHandler(null, { transferId: '12345', orderId: 'abcd1234', transferMode: 'RETRY', beneficiaryId: 1 })
  console.log(handler)
}

testfunction()
