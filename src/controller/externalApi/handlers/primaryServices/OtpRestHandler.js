const _ = require('lodash')

const OtpController = require('../../primaryServices/OtpController')
const errorMsg = require('../../../../util/error')
const log = require('../../../../util/log')
const ValidationError = require('../../../../errors/ValidationError')
const Joi = require('joi')

class OtpRestHandler {
  /**
   * Affiliate Generate Token API
   * @param {{ma_user_id:number,userid:number,order_id:string,otp_type:string}} fields
   * @returns
   */
  static async resendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resendOTP', type: 'request', fields })
    try {
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        order_id: Joi.string().required().error(new ValidationError(2001, 'order_id is required field and must be string')),
        otp_type: Joi.string().required().error(new ValidationError(2001, 'otp_type is required field and must be string')),
        sessionRQ: Joi.string().error(new ValidationError(2001, 'sessionRQ  must be string')),
        ma_transfers_id: Joi.number().options({ convert: false }).integer().error(new ValidationError(2001, 'ma_transfers_id  must be number')),
        ma_beneficiaries_id: Joi.number().options({ convert: false }).integer().error(new ValidationError(2001, 'ma_beneficiaries_id  must be number'))
      })

      await schema.validateAsync(fields)

      return await OtpController.resendOTP(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOTP', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = OtpRestHandler
