const _ = require('lodash')

const generateTokenController = require('../../generateTokenController')
const errorMsg = require('../../../../util/error')
const log = require('../../../../util/log')
const ValidationError = require('../../../../errors/ValidationError')
const Joi = require('joi')
class TokenRestHandler {
  /**
   * Affiliate Generate Token API
   * @param {{affiliate_id:number,api_key:string,checksum :string}} fields
   * @returns
   */
  static async generateToken (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateToken', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        api_key: Joi.string().required().error(new ValidationError(2001, 'api_key is required field and must be string')),
        checksum: Joi.string().required().error(new ValidationError(2001, 'checksum is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await generateTokenController.getToken(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    }
  }

  /**
   * Affiliate Session API
   * @param {{affiliate_id:number,merchant_id:number,channelCode:number,api_key:string,checksum :string}} fields
   * @returns
   */
  static async generateSession (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateSession', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        channelCode: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'channelCode is required field and must be number')),
        api_key: Joi.string().required().error(new ValidationError(2001, 'api_key is required field and must be string')),
        checksum: Joi.string().required().error(new ValidationError(2001, 'checksum is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      /* request updated */
      fields.ma_user_id = fields.merchant_id
      return await generateTokenController.getSession(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  static async generateKeys (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateKeys', type: 'request', fields })
      if (!_.has(fields, 'affiliate_id') || typeof fields.affiliate_id != 'number') {
        throw new ValidationError(2001, 'affiliate_id is required field and must be number')
      }
      return await generateTokenController.generateKeys(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateKeys', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = TokenRestHandler
