const requestLogger = require('../../logging/LogController')
const affiliateController = require('../../affliliateController')
const moment = require('moment-timezone')
const errorMsg = require('../../../../util/error')
const util = require('../../../../util/util')

class AffiliateGraphqlHandler {
  static async getAffiliate (_, fields) {
    const request_datetime = moment.tz('Asia/Kolkata').toISOString()
    const requestStart = process.hrtime()
    try {
      const response_json = await affiliateController.getAffiliate(fields)
      /* logging incoming request and response */
      const requestEnd = process.hrtime(requestStart)
      const response_time = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000
      requestLogger.log({
        response_time,
        request_type: util.externalServiceList.MERCHANT_LIST.name,
        request_url: `${util.externalServiceList.MERCHANT_LIST.graphqlEndpoint}`,
        affiliate_id: fields.affiliate_id,
        merchant_id: fields.merchant_id || 'NULL',
        request_datetime,
        response_json,
        request_json: fields,
        addedon: moment.tz('Asia/Kolkata').toISOString()
      })
    } catch (error) {
      /* logging incoming request and response */
      const requestEnd = process.hrtime(requestStart)
      const response_time = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      requestLogger.log({
        response_time,
        request_type: util.externalServiceList.MERCHANT_LIST.name,
        request_url: `${util.externalServiceList.MERCHANT_LIST.graphqlEndpoint}`,
        affiliate_id: fields.affiliate_id,
        merchant_id: fields.merchant_id || 'NULL',
        request_datetime,
        response_json,
        request_json: fields,
        addedon: moment.tz('Asia/Kolkata').toISOString()
      })
      return response_json
    }
  }
}

module.exports = AffiliateGraphqlHandler
