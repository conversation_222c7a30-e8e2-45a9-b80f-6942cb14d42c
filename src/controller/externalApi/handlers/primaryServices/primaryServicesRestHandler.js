const _ = require('lodash')

const PrimaryServicesController = require('../../primaryServices/primaryServicesController')
const errorMsg = require('../../../../util/error')
const log = require('../../../../util/log')
const ValidationError = require('../../../../errors/ValidationError')
const Joi = require('joi')
const MAX_LIMIT = 50

class PrimaryServicesRestHandler {
  /**
   *
   * @param {{email:string,firstname:string,lastname:string,contactno:string,businessName:string,businessAddress:string,city:number,state:number,pincode:string,panNumber?:string,affiliate_id:number,DOB:string,ipAddress:string}} fields
   * @returns
   */
  static async merchantOnboarding (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'merchantOnboarding', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        state: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'state is required field and must be number')),
        city: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'city is required field and must be number')),
        firstname: Joi.string().required().error(new ValidationError(2001, 'firstname is required field and must be string')),
        lastname: Joi.string().required().error(new ValidationError(2001, 'lastname is required field and must be string')),
        businessName: Joi.string().required().error(new ValidationError(2001, 'businessName is required field and must be string')),
        businessAddress: Joi.string().required().error(new ValidationError(2001, 'businessAddress is required field and must be string')),
        pincode: Joi.string().required().error(new ValidationError(2001, 'pincode is required field and must be string')),
        DOB: Joi.string().required().error(new ValidationError(2001, 'DOB is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        panNumber: Joi.string().required().error(new ValidationError(2001, 'panNumber is required field and must be string')),
        email: Joi.string().email().required().error(new ValidationError(2001, 'email is required field,must be string and must be valid email')),
        contactno: Joi.string().min(10).max(10).required().error(new ValidationError(2001, 'contactno is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.merchantOnboarding(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'merchantOnboarding', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number}} fields
   * @returns
   */
  static async getWalletBalance (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalance', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.getWalletBalance(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalance', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit:number,offset:number,status:['Y','N','L','O','D'],merchant_id?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async getMerchantList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantList', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        status: Joi.string().optional().valid('Y', 'N', 'L', 'O', 'D').error(new ValidationError(2001, 'status must be string or invalid status type')),
        limit: Joi.number().options({ convert: false }).integer().less(MAX_LIMIT).optional().options({ convert: false }).error(new ValidationError(2001, 'limit  must be number and less than 50')),
        offset: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'offset must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'merchant_id must be number')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text  must be string')),
        search_type: Joi.string().optional().valid('firstname', 'lastname', 'email').error(new ValidationError(2001, 'search_type must be string or invalid Search type'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.getMerchantList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   @param {{affiliate_id:number,merchant_id?:number,mode?:['dr','cr'],limit?:number,offset?:number,transaction_type?:number,search_text?:string,search_type?:string,fromdate?:string,todate?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async ledgerHistory (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerHistory', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        limit: Joi.number().options({ convert: false }).integer().less(MAX_LIMIT).optional().error(new ValidationError(2001, 'limit  must be number and less than 50')),
        offset: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'offset must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'merchant_id must be number')),
        transaction_type: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'transaction_type must be number')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text must be string')),
        search_type: Joi.string().optional().valid('aggregator_order_id').error(new ValidationError(2001, 'search_type  must be string or invalid Search type')),
        mode: Joi.string().optional().valid('cr', 'dr').error(new ValidationError(2001, 'mode  must be string or invalid mode')),
        fromdate: Joi.string().optional().error(new ValidationError(2001, 'fromdate  must be string')),
        todate: Joi.string().optional().error(new ValidationError(2001, 'fromdate  must be string'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.ledgerHistory(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerHistory', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id?:number,transaction_status?:['I','P','PS','S','F'],limit?:number,offset?:number,transaction_type?:number,,search_text?:string,search_type?:string,fromdate?:string,todate?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async transactionHistory (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionHistory', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        limit: Joi.number().options({ convert: false }).integer().less(MAX_LIMIT).optional().error(new ValidationError(2001, 'limit  must be number and less than 50')),
        offset: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'offset  must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'merchant_id  must be number')),
        transaction_type: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'transaction_type  must be number')),
        transaction_status: Joi.string().valid('I', 'P', 'PS', 'S', 'F', 'R', 'REV').optional().error(new ValidationError(2001, 'transaction_status  must be string')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text  must be string')),
        search_type: Joi.string().optional().valid('order_id', 'transaction_id').error(new ValidationError(2001, 'search_type  must be string or invalid Search type')),
        fromdate: Joi.string().optional().error(new ValidationError(2001, 'fromdate  must be string')),
        todate: Joi.string().optional().error(new ValidationError(2001, 'todate  must be string'))
      })

      await schema.validateAsync(fields)
      return await PrimaryServicesController.transactionHistory(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionHistory', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id?:number,aggregator_order_id:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async transactionStatus (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionStatus', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        merchant_id: Joi.number().integer().options({ convert: false }).optional().error(new ValidationError(2001, 'merchant_id must be number')),
        order_id: Joi.string().required().error(new ValidationError(2001, 'order_id is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.transactionStatus(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionStatus', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async channelList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'channelList', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.getChannelList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getChannelList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit?:number,offset?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async bankList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        limit: Joi.number().options({ convert: false }).integer().less(MAX_LIMIT).optional().error(new ValidationError(2001, 'limit  must be number and less than 50')),
        offset: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'offset  must be number')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text  must be string')),
        search_type: Joi.string().optional().valid('bank_name').error(new ValidationError(2001, 'search_type  must be string or invalid Search type'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.bankList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit?:number,offset?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async stateList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'stateList', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        limit: Joi.number().integer().options({ convert: false }).less(MAX_LIMIT).optional().error(new ValidationError(2001, 'limit must be number and less than 50')),
        offset: Joi.number().integer().options({ convert: false }).optional().error(new ValidationError(2001, 'offset must be number')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text must be string')),
        search_type: Joi.string().optional().valid('name').error(new ValidationError(2001, 'search_type  must be string or invalid Search type'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.stateList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'stateList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,state_id?:number,limit?:number,offset?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async cityList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'cityList', type: 'request', fields })
      /* Type Validation */
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        state_id: Joi.number().integer().optional().options({ convert: false }).error(new ValidationError(2001, 'state_id must be number')),
        limit: Joi.number().less(MAX_LIMIT).optional().options({ convert: false }).error(new ValidationError(2001, 'limit  must be number and less than 50')),
        offset: Joi.number().integer().optional().options({ convert: false }).error(new ValidationError(2001, 'offset  must be number')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text  must be string')),
        search_type: Joi.string().optional().valid('name').error(new ValidationError(2001, 'search_type  must be string or invalid Search type'))
      })

      await schema.validateAsync(fields)

      return await PrimaryServicesController.cityList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'cityList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = PrimaryServicesRestHandler
