const requestLogger = require('../../logging/LogController')
const generateTokenController = require('../../generateTokenController')
const moment = require('moment-timezone')
const errorMsg = require('../../../../util/error')
const util = require('../../../../util/util')

class GenerateTokenGraphqlHandler {
  static async getToken (_, fields) {
    try {
      return await generateTokenController.getToken(fields)
    } catch (error) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}

module.exports = GenerateTokenGraphqlHandler
