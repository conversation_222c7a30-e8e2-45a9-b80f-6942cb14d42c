const _ = require('lodash')
const moment = require('moment-timezone')
const { graphql } = require('graphql')
const { serverlessErrorHandler } = require('./src/util/errorHandler')

const ApplicationError = require('./../../../errors/ApplicationError')
const middleware = require('./../../../util/middleware')
const requestLogger = require('./../../../controller/externalApi/logging/LogController')
const errorMsg = require('./../../../util/error')
const ValidationError = require('./../../../errors/ValidationError')
const { externalVerifyToken } = require('./../../../util/token')
const AffiliateController = require('../affliliateController')
const log = require('../../../util/log')
const { encryptExternalApiResponse, decryptExternalApiRequest } = require('../../../util/common.js')
const common = require('./src/util/common')
const util = require('./src/util/util')

class GraphqlAwsLambdaHandler {
  /**
   *
   * @param {Object} headers
   * @param {Array<String>} headerParams
   */
  static headerValidation (headers, headerParams) {
    for (const param of headerParams) {
      if (!_.has(headers, param) || headers[param] == '') {
        throw new ValidationError(1028, `${errorMsg.responseCode[1028]} : required header missing !`)
      }
    }
  }

  /**
   *
   * @param {Object} headers
   */
  static async tokenValidation (headers) {
    try {
      if (!_.has(headers, 'token') || headers.token == '') {
        throw new ValidationError(1028, `${errorMsg.responseCode[1028]} : required header missing !`)
      }
      const token = headers.token
      /* token verify */
      const payload = await externalVerifyToken(token)
      if (!_.has(payload, 'affiliate_id')) {
        throw new ValidationError(1028, `${errorMsg.responseCode[1028]} : invalid token !`)
      }
      const affiliate_id = payload.affiliate_id
      /* Afflicate Status Check */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id })
      log.logger({ pagename: require('path').basename(__filename), action: 'tokenValidation', type: 'response', fields: affiliateResponse })
      if (affiliateResponse.status != 200) {
        throw new ValidationError(affiliateResponse.respcode, affiliateResponse.message)
      }

      if (affiliateResponse.respcode == 1002) {
        throw new ValidationError(1028, `${errorMsg.responseCode[1028]} : invalid user !`)
      }
      const affiliate = affiliateResponse.affiliate
      if (affiliate.user_status != 'Y') {
        throw new ValidationError(1028, `${errorMsg.responseCode[1028]} : invalid user !`)
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'tokenValidation', type: 'response', fields: error })
      throw new ValidationError(1028, error.message)
    }
  }

  /**
   *
   * @param {Object} event
   * @param {number} affiliate_id
   */
  static async IpAddressValidation (event, affiliate_id) {
    const ip_address = _.has(event, 'requestContext.identity.sourceIp') ? event.requestContext.identity.sourceIp : event.headers['X-Forwarded-For']
    /* IP Whitelist Check */
    const isExteralApiUserIPWhitelistedResp = await middleware.isExteralApiUserIPWhitelisted({ affiliate_id, ip_address })
    if (isExteralApiUserIPWhitelistedResp.status != 200) {
      throw new ApplicationError(isExteralApiUserIPWhitelistedResp.respcode, isExteralApiUserIPWhitelistedResp.message)
    }
  }

  /**
   *
   * @param {{event:object,headerParams:Array<string>,request_type:string,request_url:string,isTokenRequired:boolean,requestParams:Object,callback:function,schema:GraphQLSchema}} fields
   * @returns Promise<{statusCode:number,body:string}>
   */
  static async processRequest (fields) {
    const { event, headerParams, isTokenRequired, isIpWhiteListRequired, isEncryptionRequired, requestParams, request_type, callback, schema } = fields
    let decKey = ''
    let encKey = ''
    try {
      const merchant_id = event.headers.merchant_id || 0
      this.headerValidation(event.headers, headerParams)
      if (isTokenRequired) {
        await this.tokenValidation(event.headers)
      }
      const affiliate_id = event.headers.affiliate_id
      if (isIpWhiteListRequired) {
        await this.IpAddressValidation(event, affiliate_id)
      }

      if (isEncryptionRequired) {
        const requestKeyResponse = await AffiliateController.getKeys({ affiliate_id })
        if (requestKeyResponse.status != 200) {
          throw new ApplicationError(requestKeyResponse.respcode, requestKeyResponse.message)
        }
        decKey = requestKeyResponse.request_key
        encKey = requestKeyResponse.response_key
      }

      let params = requestParams
      if (isEncryptionRequired) {
        params = decryptExternalApiRequest({ decKey, encryptedTxt: params })
      }
      if (typeof params == 'string') {
        params = JSON.parse(params)
      }
      /* CALL MAIN HANDLER */
      const response_json = await callback(params)
      return {
        statusCode: 200,
        body: response_json,
        decKey,
        encKey,
        params
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: `handler-${request_type}`, type: 'response', fields: error })
      const response_json = { status: 500, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.message && error.code) {
        response_json.message = error.message
        response_json.respcode = error.code
      }

      if (error.name == 'TokenExpiredError') {
        response_json.message = error.message
        response_json.respcode = 401
      }
      // The output from a Lambda proxy integration must be
      // in the following JSON object. The 'headers' property
      // is for custom response headers in addition to standard
      // ones. The 'body' property  must be a JSON string. For
      // base64-encoded payload, you must also set the 'isBase64Encoded'
      // property to 'true'.
      return {
        statusCode: 400,
        body: response_json,
        decKey,
        encKey
      }
    }
  }

  /**
   *
   * @param {{event:object,headerParams:Array<string>,request_type:string,request_url:string,isTokenRequired:boolean,requestParams:Object,callback:function,schema:GraphQLSchema}} fields
   * @returns Promise<{statusCode:number,body:string}>
   */
  static async handler (fields) {
    const { event, request_type, request_url, isEncryptionRequired } = fields
    try {
      /* Down Time Check */
      const downtimeresp = await common.downTime()
      if (downtimeresp[0] != 'undefined' && downtimeresp[0] != null && downtimeresp[0].notice_flag == 'Y') {
        throw new ApplicationError(301, downtimeresp[0].message)
      }
      fields.requestParams = event.data.query
      const requestStart = process.hrtime()
      const request_datetime = moment.tz('Asia/Kolkata').toLocaleString()
      const response = await this.processRequest(fields)
      const requestEnd = process.hrtime(requestStart)
      const response_time = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000
      await requestLogger.log({
        response_time,
        request_type,
        request_url,
        affiliate_id: event.headers.affiliate_id || 'NULL',
        merchant_id: 0,
        request_datetime,
        response_json: response.body,
        request_params: response.params || {},
        request_json: event,
        response_datetime: moment.tz('Asia/Kolkata').toLocaleString(),
        addedon: moment.tz('Asia/Kolkata').toLocaleString()
      })

      response.body = JSON.stringify(response.body)
      const encKey = response.encKey
      if (isEncryptionRequired) {
        response.body = encryptExternalApiResponse({ text: response.body, encKey })
      }
      return { statusCode: response.statusCode, body: JSON.stringify(response.body) }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: `handler-${request_type}`, type: 'response', fields: error })
      const defaultErrorResponse = { status: 500, respcode: 5001, message: 'Invalid Request' }
      if (error instanceof ApplicationError) {
        defaultErrorResponse.respcode = error.code
        defaultErrorResponse.message = error.message
      }
      return {
        statusCode: 500,
        body: JSON.stringify(defaultErrorResponse)
      }
    }
  }
}

module.exports = GraphqlAwsLambdaHandler
