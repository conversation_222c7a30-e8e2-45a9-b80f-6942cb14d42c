const _ = require('lodash')

const RefundController = require('../../../channels/DMT/refundController')
const errorMsg = require('../../../../../util/error')
const log = require('../../../../../util/log')
const ValidationError = require('../../../../../errors/ValidationError')
const Joi = require('joi')
class RefundRestHandler {
  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number}} fields
   * @returns
   */
  static async refundTransactionList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string'))
      })
      await schema.validateAsync(fields)
      return await RefundController.refundTransactionList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_transfers_id:string}} fields
   * @returns
   */
  static async refundSendOTP (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundSendOTP', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        ma_transfers_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'ma_transfers_id is required field and must be number'))
      })
      await schema.validateAsync(fields)

      return await RefundController.refundSendOTP(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundSendOTP', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,otp:string,ma_transfers_id:string}} fields
   * @returns
   */
  static async refundVerifyOTP (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundVerifyOTP', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        ma_transfers_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'ma_transfers_id is required field and must be number')),
        otp: Joi.string().required().error(new ValidationError(2001, 'otp is required field and must be string')),
        order_id: Joi.string().required().error(new ValidationError(2001, 'order_id is required field and must be string'))
      })
      await schema.validateAsync(fields)
      return await RefundController.refundVerifyOTP(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundVerifyOTP', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = RefundRestHandler
