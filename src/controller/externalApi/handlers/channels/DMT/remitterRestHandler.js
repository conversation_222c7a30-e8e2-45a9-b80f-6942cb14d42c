const _ = require('lodash')

const RemitterController = require('../../../channels/DMT/remitterController')
const CustomerController = require('../../../../customer/customerController')
const errorMsg = require('../../../../../util/error')
const ValidationError = require('../../../../../errors/ValidationError')
const log = require('../../../../../util/log')
const Joi = require('joi')

class RemitterRestHandler {
  /**
   *
   * @param {{affiliate_id:number,mobile_number:string,ma_user_id:number,userid:number}} fields
   * @returns
   */
  static async remitterLogin (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLogin', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        mobile_number: Joi.string().required().error(new ValidationError(2001, 'mobile_number is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      const isMobileNumberValidResult = CustomerController.isMobileNumberValid({ mobile_number: fields.mobile_number })

      log.logger({ pagename: require('path').basename(__filename), action: 'isMobileNumberValid', type: 'response', fields: isMobileNumberValidResult })

      if (isMobileNumberValidResult.status != 200) throw new ValidationError(2001, isMobileNumberValidResult.message)

      fields.mobile_number = isMobileNumberValidResult.mobile_number
      fields.countrycode = isMobileNumberValidResult.countrycode

      return await RemitterController.remitterLogin(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLogin', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,mobile_number:string,sessionRQ:string,first_name:string,last_name:string,ma_user_id:number,userid:number}} fields
   * @returns
   */
  static async remitterRegistration (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        mobile_number: Joi.string().required().error(new ValidationError(2001, 'mobile_number is required field and must be string')),
        first_name: Joi.string().required().error(new ValidationError(2001, 'first_name is required field and must be string')),
        last_name: Joi.string().required().error(new ValidationError(2001, 'last_name is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string'))
      })

      await schema.validateAsync(fields)

      const isMobileNumberValidResult = CustomerController.isMobileNumberValid({ mobile_number: fields.mobile_number })

      log.logger({ pagename: require('path').basename(__filename), action: 'isMobileNumberValid', type: 'response', fields: isMobileNumberValidResult })

      if (isMobileNumberValidResult.status != 200) throw new ValidationError(2001, isMobileNumberValidResult.message)

      fields.mobile_number = isMobileNumberValidResult.mobile_number
      fields.countrycode = isMobileNumberValidResult.countrycode

      return await RemitterController.remitterRegistration(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,mobile_number:string,otp:string,order_id:string,ma_user_id:number,userid:number}} fields
   * @returns
   */
  static async remitterRegistrationVerifyOTP (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistrationVerifyOTP', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        mobile_number: Joi.string().required().error(new ValidationError(2001, 'mobile_number is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        otp: Joi.string().required().error(new ValidationError(2001, 'otp is required field and must be string')),
        order_id: Joi.string().required().error(new ValidationError(2001, 'order_id is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await RemitterController.remitterRegistrationVerifyOTP(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistrationVerifyOTP', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,uic:string,sessionRQ:string,ma_user_id:number,userid:number}} fields
   * @returns
   */
  static async remitterLimit (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLimit', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await RemitterController.remitterLimit(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLimit', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = RemitterRestHandler
