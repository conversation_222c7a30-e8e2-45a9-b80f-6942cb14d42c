const _ = require('lodash')

const BeneficiaryController = require('../../../channels/DMT/beneficiaryController')
const errorMsg = require('../../../../../util/error')
const log = require('../../../../../util/log')
const ValidationError = require('../../../../../errors/ValidationError')
const Joi = require('joi')
class BeneficiaryRestHandler {
  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,beneficiary_name:string,account_number:string,ifsc_code:string,ben_mobile_number:string,relationship:string}} fields
   * @returns
   */
  static async addBeneficiary (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        beneficiary_name: Joi.string().required().error(new ValidationError(2001, 'beneficiary_name is required field and must be string')),
        account_number: Joi.string().required().error(new ValidationError(2001, 'account_number is required field and must be string')),
        ifsc_code: Joi.string().required().error(new ValidationError(2001, 'ifsc_code is required field and must be string')),
        ben_mobile_number: Joi.string().required().error(new ValidationError(2001, 'ben_mobile_number is required field and must be string')),
        relationship: Joi.string().optional().error(new ValidationError(2001, 'relationship must be string'))
      })

      await schema.validateAsync(fields)

      return await BeneficiaryController.addBeneficiary(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,otp:string,order_id:string,ma_beneficiaries_id:string }} fields
   * @returns
   */
  static async addBeneficiaryVerifyOTP (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryVerifyOTP', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        ma_beneficiaries_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'ma_beneficiaries_id is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        otp: Joi.string().required().error(new ValidationError(2001, 'otp is required field and must be string')),
        order_id: Joi.string().required().error(new ValidationError(2001, 'order_id is required field and must be string'))
      })

      await schema.validateAsync(fields)

      return await BeneficiaryController.addBeneficiaryVerifyOTP(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryVerifyOTP', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,beneficiary_name:string,account_number:string,ifsc_code:string,ben_mobile_number:string,relationship:string}} fields
   * @returns
   */
  static async verifyAddBeneficiary (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyAddBeneficiary', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        beneficiary_name: Joi.string().required().error(new ValidationError(2001, 'beneficiary_name is required field and must be string')),
        account_number: Joi.string().required().error(new ValidationError(2001, 'account_number is required field and must be string')),
        ifsc_code: Joi.string().required().error(new ValidationError(2001, 'ifsc_code is required field and must be string')),
        ben_mobile_number: Joi.string().required().error(new ValidationError(2001, 'ben_mobile_number is required field and must be string')),
        relationship: Joi.string().error(new ValidationError(2001, 'relationship must be string')),
        transaction_id: Joi.string().alphanum().min(5).max(15).required().error(new ValidationError(2001, 'transaction_id is required field,must be alphanumeric,min 5 and max 15 in length'))
      })

      await schema.validateAsync(fields)
      return await BeneficiaryController.verifyAddBeneficiary(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOTP', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,ma_beneficiaries_id:string }} fields
   * @returns
   */
  static async sendDeleteBeneficiaryOtp (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendDeleteBeneficiaryOtp', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        ma_beneficiaries_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'ma_beneficiaries_id is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string'))
      })
      await schema.validateAsync(fields)

      return await BeneficiaryController.sendDeleteBeneficiaryOtp(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendDeleteBeneficiaryOtp', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,otp:string,order_id:string,ma_beneficiaries_id:string }} fields
   * @returns
   */
  static async verifyDeleteBeneficiaryOtp (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryOtp', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        ma_beneficiaries_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'ma_beneficiaries_id is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        order_id: Joi.string().required().error(new ValidationError(2001, 'order_id is required field and must be string')),
        otp: Joi.string().required().error(new ValidationError(2001, 'otp is required field and must be string'))
      })
      await schema.validateAsync(fields)

      return await BeneficiaryController.verifyDeleteBeneficiaryOtp(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryOtp', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,limit:number,offset:number,search_text?:string,search_type?:string}} fields
   * @returns
   */
  static async beneficiaryList (fields) {
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryList', type: 'request', fields })
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        limit: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'limit  must be number')),
        offset: Joi.number().options({ convert: false }).integer().optional().error(new ValidationError(2001, 'offset  must be number')),
        search_text: Joi.string().optional().error(new ValidationError(2001, 'search_text  must be string')),
        search_type: Joi.string().optional().allow('name', 'acc_no', 'mob_no').error(new ValidationError(2001, 'search_type  must be string or invalid Search type'))
      })
      await schema.validateAsync(fields)

      return await BeneficiaryController.beneficiaryList(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryList', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = BeneficiaryRestHandler
