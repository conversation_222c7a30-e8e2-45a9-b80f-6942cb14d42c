const _ = require('lodash')
const moment = require('moment-timezone')
const { TokenExpiredError, JsonWebTokenError } = require('jsonwebtoken')

const ApplicationError = require('./../../../errors/ApplicationError')
const middleware = require('./../../../util/middleware')
const requestLogger = require('./../../../controller/externalApi/logging/LogController')
const errorMsg = require('./../../../util/error')
const ValidationError = require('./../../../errors/ValidationError')
const { externalVerifyToken } = require('./../../../util/token')
const AffiliateController = require('../affliliateController')
const log = require('../../../util/log')
const { encryptExternalApiResponse, decryptExternalApiRequest, downTime } = require('../../../util/common')
const TransactionController = require('../../transaction/transactionController')
const PrimaryServicesController = require('../primaryServices/primaryServicesController')

const Joi = require('joi')
class RestApiAwsLambdaHandler {
  static getRequestEvent () {
    return this.requestEvent
  }

  static setRequestEvent (requestEvent) {
    this.requestEvent = requestEvent
  }

  static getRequestFieds () {
    return this.requestFieds
  }

  static setRequestFieds (requestFieds) {
    this.requestFieds = requestFieds
  }

  static getMerchantId () {
    return this.merchant_id
  }

  static setMerchantId (merchant_id) {
    this.merchant_id = merchant_id
  }

  static getMerchantUserId () {
    return this.merchant_user_id
  }

  static setMerchantUserId (merchant_user_id) {
    this.merchant_user_id = merchant_user_id
  }

  static getAffiliateId () {
    return this.affiliate_id
  }

  static setAffiliateId (affiliate_id) {
    this.affiliate_id = affiliate_id
  }

  static getDecKey () {
    return this.decKey
  }

  static setDecKey (decKey) {
    this.decKey = decKey
  }

  static getEncKey () {
    return this.encKey
  }

  static setEncKey (encKey) {
    this.encKey = encKey
  }

  static getApiKey () {
    return this.apiKey
  }

  static setApiKey (apiKey) {
    this.apiKey = apiKey
  }

  static getIpAddress () {
    return this.ipAddress
  }

  static setIpAddress (ipAddress) {
    this.ipAddress = ipAddress
  }

  static headerValidation () {
    const headers = this.getRequestEvent().headers
    const headerParams = this.getRequestFieds().headerParams
    for (const param of headerParams) {
      if (!_.has(headers, param) || headers[param] == '') throw new ValidationError(2001, 'Required header missing !')
    }
  }

  /**
   * JWT Token Validation Based Upon Affiliate API KEY
   */
  static async tokenValidation () {
    const headers = this.getRequestEvent().headers
    if (!_.has(headers, 'token') || headers.token == '') {
      throw new ValidationError(401, 'Required header missing !')
    }
    const token = headers.token
    /* token verify */
    let payload = {}
    try {
      payload = await externalVerifyToken({ token, key: this.getApiKey() })
    } catch (error) {
      if (error instanceof TokenExpiredError) throw new ApplicationError(401, 'Token expired !')
      else if (error instanceof JsonWebTokenError) throw new ApplicationError(401, 'Token invalid !')
      else { throw new ApplicationError(401, 'Token invalid !') }
    }

    if (!_.has(payload, 'affiliate_id')) {
      throw new ApplicationError(401, 'Invalid token !')
    }

    /* Afflicate Status Check */
    const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: payload.affiliate_id })
    log.logger({ pagename: require('path').basename(__filename), action: 'tokenValidation', type: 'response', fields: affiliateResponse })
    if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

    if (affiliateResponse.respcode == 1002) throw new ApplicationError(401, 'Invalid user !')
  }

  /**
   * Session Token Validation Based Upon Affiliate API KEY
   */
  static async sessionTokenValidation () {
    const headers = this.getRequestEvent().headers
    const requiredChannel = this.getRequestFieds().requiredChannel || []
    if (!_.has(headers, 'session_token') || headers.session_token == '') {
      throw new ValidationError(401, 'Required header missing !')
    }
    const token = headers.session_token
    /* token verify */
    let payload = {}
    try {
      payload = await externalVerifyToken({ token, key: this.getApiKey() })
    } catch (error) {
      if (error instanceof TokenExpiredError) throw new ApplicationError(401, 'Token expired !')
      else if (error instanceof JsonWebTokenError) throw new ApplicationError(401, 'Token invalid !')
      else { throw new ApplicationError(401, 'Token invalid !') }
    }
    if (!_.has(payload, 'affiliate_id')) throw new ApplicationError(401, 'Invalid Token !')

    const affiliate_id = payload.affiliate_id
    const merchant_id = payload.merchant_id
    const channelCode = payload.channelCode

    /* Afflicate Status Check */
    const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id })
    log.logger({ pagename: require('path').basename(__filename), action: 'tokenValidation', type: 'response', fields: affiliateResponse })
    if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

    if (affiliateResponse.respcode == 1002) throw new ApplicationError(401, 'Invalid user !')

    const affiliate = affiliateResponse.affiliate
    if (affiliate.user_status != 'Y') throw new ApplicationError(401, 'Inactive user !')

    if (requiredChannel.length > 0) {
      const requireChannelCodeList = requiredChannel.map(channel => TransactionController.getAffiliateChannelTransactionType(channel))
      if (!requireChannelCodeList.includes(channelCode)) throw new ApplicationError(401, 'Channel not assigned !')

      const channelListResponse = await PrimaryServicesController.getChannelList({ affiliate_id: affiliate_id })

      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'channelListResponse', fields: channelListResponse })

      if (channelListResponse.status != 200) throw new ApplicationError(channelListResponse.respcode, channelListResponse.message)

      const channelCodeList = channelListResponse.data.channels.map(channel => channel.channelCode.toString())

      if (!channelCodeList.includes(channelCode.toString())) throw new ApplicationError(401, 'Channel not assigned !')
    }

    const affiliateMerchantResponse = await AffiliateController.getAffiliateMerchant({ affiliate_id, merchant_id })

    const merchant = affiliateMerchantResponse.affiliateMerchant
    this.setMerchantUserId(merchant.userid)
    this.setMerchantId(merchant_id)
    if (affiliateMerchantResponse.status != 200) throw new ApplicationError(affiliateMerchantResponse.respcode, affiliateMerchantResponse.message)

    if (affiliateMerchantResponse.respcode == 1002) throw new ApplicationError(401, 'Invalid Merchant User !')

    if (merchant.user_status != 'Y') throw new ApplicationError(401, 'Inactive Merchant User !')
  }

  /**
   *  Incoming IP Address Validation
   */
  static async IpAddressValidation () {
    const event = this.getRequestEvent()
    const affiliate_id = event.headers.affiliate_id
    const ip_address = _.has(event, 'requestContext.identity.sourceIp') ? event.requestContext.identity.sourceIp : event.headers['X-Forwarded-For']
    /* IP Whitelist Check */
    const isExteralApiUserIPWhitelistedResp = await middleware.isExteralApiUserIPWhitelisted({ affiliate_id, ip_address })
    if (isExteralApiUserIPWhitelistedResp.status != 200) throw new ApplicationError(401, isExteralApiUserIPWhitelistedResp.message)
    this.setIpAddress(ip_address)
  }

  /**
   * Validation and Process Incoming Request
   * @returns
   */
  static async processRequest () {
    const {
      isTokenRequired,
      isIpWhiteListRequired,
      isEncryptionRequired,
      requestParams,
      request_type,
      callback,
      isSessionTokenRequired
    } = this.getRequestFieds()
    try {
      log.logger({
        pagename: require('path').basename(__filename),
        action: `handler-processRequest-${request_type}`,
        type: 'request',
        fields: {
          isTokenRequired,
          isIpWhiteListRequired,
          isEncryptionRequired,
          requestParams,
          request_type,
          isSessionTokenRequired
        }
      })
      await this.headerValidation()

      const affiliate_id = this.getRequestEvent().headers.affiliate_id

      const schema = Joi.number().integer().error(new ValidationError(401, 'Invalid affiliate id'))

      schema.validateAsync(affiliate_id)

      if (isIpWhiteListRequired) {
        await this.IpAddressValidation()
      }

      const requestKeyResponse = await AffiliateController.getKeys({ affiliate_id })
      if (requestKeyResponse.status != 200) {
        throw new ApplicationError(requestKeyResponse.respcode, requestKeyResponse.message)
      }
      this.setDecKey(requestKeyResponse.request_key)
      this.setEncKey(requestKeyResponse.response_key)
      this.setApiKey(requestKeyResponse.api_key)

      if (isTokenRequired) {
        await this.tokenValidation()
      }

      if (isSessionTokenRequired) {
        await this.sessionTokenValidation()
      }

      let params = requestParams
      if (isEncryptionRequired) {
        log.logger({ pagename: require('path').basename(__filename), action: `handler-processRequest-${request_type}`, type: 'decryptExternalApiRequest request', fields: { decKey: this.getDecKey(), encryptedTxt: params } })

        params = decryptExternalApiRequest({ decKey: this.getDecKey(), encryptedTxt: params })

        log.logger({ pagename: require('path').basename(__filename), action: `handler-processRequest-${request_type}`, type: 'decryptExternalApiRequest', fields: params })
      }

      if (typeof params == 'string') {
        params = JSON.parse(params)
      }

      const paramsAffiliateID = params.affiliate_id
      /* CHECK IF THE REQUEST AFFILIATE ID is same as header affiliate ID */
      if (paramsAffiliateID != affiliate_id) {
        throw new ApplicationError(401, 'Invalid user !')
      }

      /* UPDATE THE RESPONSE JSON */
      if (isSessionTokenRequired) {
        /* CHECK IF THE REQUEST merchant_id is same as params merchant id */
        if (this.getMerchantId() != params.merchant_id) throw new ApplicationError(401, 'Invalid user !')

        params = { ...params, userid: this.getMerchantUserId(), ma_user_id: this.getMerchantId() }
      }
      /* SET INCOMING REQUEST ADDITIONAL PARAMETERS */
      params = { ...params, ipAddress: this.getIpAddress() }
      const responseParams = _.cloneDeep(params)
      /* CALL MAIN HANDLER */
      const response_json = await callback(params)
      return {
        statusCode: 200,
        body: response_json,
        params: responseParams
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: `handler-${request_type}`, type: 'catch-error', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.message && error.code) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      // The output from a Lambda proxy integration must be
      // in the following JSON object. The 'headers' property
      // is for custom response headers in addition to standard
      // ones. The 'body' property  must be a JSON string. For
      // base64-encoded payload, you must also set the 'isBase64Encoded'
      // property to 'true'.
      if (error.code == 401) {
        return {
          statusCode: 401,
          body: response_json
        }
      }
      return {
        statusCode: 400,
        body: response_json
      }
    }
  }

  /**
   * Process Incoming Request
   * @returns
   */
  static requestParams () {
    const event = this.getRequestEvent()
    console.log('REQUEST EVENT', event)
    log.logger({ pagename: require('path').basename(__filename), action: 'requestParams', type: 'request', fields: event.data })
    // event.data = JSON.parse(event.body) // sls testing code
    return event.data.query
  }

  /**
   * AWS LAMBDA Common Handler
   * @param {{event:object,headerParams:Array<string>,request_type:string,request_url:string,isTokenRequired:boolean,requestParams:Object,callback:function,isSessionTokenRequired?:Boolean}} fields
   * @returns Promise<{statusCode:number,body:string}>
   */
  static async handler (fields) {
    const { event, request_type, request_url, isEncryptionRequired } = fields
    try {
      if (!_.has(fields, 'isSessionTokenRequired')) {
        fields.isSessionTokenRequired = false
      }

      /* Down Time Check */
      const downtimeresp = await downTime()
      if (downtimeresp[0] != 'undefined' && downtimeresp[0] != null && downtimeresp[0].notice_flag == 'Y') {
        throw new ApplicationError(301, downtimeresp[0].message)
      }
      this.setRequestEvent(fields.event)
      fields.requestParams = this.requestParams()
      this.setRequestFieds(fields)
      const requestStart = process.hrtime()
      const request_datetime = moment.tz('Asia/Kolkata').format('YYYY-MM-DD HH:MM:SS')
      const response = await this.processRequest()
      const requestEnd = process.hrtime(requestStart)
      const response_time = (requestEnd[0] * 1000000000 + requestEnd[1]) / 1000000

      /* Remove Fail: From Message */
      if (response.body.message.match('Fail:')) {
        response.body.message = response.body.message.replace('Fail:', '').trim(/\s/)
      }

      // requestLogger.log({
      //   response_time,
      //   request_type,
      //   request_url,
      //   affiliate_id: event.headers.affiliate_id || 'NULL',
      //   merchant_id: 0,
      //   request_datetime,
      //   response_json: response.body,
      //   request_params: response.params || {},
      //   request_json: event,
      //   response_datetime: moment.tz('Asia/Kolkata').format('YYYY-MM-DD HH:MM:SS'),
      //   addedon: moment.tz('Asia/Kolkata').format('YYYY-MM-DD HH:MM:SS')
      // })

      const encKey = this.getEncKey()
      if (isEncryptionRequired && encKey) {
        response.body = encryptExternalApiResponse({ text: JSON.stringify(response.body), encKey })
      }

      return {
        statusCode: response.statusCode,
        body: JSON.stringify(response.body)
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: `handler-${request_type}`, type: 'response', fields: error })
      let statusCode = 500
      const defaultErrorResponse = { status: 500, respcode: 5001, message: 'Invalid Request' }
      if (error instanceof ApplicationError || error instanceof ValidationError) {
        statusCode = 400
        defaultErrorResponse.status = 400
        defaultErrorResponse.respcode = error.code
        defaultErrorResponse.message = error.message
      }
      return {
        statusCode,
        body: JSON.stringify(defaultErrorResponse)
      }
    }
  }
}

module.exports = RestApiAwsLambdaHandler
