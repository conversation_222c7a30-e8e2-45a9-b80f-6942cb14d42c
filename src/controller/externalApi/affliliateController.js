const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const util = require('../../util/util')
const { makeRandomid } = require('../../util/common')
const PointsLedgerController = require('../creditDebit/pointsLedgerController')
const PointsDetailsController = require('../creditDebit/pointsDetailsController')
const BalanceController = require('../balance/balanceController')
const LoginController = require('../login/loginController')
const ApplicationError = require('../../errors/ApplicationError')

class AffiliateController extends DAO {
  /**
   *
   * @param {{affiliate_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async getAffiliate ({ affiliate_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliate', type: 'request', fields: { affiliate_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateQuery = `SELECT u.name,u.email,mum.user_status,mum.user_type,mum.userid,mum.profileid as ma_user_id,mum.state,mum.company,mum.sales_id FROM ma_affiliate_mgmt mam LEFT JOIN  users u  ON mam.ma_user_id = u.profileid  LEFT JOIN ma_user_master mum ON u.profileid=mum.profileid  WHERE mam.ma_affiliate_mgmt_id=${affiliate_id}  AND u.status=2 AND mum.user_status = 'Y' AND mam.affiliate_status = 'A' LIMIT 1`
      const affiliateResult = await this.rawQuery(affiliateQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateResult', type: 'result', fields: affiliateResult })

      if (affiliateResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, affiliate: affiliateResult[0] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliate', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async getAdditionalAffiliateData ({ affiliate_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliate', type: 'request', fields: { affiliate_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateQuery = `SELECT sales_id FROM merchant_sales_id_mapping WHERE ma_user_id=${affiliate_id} LIMIT 1`
      const affiliateResult = await this.rawQuery(affiliateQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateResult', type: 'result', fields: affiliateResult })

      if (affiliateResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, affiliate: affiliateResult[0] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliate', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async whiteListIpAddress ({ affiliate_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliate', type: 'request', fields: { affiliate_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const whiteListIpAddressQuery = `SELECT GROUP_CONCAT(ip_address) AS ip_address FROM ma_affiliate_whitelist_ip WHERE affiliate_id=${affiliate_id} AND ip_status='A'`
      const whiteListIpAddressResult = await this.rawQuery(whiteListIpAddressQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'whiteListIpAddressResult', type: 'result', fields: whiteListIpAddressResult })

      if (whiteListIpAddressResult.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      const ipAddressesResult = whiteListIpAddressResult[0].ip_address

      if (!ipAddressesResult) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, ipAddresses: ipAddressesResult.split(',') }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliate', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async getAffiliateMerchant ({ merchant_id, affiliate_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'request', fields: { merchant_id, affiliate_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateResp = await this.getAffiliate({ affiliate_id, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateResp', type: 'result', fields: affiliateResp })

      if (affiliateResp.status != 200) return affiliateResp

      if (affiliateResp.respcode == 1002) throw new ApplicationError(401, 'Invalid user !')

      const { affiliate } = affiliateResp

      const affiliateMerchantQuery = `SELECT email_id,user_status,userid FROM ma_user_master WHERE profileid=${merchant_id} AND distributer_user_master_id='${affiliate.ma_user_id}' LIMIT 1`
      const affiliateMerchantResult = await this.rawQuery(affiliateMerchantQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateMerchantResult', type: 'result', fields: affiliateMerchantResult })

      if (affiliateMerchantResult.length == 0) {
        return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, affiliateMerchant: affiliateMerchantResult[0] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{merchant_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async isMerchantAffiliatedMerchant ({ merchant_id, connectionRead }) {
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateMerchantCheckQuery = `SELECT u.profileid as affiliate_ma_user_id, u.id as affiliate_userid, mam.ma_affiliate_mgmt_id as affiliate_id FROM ma_user_master mum INNER JOIN ma_affiliate_mgmt mam ON mum.distributer_user_master_id = mam.ma_user_id INNER JOIN users u ON u.profileid = mam.ma_user_id WHERE mum.profileid = ${merchant_id}`
      const affiliateMerchantCheckResponse = await this.rawQuery(affiliateMerchantCheckQuery, connRead)

      if (affiliateMerchantCheckResponse.length == 0) return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, ...affiliateMerchantCheckResponse[0] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isMerchantAffiliatedMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit:number,offset:number,search_text?:string,search_type?:string,status:['Y','N','L','O','D'],merchant_id?:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async getAffiliateMerchantList ({ affiliate_id, limit, offset, search_text, search_type, status, merchant_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'request', fields: { affiliate_id, limit, offset, search_text, search_type, status, merchant_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateResp = await this.getAffiliate({ affiliate_id, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateResp', type: 'result', fields: affiliateResp })

      if (affiliateResp.status != 200) return affiliateResp

      if (affiliateResp.respcode == 1002) throw new ApplicationError(401, 'Invalid user !')

      const { affiliate } = affiliateResp

      let affiliateMerchantQuery = `SELECT mum.profileid as merchant_id,mum.firstname,mum.lastname,u.email,mum.user_type,mum.user_status FROM ma_user_master mum LEFT JOIN users u ON mum.profileid=u.profileid  WHERE mum.distributer_user_master_id='${affiliate.ma_user_id}'`
      affiliateMerchantQuery += `AND mum.user_status='${status}' `
      if (search_text && search_type == 'firstname') {
        affiliateMerchantQuery += `AND mum.firstname like '%${search_text}%' `
      }
      if (search_text && search_type == 'lastname') {
        affiliateMerchantQuery += `AND mum.lastname like '%${search_text}%' `
      }
      if (search_text && search_type == 'email') {
        affiliateMerchantQuery += `AND u.email like '%${search_text}%' `
      }
      if (merchant_id != 0) {
        affiliateMerchantQuery += `AND mum.profileid=${merchant_id} `
      }
      affiliateMerchantQuery += ` ORDER BY mum.ma_user_master_id DESC LIMIT ${offset},${limit}`
      const affiliateMerchantResult = await this.rawQuery(affiliateMerchantQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateMerchantResult', type: 'result', fields: affiliateMerchantResult })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, affiliateMerchant: affiliateMerchantResult }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async generateKeys ({ affiliate_id, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'request', fields: { affiliate_id, connection } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      /* Generate API KEY */

      const api_key = makeRandomid(20)
      const request_key = makeRandomid(32)
      const response_key = makeRandomid(32)
      /* Generate REQUEST And RESPONSE KEY */
      const insertAPIKeysQuery = `INSERT INTO ma_affiliate_api_master (affiliate_ma_user_id, api_key, request_key, response_key, status) VALUES ('${affiliate_id}', '${api_key}', '${request_key}', '${response_key}', 'A')`
      const insertAPIKeysResult = await this.rawQuery(insertAPIKeysQuery, conn)
      if (insertAPIKeysResult.insertId) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, api_key, request_key, response_key }
      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *  Fetch Affiliate Keys
   * @param {{affiliate_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async getKeys ({ affiliate_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'request', fields: { affiliate_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateKeysQuery = `SELECT api_key,request_key,response_key FROM ma_affiliate_key_mgmt WHERE affiliate_id ='${affiliate_id}' AND affiliate_key_status = 'A' LIMIT 1`
      const affiliateKeysResult = await this.rawQuery(affiliateKeysQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateMerchantResult', type: 'result', fields: affiliateKeysResult })

      if (affiliateKeysResult.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, ...affiliateKeysResult[0] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{data:Object,connection?:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async sendMoney ({ data, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'request', fields: data })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: data.commissionType }, conn)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      let refundText = ''
      if (data.ma_status == 'R') {
        refundText = ' - Refund'
      } else if (data.ma_status == 'REV') {
        refundText = ' - Reverse'
      }
      const debitDesc = 'Debit - ' + descType + refundText
      const creditDesc = 'Credit - ' + descType + refundText

      await mySQLWrapper.beginTransaction(conn)

      const pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
        ma_user_id: data.distributorId,
        amount: data.amount,
        transactionType: data.commissionType,
        connection: conn
      })

      if (pointsDetailsEntries.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return pointsDetailsEntries
      }

      // Debit from Distributer
      const distributorLedgerId = await PointsLedgerController.createEntry('_', {
        ma_user_id: data.distributorId,
        amount: data.amount,
        mode: 'dr',
        transaction_type: data.commissionType,
        description: debitDesc,
        ma_status: data.ma_status,
        orderid: data.orderid,
        userid: data.userid,
        corresponding_id: data.retailerId,
        connection: conn
      })
      if (distributorLedgerId.status === 400) {
        await mySQLWrapper.rollback(connection)
        return distributorLedgerId
      }

      // Create entry in ledger details table
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await PointsDetailsController.createEntry('_', {
          ma_user_id: data.distributorId,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: distributorLedgerId.id,
          orderid: data.orderid,
          userid: data.userid,
          ma_status: 'S',
          connection: conn
        })

        if (entry.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return entry
        }
      }

      // Credit In Retailer after subtracting  commission
      const retailerLedgerId = await PointsLedgerController.createEntry('_', {
        ma_user_id: data.retailerId,
        amount: data.amount,
        mode: 'cr',
        transaction_type: data.commissionType,
        ma_status: data.ma_status,
        userid: data.userid,
        description: creditDesc,
        orderid: data.orderid,
        corresponding_id: data.distributorId,
        connection: conn
      })
      if (retailerLedgerId.status === 400) {
        await mySQLWrapper.rollback(connection)
        return retailerLedgerId
      }

      await mySQLWrapper.commit(conn)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoney', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.sqlMessage }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{data:Object,connection?:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async sendMoneyReverse ({ data, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendMoneyReverse', type: 'request', fields: data })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const sendMoneyRec = await this.rawQuery('SELECT ma_user_id,amount,userid from ma_points_ledger_master where transaction_type = "3" AND parent_id="' + data.orderid + '"', conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoneyRev', type: 'request', fields: { sendMoneyRec } })
      if (sendMoneyRec.length == 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
      const emitraId = await this.rawQuery('SELECT dt.profileid,dt.userid FROM ma_user_master dt join ma_user_master rt on dt.profileid=rt.distributer_user_master_id where rt.profileid=' + data.ma_user_id + ' limit 1', conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoneyRev', type: 'emitraId', fields: { emitraId } })
      const common = require('../../util/common')
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '3' }, conn)
      const descType = descObj.code_desc ? descObj.code_desc : ''
      const refundText = ' - Reverse'
      const debitDesc = 'Debit - ' + descType + refundText
      const creditDesc = 'Credit - ' + descType + refundText

      await mySQLWrapper.beginTransaction(conn)

      const pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
        ma_user_id: data.ma_user_id,
        amount: data.amount,
        transactionType: data.commissionType,
        connection
      })

      if (pointsDetailsEntries.status === 400) {
        if (isSet) await mySQLWrapper.rollback(connection)
        return pointsDetailsEntries
      }

      // Debit In Retailer
      const retailerLedgerId = await PointsLedgerController.createEntry('_', {
        ma_user_id: data.ma_user_id,
        amount: Math.abs(data.amount),
        mode: 'dr',
        transaction_type: '3',
        ma_status: 'REV',
        userid: emitraId[0].userid,
        description: debitDesc,
        orderid: 'REV-' + data.orderid,
        corresponding_id: emitraId[0].profileid,
        credit_reversal_status: 'Y',
        connection: conn
      })
      if (retailerLedgerId.status === 400) {
        await mySQLWrapper.rollback(connection)
        return retailerLedgerId
      }

      // Create entry in ledger details table
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await PointsDetailsController.createEntry('_', {
          ma_user_id: data.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: retailerLedgerId.id,
          orderid: data.orderid,
          userid: data.userid,
          ma_status: 'S',
          connection: connection
        })

        if (entry.status === 400) {
          if (isSet) await mySQLWrapper.rollback(connection)
          return entry
        }
      }

      // Credit Fo Distributer
      const distributorLedgerId = await PointsLedgerController.createEntry('_', {
        ma_user_id: emitraId[0].profileid,
        amount: Math.abs(data.amount),
        mode: 'cr',
        transaction_type: '3',
        description: creditDesc,
        ma_status: 'REV',
        orderid: 'REV-' + data.orderid,
        userid: emitraId[0].userid,
        corresponding_id: data.ma_user_id,
        credit_reversal_status: 'Y',
        connection: conn
      })

      if (distributorLedgerId.status === 400) {
        await mySQLWrapper.rollback(connection)
        return distributorLedgerId
      }

      await mySQLWrapper.commit(conn)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendMoneyReverse', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.sqlMessage }
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *  Fetch Affiliate Keys
   * @param {{affiliate_id:number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async getChannelList ({ affiliate_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'request', fields: { affiliate_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateResp = await this.getAffiliate({ affiliate_id, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateResp', type: 'result', fields: affiliateResp })

      if (affiliateResp.status != 200) return affiliateResp

      if (affiliateResp.respcode == 1002) throw new ApplicationError(401, 'Invalid user !')

      const { affiliate } = affiliateResp

      /* FETCH CHANNEL FROM 'MA' SIDE */
      const channelListThroughAPIResp = await LoginController.getChannelListThroughAPI(affiliate.ma_user_id, affiliate.user_type)

      log.logger({ pagename: require('path').basename(__filename), action: 'getChannelListThroughAPI', type: 'result', fields: channelListThroughAPIResp })

      // if (channelListThroughAPIResp.status != 200) return channelListThroughAPIResp

      let maChannelList = []

      if (channelListThroughAPIResp.channel_data) {
        maChannelList = channelListThroughAPIResp.channel_data.split('|').map(channel => channel.toUpperCase())
      }

      /* FETCH CHANNEL FROM DB SIDE */
      const affiliateChannelListQuery = `SELECT affiliate_channel_name,affiliate_ma_flag FROM ma_affiliate_channels_master WHERE affiliate_id=${affiliate_id} AND affiliate_record_status = 'Y'`

      const affiliateChannelListResult = await this.rawQuery(affiliateChannelListQuery, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateChannelListResult', type: 'result', fields: affiliateChannelListResult })

      if (affiliateChannelListResult.length == 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, channels: [] }

      const channels = []

      for (let index = 0; index < affiliateChannelListResult.length; index++) {
        const channel = affiliateChannelListResult[index]
        if (channel.affiliate_ma_flag == 'Y' && maChannelList.includes(channel.affiliate_channel_name)) channels.push(channel.affiliate_channel_name)

        if (channel.affiliate_ma_flag != 'Y') channels.push(channel.affiliate_channel_name)
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, channels }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  static async isMerchantTypeAffiliate ({ merchant_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'request', fields: { merchant_id } })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const isMerchantTypeAffiliateQuery = `SELECT ma_affiliate_mgmt_id FROM ma_affiliate_mgmt WHERE ma_user_id=${merchant_id} AND affiliate_status= 'A' LIMIT 1`
      const isMerchantTypeAffiliateResult = await this.rawQuery(isMerchantTypeAffiliateQuery, connRead)

      if (isMerchantTypeAffiliateResult.length > 0) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }

      return this.isMerchantAffiliatedMerchant({ merchant_id, connectionRead: connRead })
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAffiliateMerchant', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }
}

module.exports = AffiliateController
