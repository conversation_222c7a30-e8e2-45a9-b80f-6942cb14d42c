const crypto = require('crypto')

const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const jwt = require('../../util/token')
const util = require('../../util/util')
const AffiliateController = require('./affliliateController')
const LoginController = require('../login/loginController')
const TransactionController = require('../transaction/transactionController')
const checksum = require('../../util/checksum')
const ValidationError = require('../../errors/ValidationError')
const ApplicationError = require('../../errors/ApplicationError')
const PrimaryServicesController = require('./primaryServices/primaryServicesController')

class GenerateTokenController extends DAO {
  /**
    * Affiliate Generate Token API
    * @param {{affiliate_id:number,merchant_id:number,api_key:string,checksum :string}} fields
    */
  static async getToken (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      let merchant_id = fields.merchant_id || 0 // default merchant id

      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'affiliateResponse', fields: affiliateResponse })
      if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      const affiliateKeyResponse = await AffiliateController.getKeys({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'affiliateKeyResponse', fields: affiliateKeyResponse })

      if (affiliateKeyResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateKeyResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      if (affiliateKeyResponse.api_key != fields.api_key) throw new ValidationError(2001, 'Invalid API Key !')

      const checksumString = `${fields.affiliate_id}:=${merchant_id}:=${affiliateKeyResponse.api_key}`
      const encryptedHash = checksum.checksum512(checksumString)

      if (!crypto.timingSafeEqual(Buffer.from(fields.checksum), Buffer.from(encryptedHash))) {
        throw new ValidationError(401, 'Invalid Checksum !')
      }

      /* Merchant Validation */
      if (merchant_id != 0) {
        const affiliateMerchantResponse = await AffiliateController.getAffiliateMerchant({ affiliate_id: fields.affiliate_id, merchant_id: fields.merchant_id, connectionRead })

        log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'affiliateMerchantResponse', fields: affiliateMerchantResponse })

        if (affiliateMerchantResponse.status != 200) throw new ApplicationError(affiliateMerchantResponse.respcode, affiliateMerchantResponse.message)

        if (affiliateMerchantResponse.respcode == 1002) throw new ValidationError(2001, 'Invalid Merchant User !')
        merchant_id = fields.merchant_id
      }

      /* generate token */
      const token = await jwt.externalSignToken({ payload: { affiliate_id: fields.affiliate_id, merchant_id }, key: affiliateKeyResponse.api_key, expiresIn: util[process.env.NODE_ENV || 'development'].externalApi.jwtExternalExpirySeconds })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { token } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
    * Affiliate Session Token API
    * @param {{affiliate_id:number,ma_user_id:number,api_key:string,checksum :string,channelCode:number}} fields
    */
  static async getSession (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'affiliateResponse', fields: affiliateResponse })
      if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      const { affiliate } = affiliateResponse

      const affiliateKeyResponse = await AffiliateController.getKeys({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'affiliateKeyResponse', fields: affiliateKeyResponse })

      if (affiliateKeyResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateKeyResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      if (affiliateKeyResponse.api_key != fields.api_key) throw new ValidationError(2001, 'Invalid API Key !')

      const checksumString = `${fields.affiliate_id}:=${fields.ma_user_id}:=${fields.channelCode}:=${affiliateKeyResponse.api_key}`
      const encryptedHash = await checksum.checksum512(checksumString)

      if (!crypto.timingSafeEqual(Buffer.from(fields.checksum), Buffer.from(encryptedHash))) {
        throw new ValidationError(401, 'Invalid Checksum !')
      }

      const affiliateMerchantResponse = await AffiliateController.getAffiliateMerchant({ affiliate_id: fields.affiliate_id, merchant_id: fields.ma_user_id, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'affiliateMerchantResponse', fields: affiliateMerchantResponse })

      if (affiliateMerchantResponse.status != 200) throw new ApplicationError(affiliateMerchantResponse.respcode, affiliateMerchantResponse.message)

      if (affiliateMerchantResponse.respcode == 1002) throw new ValidationError(2001, 'Invalid Merchant User !')

      /* channel Validation */
      const channelListResponse = await PrimaryServicesController.getChannelList({ affiliate_id: fields.affiliate_id, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'channelListResponse', fields: channelListResponse })

      if (channelListResponse.status != 200) throw new ApplicationError(channelListResponse.respcode, channelListResponse.message)

      const channelCodeList = channelListResponse.data.channels.map(channel => channel.channelCode.toString())

      if (!channelCodeList.includes(fields.channelCode.toString())) throw new ValidationError(2001, 'Invalid channel code !')

      /* generate token */
      const sessionToken = await jwt.externalSignToken({
        payload: { affiliate_id: fields.affiliate_id, merchant_id: fields.ma_user_id, channelCode: fields.channelCode },
        key: affiliateKeyResponse.api_key,
        expiresIn: util[process.env.NODE_ENV || 'development'].externalApi.jwtExternalSessionExpirySeconds
      })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { sessionToken } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
    * Affiliate Generate Token API
    * @param {{affiliate_id:number}} fields
    */
  static async generateKeys (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'generateKeys', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connection })
      if (affiliateResponse.status != 200) {
        return affiliateResponse
      }

      if (affiliateResponse.respcode == 1002) throw new ApplicationError(401, 'Invalid User !')

      /* generate token */
      return await AffiliateController.generateKeys({ affiliate_id: fields.affiliate_id, connection })
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getToken', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }
}

module.exports = GenerateTokenController
