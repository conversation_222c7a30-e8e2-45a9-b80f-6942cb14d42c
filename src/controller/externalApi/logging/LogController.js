const MongoDbConnector = require('../../../lib/mongoDbConnector')
const log = require('../../../util/log')
const errorMsg = require('../../../util/error')
// const DAO = require('../../../lib/dao')
// const mySQLWrapper = require('../../../lib/mysqlWrapper')
// class LogController extends DAO {
class LogController {
  /**
   * Collection Name
   */
  static get COLLECTION_NAME () {
    return 'externalApi'
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id:number,request_type:string,request_url:string,request_datetime:string,request_json:object,response_json:object,response_time:number}} fields
   */
  static async log (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'log', type: 'insertNewRequest', fields: fields })
    // const connection = await mySQLWrapper.getConnectionFromPool()
    const connection = await MongoDbConnector.connection()
    if (connection === false) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 } // mongodb connection failed
    try {
      const logObject = {
        affiliate_id: fields.affiliate_id,
        merchant_id: fields.merchant_id,
        request_type: fields.request_type,
        request_url: fields.request_url,
        request_datetime: fields.request_datetime,
        request_json: JSON.stringify(fields.request_json),
        request_params: JSON.stringify(fields.request_params),
        response_json: JSON.stringify(fields.response_json),
        response_datetime: fields.response_datetime,
        response_time: fields.response_time
        // added_on: fields.addedon
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'log', type: 'logObject', fields: logObject })
      // const insertNewRequest = await connection.collection(this.COLLECTION_NAME).insertOne(logObject)
      // this.TABLE_NAME = 'ma_external_api_logs'
      // const insertNewRequest = await this.insert(connection, { data: logObject })
      // log.logger({ pagename: require('path').basename(__filename), action: 'log', type: 'insertNewRequest', fields: insertNewRequest })
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'log', type: 'catcherror', fields: error })
      // throw new Error(error)
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      MongoDbConnector.close()
      // connection.release()
    }
  }
}

module.exports = LogController
