const _ = require('lodash')

const log = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const ValidationError = require('../../../../errors/ValidationError')
const ApplicationError = require('../../../../errors/ApplicationError')
const DmtSession = require('../../../session/dmt_session')
const DAO = require('../../../../lib/dao')
const DmtBeneficiaryController = require('../../../beneficiary/beneficiaryController')
const DmtCustomerController = require('../../../customer/customerController')
const bankBranchController = require('../../../bankDetails/bankBranchController')
const { generateOrderID } = require('../../../../util/common_fns')

class BeneficiaryController extends DAO {
  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,beneficiary_name:string,account_number:string,ifsc_code:string,ben_mobile_number:string,relationship:string}} fields
   */
  static async addBeneficiary (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ uic: fields.uic, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      fields.mobile_number = isRemitterActiveResp.data.mobile_number

      /* set parameter for next request */
      fields.ifsc = fields.ifsc_code
      const fetchBankIDResult = await bankBranchController.getBranchByIfsc('_', fields)
      if (fetchBankIDResult.status != 200) throw new ValidationError(2001, 'Invalid isfc code')
      /* set ma_bank_master_id next request */
      fields.ma_bank_master_id = fetchBankIDResult.ma_bank_master_id

      fields.bank_verify = false
      fields.addFrombank = true
      fields.aggregator_order_id = generateOrderID()
      fields.re_account_number = fields.account_number
      const addBeneficiaryResponse = await DmtBeneficiaryController.addAffiliateRemitterBeneficiary({ fields, connectionRead, connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'addBeneficiaryResponse', fields: addBeneficiaryResponse })

      if (addBeneficiaryResponse.status != 200) throw new ApplicationError(addBeneficiaryResponse.respcode, addBeneficiaryResponse.message)

      const response = {
        status: 200,
        message: 'Success',
        respcode: 1000,
        data: {
          order_id: addBeneficiaryResponse.orderid,
          ma_beneficiaries_id: addBeneficiaryResponse.ma_beneficiaries_id,
          bene_name: fields.beneficiary_name,
          bank_account_number: fields.account_number,
          ben_mobile_number: fields.ben_mobile_number,
          ifsc_code: fields.ifsc_code
        }
      }

      if (addBeneficiaryResponse.respcode == 2202) {
        response.message = addBeneficiaryResponse.message
        response.respcode = addBeneficiaryResponse.respcode
      }

      return response
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,order_id:string}} fields
   */
  static async addBeneficiaryResendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryResendOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Fields Validation */
      if (!_.has(fields, 'sessionRQ') || !_.isString(fields.sessionRQ)) {
        throw new ValidationError(2001, 'sessionRQ is required field and must be string')
      }

      const otpBankSQL = `SELECT ma_bank_on_boarding_id,response,mobile FROM ma_otp_master WHERE aggregator_order_id = "${fields.order_id}" limit 1`
      const otpBankData = await this.rawQuery(otpBankSQL, connectionRead)

      if (otpBankData.length == 0) throw new ApplicationError(1104, errorMsg.responseCode[1104])

      fields.mobile_number = otpBankData[0].mobile

      /* SESSIONRQ Validation */
      const sessionDataResponse = await DmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'sessionData', fields: sessionDataResponse })

      if (sessionDataResponse.status != 200) throw new ValidationError(sessionDataResponse.respcode, sessionDataResponse.message)

      fields.orderid = fields.order_id

      const resendBeneficiaryResponse = await DmtBeneficiaryController.addBeneficiaryResendOtp('_', fields)

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'resendBeneficiaryResponse', fields: resendBeneficiaryResponse })

      if (resendBeneficiaryResponse.status != 200) throw new ApplicationError(resendBeneficiaryResponse.respcode, resendBeneficiaryResponse.message)

      return {
        status: 200,
        message: 'Success',
        respcode: 1000
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryResendOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,otp:string,order_id:string,ma_beneficiaries_id:string }} fields
   */
  static async addBeneficiaryVerifyOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPAddBeneficiary', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ uic: fields.uic, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      fields.mobile_number = isRemitterActiveResp.data.mobile_number

      fields.bank_verify = false
      const verifyBeneficiaryResponse = await DmtBeneficiaryController.verifyBeneficiaryOtpV2('_', {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        orderid: fields.order_id,
        mobile_number: fields.mobile_number,
        ma_beneficiaries_id: fields.ma_beneficiaries_id,
        uic: fields.uic,
        bank_verify: false,
        otp: fields.otp,
        sessionRQ: fields.sessionRQ,
        isOtpRequired: true
      }, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'verifyBeneficiaryResponse', fields: verifyBeneficiaryResponse })

      if (verifyBeneficiaryResponse.status != 200) throw new ApplicationError(verifyBeneficiaryResponse.respcode, verifyBeneficiaryResponse.message)

      const beneficiary = verifyBeneficiaryResponse.beneficiary_list[0]
      return {
        status: 200,
        message: 'Success',
        respcode: 1000,
        data: {
          orderid: fields.order_id,
          ma_beneficiaries_id: beneficiary.ma_beneficiaries_id,
          bene_name: beneficiary.bene_name,
          bank_account_number: beneficiary.account_number,
          ben_mobile_number: beneficiary.ben_mobile_number,
          ifsc_code: beneficiary.ifsc_code
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPAddBeneficiary', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,beneficiary_name:string,account_number:string,ifsc_code:string,ben_mobile_number:string,relationship:string}} fields
   */
  static async verifyAddBeneficiary (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyAddBeneficiary', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ uic: fields.uic, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      fields.mobile_number = isRemitterActiveResp.data.mobile_number

      /* set parameter for next request */
      fields.ifsc = fields.ifsc_code
      const fetchBankIDResult = await bankBranchController.getBranchByIfsc('_', fields)
      if (fetchBankIDResult.status != 200) throw new ValidationError(2001, 'Invalid isfc code')
      /* set ma_bank_master_id next request */
      fields.ma_bank_master_id = fetchBankIDResult.ma_bank_master_id

      fields.bank_verify = true
      fields.addFrombank = true
      fields.aggregator_order_id = generateOrderID()
      fields.re_account_number = fields.account_number
      const addBeneficiaryResponse = await DmtBeneficiaryController.addAffiliateRemitterBeneficiary({ fields, connectionRead, connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'addBeneficiaryResponse', fields: addBeneficiaryResponse })

      if (addBeneficiaryResponse.status != 200) throw new ApplicationError(addBeneficiaryResponse.respcode, addBeneficiaryResponse.message)

      const response = {
        status: 200,
        message: 'Success',
        respcode: 1000,
        data: {
          ma_bene_verification_id: addBeneficiaryResponse.ma_bene_verification_id,
          verification_message: addBeneficiaryResponse.verification_message,
          bank_bene_name: addBeneficiaryResponse.bank_bene_name || '',
          bank_account_number: fields.account_number,
          ben_mobile_number: fields.ben_mobile_number,
          ifsc_code: fields.ifsc_code,
          order_id: addBeneficiaryResponse.order_id,
          transaction_id: addBeneficiaryResponse.transaction_id
        }
      }

      return response
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyAddBeneficiary', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,ma_beneficiaries_id:string }} fields
   */
  static async sendDeleteBeneficiaryOtp (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ uic: fields.uic, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      /* SESSIONRQ Validation */
      const sessionDataResponse = await DmtSession.getDMTSessionData(connectionRead, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'sessionData', fields: sessionDataResponse })

      if (sessionDataResponse.status != 200) throw new ValidationError(sessionDataResponse.respcode, sessionDataResponse.message)

      /* SET CONNECTION FOR NEXT REQUEST */
      fields.mobile_number = isRemitterActiveResp.data.mobile_number
      fields.ma_bank_on_boarding_id = sessionDataResponse.handler.BANK_ON_BOARDING_ID
      fields.handler = sessionDataResponse.handler

      const deleteBeneficiaryResponse = await DmtBeneficiaryController.deleteAffiliateBeneficiary({ fields, connectionRead, connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'deleteBeneficiaryResponse', fields: deleteBeneficiaryResponse })

      if (deleteBeneficiaryResponse.status != 200) throw new ApplicationError(deleteBeneficiaryResponse.respcode, deleteBeneficiaryResponse.message)

      return {
        status: 200,
        message: 'OTP Send Successfully',
        respcode: 2202,
        data: {
          order_id: deleteBeneficiaryResponse.orderid
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{sessionRQ:string,order_id:string,ma_beneficiaries_id:number}} fields
   */
  static async deleteBeneficiaryResendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryResendOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Fields Validation */
      if (!_.has(fields, 'sessionRQ') || !_.isString(fields.sessionRQ)) {
        throw new ValidationError(2001, 'sessionRQ is required field and must be string')
      }
      if (!_.has(fields, 'order_id') || !_.isString(fields.order_id)) {
        throw new ValidationError(2001, 'order_id is required field and must be string')
      }
      if (!_.has(fields, 'ma_beneficiaries_id') || !_.isNumber(fields.ma_beneficiaries_id)) {
        throw new ValidationError(2001, 'ma_beneficiaries_id is required field and must be integer')
      }

      const otpBankSQL = `SELECT ma_bank_on_boarding_id,response,mobile FROM ma_otp_master WHERE aggregator_order_id = "${fields.order_id}" LIMIT 1`
      const otpBankData = await this.rawQuery(otpBankSQL, connectionRead)

      if (otpBankData.length == 0) throw new ApplicationError(1104, errorMsg.responseCode[1104])

      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ mobile_number: otpBankData[0].mobile, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      /* SET CONNECTION FOR NEXT REQUEST */
      fields.mobile_number = otpBankData[0].mobile
      fields.uic = isRemitterActiveResp.data.uic
      fields.ma_bank_on_boarding_id = otpBankData[0].ma_bank_on_boarding_id
      fields.orderid = fields.order_id

      const resentOtpDeleteBeneficiaryResponse = await DmtBeneficiaryController.deleteAffiliateBeneficiary({ fields, connectionRead, connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'resentOtpDeleteBeneficiaryResponse', fields: resentOtpDeleteBeneficiaryResponse })

      if (resentOtpDeleteBeneficiaryResponse.status != 200) throw new ApplicationError(resentOtpDeleteBeneficiaryResponse.respcode, resentOtpDeleteBeneficiaryResponse.message)

      return {
        status: 200,
        message: 'Success',
        respcode: 1000
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryResendOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number,otp:string,order_id:string,ma_beneficiaries_id:string }} fields
   */
  static async verifyDeleteBeneficiaryOtp (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryVerifyOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ uic: fields.uic, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      /* SESSIONRQ Validation */
      const sessionDataResponse = await DmtSession.getDMTSessionData(connectionRead, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'sessionData', fields: sessionDataResponse })

      if (sessionDataResponse.status != 200) throw new ValidationError(sessionDataResponse.respcode, sessionDataResponse.message)

      /* SET CONNECTION FOR NEXT REQUEST */
      fields.mobile_number = isRemitterActiveResp.data.mobile_number
      fields.ma_bank_on_boarding_id = sessionDataResponse.handler.BANK_ON_BOARDING_ID
      fields.handler = sessionDataResponse.handler
      fields.orderid = fields.order_id

      const verifyBeneficiaryResponse = await DmtBeneficiaryController.verifyDeleteAffiliateBeneficiary({ fields, connectionRead, connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'verifyBeneficiaryResponse', fields: verifyBeneficiaryResponse })

      if (verifyBeneficiaryResponse.status != 200) throw new ApplicationError(verifyBeneficiaryResponse.respcode, verifyBeneficiaryResponse.message)

      return {
        status: 200,
        message: 'Beneficiary deleted successfully',
        respcode: 1000
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryVerifyOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,limit:number,offset:number}} fields
   */
  static async beneficiaryList (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Set Default Limit & Offset Value */
      if (!_.has(fields, 'limit')) {
        fields.limit = 10
      }
      if (!_.has(fields, 'offset')) {
        fields.limit = 0
      }
      const beneficaryListResponse = await DmtBeneficiaryController.beneficiariesList({ fields, connection, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficaryListResponse', type: 'listBeneficiaries', fields: beneficaryListResponse })

      if (beneficaryListResponse.status != 200) throw new ApplicationError(beneficaryListResponse.respcode, beneficaryListResponse.message)

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        data: {
          beneficiaries: beneficaryListResponse.beneficiaries
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }
}

module.exports = BeneficiaryController
