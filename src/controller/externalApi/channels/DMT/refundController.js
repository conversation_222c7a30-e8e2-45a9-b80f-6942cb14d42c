const _ = require('lodash')

const log = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const ValidationError = require('../../../../errors/ValidationError')
const ApplicationError = require('../../../../errors/ApplicationError')
const DAO = require('../../../../lib/dao')
const DmtRefundController = require('../../../refund/refundtransferController')

class RefundController extends DAO {
  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_user_id:number,userid:number}} fields
   */
  static async refundTransactionList (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* SET CONNECTION FOR NEXT REQUEST */
      fields.connection = connectionRead
      const refundTransactionListResponse = await DmtRefundController.refundTransfers({ fields, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'response', fields: refundTransactionListResponse })

      if (refundTransactionListResponse.status != 200) throw new ApplicationError(refundTransactionListResponse.respcode, refundTransactionListResponse.message)

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        data: {
          transactions: refundTransactionListResponse.transfersData
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{sessionRQ:string,ma_transfers_id:string}} fields
   */
  static async refundSendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refundSendOTP', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* SET CONNECTION FOR NEXT REQUEST */
      fields.action = 1 // SEND
      const refundSendOTPResponse = await DmtRefundController.refundTransfer('_', fields)

      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'response', fields: refundSendOTPResponse })

      if (refundSendOTPResponse.status != 200 && refundSendOTPResponse.respcode == 1002) throw new ApplicationError(refundSendOTPResponse.respcode, 'Invalid ma_transfers_id')

      if (refundSendOTPResponse.status != 200) throw new ApplicationError(refundSendOTPResponse.respcode, refundSendOTPResponse.message)

      return {
        status: 200,
        message: 'OTP Send Successfully',
        respcode: 2202,
        data: {
          order_id: refundSendOTPResponse.orderid
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundSendOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{sessionRQ:string,order_id:string,ma_transfers_id:number}} fields
   */
  static async refundResendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refundResendOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Fields Validation */
      if (!_.has(fields, 'sessionRQ') || !_.isString(fields.sessionRQ)) {
        throw new ValidationError(2001, 'sessionRQ is required field and must be string')
      }
      if (!_.has(fields, 'order_id') || !_.isString(fields.order_id)) {
        throw new ValidationError(2001, 'order_id is required field and must be string')
      }
      if (!_.has(fields, 'ma_transfers_id') || !_.isNumber(fields.ma_transfers_id)) {
        throw new ValidationError(2001, 'ma_transfers_id is required field and must be integer')
      }

      const otpBankSQL = `SELECT ma_bank_on_boarding_id,response,mobile FROM ma_otp_master WHERE aggregator_order_id = "${fields.order_id}" LIMIT 1`
      const otpBankData = await this.rawQuery(otpBankSQL, connectionRead)

      if (otpBankData.length == 0) throw new ApplicationError(2001, errorMsg.responseCode[1104])

      const isOTPCustomResp = await this.isOTPCustom({ ma_transfers_id: fields.ma_transfers_id, connectionRead })

      if (isOTPCustomResp.status != 200) throw new ApplicationError(isOTPCustomResp.respcode, isOTPCustomResp.message)

      /* SET CONNECTION FOR NEXT REQUEST */
      fields.mobile_number = otpBankData[0].mobile
      fields.orderid = fields.order_id
      fields.airpayotp = isOTPCustomResp.airpayotp
      fields.action = 2 // SEND
      const refundSendOTPResponse = await DmtRefundController.refundTransfer('_', fields)

      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'response', fields: refundSendOTPResponse })

      if (refundSendOTPResponse.status != 200 && refundSendOTPResponse.respcode == 1002) throw new ApplicationError(refundSendOTPResponse.respcode, 'Invalid ma_transfers_id')

      if (refundSendOTPResponse.status != 200) throw new ApplicationError(refundSendOTPResponse.respcode, refundSendOTPResponse.message)

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000]
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundResendOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{uic:string,sessionRQ:string,ma_transfers_id:string}} fields
   */
  static async refundVerifyOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refundVerifyOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const isOTPCustomResp = await this.isOTPCustom({ ma_transfers_id: fields.ma_transfers_id, connectionRead })

      if (isOTPCustomResp.status != 200) throw new ApplicationError(isOTPCustomResp.respcode, isOTPCustomResp.message)

      /* SET CONNECTION FOR NEXT REQUEST */
      fields.airpayotp = isOTPCustomResp.airpayotp
      fields.orderid = fields.order_id
      fields.action = 3 // VERIFY
      const refundVerifyOTPResponse = await DmtRefundController.refundTransfer('_', fields)

      log.logger({ pagename: require('path').basename(__filename), action: 'refundTransactionList', type: 'response', fields: refundVerifyOTPResponse })

      if (refundVerifyOTPResponse.status != 200) throw new ApplicationError(refundVerifyOTPResponse.respcode, refundVerifyOTPResponse.message)

      return {
        status: 200,
        respcode: 1000,
        message: 'Transferred Refunded Successfully'
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'refundVerifyOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{ma_transfers_id:Number,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async isOTPCustom ({ ma_transfers_id, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPriorityBankLimit', type: 'request', fields: ma_transfers_id })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const transferdataSQL = `SELECT ma_bank_on_boarding,bank_response,ma_transaction_master_id,(transfer_amount+bank_charges) as total_amount,request_number FROM ma_transfers WHERE ma_transfers_id = ${ma_transfers_id} AND transfer_status = 'P' AND refund_flag = 'Y' limit 1`
      const transferdata = await this.rawQuery(transferdataSQL, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpRefund', type: 'transferdata', fields: transferdata })

      if (transferdata.length == 0) throw new ApplicationError(2001, 'Invalid ma_transfers_id')

      const bankdetails = await DmtRefundController.getBanksDetails('', { ma_bank_on_boarding_id: transferdata[0].ma_bank_on_boarding, entity_type: 'REFUND' }, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpRefund', type: 'bankdetails', fields: bankdetails })
      let airpayotp = true
      if (bankdetails.status === 200 && bankdetails.data.length > 0 && bankdetails.data[0].otp_required == 'YES') {
        airpayotp = false
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, airpayotp }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getPriorityBankLimit', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
    }
  }
}

module.exports = RefundController
