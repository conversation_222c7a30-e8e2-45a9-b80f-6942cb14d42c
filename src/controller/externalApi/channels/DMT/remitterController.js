
const _ = require('lodash')

const log = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const CustomerController = require('../../../customer/customerController')
const ValidationError = require('../../../../errors/ValidationError')
const ApplicationError = require('../../../../errors/ApplicationError')
const DmtSession = require('../../../session/dmt_session')
const CustomerBankMapController = require('../../../customer/customerBankMapController')
const BeneficiaryController = require('../../../beneficiary/beneficiaryController')
const OtpController = require('../../../otp/otpController')
const common = require('../../../../util/common')
const DAO = require('../../../../lib/dao')

class RemitterController extends DAO {
  /**
   *
   * @param {{affiliate_id:number,mobile_number:string,ma_user_id:number,userid:number}} fields
   */
  static async remitterLogin (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'remitterLogin', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Validation */
      const remitterDetailResult = await CustomerController.remitterDetail({ mobile_number: fields.mobile_number, connection: connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterDetail', type: 'response', fields: remitterDetailResult })

      if (remitterDetailResult.status != 200) throw new ValidationError(2001, remitterDetailResult.message)

      const { ischeckRemitterExist } = remitterDetailResult

      const isCustomerBlackListed = await CustomerController.isCustomerBlackListed({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        remitterDetails: ischeckRemitterExist
      }, connection)

      if (isCustomerBlackListed.status != 200) throw new ValidationError(isCustomerBlackListed.respcode, isCustomerBlackListed.message)
      /* create sesssionRQ */
      const createSessionResult = await CustomerController.createSession({
        mobile_number: fields.mobile_number,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        connection
      })

      const { bankHandler, sessionData } = createSessionResult
      /* fields set for next request */
      fields.bankHandler = bankHandler
      fields.sessionData = sessionData
      log.logger({ pagename: require('path').basename(__filename), action: 'createSessionResult', type: 'response', fields: createSessionResult })

      if (createSessionResult.status != 200) throw new ApplicationError(createSessionResult.respcode, createSessionResult.message)

      // remitter Not exist for any bank
      if (ischeckRemitterExist.length == 0) return await this.newRemitterLogin({ fields, connection, connectionRead })
      // customer status is disabled
      if (ischeckRemitterExist.length != 0 && ischeckRemitterExist[0].customer_status != 'Y') throw new ApplicationError(1068, errorMsg.responseCode[1068])
      // remitter  exist for any bank
      if (ischeckRemitterExist.length != 0 && ischeckRemitterExist[0].customer_status == 'Y') {
        fields.sessionData.uic = ischeckRemitterExist[0].uic // assign existing uic
        fields.remitter_name = ischeckRemitterExist[0].remitter_name
        /* update data for next request */
        fields = { ...fields, ...ischeckRemitterExist[0] }
        return await this.existingRemitterLogin({ fields, connection, connectionRead })
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLogin', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async newRemitterLogin ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'newRemitterLogin', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* save session */
      const sessionRQ = await DmtSession.insertData(conn, fields.sessionData)
      return {
        status: 200,
        message: 'Customer Doesn\'t Exists !',
        respcode: 2201,
        data: {
          sessionRQ,
          remitterDetail: {},
          beneficiaries: []
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'newRemitterLogin', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async existingRemitterLogin ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterLogin', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const ma_bank_on_boarding_id = fields.sessionData.ma_bank_on_boarding_id
      const isRemitterRegisteredResp = await CustomerBankMapController.isCustomerRegister(fields.uic, ma_bank_on_boarding_id, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLogin', type: 'existingRemitterLogin', fields: isRemitterRegisteredResp })

      if (isRemitterRegisteredResp.status != 200) throw new ApplicationError(isRemitterRegisteredResp.respcode, isRemitterRegisteredResp.message)

      /* save session */
      const sessionRQ = await DmtSession.insertData(conn, fields.sessionData)

      /* set sessionRQ for next request */
      fields.sessionRQ = sessionRQ

      log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterLogin', type: 'sessionRQ', fields: sessionRQ })

      /* customer not register with bank */
      if (isRemitterRegisteredResp.respcode === 1002) {
        return {
          status: 200,
          message: 'Customer Doesn\'t Exists !',
          respcode: 2201,
          data: {
            sessionRQ,
            remitterDetail: {},
            beneficiaries: []
          }
        }
      }

      /* customer already register with bank */
      if (isRemitterRegisteredResp.respcode === 1000) {
        /* REMITTER NAME CORRECTION */
        const remitterNameCorrectionResult = await CustomerController.remitterNameCorrection({
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          handler: fields.bankHandler.handler,
          remitterName: fields.remitter_name,
          connection
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterLogin', type: 'remitterNameCorrectionResult', fields: remitterNameCorrectionResult })

        if (remitterNameCorrectionResult.status != 200) throw new ApplicationError(remitterNameCorrectionResult.respcode, remitterNameCorrectionResult.message)

        /* Remitter name is not valid registration required */
        if (remitterNameCorrectionResult.status == 200 && remitterNameCorrectionResult.respcode == 1235) {
          return {
            status: 200,
            message: 'Customer Doesn\'t Exists !',
            respcode: 2201,
            data: {
              sessionRQ,
              remitterDetail: {},
              beneficiaries: []
            }
          }
        }

        /* Remitter Login if remitter is already registered with priority parnter bank */

        const remitterLimitResponse = await this.getPriorityBankLimit({ fields, connectionRead: connRead })

        if (remitterLimitResponse.status != 200) throw new ApplicationError(remitterLimitResponse.respcode, remitterLimitResponse.message)

        /* SET LIMIT AND OFFSET */
        fields.limit = 10
        fields.offset = 0
        const beneficiariesResponse = await this.beneficiaryList({ fields, connectionRead: connRead, connection: conn })

        if (beneficiariesResponse.status != 200) throw new ApplicationError(beneficiariesResponse.respcode, beneficiariesResponse.message)

        return {
          status: 200,
          message: 'Success',
          respcode: 1000,
          data: {
            sessionRQ,
            remitterDetail: {
              uic: fields.uic,
              mobile_number: fields.mobile_number,
              remitter_name: fields.remitter_name,
              ...remitterLimitResponse.limit
            },
            beneficiaries: beneficiariesResponse.beneficiaries
          }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterLogin', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,mobile_number:string,sessionRQ:string,first_name:string,last_name:string,ma_user_id:number,userid:number}} fields
   */
  static async remitterRegistration (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Validation */
      const remitterDetailResult = await CustomerController.remitterDetail({ mobile_number: fields.mobile_number, connection: connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'remitterDetail', fields: remitterDetailResult })

      if (remitterDetailResult.status != 200) throw new ValidationError(2001, remitterDetailResult.message)

      const { ischeckRemitterExist } = remitterDetailResult

      const isCustomerBlackListed = await CustomerController.isCustomerBlackListed({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        remitterDetails: ischeckRemitterExist
      }, connection)

      if (isCustomerBlackListed.status != 200) throw new ValidationError(isCustomerBlackListed.respcode, isCustomerBlackListed.message)
      /* SESSIONRQ Validation */
      const sessionDataResponse = await DmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'sessionData', fields: sessionDataResponse })

      if (sessionDataResponse.status != 200) throw new ValidationError(sessionDataResponse.respcode, sessionDataResponse.message)

      const { handler } = sessionDataResponse

      const nameValidationResponse = await CustomerController.remitterNameValidation({
        ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
        remitterName: { firstName: fields.first_name, lastName: fields.last_name },
        handler,
        ma_user_id: fields.ma_user_id,
        connection
      })

      if (nameValidationResponse.status != 200) throw new ValidationError(nameValidationResponse.respcode, nameValidationResponse.message)

      /* UPDATE REQUEST  */
      fields.sessionData = sessionDataResponse
      fields.remitterName = `${fields.first_name} ${fields.last_name}`
      // remitter Not exist for any bank
      if (ischeckRemitterExist.length == 0) return await this.newRemitterRegistration({ fields, connection, connectionRead })
      // customer status is disabled
      if (ischeckRemitterExist.length != 0 && ischeckRemitterExist[0].customer_status != 'Y') throw new ApplicationError(1068, errorMsg.responseCode[1068])
      // remitter  exist for any bank
      if (ischeckRemitterExist.length != 0 && ischeckRemitterExist[0].customer_status == 'Y') {
        /* UPDATE REQUEST  */
        fields.remitterData = ischeckRemitterExist[0]
        fields.uic = ischeckRemitterExist[0].uic
        return await this.existingRemitterRegistration({ fields, connection, connectionRead })
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async newRemitterRegistration ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'newRemitterRegistration', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const preValidateResponse = await this.remitterPreValidation({ fields, connection: conn, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'newRemitterRegistration', type: 'remitterPreValidation', fields: preValidateResponse })

      if (preValidateResponse.status != 200) throw new ApplicationError(preValidateResponse.respcode, preValidateResponse.message)

      if (preValidateResponse.isRemitterNew) {
        /* INSERT NEW ENTRY IN 'ma_customer_detail' */
        const customerFields = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          mobile_number: fields.mobile_number,
          remitter_name: fields.remitterName,
          uic: fields.sessionData.data.uicSession,
          remitter_id: 0,
          kyc_status: 'NV',
          customer_status: 'Y',
          country_code: fields.countrycode
        }
        const newCustomerResponse = await CustomerController.insertNewRemitter({ customerFields, connection: conn })

        if (newCustomerResponse.status != 200) throw new ApplicationError(newCustomerResponse.respcode, newCustomerResponse.message)
      }
      return {
        status: 200,
        message: 'OTP Send Successfully',
        respcode: 2202,
        data: {
          order_id: preValidateResponse.order_id
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'newRemitterRegistration', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async existingRemitterRegistration ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterRegistration', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const handler = fields.sessionData.handler
      const ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID
      const isRemitterRegisteredResp = await CustomerBankMapController.isCustomerRegister(fields.uic, ma_bank_on_boarding_id, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLogin', type: 'existingRemitterLogin', fields: isRemitterRegisteredResp })

      if (isRemitterRegisteredResp.status != 200) throw new ApplicationError(isRemitterRegisteredResp.respcode, isRemitterRegisteredResp.message)

      /* customer already register with bank */
      if (isRemitterRegisteredResp.respcode === 1000) {
        fields.bankMappingFields = isRemitterRegisteredResp.data
        return await this.existingRemitterRegistrationNameValidation({ fields, connection: conn, connectionRead: connRead })
      }

      const preValidateResponse = await this.remitterPreValidation({ fields, connection: conn, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'newRemitterRegistration', type: 'remitterPreValidation', fields: preValidateResponse })

      if (preValidateResponse.status != 200) throw new ApplicationError(preValidateResponse.respcode, preValidateResponse.message)

      /* UPDATE 'ma_customer_detail' */
      const data = { remitter_name: `${fields.first_name} ${fields.last_name}` }
      this.TABLE_NAME = 'ma_customer_details'
      await this.updateWhere(conn, { data, id: fields.remitterData.ma_customer_details_id, where: 'ma_customer_details_id' })

      return {
        status: 200,
        message: 'OTP Send Successfully',
        respcode: 2202,
        data: {
          order_id: preValidateResponse.order_id
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterRegistration', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async existingRemitterRegistrationNameValidation ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterRegistration', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const handler = fields.sessionData.handler
      const ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID

      const { firstName, lastName } = common.splitRemitterNameWithSpace(fields.remitterData.remitter_name)
      const nameValidationResponse = await CustomerController.remitterNameValidation({
        ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
        remitterName: { firstName, lastName },
        handler,
        ma_user_id: fields.ma_user_id,
        connection: conn
      })

      if (nameValidationResponse.status == 200) {
        return {
          status: 200,
          message: 'Customer Already Registered !',
          respcode: 2202
        }
      }

      /* UPDATE 'ma_customer_detail' */
      const data = { remitter_name: `${fields.first_name} ${fields.last_name}` }
      this.TABLE_NAME = 'ma_customer_details'
      await this.updateWhere(conn, { data, id: fields.remitterData.ma_customer_details_id, where: 'ma_customer_details_id' })

      /* UPDATE THE BANK MAPPING TO PENDING */
      CustomerBankMapController.updateWhereData(conn, {
        data: { bank_status: 'P' },
        id: fields.bankMappingFields.ma_customer_details_bank_id,
        where: 'ma_customer_details_bank_id'
      })

      /* SEND CUSTOM OTP */
      const sentOtpResponse = await OtpController.sentInternalOtp({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        otp_type: 'CO',
        other: {},
        smsMessage: '',
        connection: conn
      })

      if (sentOtpResponse.status == 400 || !sentOtpResponse.status) throw new ApplicationError(sentOtpResponse.respcode, sentOtpResponse.message)

      return {
        status: 200,
        message: 'OTP Send Successfully',
        respcode: 2202,
        data: {
          order_id: sentOtpResponse.data.aggregator_order_id
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterRegistration', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async remitterPreValidation ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterRegistration', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const preValidateResponse = await CustomerController.doPreValidationAtBankEnd({
        bankHandler: fields.sessionData,
        mobile_number: fields.mobile_number,
        ma_user_id: fields.ma_user_id,
        remitter_name: fields.remitterName,
        connection: conn
      })

      if (preValidateResponse.status != 200) throw new ApplicationError(preValidateResponse.respcode, preValidateResponse.message)
      /* PREVALIDATION FROM BANK SIDE  */
      /* IF REMITTER ALREADY EXITS SEND CUSTOM OTP */
      if (preValidateResponse.isRemitterAlreadyRegistered) {
        const sentOtpResponse = await OtpController.sentInternalOtp({
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          mobile_number: fields.mobile_number,
          otp_type: 'CO',
          other: {},
          smsMessage: '',
          connection: conn
        })
        if (sentOtpResponse.status == 400 || !sentOtpResponse.status) throw new ApplicationError(sentOtpResponse.respcode, sentOtpResponse.message)
        return {
          status: 200,
          message: 'OTP Send Successfully',
          respcode: 2202,
          order_id: sentOtpResponse.data.aggregator_order_id
        }
      }
      /* IF REMITTER NOT EXISTS THEN SEND OTP BASED UPON THE PARTNER BANK  */
      if (!preValidateResponse.isRemitterAlreadyRegistered) {
        const ma_bank_on_boarding_id = fields.sessionData.handler.BANK_ON_BOARDING_ID
        const bankOtpResponse = await CustomerController.isSenderOtpBankSide({ ma_bank_on_boarding_id, connection: connRead })

        if (bankOtpResponse.status != 200) throw new ApplicationError(bankOtpResponse.respcode, bankOtpResponse.message)

        const data = {
          handler: fields.sessionData.handler,
          BANK_OP: 'SEND_OTP_ADD_REMITTER',
          bankName: fields.sessionData.handler.BANK_NAME,
          bankOtp: bankOtpResponse.flag,
          sessionRQ: fields.sessionRQ,
          senderDetails: true,
          remitterName: fields.remitterName
        }
        const sentOtpResponse = await OtpController.sentBankOtp(fields.ma_user_id, fields.userid, fields.mobile_number, 'CO', data, connection)

        if (sentOtpResponse.status == 400 || !sentOtpResponse.status) throw new ApplicationError(sentOtpResponse.respcode, sentOtpResponse.message)
        return {
          status: 200,
          message: 'OTP Send Successfully',
          respcode: 2202,
          order_id: sentOtpResponse.data.aggregator_order_id,
          isRemitterNew: true
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'existingRemitterRegistration', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{sessionRQ:string,order_id:string}} fields
   */
  static async remitterRegistrationResendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistrationResendOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Fields Validation */
      if (!_.has(fields, 'sessionRQ') || !_.isString(fields.sessionRQ)) {
        throw new ValidationError(2001, 'sessionRQ is required field and must be string')
      }

      const isOTPCustomResp = await this.isOTPCustom({ fields, connectionRead })
      if (isOTPCustomResp.status != 200) throw new ApplicationError(isOTPCustomResp.respcode, isOTPCustomResp.message)

      fields.mobile_number = isOTPCustomResp.mobile_number
      fields.ma_bank_on_boarding_id = isOTPCustomResp.ma_bank_on_boarding_id
      const isCustomOTP = isOTPCustomResp.isCustomOTP
      /* SESSIONRQ Validation */
      const sessionDataResponse = await DmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'sessionData', fields: sessionDataResponse })

      if (sessionDataResponse.status != 200) throw new ValidationError(sessionDataResponse.respcode, sessionDataResponse.message)

      const { handler } = sessionDataResponse

      let bankOtp = true
      if (!isCustomOTP) {
        const bankOtpResponse = await CustomerController.isSenderOtpBankSide({ ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id, connection: connectionRead })

        if (bankOtpResponse.status != 200) throw new ApplicationError(bankOtpResponse.respcode, bankOtpResponse.message)

        /* SEND BANK SIDE OTP */
        if (bankOtpResponse.flag) {
          const resendBankOtpResponse = await OtpController.resentBankOtp(handler, 'RESEND_OTP_ADD_REMITTER', fields.mobile_number, fields.order_id, 'CO', connection, fields.sessionRQ, 2002)

          if (resendBankOtpResponse.status === true) {
            return {
              status: 200,
              respcode: 2202,
              message: 'OTP Send Successfully'
            }
          }

          return {
            status: 400,
            respcode: resendBankOtpResponse.respcode,
            message: resendBankOtpResponse.message
          }
        }
        bankOtp = !bankOtpResponse.flag
      }
      /* CUSTOM OTP */
      if (isCustomOTP || bankOtp) {
        const resendBankOtpResponse = await OtpController.resendInternalOtp({
          mobile_number: fields.mobile_number,
          orderid: fields.order_id,
          otp_type: 'CO',
          connection
        })

        if (resendBankOtpResponse.status === true) {
          return {
            status: 200,
            respcode: 2202,
            message: 'OTP Send Successfully'
          }
        }

        return {
          status: 400,
          respcode: resendBankOtpResponse.respcode,
          message: resendBankOtpResponse.message
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistrationResendOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{mobile_number:string,otp:string,order_id:string}} fields
   */
  static async remitterRegistrationVerifyOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistrationVerifyOTP', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Validation */
      const remitterDetailResult = await CustomerController.remitterDetail({ mobile_number: fields.mobile_number, connection: connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'remitterDetail', fields: remitterDetailResult })

      if (remitterDetailResult.status != 200) throw new ValidationError(2001, remitterDetailResult.message)

      const { ischeckRemitterExist } = remitterDetailResult

      /* SET REMITTER NAME FIELDS */
      const { firstName, lastName } = common.splitRemitterNameWithSpace(ischeckRemitterExist[0].remitter_name)
      fields.firstName = firstName
      fields.lastName = lastName
      fields.remitter_name = ischeckRemitterExist[0].remitter_name

      const isCustomerBlackListed = await CustomerController.isCustomerBlackListed({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        remitterDetails: ischeckRemitterExist
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'isCustomerBlackListed', fields: isCustomerBlackListed })

      if (isCustomerBlackListed.status != 200) throw new ValidationError(isCustomerBlackListed.respcode, isCustomerBlackListed.message)
      /* SESSIONRQ Validation */
      const sessionDataResponse = await DmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'sessionData', fields: sessionDataResponse })

      if (sessionDataResponse.status != 200) throw new ValidationError(sessionDataResponse.respcode, sessionDataResponse.message)

      const { handler } = sessionDataResponse
      /* VERIFY OTP */

      const isOTPCustomResp = await this.isOTPCustom({ fields, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'isOTPCustom', fields: isOTPCustomResp })

      if (isOTPCustomResp.status != 200) throw new ApplicationError(isOTPCustomResp.respcode, isOTPCustomResp.message)

      fields.mobile_number = isOTPCustomResp.mobile_number
      fields.uic = ischeckRemitterExist[0].uic
      fields.ma_bank_on_boarding_id = isOTPCustomResp.ma_bank_on_boarding_id
      const isCustomOTP = isOTPCustomResp.isCustomOTP

      let verifyOtpResponse = {}
      /* VERIFY OTP */
      if (isCustomOTP) {
        verifyOtpResponse = await OtpController.verifyOtp(fields.order_id, 'CO', fields.otp, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'verifyOtp-isCustomOTP', fields: verifyOtpResponse })

        if (verifyOtpResponse.status != true) throw new ApplicationError(verifyOtpResponse.respcode, verifyOtpResponse.message)

        verifyOtpResponse.remitter_id = fields.mobile_number // set param for nxt request
      }

      /* BANK SIDE VERIFICATION AND BANK API CALL */
      if (!isCustomOTP) {
        const bankOtpResponse = await CustomerController.isSenderOtpBankSide({ ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id, connection: connectionRead })

        if (bankOtpResponse.status != 200) throw new ApplicationError(bankOtpResponse.respcode, bankOtpResponse.message)

        const data = {
          handler: handler,
          BANK_OP: 'ADD_REMITTER',
          bankName: handler.BANK_NAME,
          bankOtp: bankOtpResponse.flag,
          fields: fields,
          required_field: bankOtpResponse.required_field
        }

        verifyOtpResponse = await OtpController.verifyBankOtp(fields.order_id, 'CO', fields.otp, data, connection, fields.sessionRQ)

        log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistration', type: 'verifyOtp', fields: verifyOtpResponse })

        if (verifyOtpResponse.status != 200) throw new ApplicationError(verifyOtpResponse.respcode, verifyOtpResponse.message)
      }

      const isMappingPresent = await CustomerBankMapController.isCustomerMappingPresent({
        uic: ischeckRemitterExist[0].uic,
        ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
        connectionRead
      })

      if (isMappingPresent.status != 200) throw new ApplicationError(isMappingPresent.respcode, isMappingPresent.message)

      if (isMappingPresent.respcode == 1002) {
        /* INSERT NEW ENTRY IN CUSTOMER BANK MAPPING */
        const customerFields = {
          ma_customer_details_id: ischeckRemitterExist[0].ma_customer_details_id,
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          uic: fields.uic,
          ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id,
          mobile_number: fields.mobile_number,
          remitter_id: verifyOtpResponse.remitter_id,
          bankname: handler.BANK_NAME
        }
        const insertBankMappingResponse = await CustomerController.insertNewRemitterBankMapping({ customerFields, connection })
        if (insertBankMappingResponse.status != 200) throw new ApplicationError(insertBankMappingResponse.respcode, insertBankMappingResponse.message)
      }

      if (isMappingPresent.respcode == 1000 && isMappingPresent.data.bank_status != 'S') {
        CustomerBankMapController.updateWhereData(connection, {
          data: { bank_status: 'S' },
          id: isMappingPresent.data.ma_customer_details_bank_id,
          where: 'ma_customer_details_bank_id'
        })
      }

      /* RETURN THE RESULT */
      fields.sessionData = sessionDataResponse.data
      const remitterLimitResponse = await this.getPriorityBankLimit({ fields, connectionRead })

      if (remitterLimitResponse.status != 200) throw new ApplicationError(remitterLimitResponse.respcode, remitterLimitResponse.message)

      const beneficiariesResponse = await this.beneficiaryList({ fields, connectionRead, connection })

      if (beneficiariesResponse.status != 200) throw new ApplicationError(beneficiariesResponse.respcode, beneficiariesResponse.message)

      return {
        status: 200,
        message: 'Success',
        respcode: 1000,
        data: {
          sessionRQ: fields.sessionRQ,
          remitterDetail: {
            uic: ischeckRemitterExist[0].uic,
            mobile_number: fields.mobile_number,
            remitter_name: ischeckRemitterExist[0].remitter_name,
            ...remitterLimitResponse.limit
          },
          beneficiaries: beneficiariesResponse.beneficiaries
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterRegistrationVerifyOTP', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,uic:string,sessionRQ:string,ma_user_id:number,userid:number}} fields
   */
  static async remitterLimit (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'remitterLimit', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Validation */
      const remitterDetailResult = await CustomerController.remitterDetail({ uic: fields.uic, connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterDetail', type: 'response', fields: remitterDetailResult })

      if (remitterDetailResult.status != 200) throw new ValidationError(2001, remitterDetailResult.message)

      if (remitterDetailResult.ischeckRemitterExist.length == 0) throw new ValidationError(2001, 'Invalid Customer !')

      const { ischeckRemitterExist } = remitterDetailResult

      const isCustomerBlackListed = await CustomerController.isCustomerBlackListed({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        remitterDetails: ischeckRemitterExist
      })

      if (isCustomerBlackListed.status != 200) throw new ValidationError(isCustomerBlackListed.respcode, isCustomerBlackListed.message)

      // remitter Not exist for any bank
      if (ischeckRemitterExist.length == 0) throw new ValidationError(2001, 'Invalid Customer !')
      // customer status is disabled
      if (ischeckRemitterExist.length != 0 && ischeckRemitterExist[0].customer_status != 'Y') throw new ApplicationError(1068, errorMsg.responseCode[1068])
      // remitter  exist for any bank
      if (ischeckRemitterExist.length != 0 && ischeckRemitterExist[0].customer_status == 'Y') {
        /* update data for next request */
        fields = { ...fields, ...ischeckRemitterExist[0] }

        const remitterLimitResponse = await this.getRemitterLimit({ fields, connection })
        log.logger({ pagename: require('path').basename(__filename), action: 'remitterLimitResponse', type: 'request', fields: remitterLimitResponse })
        if (remitterLimitResponse.status != 200) throw new ApplicationError(remitterLimitResponse.respcode, remitterLimitResponse.message)

        return {
          status: 200,
          message: errorMsg.responseCode[1000],
          respcode: 1000,
          data: {
            limit: remitterLimitResponse.limit
          }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterLimit', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connection.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async getPriorityBankLimit ({ fields, connectionRead, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPriorityBankLimit', type: 'request', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const priorityBankID = fields.sessionData.ma_bank_on_boarding_id

      /* Remove ma_bank_on_boarding_id from response */
      fields.removeBankId = false
      const remitterLimitResponse = await this.getRemitterLimit({ fields, connection: conn })

      if (remitterLimitResponse.status != 200) throw new ApplicationError(remitterLimitResponse.respcode, remitterLimitResponse.message)

      let limit = {}

      remitterLimitResponse.limit.forEach(bankLimit => {
        if (bankLimit.ma_bank_on_boarding_id == priorityBankID) {
          const { ...restLimitResponse } = bankLimit
          limit = restLimitResponse
        }
      })
      const { ma_bank_on_boarding_id, ...resLimit } = limit
      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        limit: resLimit
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getPriorityBankLimit', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:connection:Promise<mySQLWrapper.getConnectionFromPool()>}} fields
   */
  static async getRemitterLimit ({ fields, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterLimit', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const remitterLimitResponse = await CustomerController.getRemitterLimit('_', fields, conn)

      if (remitterLimitResponse.status != 200) throw new ApplicationError(remitterLimitResponse.respcode, remitterLimitResponse.message)

      const modifyLimitResponse = []
      remitterLimitResponse.bankListLimits.forEach(bankLimit => {
        const { bank_name, bank_logo, isKycAvailable, bankKycStatus, bankRegisterStatus, ...limitResponse } = bankLimit
        modifyLimitResponse.push(limitResponse)
      })

      if (fields.removeBankId == false) {
        return {
          status: 200,
          message: errorMsg.responseCode[1000],
          respcode: 1000,
          limit: modifyLimitResponse
        }
      }

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        limit: modifyLimitResponse.map(limit => {
          const { ma_bank_on_boarding_id, ...restLimit } = limit
          return restLimit
        })
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async beneficiaryList ({ fields, connectionRead, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryList', type: 'request', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const beneficaryListResponse = await BeneficiaryController.beneficiariesList({ fields, connection: conn, connectionRead: connRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficaryListResponse', type: 'listBeneficiaries', fields: beneficaryListResponse })

      if (beneficaryListResponse.status != 200) throw new ApplicationError(beneficaryListResponse.respcode, beneficaryListResponse.message)

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        beneficiaries: beneficaryListResponse.beneficiaries
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryList', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async isOTPCustom ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getPriorityBankLimit', type: 'request', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const otpBankSQL = `SELECT ma_bank_on_boarding_id,response,mobile FROM ma_otp_master WHERE aggregator_order_id = "${fields.order_id}" LIMIT 1`
      const otpBankData = await this.rawQuery(otpBankSQL, connectionRead)

      if (otpBankData.length == 0) throw new ApplicationError(1104, errorMsg.responseCode[1104])

      const mobile_number = otpBankData[0].mobile
      const ma_bank_on_boarding_id = otpBankData[0].ma_bank_on_boarding_id
      const otpResponse = JSON.parse(otpBankData[0].response)
      const isCustomOTP = otpResponse.isCustomOTP == true

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, mobile_number, ma_bank_on_boarding_id, isCustomOTP }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getPriorityBankLimit', type: 'catcherror', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
    }
  }
}

module.exports = RemitterController
