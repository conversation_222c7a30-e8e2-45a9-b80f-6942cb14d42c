
const log = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const DAO = require('../../../../lib/dao')
const DmtTransfersController = require('../../../transfers/transfersController')
const ApplicationError = require('../../../../errors/ApplicationError')
const DmtCustomerController = require('../../../customer/customerController')
const { generateOrderID } = require('../../../../util/common_fns')

class TransferController extends DAO {
  /**
   *
   * @param {{sessionRQ:string,amount:number,ma_user_id:number,userid:number,ma_beneficiaries_id:number,uic:string,transfer_mode:string}} fields
   */
  static async transfers (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* UIC Validation */
      const isRemitterActiveResp = await DmtCustomerController.isRemitterActive({ uic: fields.uic, connection: connectionRead })

      if (isRemitterActiveResp.status != 200) throw new ApplicationError(isRemitterActiveResp.respcode, isRemitterActiveResp.message)

      fields.mobile_number = isRemitterActiveResp.data.mobile_number
      /* GENERATE ORDERID  */
      fields.aggregator_order_id = generateOrderID()

      const transfersResponse = await DmtTransfersController.affiliateTransfer({ fields, connection, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'transfersResponse', fields: transfersResponse })

      if (transfersResponse.status != 200) throw new ApplicationError(transfersResponse.respcode, transfersResponse.message)

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        data: {
          transaction: { ...transfersResponse }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
      connection.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,ma_user_id:number,userid:number,order_id:string}} fields
   */
  static async transferStatus (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const transfersResponse = await DmtTransfersController.transferStatus({ ...fields, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'transfersResponse', fields: transfersResponse })

      if (transfersResponse.status != 200) throw new ApplicationError(transfersResponse.respcode, transfersResponse.message)

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        data: {
          transfer: { ...transfersResponse.data }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }
}

module.exports = TransferController
