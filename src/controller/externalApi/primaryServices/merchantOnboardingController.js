const j2xml = require('js2xmlparser')
const xml2js = require('fast-xml-parser')
const axios = require('axios')
const _ = require('lodash')
const bcrypt = require('bcryptjs')

const DAO = require('../../../lib/dao')
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const log = require('../../../util/log')
const errorMsg = require('../../../util/error')
const util = require('../../../util/util')
const { checksum256 } = require('../../../util/checksum')
const ApplicationError = require('../../../errors/ApplicationError')
const { encryptLeadApiRequest, decryptLeadApiResponse, getSystemCodes } = require('../../../util/common')
const AffiliateController = require('../affliliateController')
const qs = require('qs')

class MerchantOnboardingController extends DAO {
  /**
   *
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async addMerchant ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addMerchant', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const affiliateDataResp = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead: connRead })

      if (affiliateDataResp.status != 200 || affiliateDataResp.respcode == 1002) {
        throw new ApplicationError(affiliateDataResp.respcode, affiliateDataResp.message)
      }

      const { affiliate } = affiliateDataResp

      const { email, ...affiliateRest } = affiliate

      fields = { ...fields, ...affiliateRest }

      const addMSMerchantApiResponse = await this.addMSMerchantApiCall({ fields, connection: conn, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'addMSMerchantApiCall', type: 'response', fields: addMSMerchantApiResponse })

      if (addMSMerchantApiResponse.status != 200) return addMSMerchantApiResponse

      const insertNewUser = await this.insertNewUser({ fields, apiResponse: addMSMerchantApiResponse.response, connection: conn })

      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'response', fields: insertNewUser })

      if (insertNewUser.status != 200) return insertNewUser

      /* update request  */
      fields.requestId = addMSMerchantApiResponse.insertId
      fields.requestObject = addMSMerchantApiResponse.requestObject
      // const addLeadMerchantApiResponse = await this.addLeadMerchantApiCall({ fields, connection })

      // log.logger({ pagename: require('path').basename(__filename), action: 'addLeadMerchantApiResponse', type: 'response', fields: addLeadMerchantApiResponse })

      const { apikey, password, username, mcc, distributer_user_master_id, profileid, userid, ...restUser } = insertNewUser.user

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, merchant: { ...restUser, merchant_id: profileid } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addMerchant', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,apiResponse:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns
   */
  static async insertNewUser ({ fields, apiResponse, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'request', fields: fields })
    // const isSet = (connection === null || connection === undefined)
    // const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    console.log("insertNewUser connection1",connection)
    const conn = await mySQLWrapper.getConnectionFromPool()
    console.log("insertNewUser connection1",conn)
    //await mySQLWrapper.beginTransaction(conn)
    try {
      /* INSERT INTO users */
      this.TABLE_NAME = 'users'
      const userData = {
        name: `${fields.firstname} ${fields.lastname}`,
        email: apiResponse.USER_EMAIL,
        password: await bcrypt.hash(apiResponse.PASSWORD, await bcrypt.genSalt(10)),
        status: 2,
        username: apiResponse.USERNAME,
        profileid: apiResponse.MERCHANTID,
        mobile: fields.contactno,
        user_type: 'web' // affiliate user type as web
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'userData', fields: userData })
      await mySQLWrapper.beginTransaction(conn)
      const insertNewUser = await this.insert(conn, { data: userData })
      if (insertNewUser.affectedRows == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }
      const userid = insertNewUser.insertId
      /* INSERT INTO ma_user_master */

      const roleIdQuery = "SELECT id FROM roles WHERE slug = 'web_role'"
      const roleIdResult = await this.rawQuery(roleIdQuery, conn)
      if (roleIdResult.length == 0) throw new ApplicationError(1002, 'Role Unknow')

      const roleid = roleIdResult[0].id

      const mccCodeQuery = 'SELECT mcc_code FROM ma_mcc_master WHERE ma_mcc_master_id = 1'
      const mccCodeResult = await this.rawQuery(mccCodeQuery, conn)
      if (mccCodeResult.length == 0) throw new ApplicationError(1002, 'Mcc Unknow')

      const mcc = mccCodeResult[0].mcc_code

      this.TABLE_NAME = 'ma_user_master'
      const userMasterData = {
        profileid: apiResponse.MERCHANTID,
        userid,
        distributer_user_master_id: fields.ma_user_id,
        email_id: apiResponse.USER_EMAIL,
        mobile_id: fields.contactno,
        firstname: fields.firstname,
        lastname: fields.lastname,
        address: fields.businessAddress,
        city: fields.city,
        state: fields.state,
        country: fields.country || 'India',
        pincode: fields.pincode,
        user_type: 'RT',
        username: apiResponse.APIUSERNAME,
        password: apiResponse.APIPASSWORD,
        apikey: apiResponse.APIKEY,
        gst_number: '',
        pan: fields.panNumber || '',
        votar_id: 0,
        company: fields.company || '',
        sales_id: 0,
        zone: fields.zone || '',
        aadhar_number: '',
        mer_user: 'mer',
        esign_link: '',
        bt_qr_string: '',
        dob: fields.DOB,
        mcc,
        activation_level: 1,
        roleid,
        user_status: 'Y'
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'userMasterData', fields: userMasterData })
      const insertNewUserMaster = await this.insert(conn, { data: userMasterData })
      if (insertNewUserMaster.affectedRows == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }
      const ma_user_master_id = insertNewUserMaster.insertId
      /* INSERT INTO ma_gold_agent_temp_data */
      this.TABLE_NAME = 'ma_gold_agent_temp_data'
      /* REMOVING ADDITIONAL KEYS */
      const goldAgentTempData = {
        ...userMasterData,
        ma_user_master_id,
        status: 'C'
      }
      /* delete unwanted keys */
      delete goldAgentTempData.activation_level
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'goldAgentTempData', fields: goldAgentTempData })
      const insertGoldAgentTempData = await this.insert(conn, { data: goldAgentTempData })
      if (insertGoldAgentTempData.affectedRows == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }
      /* INSERT INTO ma_user_profiles_master */
      this.TABLE_NAME = 'ma_user_profiles_master'
      const profileMasterData = {
        ma_user_profiles_master_id: apiResponse.MERCHANTID
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'profileMasterData', fields: profileMasterData })
      const insertProfileMasterData = await this.insert(conn, { data: profileMasterData })
      if (insertProfileMasterData.affectedRows == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }
      /* INSERT INTO ma_allow_withdrawal */
      const allowedWalletQuery = 'SELECT * FROM system_codes WHERE code_id = \'200003\''
      const allowedWalletResult = await this.rawQuery(allowedWalletQuery, conn)
      if (allowedWalletResult.length == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }

      const allowedWalletFlagQuery = 'SELECT * FROM system_codes WHERE code_id = \'200009\''
      const allowedWalletFlagResult = await this.rawQuery(allowedWalletFlagQuery, conn)
      if (allowedWalletFlagResult.length == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }

      const allowedWalletFlag = {}
      allowedWalletFlagResult.forEach(allowedFlag => {
        allowedWalletFlag[allowedFlag.code_val] = allowedFlag
      })

      /* BULK INSERT ALL THE WALLET TYPE */
      const allowedWithdrawalList = []
      for (let index = 0; index < allowedWalletResult.length; index++) {
        const result = allowedWalletResult[index]
        const [walletType, code_id] = result.code_desc.split('::')
        const allowedWalletTransaction = await getSystemCodes(this, { code: code_id }, conn)
        allowedWithdrawalList.push(`(${apiResponse.MERCHANTID},${result.code_val},'${allowedWalletTransaction}',${result.code_val},'${allowedWalletFlag[result.code_val].code_desc}')`)
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'allowedWithdrawalList', fields: allowedWithdrawalList })
      this.TABLE_NAME = 'ma_allow_withdrawal'
      await this.insertBulk(conn, { fields: '(`ma_user_id`,`priority`,`transaction_type`,`wallet_type`,`allow_flag`)', data: allowedWithdrawalList.join(',') })

      /* INSERT INTO ma_gst_master */
      const percentage = await getSystemCodes(this, { code: 200002 }, conn)
      this.TABLE_NAME = 'ma_gst_master'
      const gstMasterData = {
        ma_user_id: apiResponse.MERCHANTID,
        percentage
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'gstMasterData', fields: gstMasterData })
      const insertGstMasterData = await this.insert(conn, { data: gstMasterData })
      if (insertGstMasterData.affectedRows == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }
      /* INSERT INTO ma_user_on_boarding_bank_mapping */
      const onBoardingBankQuery = `SELECT * FROM ma_user_on_boarding_bank_mapping WHERE onboarding_status = 'Y' AND ma_user_id=${fields.ma_user_id}`
      const onBoardingBankResult = await this.rawQuery(onBoardingBankQuery, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'onBoardingBankResult', fields: onBoardingBankResult })
      if (onBoardingBankResult.length > 0) {
        /* INSERT BANK MAPPING */
        const bankMappingList = []
        onBoardingBankResult.forEach(bank => {
          bankMappingList.push(`(${apiResponse.MERCHANTID},${bank.ma_bank_on_boarding_id},'${bank.locationid}','${bank.bankmid}','${bank.bankmid_master_id}',${bank.min_amount},${bank.max_amount},${bank.priority},'${bank.onBoarding_status}')`)
        })
        this.TABLE_NAME = 'ma_user_on_boarding_bank_mapping'
        await this.insertBulk(conn, { fields: '(`ma_user_id`,`ma_bank_on_boarding_id`,`locationid`,`bankmid`,`bankmid_master_id`,`min_amount`,`max_amount`,`priority`,`onBoarding_status`)', data: bankMappingList.join(',') })
      }
      /* INSERT INTO ma_merchant_settings_mapping */
      this.TABLE_NAME = 'ma_merchant_settings_mapping'
      const merchantSettingMappingData = {
        ma_user_id: apiResponse.MERCHANTID,
        settings_flag: '{\'ma_incentive_master\':true,\'ma_incentive_details\':true,\'ma_allow_withdrawal\':true,\'ma_gst_master\':true,\'ma_commission_master\':true,\'ma_commission_slab\':true}'
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'merchantSettingMappingData', fields: merchantSettingMappingData })
      const insertMerchantSettingMapping = await this.insert(conn, { data: merchantSettingMappingData })
      if (insertMerchantSettingMapping.affectedRows == 0) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(1022, errorMsg.responseCode[1022])
      }

      await mySQLWrapper.commit(conn)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, user: userMasterData }
    } catch (error) {
      await mySQLWrapper.rollback(conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'insertNewUser', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (_.isNumber(error.code) && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      //if (isSet) conn.release()
      conn.release()
    }
  }

  /**
   *
   * @param {{email:string,firstname:string,lastname:string,contactno:string,businessName:string,businessAddress:string,city:number,state:number,pincode:string,panNumber:string,affiliate_id:number,DOB:string,ipAddress:string}} fields
   * @returns Object
   */
  static async addMSMerchantApiCall ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addMSMerchantApiCall', type: 'request', fields: fields })
    //const isSet = (connection === null || connection === undefined)
    // const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    console.log("connection1",connection)
    console.log("addMSMerchantApiCall connection2",connectionRead)
    const conn = await mySQLWrapper.getConnectionFromPool()
    console.log("addMSMerchantApiCall connection1",conn)
    // const isSetRead = (connectionRead === null || connectionRead === undefined)
    // const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    const connRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const env = util[process.env.NODE_ENV || 'development']

      const requestObject = {
        KEY: env.externalApi.maAddMerchantKey,
        EMAIL: fields.email,
        FIRSTNAME: fields.firstname,
        LASTNAME: fields.lastname,
        CONTACTNO: fields.contactno,
        BUSINESS: fields.businessName,
        BUSINESSADDRESS: fields.businessAddress,
        BUSINESSCITY: fields.city,
        BUSINESSSTATE: fields.state,
        BUSINESSCOUNTRY: '',
        BUSINESSPINCODE: fields.pincode,
        BUSINESSLANDLINE: '',
        BUSINESSTYPE: 1,
        BUSINESSCATEGORY: 1,
        MCC_CODE: 5941,
        BUSINESSWEBSITE: '',
        BANK_NAME: '',
        BANK_ADDRESS: '',
        BANK_CITY: '',
        BANK_STATE: '',
        BANK_PINCODE: '',
        BANK_LANDLINE: '',
        BANK_ACCOUNTTYPE: '',
        BANK_ACCOUNTNAME: '',
        BANK_ACCOUNTNO: '',
        BANK_IFSCCODE: '',
        IPADDRESS: fields.ipAddress || '',
        PURCHASEPLAN: 1,
        PAN_NO: fields.panNumber,
        GST_NO: '',
        GST_STATE: '',
        MERCHANT_LEGAL_NAME: fields.company,
        ZONE: 4,
        MERCHANT_TYPE: 'AC',
        SALES_USER_ID: fields.sales_id,
        DISTRIBUTOR_ID: fields.ma_user_id,
        LEAD_TYPE: 'retailer',
        BT_QR_STRING: '',
        MERCHANT_PARTNER: 'EM',
        DOB: fields.DOB,
        ANNUALTURNOVER: '',
        BRAND: ''
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addMSMerchantApiCall', type: 'requestObject', fields: requestObject })

      /* INGORE CDATA */
      const cdataKeys = Object.keys(requestObject).filter(key => key != 'KEY')

      const xmlRequestString = await this.getXmlRequestObject({ wrapIn: 'REQUEST', payLoad: { REGISTRATION: requestObject }, cdataKeys })

      log.logger({ pagename: require('path').basename(__filename), action: 'getXmlRequestObject', type: 'response', fields: xmlRequestString })

      if (xmlRequestString.status != 200) return xmlRequestString

      // $inputArr['EMAIL'].$inputArr['FIRSTNAME'].$inputArr['LASTNAME'].$inputArr['PAN_NO'].$inputArr['BANK_ACCOUNTNO'].$inputArr['MERCHANT_LEGAL_NAME']
      const checksum = checksum256(`${fields.email}${fields.firstname}${fields.lastname}${fields.panNumber}${fields.accountNumber || ''}${fields.company || ''}`)

      /* REQUEST PAYLOAD */
      const payload = {
        enc: encryptLeadApiRequest({ text: xmlRequestString.xmlString }),
        checksum
      }

      const endPoint = `${env.externalApi.maBaseURL}/${env.externalApi.maEndpoints.ADD_MERCHANT}`
      console.time("start")

      /* AXIOS API CALL */
      const addMerchantResponse = await axios.post(
        endPoint,
        payload,
        { headers: { 'Content-Type': 'text/xml; charset=UTF8' } }
      )
      console.timeEnd("end")

      log.logger({ pagename: require('path').basename(__filename), action: 'addMSMerchantApiCall', type: 'API Response', fields: addMerchantResponse })

      if (addMerchantResponse.status != 200) throw new ApplicationError(2201, 'Server Down')

      const decryptResponse = decryptLeadApiResponse({ encryptedTxt: addMerchantResponse.data })

      const parseResponse = xml2js.parse(decryptResponse, { parseTrueNumberOnly: true }, true)

      log.logger({ pagename: require('path').basename(__filename), action: 'addMSMerchantApiCall', type: 'ADD_MERCHANT', fields: parseResponse })

      let ms_api_status = 400

      if (!parseResponse.RESPONSE || !parseResponse.RESPONSE.REGISTRATION) throw new ApplicationError(1001, 'Unknow Response')

      ms_api_status = parseResponse.RESPONSE.REGISTRATION.STATUS
      console.log("ms_api_status",ms_api_status)

      if (ms_api_status == 400) throw new ApplicationError(2001, parseResponse.RESPONSE.REGISTRATION.STATUSMSG)

      /* INSERT INCOMING REQUEST */
      this.TABLE_NAME = 'ma_affiliate_merchant_onboarding_request'
      const data = {
        affiliate_id: fields.ma_user_id,
        ms_api_request_body: xmlRequestString.xmlString,
        ms_api_response_body: decryptResponse,
        ms_api_status
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'addMSMerchantApiCall', type: 'insertNewRequestData', fields: data })
      const insertNewRequest = await this.insert(conn, { data })

     if (insertNewRequest.affectedRows == 0) throw new ApplicationError(1022, errorMsg.responseCode[1022])

      const insertId = insertNewRequest.insertId

      const { STATUS, STATUSMSG, PARAM } = parseResponse.RESPONSE.REGISTRATION

      if (STATUS == '332' || STATUS == '616') throw new ApplicationError(1001, STATUSMSG)

      if (!PARAM || Object.keys(PARAM).length == 0 || !PARAM.USER || Object.keys(PARAM.USER).length == 0) throw new ApplicationError(1001, 'Unknow Response')

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: PARAM.USER, requestObject: { ...requestObject, ...PARAM.USER }, insertId }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addMerchant', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (_.isNumber(error.code) && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }

      if (error.isAxiosError) {
        defaultErrorResponse.message = error.response.statusText
        defaultErrorResponse.respcode = 2201
      }

      return defaultErrorResponse
    } finally {
      // if (isSetRead) connRead.release()
      // if (isSet) conn.release()
      connRead.release()
      conn.release()
    }
  }

  /**
   *
   * @param {{fields:Object,apiResponse:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>}} param0
   * @returns Object
   */
  static async addLeadMerchantApiCall ({ fields, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addLeadMerchantApiCall', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const env = util[process.env.NODE_ENV || 'development']
      const { requestObject: requestParams } = fields
      const requestObject = {
        EMAIL: requestParams.EMAIL || '',
        CONTACTNO: requestParams.CONTACTNO || '',
        FIRSTNAME: requestParams.FIRSTNAME || '',
        LASTNAME: requestParams.LASTNAME || '',
        BUSINESS: requestParams.BUSINESS || '',
        BUSINESSADDRESS: requestParams.BUSINESSADDRESS || '',
        BUSINESSSTATE: requestParams.BUSINESSSTATE || '',
        BUSINESSPINCODE: requestParams.BUSINESSPINCODE || '',
        PAN_NO: requestParams.PAN_NO || '',
        ZONE: requestParams.ZONE || '',
        SALES_USER_ID: requestParams.SALES_USER_ID || '',
        DISTRIBUTOR_ID: requestParams.DISTRIBUTOR_ID || '',
        LEAD_TYPE: requestParams.LEAD_TYPE || '',
        MERCHANTID: requestParams.MERCHANTID || '',
        USERNAME: requestParams.USERNAME || '',
        BUSINESSCITY: requestParams.BUSINESSCITY || '',
        BUSINESSLANDLINE: requestParams.BUSINESSLANDLINE || '',
        BUSINESSTYPE: requestParams.BUSINESSTYPE || '',
        BUSINESSCATEGORY: requestParams.BUSINESSCATEGORY || '',
        AADHAR: requestParams.AADHAR || ''
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addLeadMerchantApiCall', type: 'requestObject', fields: requestObject })
      let parameters = ''
      for (const key in requestObject) {
        parameters += requestObject[key]
      }
      const checksum = checksum256(`${parameters}${env.externalApi.leadApiChecksumString}`)

      /* REQUEST PAYLOAD */

      log.logger({ pagename: require('path').basename(__filename), action: 'addLeadMerchantApiCall', type: 'request', fields: fields })

      const endPoint = `${env.externalApi.leadBaseURL}/${env.externalApi.leadEndpoints.ADD_MERCHANT}`

      /* AXIOS API CALL */
      const addMerchantResponse = await axios.post(
        endPoint,
        requestObject,
        {
          headers: {
            'Content-Type': 'application/json',
            AIRPAYKEY: env.externalApi.leadMerchantOnboardingKey,
            AFFILIATE: env.externalApi.leadMerchantOnboardingAffiliate,
            CHECKSUM: checksum
          }
        }
      )

      log.logger({ pagename: require('path').basename(__filename), action: 'addLeadMerchantApiCall', type: 'API Response', fields: addMerchantResponse })

      const { data: addLeadResponse } = addMerchantResponse

      /* UDPATE  */
      this.TABLE_NAME = 'ma_affiliate_merchant_onboarding_request'
      const data = {
        lead_api_request_body: JSON.stringify(requestObject),
        lead_api_response_body: JSON.stringify(addLeadResponse),
        lead_api_status: addLeadResponse.status
      }
      const updateRequestResponse = await this.updateWhere(conn, { data, id: fields.requestId, where: 'ma_affiliate_merchant_onboarding_request_id' })

      log.logger({ pagename: require('path').basename(__filename), action: 'addLeadMerchantApiCall', type: 'API Response', fields: updateRequestResponse })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addMerchant', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }

      if (error.isAxiosError) {
        defaultErrorResponse.message = error.response.statusText
        defaultErrorResponse.respcode = 2201
      }

      return defaultErrorResponse
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{wrapIn:string,payLoad:Object,cdataKeys:Array<string>}} param0
   * @returns
   */
  static async getXmlRequestObject ({ wrapIn, payLoad, cdataKeys }) {
    try {
      /* JS OBJECT TO XML */
      const xmlOption = {
        declaration: {
          include: true
        },
        format: {
          doubleQuotes: true,
          pretty: true
        },
        useSelfClosingTagIfEmpty: false,
        cdataKeys
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, xmlString: j2xml.parse(wrapIn, payLoad, xmlOption) }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addMerchant', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    }
  }

  /**
   *
   * @param {fields:{pincode:string,city:number,state:number},connectionRead:Promise<mySQLWrapper.getConnectionFromPool()>} param0
   * @returns
   */
  static async pincodeValidation ({ fields, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'pincodeValidation', type: 'request', fields: fields })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const env = util[process.env.NODE_ENV || 'development']
      /* 256HASH */
      const checksum = checksum256(`${fields.pincode}${env.externalApi.leadApiChecksumString}`)
      const config = {
        method: 'post',
        url: env.externalApi.leadPincodeCheckEndpoint,
        headers: {
          APIKEY: env.airpayKey,
          AFFILIATE: env.externalApi.leadAffiliateKey,
          CHECKSUM: checksum,
          AIRPAYKEY: env.externalApi.leadAirpayKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: qs.stringify({ pincode: fields.pincode })
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'pincodeValidation', type: 'config axios', fields: config })
      /* API CALL */
      const pincodeResponse = await axios(config)
      if (pincodeResponse.status != 200) throw new ApplicationError(2001, pincodeResponse.message)
      log.logger({ pagename: require('path').basename(__filename), action: 'pincodeValidation', type: 'response', fields: pincodeResponse })
      const { data, status } = pincodeResponse.data
      if (status != 200) throw new ApplicationError(2201, 'API Error')
      if (!data || data.length === 0) throw new ApplicationError(2001, 'Invalid Pincode')
      const { REGION_NAME } = data[0]
      /* CHECK STATE ID VALIDATION */
      const checkCityQuery = `SELECT id, state_id FROM ma_cities_master where name = '${REGION_NAME}' LIMIT 1`
      const checkCityResult = await this.rawQuery(checkCityQuery, connRead)
      if (checkCityResult.length == 0) throw new ApplicationError(2001, 'Invalid Pincode')
      const { id, state_id } = checkCityResult[0]
      if (fields.state != state_id) throw new ApplicationError(2001, 'Invalid State')
      if (fields.city != id) throw new ApplicationError(2001, 'Invalid City')

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addMerchant', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSetRead) connRead.release()
    }
  }
}

module.exports = MerchantOnboardingController
