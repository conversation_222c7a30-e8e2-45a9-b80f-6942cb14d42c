const moment = require('moment')
const _ = require('lodash')

const DAO = require('../../../lib/dao')
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const log = require('../../../util/log')
const errorMsg = require('../../../util/error')
const BalanceController = require('../../balance/balanceController')
const AffiliateController = require('../affliliateController')
const LedgerHistoryController = require('../../ledgerHistory/ledgerHistoryController')
const TransactionController = require('../../transaction/transactionController')
const BankMasterController = require('../../bankDetails/bankMasterController')
const ApplicationError = require('../../../errors/ApplicationError')
const ValidationError = require('../../../errors/ValidationError')
const MerchantOnboardingController = require('./merchantOnboardingController')
const Validator = require('../../../util/validator')
class PrimaryServicesController extends DAO {
  /**
   *
   * @param {{email:string,firstname:string,lastname:string,contactno:string,businessName:string,businessAddress:string,city:number,state:number,pincode:string,panNumber?:string,affiliate_id:number,DOB:string,ipAddress:string}} fields
   */
  static async merchantOnboarding (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'merchantOnboarding', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* email validation */
      if (!Validator.validInput('email', fields.email)) throw new ValidationError(2001, 'Invalid Email')
      /* pan validation */
      if (fields.panNumber && !Validator.validatePAN(fields.panNumber)) throw new ValidationError(2001, 'Invalid PAN')
      /* contact no validation */
      if (!Validator.validInput('indianmobile', fields.contactno)) throw new ValidationError(2001, 'Invalid Contact No.')
      /* pincode validation */
      if (!Validator.validInput('indianpincode', fields.pincode)) throw new ValidationError(2001, 'Invalid Pincode')
      /* city & state validation FROM API */
      const pincodeValidationResp = await MerchantOnboardingController.pincodeValidation({ fields, connection, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'merchantOnboarding', type: 'pincodeValidationResp', fields: pincodeValidationResp })
      if (pincodeValidationResp.status != 200) throw new ApplicationError(pincodeValidationResp.respcode, pincodeValidationResp.message)
      /* DOB validation */
      const DOB = moment(fields.DOB, 'YYYY-MM-DD', true)
      if (!DOB.isValid()) throw new ValidationError(2001, 'Invalid DOB')
      /* businessAddress validation */
      if (!Validator.validInput('paytmaddress', fields.businessAddress)) throw new ValidationError(2001, 'Invalid Address')
      const addMerchantResponse = await MerchantOnboardingController.addMerchant({ fields, connection, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'merchantOnboarding', type: 'addMerchant', fields: addMerchantResponse })
      if (addMerchantResponse.status != 200) throw new ApplicationError(addMerchantResponse.respcode, addMerchantResponse.message)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { merchant: addMerchantResponse.merchant } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'merchantOnboarding', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   * External API : Affiliate Wallet Balance
   * @param {{affiliate_id:number}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async getWalletBalance (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalance', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const affiliateResp = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'affiliateResp', type: 'result', fields: affiliateResp })

      if (affiliateResp.status != 200) return affiliateResp

      const { affiliate } = affiliateResp

      const balanceResponse = await BalanceController.getPointsBalance('_', { ma_user_id: affiliate.ma_user_id, connectionRead, ma_status: 'ACTUAL', isMobile: true })

      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalance', type: 'balanceResponse', fields: balanceResponse })
      if (balanceResponse.status != 200) throw new ApplicationError(balanceResponse.respcode, balanceResponse.message)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { amount: parseFloat(balanceResponse.amount).toFixed(2) } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getWalletBalance', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit:number,offset:number,status:['Y','S'],merchant_id?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async getMerchantList (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      if (fields.status && !['Y', 'N', 'L', 'O', 'D'].includes(fields.status)) throw new ValidationError(2001, 'Invalid Status !')

      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'getSession', type: 'affiliateResponse', fields: affiliateResponse })
      if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      const merchantListResponse = await AffiliateController.getAffiliateMerchantList({
        affiliate_id: fields.affiliate_id,
        limit: fields.limit || 10,
        offset: fields.offset || 0,
        search_text: fields.search_text || '',
        search_type: fields.search_type || '',
        status: fields.status || 'Y',
        merchant_id: fields.merchant_id || 0,
        connectionRead
      })

      if (merchantListResponse.status != 200) throw new ApplicationError(merchantListResponse.respcode, merchantListResponse.message)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { merchants: merchantListResponse.affiliateMerchant } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id?:number,mode?:['dr','cr'],limit?:number,offset?:number,transaction_type?:number,search_text?:string,search_type?:string,fromdate?:string,todate?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async ledgerHistory (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'ledgerHistory', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* Params Validation  */
      if (_.has(fields, 'mode') && !['cr', 'dr'].includes(fields.mode)) throw new ValidationError(2001, 'Invalid Mode !')

      if (_.has(fields, 'fromdate') && !fields.todate) throw new ValidationError(2001, 'todate Missing !')

      if (_.has(fields, 'todate') && !fields.todate) throw new ValidationError(2001, 'fromDate Missing !')

      if (_.has(fields, 'fromdate') && _.has(fields, 'todate')) {
        const fromDate = moment(fields.fromdate, 'YYYY-MM-DD', true)
        const toDate = moment(fields.todate, 'YYYY-MM-DD', true)

        if (!fromDate.isValid() || !toDate.isValid()) throw new ValidationError(2001, 'Invalid Date Format !')

        if (fromDate > toDate) throw new ValidationError(2001, 'Invalid Date Format !')
      }

      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerHistory', type: 'affiliateResponse', fields: affiliateResponse })
      if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      const { affiliate } = affiliateResponse

      const pointsLedgerResponse = await LedgerHistoryController.getAffiliatePointsLedgerHistory({
        affiliate_id: affiliate.ma_user_id,
        limit: fields.limit || 10,
        offset: fields.offset || 0,
        search_text: fields.search_text || '',
        search_type: fields.search_type || '',
        mode: fields.mode || '',
        merchant_id: fields.merchant_id || 0,
        fromdate: fields.fromdate || '',
        todate: fields.todate || '',
        transaction_type: fields.transaction_type || '',
        connectionRead
      })
      if (pointsLedgerResponse.status != 200) throw new ApplicationError(pointsLedgerResponse.respcode, pointsLedgerResponse.message)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { legders: pointsLedgerResponse.pointsLedgerResult } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerHistory', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id?:number,transaction_status?:['I','P','PS','S','F'],limit?:number,offset?:number,transaction_type?:number,search_params?:string,fromdate?:string,todate?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async transactionHistory (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transactionHistory', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()
    try {
      /* Params Validation  */
      if (fields.transaction_status && !['I', 'P', 'PS', 'S', 'F', 'R', 'REV'].includes(fields.transaction_status)) {
        throw new ValidationError(2001, 'Invalid Transaction Status !')
      }

      if (_.has(fields, 'fromdate') && !fields.todate) throw new ValidationError(2001, 'todate Missing !')

      if (_.has(fields, 'todate') && !fields.todate) throw new ValidationError(2001, 'fromDate Missing !')

      if (_.has(fields, 'fromdate') && _.has(fields, 'todate')) {
        const fromDate = moment(fields.fromdate, 'YYYY-MM-DD', true)
        const toDate = moment(fields.todate, 'YYYY-MM-DD', true)

        if (!fromDate.isValid() || !toDate.isValid()) throw new ValidationError(2001, 'Invalid Date Format !')

        if (fromDate > toDate) throw new ValidationError(2001, 'fromdate is greater than the todate')
      }

      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionHistory', type: 'affiliateResponse', fields: affiliateResponse })
      if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      const { affiliate } = affiliateResponse

      const transactionListResponse = await TransactionController.getAffiliateTransactionList({
        affiliate_id: affiliate.ma_user_id,
        limit: fields.limit || 10,
        offset: fields.offset || 0,
        search_params: fields.search_params || '',
        search_text: fields.search_text || '',
        search_type: fields.search_type || '',
        merchant_id: fields.merchant_id || 0,
        fromdate: fields.fromdate || '',
        todate: fields.todate || '',
        transaction_type: fields.transaction_type || '',
        transaction_status: fields.transaction_status || '',
        connectionRead
      })

      if (transactionListResponse.status != 200) throw new ApplicationError(transactionListResponse.respcode, transactionListResponse.message)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { transactions: transactionListResponse.transactionList } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionHistory', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,merchant_id?:number,aggregator_order_id:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string}>
   */
  static async transactionStatus (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transactionStatus', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* affiliate validation */
      const affiliateResponse = await AffiliateController.getAffiliate({ affiliate_id: fields.affiliate_id, connectionRead })
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionStatus', type: 'affiliateResponse', fields: affiliateResponse })
      if (affiliateResponse.status != 200) throw new ApplicationError(affiliateResponse.respcode, affiliateResponse.message)

      if (affiliateResponse.respcode == 1002) throw new ValidationError(401, 'Invalid User !')

      const { affiliate } = affiliateResponse

      const transactionStatusResponse = await TransactionController.getAffiliateTransactionDetail({
        affiliate_id: affiliate.ma_user_id,
        merchant_id: fields.merchant_id || '',
        aggregator_order_id: fields.order_id,
        connectionRead
      })

      if (transactionStatusResponse.status != 200) throw new ApplicationError(transactionStatusResponse.respcode, transactionStatusResponse.message)

      if (transactionStatusResponse.respcode == 1002) throw new ValidationError(2001, 'Invalid Transaction ID !')

      if (transactionStatusResponse.status != 200) {
        return transactionStatusResponse
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { transaction: transactionStatusResponse.transactionDetailResult[0] } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionStatus', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number}} fields
   * @returns Promise<{status:number,respcode:number,message:string,data:any}>
   */
  static async getChannelList (fields, connectionRead = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getChannelList', type: 'request', fields: fields })
    const isSet = (connectionRead === null || connectionRead === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const channelListResp = await AffiliateController.getChannelList({ affiliate_id: fields.affiliate_id, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'getChannelList', type: 'affiliateResponse', fields: channelListResp })
      if (channelListResp.status != 200) throw new ApplicationError(channelListResp.respcode, channelListResp.message)

      const channels = channelListResp.channels.map(channel => ({ channelName: channel, channelCode: TransactionController.getAffiliateChannelTransactionType(channel) }))

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: { channels } }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getChannelList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      if (isSet) connRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit?:number,offset?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string,data:any}>
   */
  static async bankList (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* Set Default Limit & Offset Value */
      if (!_.has(fields, 'limit')) {
        fields.limit = 10
      }
      if (!_.has(fields, 'offset')) {
        fields.offset = 0
      }
      if (_.has(fields, 'search_text')) {
        fields.bank_name = fields.search_text
      }

      const bankListResponse = await BankMasterController.getBanks('_', fields, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'bankListResponse', type: 'listBeneficiaries', fields: bankListResponse })

      if (bankListResponse.status != 200) throw new ApplicationError(bankListResponse.respcode, bankListResponse.message)

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        data: {
          banks: bankListResponse.bankList
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,limit?:number,offset?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string,data:any}>
   */
  static async stateList (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* Set Default Limit & Offset Value */
      if (!_.has(fields, 'limit')) {
        fields.limit = 10
      }
      if (!_.has(fields, 'offset')) {
        fields.offset = 0
      }

      let stateListQuery = "SELECT id,region_id,zone_id,name FROM ma_states_master WHERE record_status =  'active'"

      if (fields.search_text) {
        stateListQuery += ` AND name like '%${fields.search_text}%'`
      }
      stateListQuery += `LIMIT ${fields.offset},${fields.limit}`

      const stateListResult = await this.rawQuery(stateListQuery, connectionRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'stateList', type: 'stateList', fields: stateListResult })

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        data: {
          states: stateListResult
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{affiliate_id:number,state_id?:number,limit?:number,offset?:number,search_text?:string,search_type?:string}} fields
   * @returns Promise<{status:number,respcode:number,message:string,data:any}>
   */
  static async cityList (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      /* Set Default Limit & Offset Value */
      if (!_.has(fields, 'limit')) {
        fields.limit = 10
      }
      if (!_.has(fields, 'offset')) {
        fields.offset = 0
      }

      let cityListQuery = "SELECT mcm.id as city_id,mcm.state_id,msm.region_id,msm.zone_id,msm.name as state_name,mcm.name as city_name  FROM ma_cities_master mcm LEFT JOIN ma_states_master msm ON mcm.state_id = msm.id WHERE msm.record_status =  'active'"

      if (fields.search_text) {
        cityListQuery += ` AND mcm.name like '%${fields.search_text}%'`
      }

      if (fields.state_id) {
        cityListQuery += ` AND mcm.state_id = ${fields.state_id}`
      }

      cityListQuery += ` LIMIT ${fields.offset},${fields.limit}`

      const cityListResult = await this.rawQuery(cityListQuery, connectionRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'cityList', type: 'cityListResult', fields: cityListResult })

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        data: {
          cities: cityListResult
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankList', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }
}

module.exports = PrimaryServicesController
