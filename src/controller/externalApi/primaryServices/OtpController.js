const mySQLWrapper = require('../../../lib/mysqlWrapper')
const log = require('../../../util/log')
const errorMsg = require('../../../util/error')
const DAO = require('../../../lib/dao')
const ApplicationError = require('../../../errors/ApplicationError')
const RemitterController = require('../channels/DMT/remitterController')
const BeneficiaryController = require('../channels/DMT/beneficiaryController')
const RefundController = require('../channels/DMT/refundController')

class OtpController extends DAO {
  /**
   * Affiliate Generate Token API
   * @param {{ma_user_id:number,userid:number,order_id:string,otp_type:string}} fields
   * @returns
   */
  static async resendOTP (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resendOTP', type: 'request', fields: fields })
    try {
      /* RESEND OTP */
      switch (fields.otp_type) {
        case 'CRO':
          return await RemitterController.remitterRegistrationResendOTP(fields)
        case 'BAO':
          return await BeneficiaryController.addBeneficiaryResendOTP(fields)
        case 'BDO':
          return await BeneficiaryController.deleteBeneficiaryResendOTP(fields)
        case 'RTO':
          return await RefundController.refundResendOTP(fields)
        default:
          throw new ApplicationError(2001, 'Invalid OTP Type')
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOTP', type: 'error', fields: error })
      const errorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ApplicationError) {
        errorResponse.respcode = error.code
        errorResponse.message = error.message
      }

      return errorResponse
    }
  }
}

module.exports = OtpController
