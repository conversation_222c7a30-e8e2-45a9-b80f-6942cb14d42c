const DAO = require('../../lib/dao')
const errorMsg = require('../../util/error')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const BillPay = require('../billpay/billpay')
const common = require('../../util/common')
const util = require('../../util/util')
const validator = require('../../util/validator')
const billpayGatewayMasterCtrl = require('../../controller/billpayGatewayMaster/billpayGatewayMasterController')
// const { lokiLogger } = require('../../util/lokiLogger')

class BillpayProvider extends DAO {
  /**
       * Overrides TABLE_NAME with this class' backing table at MySQL
       */
  static get TABLE_NAME () {
    return 'ma_billpay_provider_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_billpay_provider_id'
  }

  static async getRechargeOperators (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `
    SELECT pm.*
    FROM ma_billpay_provider_master as pm
    JOIN ma_billpay_gateway_master as gm on gm.ma_billpay_gateway_id = pm.ma_billpay_gateway_id AND  gm.ma_billpay_appid = pm.ma_billpay_appid
    JOIN ma_billpay_app_master as am on am.ma_billpay_appid = gm.ma_billpay_appid
    WHERE pm.provider_status = 'Y' AND gm.gateway_status = 'Y' AND am.app_status = 'Y'
    `
      const provider_list = await this.rawQuery(sql, connection)
      if (provider_list.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], provider_list }
      }
      return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (error) {
      console.log('getRechargeOperatorsError', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'getRechargeOperators', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getRechargeOperators', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getProviderDetails (fields, connection) {
    const sql = `
    SELECT pm.*,gm.*,am.*
    FROM ma_billpay_provider_master as pm
    JOIN ma_billpay_gateway_master as gm on gm.ma_billpay_gateway_id = pm.ma_billpay_gateway_id AND  gm.ma_billpay_appid = pm.ma_billpay_appid
    JOIN ma_billpay_app_master as am on am.ma_billpay_appid = gm.ma_billpay_appid
    WHERE pm.provider_status = 'Y' AND gm.gateway_status = 'Y' AND am.app_status = 'Y'
    AND pm.ma_billpay_provider_id = ${fields.ma_billpay_provider_id} limit 1
    `
    const gatewayDetails = await this.rawQuery(sql, connection)

    return gatewayDetails
  }

  static async checkBalanceForRecharge (_, fields) {
    let connection = null
    let isSet = false
    try {
      if (!('ma_billpay_provider_id' in fields)) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid provider id provided ' }
      }

      const isValidNumber = validator.validInput('numberonly', fields.ma_billpay_provider_id.toString())
      console.log('isValidNumber', isValidNumber)
      if (!isValidNumber) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid provider id provided ' }
      }

      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
      }

      const gatewayDetails = await this.getProviderDetails(fields, connection)
      if (gatewayDetails.length > 0) {
        const currentGateway = gatewayDetails[0]

        if (currentGateway.gateway_type == 'Settlement') {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        }

        const shortname = currentGateway.shortname

        const checkBalanceObj = new BillPay(shortname, connection)

        const apiPayload = {
          api_token: currentGateway.api_token,
          gateway_user_id: currentGateway.gateway_user_id
        }
        const resultBalanceResponse = await checkBalanceObj.requestToProvider('GET_BALANCE', apiPayload)
        console.log('resultBalanceResponse', resultBalanceResponse)

        if (resultBalanceResponse.status === 200 && resultBalanceResponse.apiStatus === 'S') {
          const current_balance = resultBalanceResponse.apiData.balance

          const threshold_limit = await common.getSystemCodes(this, util.billpay.threshold_limit, connection)
          console.log('current_balance', current_balance)
          console.log('threshold_limit', threshold_limit)

          if (('amount' in fields) && current_balance < fields.amount) {
            return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
          }

          if (current_balance <= threshold_limit) {
            const notifyData = {
              app_name: currentGateway.app_name,
              threshold_limit: threshold_limit,
              gateway_user_id: currentGateway.gateway_user_id,
              current_balance: current_balance
            }

            await billpayGatewayMasterCtrl.notifyBySms(notifyData, connection)
            await billpayGatewayMasterCtrl.notifyByEmail(notifyData, connection)

            if (('amount' in fields) && current_balance > fields.amount) {
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
            }

            return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
          } else {
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
          }
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
      return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (error) {
      console.log('getRechargeOperatorsError', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBalanceForRecharge', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'checkBalanceForRecharge', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) {
        connection.release()
      }
    }
  }
}

module.exports = BillpayProvider
