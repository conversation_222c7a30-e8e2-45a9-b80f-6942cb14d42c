const bbpsconfig = {
  BBPS_MA_PAYMENTS_URL: 'https://payments.airpay.ninja/',
  BBPS_REDIS_KEY: 'bbps:providers',
  BBPS_MS_PAYMENTS_URL: 'https://partnerpay.airpay.ninja/',
  BBPS_MA_CALLBACK_URL: 'https://hqvt6tbkl5.execute-api.ap-south-1.amazonaws.com/stag/bbpsnewbill',
  BBPS_MA_RETURN_URL: 'https://hqvt6tbkl5.execute-api.ap-south-1.amazonaws.com/stag/bbpsregisterstatus',
  BBPS_NEW_BILL_SMS: 'New Bill Alert! A new <Provider> bill of <Amount> has been generated. To pay your bills, visit the nearest vyaapaar agent! Team airpay vyaapaar.',
  BBPS_NEW_BILL_SMS_TEMPLATEID: '1107164930931941248',
  BBPS_BILLPAYSUCCESS_SMS: 'Dear Customer, <Provider name> transaction of <amount> on <date time> is successful. B-Connect txn ID: <bConnectId> via Team airpay vyaapaar.',
  BBPS_BILLPAYSUCCESS_SMS_TEMPLATEID: '1107174099462871163',
  BBPS_BILLPAYFAILURE_SMS: '<Provider name> transaction of Rs <amount> on <date time> has failed. Ref: <orderid>.<signature>',
  BBPS_BILLPAYFAILURE_SMS_TEMPLATEID: '1107160230424344007',
  BBPS_MERCHANT_DETAILS_MERCID: 19378,
  BBPS_MERCHANT_DETAILS_SECRET: '5q9M2W1uKe67B3Ab',
  BBPS_MERCHANT_DETAILS_PASSWORD: 'A3IEpPKn',
  BBPS_MERCHANT_DETAILS_USERNAME: 5610027,
  BBPS_MA_PAY_INDEX_VERSION: 3,
  LOGO_URL_BBPS_ASSURED: "https://retailappdocs.s3.ap-south-1.amazonaws.com/BBPS/B+Assured+Logo_PNG.png"
}

module.exports = { ...bbpsconfig }
