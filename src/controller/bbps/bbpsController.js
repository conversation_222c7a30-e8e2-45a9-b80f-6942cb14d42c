const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common = require('../../util/common')
const parser = require('fast-xml-parser')
const sms = require('../../util/sms')
const billPayTransactionCtrl = require('../billpayTransaction/billpayTransaction')
const integrated = require('../integrated/integratedController')
const validator = require('../../util/validator')
const { basename } = require('path')
const MsBBPSIntegration = require('./integration/MsBBPSIntegration')
const MaBBPSIntegration = require('./integration/MaBBPSIntegration')
const bbpsIncentiveCtrl = require('../incentive/bbpsIncentiveController')
const makePaymentRequestCtrl = require('../transaction/makepayment_request')
const securePinCtrl = require('../securityPin/securityPinController')
const balanceController = require('../balance/balanceController')
const transactionController = require('../transaction/transactionController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const bbpsinecntive = require('../incentive/bbpsIncentiveController')
const bbpsConfig = require('./bbpsconfig')
const { md5Compare } = require('../../util/checksum')
const moment = require('moment')
/* SOUNDBOX CHANGES */
const SoundBoxManagementController = require('../soundBoxManagement/soundBoxManagementController')
// const { lokiLogger } = require('../../util/lokiLogger')

class BBPSController extends DAO {
  /**
   * Fetches Utility Details from Database
   * <AUTHOR> Andrew
   * @returns {Promise <{ key: number, value: string, web_icon: string, app_icon: string }>}
   */
  static async getUlilitiesFromDB() {
    log.debug({ pagename: basename(__filename), action: 'getUlilitiesFromDB', type: 'request' })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getUlilitiesFromDB', type: 'request' })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const utilityDetailsQuery = 'SELECT DISTINCT(provider_name) as value, CAST(provider_key AS CHAR) as `key`, web_icon, app_icon FROM ma_billpayment_utility_providers WHERE active_status = "Y"'
      const utilityDetails = await this.rawQuery(utilityDetailsQuery, connection)
      return utilityDetails
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getUlilitiesFromDB', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'getUlilitiesFromDB', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * @api Gets BBPS utilities list
   * <AUTHOR> Andrew
   * @param {*} _
   * @param {{ ma_user_id: number, userid: string }} fields
   * @returns {Promise <{ status: number, respcode: number, message: string, utility_data: { key: string, value: string }[] }>}
   */
  static async getBillPaymentUtility(_, fields) {
    log.debug({ pagename: basename(__filename), action: 'getBillPaymentUtility', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentUtility', type: 'request', fields: fields })

    // Field Validation
    const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validationResponse.status !== 200) return validationResponse

    try {
      // Fetching Utitilities
      const result = await this.getUlilitiesFromDB()
      log.debug({ pagename: basename(__filename), action: 'getBillPaymentUtility', type: 'result from db', fields: result })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentUtility', type: 'result from db', fields: result })

      return { status: 200, respcode: 1000, message: 'Success', utility_data: result }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getBillPaymentUtility', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'getBillPaymentUtility', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  /**
   * @api Get bill providers list
   * <AUTHOR> Andrew
   * @param {*|null} _
   * @param {{ ma_user_id: number, userid: number, utility_id: number, limit: number, offset: number, provider_name?: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, providers_data: string, providers_data_json: {
   *    BILLER_MASTER_ID: string,
   *    provider_name: string,
   *    provider_id: string,
   *    fields_validation: string,
   *    biller_logo: string,
   *    biller_bill_copy: string,
   *    check_amount_req: string,
   *    raw_fields: { name: string, validation: string, type: string, postKey: string }[]
   *    utility_id: string,
   *    partial_pay: string,
   *    payment_methods: {
   *      payment_method: string,
   *      min_limit: string,
   *      autopay_allowed: string,
   *      paylater_allowed: string
   *    }[],
   *    action_type: string}[],
   *    providers_count: number,
   *    nextFlag: boolean
   * }>}
   */
  static async getBillPaymentProviders(_, fields) {
    log.debug({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'request', fields })

    // Field Validation
    const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validationResponse.status !== 200) return validationResponse

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const providers_Data = []
      let providersQuery = ''
      let providersQueryCount = ''
      let nextFlag = false
      // const providersQuery = `SELECT providers_data, providers_data_json FROM ma_billpay_providers WHERE utility_id = ${fields.utility_id} limit 1`
      providersQuery = `SELECT DISTINCT provider_name, providers_data from ma_bbps_providers_validations where utility_id = ${fields.utility_id} and is_visible = 'Y' and providers_data is not null`

      if (fields.provider_name) {
        providersQuery += ` AND provider_name like '%${fields.provider_name}%'`
      }
      providersQuery += ' Order by provider_name ASC'
      if (fields.limit && (fields.offset !== null && fields.offset !== undefined)) {
        providersQuery += ` LIMIT ${fields.limit} OFFSET ${fields.offset}`
      }
      const providerData = await this.rawQuery(providersQuery, connection)

      providersQueryCount = `SELECT COUNT(DISTINCT provider_name, providers_data) as tot_count FROM ma_bbps_providers_validations where utility_id = ${fields.utility_id} and is_visible = 'Y' and providers_data is not null`
      if (fields.provider_name) {
        providersQueryCount += ` AND provider_name like '%${fields.provider_name}%'`
      }
      const providerDataCount = await this.rawQuery(providersQueryCount, connection)
      const total = providerDataCount[0].tot_count
      if (fields.limit + fields.offset < total) nextFlag = true

      log.debug({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'providerData Query', fields: { providersQuery, providersQueryCount } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'providerData Query', fields: { providersQuery, providersQueryCount } })
      log.debug({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'providerData Response', fields: { providerData, providerDataCount } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'providerData Response', fields: { providerData, providerDataCount } })

      if (providerData.length == 0) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002], providers_data_json: [], providers_count: 0, nextFlag }
      providerData.forEach(ele => {
        if (ele.providers_data && this.parseJson(ele.providers_data)) { providers_Data.push(JSON.parse(ele.providers_data)) }
      })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], providers_data_json: providers_Data, providers_count: providerDataCount[0].tot_count, nextFlag }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /**
   * Gets Merchant details from db
   * <AUTHOR> Andrew
   * @param {{ ma_user_id: number }} fields
   * @param {MySQL.Connection} connection
   * @returns {Promise <{ email_id?: string, mobile_id?: string, firstname?: string, lastname?: string, address?: string, city: string, state: string, pincode: string, country: string, secret: string, username: string, password: string, userid: string }>}
   */
  static async getBuyersData(fields, connection) {
    const merchantSql = `SELECT
        a.email_id,
        a.mobile_id,
        a.firstname,
        a.lastname,
        a.address,
        mcm.name as city,
        msm.name as state,
        a.pincode,
        a.country,
        a.apikey as secret,
        a.username,
        a.password,
        a.userid -- not required
      FROM
        ma_user_master AS a 
      JOIN
        ma_states_master as msm
      ON
        msm.id = a.state
      JOIN
        ma_cities_master as mcm on mcm.id = a.city
      WHERE
        a.user_status = 'Y' AND a.profileid = '${fields.ma_user_id}'
      LIMIT 1`
    log.debug({ pagename: basename(__filename), action: 'getBuyersData', type: 'merchantSql', fields: merchantSql })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getBuyersData', type: 'merchantSql', fields: merchantSql })
    const merchantResponse = await this.rawQuery(merchantSql, connection)

    log.debug({ pagename: basename(__filename), action: 'getBuyersData', type: 'merchantResponse', fields: merchantResponse })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getBuyersData', type: 'merchantResponse', fields: merchantResponse })
    return merchantResponse[0]
  }

  /**
   * Validates Dynamic FormData from frontend
   * <AUTHOR> Andrew
   * @param {{ form_data: string, utility_id: number, provider_id: number }} fields
   * @param {string} fields.form_data STRINGIFIED JSON
   * @param {MySQL.Connection} connection
   * @returns {Promise <{ status: number, message: string, respcode: number, form_data?: { [key: string]: string|number }, amount?: number|string, account_id?: string }>}
   */
  static async validateDynamicFields(fields, connection) {
    const user_form_data = JSON.parse(Buffer.from(fields.form_data, 'base64').toString('utf-8'))
    const form_data = {}

    // Fetching Validations from db
    const validationDetailsQuery = `SELECT
        validations,
        account_id
      FROM
        ma_bbps_providers_validations
      WHERE
        utility_id = ${fields.utility_id} and
        provider_id = ${fields.provider_id} and
        biller_master_id = ${fields.biller_master_id} and
        is_visible = 'Y'
      LIMIT 1`
    const validationDetails = await this.rawQuery(validationDetailsQuery, connection)
    log.debug({ pagename: basename(__filename), action: 'validateDynamicFields', type: 'validationDetails', fields: validationDetails })
    // lokiLogger.info({ pagename: basename(__filename), action: 'validateDynamicFields', type: 'validationDetails', fields: validationDetails })

    let amount = 0
    // If Valiations not available then it's a success
    if (validationDetails.length <= 0) {
      for (const field in user_form_data) {
        if (/amount/ig.test(field)) {
          amount = user_form_data[field]
          if (amount <= 0 && (fields.action == 'recharge' || fields.action == 'adhoc') && isNaN(amount)) return { status: 400, respcode: 1136, message: errorMsg.responseCode[1136] }
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], form_data: user_form_data, amount: +amount }
    }

    // IF validations are available, regex validation is done
    const validations = JSON.parse(validationDetails[0].validations)
    for (const validation in validations) {
      const validationDetail = validations[validation]
      if (/,\$$/g.test(validationDetail.validationWithComma) && (new RegExp(validationDetail.validationWithComma)).test('' + user_form_data[validation] + ',')) user_form_data[validation] = '' + user_form_data[validation] + ','
      if (!validator.validateField(user_form_data[validation]) || !(new RegExp(validationDetail.validationWithComma)).test(user_form_data[validation])) return { status: 400, respcode: 14004, message: errorMsg.responseCode[14004] + '' + validationDetail.name }
      if (/amount/ig.test(validation)) {
        amount = user_form_data[validation]
        if (amount <= 0 && (fields.action == 'recharge' || fields.action == 'adhoc') && isNaN(amount)) return { status: 400, respcode: 1136, message: errorMsg.responseCode[1136] }
      }
      form_data[validationDetail.name] = user_form_data[validation]
    }

    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], form_data, amount: +amount, account_id: user_form_data[validationDetails[0].account_id], details_key: validations[validationDetails[0].account_id].name }
  }

  /**
   * @api Fetchs bills from ma
   * <AUTHOR> Andrew
   * @param {*|null} _
   * @param {{ ma_user_id: number, userid: number, action_type: 'recharge' | 'instapay' | 'register' | 'adhoc', aggregator_order_id: string, provider_id: number, BILLER_MASTER_ID: number, utility_id: number, form_data: string, mobile_number: string }} fields
   * @returns {Promise <{
   *  status: number,
   *  message: string,
   *  respcode: number,
   *  surcharge: string,
   *  amount: string,
   *  makepayment_request_id: number,
   *  aggregator_order_id: string,
   *  bill_response: string,
   *  bill_response_json: {
   *    objectid: string,
   *    billid: string,
   *    billstatus: string,
   *    billnumber: string,
   *    billperiod: string,
   *    net_billamount: string,
   *    customer_name: string,
   *    billduedate: string,
   *    billamount: string,
   *    billdate: string
   * }}>}
   */
  static async initMakePayment(_, fields) {
    log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Validate mandatory fields
      const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'action_type', 'aggregator_order_id', 'provider_id', 'utility_id', 'form_data', 'mobile_number', 'BILLER_MASTER_ID'])
      if (validationResponse.status !== 200) return validationResponse

      const transaction_type = 6

      // Check utility table for utility id
      const utility_query = "Select ma_billpayment_utility_provider_id, provider_name, CAST(provider_key AS CHAR) as provider_key from ma_billpayment_utility_providers mbup where provider_name = 'Mutual Fund' and active_status = 'Y' limit 1"
      const utility_query_resp = await this.rawQuery(utility_query, connection)
      log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'utility_data', fields: { utility_query, utility_query_resp } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'utility_data', fields: { utility_query, utility_query_resp } })

      // need to change in live environment
      if (utility_query_resp.length > 0 && fields.utility_id == utility_query_resp[0].provider_key) {
        fields.action_type = 'register'
      }
      const { ma_user_id, userid, aggregator_order_id, action_type } = fields

      // Validate Enum
      const actionTypevalidationResponse = validator.validateEnum(action_type, ['instapay', 'register', 'recharge', 'adhoc'], true)
      if (actionTypevalidationResponse.status !== 200) return actionTypevalidationResponse

      // Check action type
      /*
      const sql_get_action_type = `Select action_type from ma_bbps_providers_validations where utility_id = ${fields.utility_id} and biller_master_id = ${fields.BILLER_MASTER_ID}`
      const sql_action_resp = await this.rawQuery(sql_get_action_type, connection)
      log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'sql_action_resp', fields: sql_action_resp })

      if (sql_action_resp.length > 0) {
        fields.action_type = sql_action_resp[0].action_type
      }
      */

      // Fetch Merchant details for API Call And Validate Dynamic Fields
      const [merchantDetails, validateDynamicFieldsResponse] = await Promise.all([this.getBuyersData({ ma_user_id }, connection), this.validateDynamicFields({ form_data: fields.form_data, utility_id: fields.utility_id, provider_id: fields.provider_id, biller_master_id: fields.BILLER_MASTER_ID }, connection)])
      if (validateDynamicFieldsResponse.status !== 200) return validateDynamicFieldsResponse

      let bill_details_resp = []; let insert_bill_resp = {}; let bill_paid_details_resp = []
      if (fields.action_type == 'register') {
        const check_bill_details = `select ma_bbps_register_bill_details_id, utility_id, biller_master_id, provider_id, account_id, amount, bill_due_date, paid_on, status from ma_bbps_register_bill_details 
        where utility_id = ${fields.utility_id} and biller_master_id = ${fields.BILLER_MASTER_ID} and account_id = '${validateDynamicFieldsResponse.account_id}' and bill_due_date >= NOW() and status = 'UNPAID' order by ma_bbps_register_bill_details_id desc limit 1`
        bill_details_resp = await this.rawQuery(check_bill_details, connection)
        log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'check register_bill_details', fields: { check_bill_details, bill_details_resp } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'check register_bill_details', fields: { check_bill_details, bill_details_resp } })

        if (bill_details_resp.length <= 0) {
          const sql_insert_bill_details = `insert into ma_bbps_register_bill_details (utility_id, biller_master_id, provider_id, account_id, bill_due_date) values (${fields.utility_id}, ${fields.BILLER_MASTER_ID}, ${fields.provider_id}, '${validateDynamicFieldsResponse.account_id}', DATE_ADD(NOW(), INTERVAL 1 YEAR))`
          insert_bill_resp = await this.rawQuery(sql_insert_bill_details, connection)
          log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'register_bill_details', fields: { sql_insert_bill_details, insert_bill_resp } })
          // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'register_bill_details', fields: { sql_insert_bill_details, insert_bill_resp } })
        }

        const bill_paid_details = `select ma_bbps_register_bill_details_id, utility_id, biller_master_id, provider_id, account_id, amount, bill_due_date, paid_on, DATE_FORMAT(paid_on, '%b %d,%Y') as paid_on_date, status from ma_bbps_register_bill_details 
        where utility_id = ${fields.utility_id} and biller_master_id = ${fields.BILLER_MASTER_ID} and account_id = '${validateDynamicFieldsResponse.account_id}' and status = 'PAID' order by ma_bbps_register_bill_details_id desc limit 1`
        bill_paid_details_resp = await this.rawQuery(bill_paid_details, connection)
        log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'check register_bill_details', fields: { bill_paid_details, bill_paid_details_resp } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'check register_bill_details', fields: { bill_paid_details, bill_paid_details_resp } })

        if (bill_paid_details_resp.length > 0 && ((bill_details_resp.length > 0 && bill_details_resp[0].amount == null) || bill_details_resp.length == 0)) {
          return { status: 201, message: `Bill amount of Rs. ${bill_paid_details_resp[0].amount} was paid successfully on ${bill_paid_details_resp[0].paid_on_date}`, respcode: 1000, doPayment: false }
        } else if ((bill_details_resp.length > 0 && bill_details_resp[0].amount == null)) {
          return { status: 201, message: 'Biller registration already in process', respcode: 1000, doPayment: false }
        }
      }

      // API Call
      // account_id parameter added - deepak
      let addBillerResponse = {}
      const bill_data = {
        account_id: validateDynamicFieldsResponse.account_id,
        mobile_number: fields.mobile_number,
        fname: merchantDetails.firstname,
        lname: merchantDetails.lastname,
        email: merchantDetails.email_id,
        details: validateDynamicFieldsResponse.form_data,
        billerid: fields.BILLER_MASTER_ID,
        requestid: aggregator_order_id,
        remark: `BBPS ${action_type} Type Transactions ${aggregator_order_id}`
      }
      if (fields.action_type != 'register' || (bill_details_resp.length <= 0 && bill_paid_details_resp.length <= 0)) {
        addBillerResponse = await MaBBPSIntegration.addBiller({
          ma_user_id,
          userid,
          action: action_type,
          bill_data: bill_data,
          secret: merchantDetails.secret,
          username: merchantDetails.username,
          password: merchantDetails.password,
          utility_id: fields.utility_id,
          provider_id: fields.BILLER_MASTER_ID
        })
      }
      log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'addBillerResponse', fields: addBillerResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'addBillerResponse', fields: addBillerResponse })
      const bill_details = {}
      bill_details[validateDynamicFieldsResponse.details_key] = validateDynamicFieldsResponse.account_id

      // Registeration Biller process
      if ((action_type == 'instapay' || action_type == 'register') && validator.validateField(validateDynamicFieldsResponse.account_id)) {
        const registrationQuery = `SELECT ma_billpay_registered_merchant_id, account_id, orderid, registration_status FROM ma_billpay_registered_merchant WHERE provider_id = ${fields.provider_id} AND utility_id = ${fields.utility_id} AND account_id = '${validateDynamicFieldsResponse.account_id}' and module = 'BBPS' LIMIT 1`
        const registrationData = await this.rawQuery(registrationQuery, connection)
        // If not registered already then insert
        if (registrationData.length <= 0) await this.insertBulkWithTableName(connection, 'ma_billpay_registered_merchant', '(ma_user_id, utility_id, provider_id, module, mobile_number, account_id, orderid)', [[fields.ma_user_id, fields.utility_id, fields.provider_id, 'BBPS', fields.mobile_number, validateDynamicFieldsResponse.account_id, fields.aggregator_order_id]])
        // If data is present but not registered already then update the details
        else if (registrationData[0].registration_status == 'N') {
          const registrationUpdateQuery = `Update ma_billpay_registered_merchant set ma_user_id = ${ma_user_id}, mobile_number = '${fields.mobile_number}', orderid = '${aggregator_order_id}' WHERE ma_billpay_registered_merchant_id = ${registrationData[0].ma_billpay_registered_merchant_id} and module = 'BBPS'`
          await this.rawQuery(registrationUpdateQuery, connection)
        }
      }

      let surchargeResponse = {}
      if (fields.action_type != 'register' || bill_details_resp.length > 0) {
        // Insert Entries in makepayment request
        const insertRequest = await makePaymentRequestCtrl.insertData(connection, {
          data: {
            api_request: JSON.stringify({ ...((addBillerResponse || {}).request || { billdata: [bill_data] }), account_id: validateDynamicFieldsResponse.account_id, BILLER_MASTER_ID: fields.BILLER_MASTER_ID, bill_provider_id: fields.provider_id, bill_details: bill_details, bank_account_number: validateDynamicFieldsResponse.form_data['Bank Account Number'] || '', ifsc_number: validateDynamicFieldsResponse.form_data['IFSC Number'] || '', action_type: fields.action_type, utility_id: fields.utility_id, ma_bbps_register_bill_details_id: (bill_details_resp.length > 0 ? bill_details_resp[0].ma_bbps_register_bill_details_id : '') }),
            api_response: addBillerResponse.response || JSON.stringify({ noAPICall: 'action register' }),
            ma_user_id,
            userid,
            aggregator_order_id,
            invoice_id: 0,
            mer_dtls: JSON.stringify({
              mercid: bbpsConfig.BBPS_MERCHANT_DETAILS_MERCID,
              secret: bbpsConfig.BBPS_MERCHANT_DETAILS_SECRET,
              password: bbpsConfig.BBPS_MERCHANT_DETAILS_PASSWORD,
              username: bbpsConfig.BBPS_MERCHANT_DETAILS_USERNAME
            }),
            request_status: bill_details_resp.length > 0 ? 'S' : addBillerResponse.status != 200 ? 'F' : 'S',
            bill_amount: (addBillerResponse.bill_response || {}).billamount || validateDynamicFieldsResponse.amount || (bill_details_resp.length > 0 ? bill_details_resp[0].amount : 0) || 0,
            transaction_amount: 0,
            surcharge_amount: 0,
            transaction_type: '6'
          }
        })

        const makepayment_request_id = insertRequest.insertId

        if (addBillerResponse.status == 400) return addBillerResponse
        const amount = (addBillerResponse.bill_response || {}).billamount || validateDynamicFieldsResponse.amount || (bill_details_resp.length > 0 ? bill_details_resp[0].amount : 0) || 0
        if (amount <= 0) return { status: 400, message: errorMsg.responseCode[14003], respcode: 14003 }
        // Get Surcharge
        surchargeResponse = await bbpsIncentiveCtrl.calculateSurcharge(_, { ma_user_id, transaction_type, amount, userid })

        log.debug({ pagename: basename(__filename), action: 'initMakePayment', type: 'surchargeResponse', fields: surchargeResponse })
        // lokiLogger.info({ pagename: basename(__filename), action: 'initMakePayment', type: 'surchargeResponse', fields: surchargeResponse })
        if (surchargeResponse.status === 400) return surchargeResponse

        const surchargeS = surchargeResponse.surcharge || 0
        const amountS = surchargeResponse.amount || amount

        if (makepayment_request_id > 0) {
          await makePaymentRequestCtrl.updateWhereData(connection, {
            data: {
              bill_amount: amount,
              transaction_amount: amountS,
              surcharge_amount: surchargeS
            },
            id: makepayment_request_id,
            where: 'makepayment_request_id'
          })
        }

        const bill_response_json = addBillerResponse.bill_response || {}
        const modified_bill_response_json = {
          ...(bill_response_json.customer_name && { customer_name: bill_response_json.customer_name }),
          ...(bill_response_json.billid && { bill_id: bill_response_json.billid }),
          ...(bill_response_json.billperiod && { bill_period: bill_response_json.billperiod }),
          ...(bill_response_json.billnumber && { bill_number: bill_response_json.billnumber }),
          ...(bill_response_json.billstatus && { bill_status: bill_response_json.billstatus }),
          ...(bill_response_json.billdate && { bill_date: bill_response_json.billdate }),
          ...(bill_response_json.billduedate && { bill_due_date: bill_response_json.billduedate }),
          bill_amount: common.addRupeeSymbol(amount),
          ...(bill_response_json.early_billdiscount && { early_bill_discount: common.addRupeeSymbol(bill_response_json.early_billdiscount) }),
          ...(bill_response_json.early_billamount && { early_bill_amount: common.addRupeeSymbol(bill_response_json.early_billamount) }),
          ...(bill_response_json.early_billduedate && { early_bill_due_date: bill_response_json.early_billduedate }),
          ...(bill_response_json.late_payment_amount && { late_payment_amount: common.addRupeeSymbol(bill_response_json.late_payment_amount) }),
          ...(bill_response_json.late_payment_charges && { late_payment_charges: common.addRupeeSymbol(bill_response_json.late_payment_charges) }),
          consumer_convenience_fee: common.addRupeeSymbol((+surchargeS).toFixed(2)),
          total_amount: common.addRupeeSymbol((+amountS).toFixed(2))
        }

        return {
          status: 200,
          message: errorMsg.responseCode[1000],
          respcode: 1000,
          surcharge: surchargeS,
          amount: amountS,
          makepayment_request_id,
          aggregator_order_id: aggregator_order_id,
          bill_response: Buffer.from(JSON.stringify(modified_bill_response_json)).toString('base64'),
          bill_response_json: modified_bill_response_json,
          doPayment: true
        }
      }
      return {
        status: 201,
        message: `${errorMsg.responseCode[1000]} ! Biller registration is in process`,
        respcode: 1000,
        doPayment: false
      }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'initMakePayment', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'initMakePayment', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (connection) connection.release()
    }
  }

  /**
   * @api Processes transaction
   * <AUTHOR> Andrew
   * @param {*|null} _
   * @param {{ ma_user_id: number, userid: number, action_type: 'recharge' | 'adhoc' | 'register' | 'instapay', provider_id: number, utility_id: number, aggregator_order_id: string, security_pin: string, mobile_number: string }} fields
   * @returns {Promise<{ status: number, message: string, respcode: number}>}
   */
  static async doPointBankPayment(_, fields) {
    log.debug({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'request', fields })
   
    const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'action_type', 'provider_id', 'utility_id', 'aggregator_order_id', 'security_pin', 'mobile_number'])
    if (validationResponse.status != 200) return { ...validationResponse, action_code: 1001 }
    
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // if(fields.consent_flag != 'Y'){
      //   return { status: 400, respcode: 1004, message: "Please accept the consent", action_code: 1001 }
      // }
      const makepayrequestdata = await makePaymentRequestCtrl.getRequestDataWithOrderID(connection, fields.aggregator_order_id)
      if (makepayrequestdata.length <= 0) return { status: 400, respcode: 14005, message: errorMsg.responseCode[14005], action_code: 1001 }

      const makepaymentValidationResponse = validator.validateFields(makepayrequestdata[0], ['aggregator_order_id', 'api_request', 'transaction_type', 'request_status', 'api_response', 'mer_dtls', 'transaction_amount', 'aggregator_order_id', 'bill_amount'])
      if (makepaymentValidationResponse.status != 200) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
      if (makepayrequestdata[0].aggregator_order_id !== fields.aggregator_order_id) return { status: 400, respcode: 14006, message: errorMsg.responseCode[14006] + fields.aggregator_order_id, action_code: 1001 }

      const apiRquest = JSON.parse(makepayrequestdata[0].api_request)

      if (apiRquest.utility_id != fields.utility_id || apiRquest.action_type != fields.action_type || makepayrequestdata[0].transaction_type != 6 || makepayrequestdata[0].request_status != 'S') {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
      }

      // Merchant PIN verification
      const securePinData = await securePinCtrl.verifySecurePin(null, { ma_user_id: fields.ma_user_id, userid: fields.userid, security_pin: fields.security_pin, connection })
      log.debug({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'securePinData', fields: securePinData })
      // lokiLogger.info({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'securePinData', fields: securePinData })

      if (securePinData.status === 400) return { ...securePinData, action_code: 1001 }

      // Merchant Balance Check
      //const availableBalance = await balanceController.getWalletBalancesDirect(_, { ma_user_id: fields.ma_user_id, ma_status: 'ACTUAL', balance_flag: 'SUMMARY', connection })
      
      //if (availableBalance.amount < makepayrequestdata[0].transaction_amount) return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], action_code: 1001 }
      //write
      const sql = `select state from ma_user_master where profileid = ${fields.ma_user_id} limit 1;`
      const state = await this.rawQuery(sql, connection);
      
      const { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } = await this.calculatePaymodeCharges({ amount: makepayrequestdata[0].transaction_amount, type: 'bbps', ma_user_id: makepayrequestdata[0].ma_user_id, userid: makepayrequestdata[0].userid, orderid:makepayrequestdata[0].aggregator_order_id, state_master_id: state[0].state, connection: connection})
      
      const totalAmount = charge + txnAmount + (isGstInclusive ? 0 : gst);
      /* NEW CHANGES : LEIN BALANCE CHECK */
      const lienBalanceController = require('../lienBalance/lienBalanceController')
      /* NEW CHANGES : LEIN BALANCE CHECK */
      const isTransactionAmountAboveLienBalanceResp = await lienBalanceController.isTransactionAmountAboveLienBalance({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        transactionType: '6',
        transactionAmount: parseFloat(totalAmount),
        connectionRead: connection
      })
 
      log.logger({ pagename: 'transactionController.js', action: 'isTransactionAmountAboveLienBalance', type: 'response', fields: isTransactionAmountAboveLienBalanceResp })
      // lokiLogger.info({ pagename: 'transactionController.js', action: 'isTransactionAmountAboveLienBalance', type: 'response', fields: isTransactionAmountAboveLienBalanceResp })
      if (isTransactionAmountAboveLienBalanceResp.status != 200) return isTransactionAmountAboveLienBalanceResp
 
      /* LEIN BALANCE CHECK END */

      const availableBalance = await balanceController.getWalletBalancesDirect(_, { ma_user_id: fields.ma_user_id, ma_status: 'ACTUAL', balance_flag: 'SUMMARY', connection })
      
      if (availableBalance.amount < makepayrequestdata[0].transaction_amount) return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], action_code: 1001 }

      // Check if transaction already exists
      const txnDetailsQuery = `SELECT ma_transaction_master_id FROM ma_transaction_master WHERE aggregator_order_id='${fields.aggregator_order_id}' LIMIT 1`
      const txnDetails = await this.rawQuery(txnDetailsQuery, connection)
      if (txnDetails.length > 0) return { status: 400, respcode: 1133, message: errorMsg.responseCode[1133], action_code: 1001 }

      const providerDetailsQuery = `SELECT
        bup.provider_name as utility_name,
        bpv.provider_name
      FROM
        ma_bbps_providers_validations bpv
      INNER JOIN
        ma_billpayment_utility_providers bup
      ON
        CAST(bup.provider_key AS CHAR) = bpv.utility_id
      WHERE
        bpv.utility_id = '${fields.utility_id}' and
        bpv.provider_id = '${fields.provider_id}' and
        bup.active_status = 'Y' and
        bpv.biller_master_id = '${apiRquest.BILLER_MASTER_ID}' and
        bpv.is_visible = 'Y'
      LIMIT 1`

      const providerDetails = await this.rawQuery(providerDetailsQuery, connection)
      log.logger({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'providerDetails', fields: providerDetails })
      // lokiLogger.info({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'providerDetails', fields: providerDetails })

      // Create Transaction with pending status
      /* BBPS Changes - Added Consent_flag for each transaction */
      const transactionData = await transactionController.initiateTransaction(_, { connection, ma_user_id: fields.ma_user_id, userid: fields.userid, aggregator_order_id: fields.aggregator_order_id, amount: makepayrequestdata[0].bill_amount, transaction_type: 6, remarks: 'NEW BBPS TRANSACTION', mobile_number: fields.mobile_number, provider_id: fields.provider_id, provider_name: providerDetails[0].provider_name, utility_id: fields.utility_id, utility_name: providerDetails[0].utility_name, action_type: fields.action_type, transaction_status: 'I', makePaymentResponse: makepayrequestdata[0], consent_flag: fields.consent_flag })
      if (transactionData.status === 400) return { ...transactionData, action_code: 1001 }

      // Call, Pay Index API (MA API), Point deduction , Pay Direct Bill End Point
      /* RISK MANAGEMENT Changes */
      let pointBankResponse = await this.callPayIndex({ ...fields, ...makepayrequestdata[0], ma_transaction_master_id: transactionData.transaction_id, ma_billpay_transactionid: transactionData.ma_billpay_transactionid, aggregator_order_id: fields.aggregator_order_id }, connection)
      log.debug({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'pointBankResponse', fields: pointBankResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'pointBankResponse', fields: pointBankResponse })

      // Get bbps receipt
      const receiptDetails = await transactionController.getBbpsTransactionDetails(_, { aggregator_order_id: fields.aggregator_order_id, ma_user_id: fields.ma_user_id, userid: fields.userid })
      log.debug({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'receiptDetails', fields: receiptDetails })
      // lokiLogger.info({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'receiptDetails', fields: receiptDetails })

      if (receiptDetails.status === 200) {
        delete receiptDetails.status
        delete receiptDetails.message
        delete receiptDetails.respcode
        pointBankResponse = { ...pointBankResponse, ...receiptDetails }
      }

      pointBankResponse.status = 200
      pointBankResponse.action_code = 1000
      return pointBankResponse
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'doPointBankPayment', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }
  static async calculatePaymodeCharges(fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'request', fields: fields })
    try {
      let data = ''
      if (fields.type == 'recharge') {
        data = await this.getDistributionRecharge(fields)
      }
      else if (fields.type == 'bbps') {
        // data = await bbpsinecntive.incentiveDistribution(fields);
        data = await this.getDistribution(fields)
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'collectMoney getDistribution data', fields: data })

      let charge = 0
      let bank_charge = 0
      let gst = 0
      let afterDeduct = 0
      let finalData = {}
      let isGstInclusive = false

      if (data.status == 200 &&  data[0].merchant_charges > 0) {
        // percentage
        if (data[0].merchant_charges_applied_type == '2') {
          charge = fields.amount * (data[0].merchant_charges / 100)
          console.log('charge: ' + charge)
        } else {
          charge = data[0].merchant_charges // for merchant_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        if (data[0].bank_charges_applied_type == '2') {
          bank_charge = fields.amount * (data[0].bank_charges / 100)
          console.log('charge: ' + charge)
        } else {
          bank_charge = data[0].bank_charges // for bank_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        // gst calculation
        if (data[0].gst_applied_calculation_type == 'E') {
          gst = charge * (data[0].gst_percentage / 100)
        } else if (data[0].gst_applied_calculation_type == 'I') {
          isGstInclusive = true
          gst = charge - (charge * (100 / (100 + data[0].gst_percentage)))
          gst = gst * -1 // Negating for deduction
        }

        // round off gst to 2 decimals
        gst = Math.round(gst * 100) / 100
        console.log('gst: ' + Math.abs(gst))

        afterDeduct = Number(fields.amount) - (charge + gst)
        console.log('afterDeduct: ' + afterDeduct)

        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: data[0].min_amount,
          maxAmount: data[0].max_amount,
          type: data[0].type,
          bankName: data[0].bank_name,
          isGstInclusive: isGstInclusive
        }
      } else {
        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: 0,
          maxAmount: 0,
          type: '',
          bankName: '',
          isGstInclusive: isGstInclusive
        }
      }
      
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'calc result', fields: finalData })
      return finalData
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'error', fields: err })
      return err
    }
  }
  static async getDistribution(fields) {
    const common_fns = require('../../util/common_fns')
 
    log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'request', fields: fields})
    try {
      const checkOtherConfigurationFields = { ...fields }
      delete fields.distributerIdForGetDistribution
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var sql = `SELECT * FROM ma_slabwise_distribution_bbps where ${fields.amount} between min_amount and max_amount and record_status='Y' `
 
 
      // Check condition for integrated users
      // add user id condition, to add check for whether user is web or emitra
      const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.id = ${fields.userid} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id limit 1 `
      const resultint = await this.rawQuery(userintegrated, fields.connection)
      log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        ma_user_id = util.integratedIncentive[resultint[0].integration_code]
        result = {}
        const retailerintegratedSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerintegratedSql, fields.connection)
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Integrated Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        } else {
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
        }
      }
 
      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerSql, fields.connection)
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
      const commonFunction = require('../common/commonFunctionController')
      // Check other configuration (For now this is for distributor specific)
      result = {}
      console.log('sql>>>>>>>>>', sql)
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields: checkOtherConfigurationFields, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      // Reinitializing variables
      ma_user_id = 0
      result = {}
 
      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        // state_master_id = 2 // FOR TESTING ONLY
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - State Specific', fields: result })
        result = await this.rawQuery(stateSql, fields.connection)
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
 
      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}
 
      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      result = await this.rawQuery(defaultSql, fields.connection)
      log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
  /**
   * Calls Payindex And Verify Order API
   * <AUTHOR> Andrew
   * @param {{ ma_user_id: number, mer_dtls: string, aggregator_order_id: string, bill_amount: number, api_request: string, ma_billpay_transactionid: number, action_type: 'recharge' | 'instapay' | 'register' | 'adhoc', provider_id: number, mobile_number: string, ma_transaction_master_id: number }} fields
   * @param {*|MySQL.Connection} con
   * @returns {Promise<{status: number, message: string, respcode: string}>}
   */
  static async callPayIndex(fields, con) {
    log.debug({ pagename: basename(__filename), action: 'callPayIndex', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      // get Merchant Details
      const merchantResponse = await this.getBuyersData(fields, connection)
      log.debug({ pagename: basename(__filename), action: 'callPayIndex', type: 'merchantResponse', fields: merchantResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'merchantResponse', fields: merchantResponse })
      if (merchantResponse.length <= 0) return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }

      const { aggregator_order_id, bill_amount, api_request } = fields

      const apiRequestData = JSON.parse(api_request)
      const { requestid, account_id, bill_details: details, customerid, invoice_id, BILLER_MASTER_ID, bank_account_number, ifsc_number, ma_bbps_register_bill_details_id } = apiRequestData
      // const details = bill_data.length > 0 ? bill_data[0] : bill_data
      const merchant_details = {
        mercid: bbpsConfig.BBPS_MERCHANT_DETAILS_MERCID,
        secret: bbpsConfig.BBPS_MERCHANT_DETAILS_SECRET,
        password: bbpsConfig.BBPS_MERCHANT_DETAILS_PASSWORD,
        username: bbpsConfig.BBPS_MERCHANT_DETAILS_USERNAME
      }
      // const marking_pending_ma_transaction_status = `update ma_transaction_master set transaction_status='P' where ma_transaction_master_id='${fields.ma_transaction_master_id}'`
      // const pending_ma_resp = await this.rawQuery(marking_pending_ma_transaction_status, connection)
      // log.logger({ pagename: basename(__filename), action: 'callPayIndex', type: 'UpdateMaTransactionStatus', fields: pending_ma_resp })
      /* RISK MANAGEMENT Changes */
      const data = {
        transaction_status: 'P'
      }

      const updateTransactionResult = await transactionController.updateWhereData(connection, {
        data,
        id: fields.ma_transaction_master_id,
        where: 'ma_transaction_master_id'
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

      const marking_initiate_ma_billpay_status = `update ma_billpay_transaction_master set payment_status='P' where ma_transaction_master_id='${fields.ma_transaction_master_id}'`
      const initiate_ma_bill_resp = await this.rawQuery(marking_initiate_ma_billpay_status, connection)
      log.logger({ pagename: basename(__filename), action: 'callPayIndex', type: 'UpdateBillTransactionStatus', fields: initiate_ma_bill_resp })

      // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'UpdateBillTransactionStatus', fields: initiate_ma_bill_resp })

      const response = await MaBBPSIntegration.payIndex({ ...fields, ...merchantResponse, merchant_details })

      let transactionObj = {}
      try {
        transactionObj = parser.parse(response.response)
      } catch (error) {
        log.debug({ pagename: basename(__filename), action: 'callPayIndex', type: 'Response Parsing Error', fields: error })
        // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'Response Parsing Error', fields: error })
        transactionObj = {}
      }

      log.debug({ pagename: basename(__filename), action: 'callPayIndex', type: 'PointBankResponseObject==>', fields: transactionObj })
      // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'PointBankResponseObject==>', fields: transactionObj })

      let aggregator_txn_id = 0
      let bank_rrn = ''
      let message = ''
      let ponintbankResObj = {}
      let requireConfirm = true
      let successResponse = false
      let payment_status = 'P'
      let transaction_status = 'P'
      let bill_transaction_status = 'I'
      // If we got proper response from point bank
      if (typeof transactionObj === 'object' && Object.keys(transactionObj).length > 0 && (typeof transactionObj.RESPONSE === 'object' || typeof transactionObj.TRANSACTION === 'object')) {
        ponintbankResObj = transactionObj
        const TRANS_ORDER = (transactionObj.RESPONSE || transactionObj.TRANSACTION || {}).TRANSACTION
        const rrn = (TRANS_ORDER.RRN) ? TRANS_ORDER.RRN : ''
        bank_rrn = rrn
        aggregator_txn_id = TRANS_ORDER.APTRANSACTIONID
        message = TRANS_ORDER.MESSAGE

        // Success Case from Point Bank Payment Gateway
        if (TRANS_ORDER.TRANSACTIONSTATUS == 200) {
          log.debug({ pagename: basename(__filename), action: 'callPayIndex', type: 'response', fields: {} })
          // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'response', fields: {} })
          successResponse = true
          payment_status = 'S'
          transaction_status = 'P'
          requireConfirm = false
        }
      }

      // Other than Success Case from Point Bank Payment Gateway need to do confirmation
      // OR If we not got proper response from point bank need to confirm order again
      if (requireConfirm) {
        const orderConfirmResponse = await MaBBPSIntegration.orderConfirmation({ aggregator_order_id, merchant_details })
        log.debug({ pagename: basename(__filename), action: 'callPayIndex', type: 'OuterresponseorderConfirmResponse', fields: orderConfirmResponse })
        // lokiLogger.info({ pagename: basename(__filename), action: 'callPayIndex', type: 'OuterresponseorderConfirmResponse', fields: orderConfirmResponse })

        if (orderConfirmResponse.status != 200) return orderConfirmResponse
        aggregator_txn_id = orderConfirmResponse.aggregator_txn_id
        bank_rrn = orderConfirmResponse.rrn
        message = orderConfirmResponse.transaction_reason
        ponintbankResObj = orderConfirmResponse.transactionObj

        if (orderConfirmResponse.transaction_status == 'S') {
          payment_status = 'S'
          transaction_status = 'P'
          bill_transaction_status = 'I'
          successResponse = true
        } else if (orderConfirmResponse.transaction_status == 'F') {
          payment_status = 'F'
          transaction_status = 'F'
          bill_transaction_status = 'F'
          successResponse = false
        }
      }

      return await this.updateBBPSTransaction({
        aggregator_order_id,
        aggregator_txn_id,
        transaction_status,
        payment_status,
        amount: bill_amount,
        bank_rrn,
        message,
        ponintbankResObj,
        ma_billpay_transactionid: fields.ma_billpay_transactionid,
        successResponse,
        mercid: merchant_details.mercid,
        action_type: fields.action_type,
        ma_user_id: fields.ma_user_id,
        requestid: requestid || 0,
        provider_id: fields.provider_id,
        buyerData: merchantResponse,
        account_id,
        details,
        mobile_number: fields.mobile_number,
        ma_transaction_master_id: fields.ma_transaction_master_id,
        ipnhit: false,
        customerid: customerid,
        invoice_id: invoice_id || 0,
        BILLER_MASTER_ID,
        bill_transaction_status,
        bank_account_number,
        ifsc_number,
        ma_bbps_register_bill_details_id
      }, connection)
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'callPayIndex', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'callPayIndex', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (tempConnection) connection.release()
    }
  }

  /**
   * Calls Ma Makepayment api Pays the bill and validates the response.
   * <AUTHOR> Andrew
   * @param {{ ma_transaction_master_id: number, ma_billpay_transactionid: number, aggregator_order_id: string, mobile_number: string, amount: number }} fields
   * @param {*|MySQL.Connection} con
   * @returns {Promise<{ status: number, message: string, respcode: number }>}
   */
  static async payInstantBill(fields, con) {
    log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'request', fields })

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const merchant_details = {
        mercid: bbpsConfig.BBPS_MERCHANT_DETAILS_MERCID,
        secret: bbpsConfig.BBPS_MERCHANT_DETAILS_SECRET,
        password: bbpsConfig.BBPS_MERCHANT_DETAILS_PASSWORD,
        username: bbpsConfig.BBPS_MERCHANT_DETAILS_USERNAME
      }

      const payInstantBillData = await MaBBPSIntegration.makepayment({ ...fields, merchant_details })
      log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'payInstantBillDataRes>>', fields: payInstantBillData })
      // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'payInstantBillDataRes>>', fields: payInstantBillData })

      // 100 % Success
      const billpaymentstatus = payInstantBillData.billerStatus || 'P'
      // form_data changes done - deepak
      await transactionController.updateTransactionDetails({
        form_data: JSON.stringify({ amount: fields.amount || '', mobile: fields.mobile_number || '', addition_details: payInstantBillData.addition_details || [] }),
        aggregator_order_id: fields.aggregator_order_id
      }, connection)

      const bill_data_bank_response = payInstantBillData.bank_response || {}
      const bank_response = JSON.stringify(bill_data_bank_response)

      const updateTranResponse = await transactionController.updateWhereData(connection, { data: { transaction_status: billpaymentstatus }, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
      log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'updateTranResponse', fields: updateTranResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'updateTranResponse', fields: updateTranResponse })

      const billPayData = { transaction_status: billpaymentstatus, bank_response: JSON.stringify(bank_response) }
      if (billpaymentstatus == 'F') billPayData.payment_status = billpaymentstatus
      const updateBillPayTranResponse = await billPayTransactionCtrl.updateWhereData(connection, { data: billPayData, id: fields.ma_billpay_transactionid, where: 'ma_billpay_transactionid' })
      log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })

      let billTxnDetail = {}
      // Send SMS in case of succss/failure
      if (billpaymentstatus == 'S' || billpaymentstatus == 'F') {
        const getBillTxnDetailsQuery = `SELECT * from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}' LIMIT 1`
        const getBillTxnDetails = await this.rawQuery(getBillTxnDetailsQuery, connection)
        if (getBillTxnDetails.length > 0) {
          let message = ''
          let template = ''
          if (billpaymentstatus == 'S') {
            message = bbpsConfig.BBPS_BILLPAYSUCCESS_SMS
            template = bbpsConfig.BBPS_BILLPAYSUCCESS_SMS_TEMPLATEID
          } else {
            message = bbpsConfig.BBPS_BILLPAYFAILURE_SMS
            template = bbpsConfig.BBPS_BILLPAYFAILURE_SMS_TEMPLATEID
          }
          billTxnDetail = getBillTxnDetails[0]

          const dt = new Date(billTxnDetail.updatedon)
          const datetimeformat = dt.toLocaleString('en-IN')
          message = message.replace('<Provider name>', billTxnDetail.provider_name)
          message = message.replace('<amount>', billTxnDetail.amount)
          message = message.replace('<date time>', datetimeformat)
          message = message.replace('<bConnectId>', bill_data_bank_response?.response?.response?.bbps_ref_no)

          await sms.sentSmsAsync(message, fields.mobile_number, template)
        }
      }

      let addBBPSIncentiveResponse, userSQL, integratedMer, reverseBBPSLedgersResponse, update_reg_bill_details, reg_bill_resp
      switch (billpaymentstatus) {
        case 'S':
          // give incentive to this
          addBBPSIncentiveResponse = await this.addBBPSIncentive({ aggregator_order_id: fields.aggregator_order_id, bill_amount: fields.amount }, connection)
          log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'addBBPSIncentiveResponse >> billTxnDetail', fields: { addBBPSIncentiveResponse, billTxnDetail } })
          // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'addBBPSIncentiveResponse >> billTxnDetail', fields: { addBBPSIncentiveResponse, billTxnDetail } })

          // Check if integrated merchant
          userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${billTxnDetail.userid}`
          integratedMer = await this.rawQuery(userSQL, connection)
          log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'response-int', fields: integratedMer })
          // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'response-int', fields: integratedMer })
          if (integratedMer.length > 0) {
            // Post receipt
            const receiptData = {
              action: 'POSTRECEIPT',
              aggregator_user_id: integratedMer[0].aggregator_user_id,
              aggregator_order_id: fields.aggregator_order_id,
              ma_user_id: billTxnDetail.ma_user_id
            }
            /* SOUNDBOX CHANGES */
            const isPresent = await SoundBoxManagementController.checkTransactionTypeIsAllowed(fields.ma_user_id, 6)
            log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'isPresent', fields: isPresent })
            if (isPresent) {
              const sendSoundBoxNotificationResp = await SoundBoxManagementController.sendSoundBoxNotification({
                ma_user_id: fields.ma_user_id,
                amount: fields.amount,
                txnType: 'BBPS_SUC',
                orderid: fields.aggregator_order_id,
                connectionRead: connection
              })
              log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'response', fields: sendSoundBoxNotificationResp })
            }
            const responsePostReceipt = await integrated.index(receiptData, connection)
            log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'integrated returned this', fields: responsePostReceipt })
            // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'integrated returned this', fields: responsePostReceipt })
          }
          if (fields.ma_bbps_register_bill_details_id) {
            update_reg_bill_details = `update ma_bbps_register_bill_details set status = 'PAID', remark = 'Success - ${fields.aggregator_order_id}', paid_on = NOW() where ma_bbps_register_bill_details_id = ${fields.ma_bbps_register_bill_details_id}`
            reg_bill_resp = await this.rawQuery(update_reg_bill_details, connection)
            log.debug({ pagename: basename(__filename), action: 'payInstantBill', type: 'reg_bill_resp', fields: { update_reg_bill_details, reg_bill_resp } })
            // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'reg_bill_resp', fields: { update_reg_bill_details, reg_bill_resp } })
          }
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        case 'F':
          // failed
          // revert ledgers incentive to this
          reverseBBPSLedgersResponse = await this.reverseBBPSLedgers({
            orderid: fields.aggregator_order_id
          }, connection) // What if failed
          console.log('PayInstantBillReverseBBPSLedgers >> ', reverseBBPSLedgersResponse)
          return { status: 400, respcode: 1131, message: errorMsg.responseCode[1131] + ': Transaction failed' }
      }
      log.logger({ pagename: basename(__filename), action: 'payInstantBill', type: 'PendingCase', fields: payInstantBillData })
      // lokiLogger.info({ pagename: basename(__filename), action: 'payInstantBill', type: 'PendingCase', fields: payInstantBillData })
      /* SOUNDBOX CHANGES */
      const isPresent = await SoundBoxManagementController.checkTransactionTypeIsAllowed(fields.ma_user_id, 6)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'isPresent', fields: isPresent })
      if (isPresent) {
        const sendSoundBoxNotificationResp = await SoundBoxManagementController.sendSoundBoxNotification({
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          txnType: 'BBPS_PEN',
          orderid: fields.aggregator_order_id,
          connectionRead: connection
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'sendSoundBoxNotification', type: 'response', fields: sendSoundBoxNotificationResp })
      }
      return { status: 200, respcode: 1130, message: errorMsg.responseCode[1130] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'payInstantBill', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'payInstantBill', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (tempConnection) connection.release()
    }
  }

  /**
   * Updates BBPS transaction
   * <AUTHOR> Andrew
   * @param {{ aggregator_order_id: string, aggregator_txn_id: string, transaction_status: 'I'|'P'|'S'|'F'|'R'|'REV', payment_status: 'I'|'P'|'S'|'F', amount: string, bank_rrn: string, message: string, bill_transaction_status: 'P'|'S'|'F' }} fields
   * @param {*|MySQL.Connection} con
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async updateBBPSTransaction(fields, con) {
    log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'request', fields })

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const getBillTxnDetailsQuery = `SELECT payment_status from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}' LIMIT 1`
      const getBillTxnDetails = await this.rawQuery(getBillTxnDetailsQuery, connection)
      log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'getBillTxnDetails', fields: getBillTxnDetails })
      // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'getBillTxnDetails', fields: getBillTxnDetails })
      if (getBillTxnDetails.length <= 0) return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }

      const currenrPaymentStatus = getBillTxnDetails[0].payment_status
      log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'currenrPaymentStatus === fields.payment_status ', fields: { currenrPaymentStatus, payment_status: fields.payment_status } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'currenrPaymentStatus === fields.payment_status ', fields: { currenrPaymentStatus, payment_status: fields.payment_status } })
      // Initially currenrPaymentStatus = I , Incoming expected P,S,F status
      if (currenrPaymentStatus == fields.payment_status) return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }

      // Update Transactions recevied Data
      const updateTranResponse = await transactionController.updateTransaction(null, {
        aggregator_order_id: fields.aggregator_order_id,
        aggregator_txn_id: fields.aggregator_txn_id, // airpay_id
        transaction_status: fields.transaction_status, // as dicussed with anmol,yashwant final status update later
        amount: fields.amount,
        bank_rrn: fields.bank_rrn,
        connection,
        transaction_reason: fields.message
      })

      log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'updateTranResponse', fields: updateTranResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'updateTranResponse', fields: updateTranResponse })

      const updateBillPayTranResponse = await billPayTransactionCtrl.updateWhereData(connection, {
        data: {
          transaction_status: fields.bill_transaction_status,
          payment_status: fields.payment_status,
          payment_response: JSON.stringify(fields.ponintbankResObj)
        },
        id: fields.ma_billpay_transactionid,
        where: 'ma_billpay_transactionid'
      })

      log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })
      // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'updateBillPayTranResponse', fields: updateBillPayTranResponse })

      if (fields.successResponse === true && updateTranResponse.status == 200 && updateBillPayTranResponse.affectedRows >= 0) {
        log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'Success Payment Calling', fields: 'Success Payment Calling' })
        // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'Success Payment Calling', fields: 'Success Payment Calling' })
        const paymentResponseData = await this.payInstantBill(fields, connection)
        log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'payInstantBillResponse>>', fields: paymentResponseData })
        // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'payInstantBillResponse>>', fields: paymentResponseData })
        return paymentResponseData
      }

      log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'TRANSACTIONSTATUS != 200 Pending or Failure Case Payment,', fields: { successResponse: fields.successResponse, updateTranResponse, updateBillPayTranResponse } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'TRANSACTIONSTATUS != 200 Pending or Failure Case Payment,', fields: { successResponse: fields.successResponse, updateTranResponse, updateBillPayTranResponse } })
      if (fields.payment_status == 'F') {
        const reverseBBPSLedgersResponse = await this.reverseBBPSLedgers({
          orderid: fields.aggregator_order_id
        }, connection)
        log.debug({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'updateBBPSTransactionReverseBBPSLedgers >>', fields: reverseBBPSLedgersResponse })
        // lokiLogger.info({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'updateBBPSTransactionReverseBBPSLedgers >>', fields: reverseBBPSLedgersResponse })
        return { status: 400, respcode: 1028, message: fields.message }
      }
      return { status: 200, respcode: 1130, message: errorMsg.responseCode[1130] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'updateBBPSTransaction', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (tempConnection) connection.release()
    }
  }

  /**
   * Distributes incentives to the merchants
   * <AUTHOR> Andrew
   * @param {{ aggregator_order_id: string, bill_amount: number }} fields
   * @param {*} con
   * @returns {Promise<{ status: number, message: string, respcode: number }>}
   */
  static async addBBPSIncentive(fields, con) {
    log.debug({ pagename: basename(__filename), action: 'addBBPSIncentive', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'addBBPSIncentive', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      const getOrderQuery = `select mum.profileid,mum.userid,mum.distributer_user_master_id,mum.user_type,mum.state, CAST(AES_DECRYPT(mum.pan,'${process.env.SECONDARY_ENCRPTION_KEY}') AS CHAR) as pan from ma_transaction_master as tm  
        JOIN ma_user_master as mum on tm.ma_user_id = mum.profileid 
        where tm.aggregator_order_id='${fields.aggregator_order_id}' AND mum.user_status = "Y"`
      const _user = await this.rawQuery(getOrderQuery, connection)
      if (_user.length <= 0) return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003] }

      const transactionDetails = await transactionController.findMatching('', { aggregator_order_id: fields.aggregator_order_id })
      log.debug({ pagename: basename(__filename), action: 'addBBPSIncentive', type: 'transactionDetails', fields: transactionDetails })
      // lokiLogger.info({ pagename: basename(__filename), action: 'addBBPSIncentive', type: 'transactionDetails', fields: transactionDetails })

      const incfields = {
        commission_amount: transactionDetails[0].commission_amount,
        amount: fields.bill_amount,
        ma_user_id: _user[0].profileid,
        orderid: fields.aggregator_order_id,
        userid: _user[0].userid,
        user_type: _user[0].user_type,
        distributer_user_master_id: _user[0].distributer_user_master_id,
        stateid: _user[0].state,
        pan: _user[0].pan,
        connection
      }
      await mySQLWrapper.beginTransaction(connection)

      const incentive_distribution = await bbpsinecntive.incentiveDistribution(incfields)

      if (incentive_distribution.status !== 200) {
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
      await mySQLWrapper.commit(connection)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'addBBPSIncentive', type: 'catch error', fields: error })
      // lokiLogger.error({ pagename: basename(__filename), action: 'addBBPSIncentive', type: 'catch error', fields: error })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + error.message }
    } finally {
      // Release the connection
      if (tempConnection) connection.release()
    }
  }

  /**
   * reverseBBPSLedgers - Reverses ledger entries if any are present
   * <AUTHOR> Andrew
   * @param {{ orderid: string }} fields
   * @param {*|MySQL.Connection} con
   * @returns {Promise <{ status: number, respcode: number, message: string }>}
   */
  static async reverseBBPSLedgers(fields, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const transaction_status = 'REV'
      const txnDetailsQuery = `SELECT t.*,b.payment_status,b.ma_billpay_transactionid from ma_transaction_master as t JOIN ma_billpay_transaction_master as b on b.ma_transaction_master_id = t.ma_transaction_master_id  where aggregator_order_id='${fields.orderid}' limit 1`
      const txnDetails = await this.rawQuery(txnDetailsQuery, connection)
      if (txnDetails.length <= 0) return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }

      const getPointsDetailsQuery = `SELECT * from ma_points_ledger_master where mode = 'dr' AND orderid='${fields.orderid}'AND transaction_type = '${txnDetails[0].transaction_type}'`
      const getPointsDetails = await this.rawQuery(getPointsDetailsQuery, connection)
      if (getPointsDetails.length <= 0) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' Debit Entry not found !' }

      // Check if integrated merchant
      const integratedMerQuery = `SELECT id, aggregator_user_id FROM users WHERE user_type='integrated' and id = ${txnDetails[0].userid}`
      const integratedMer = await this.rawQuery(integratedMerQuery, connection)
      log.debug({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'REFUND', fields: integratedMer })
      // lokiLogger.info({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'REFUND', fields: integratedMer })
      if (integratedMer.length > 0) {
        const postdata = { action: 'REFUND', aggregator_user_id: integratedMer[0].aggregator_user_id, aggregator_order_id: fields.orderid }
        const res = await integrated.index(postdata, connection)
        log.debug({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'integrated returned this', fields: res })
        // lokiLogger.info({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'integrated returned this', fields: res })
        if (res.status != 200) return res
      }

      // Reverse BBPS txn if failed in settlement
      const reverseTxnFields = {
        distributorId: util.airpayUserId,
        retailerId: txnDetails[0].ma_user_id,
        amount: txnDetails[0].amount,
        userid: txnDetails[0].userid,
        orderid: transaction_status + '-' + fields.orderid,
        commissionType: txnDetails[0].transaction_type,
        txnStatus: transaction_status,
        connection
      }
      const resp = await pointsLedger.sendMoney(null, reverseTxnFields)
      log.debug({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'sendMoneyRevert', fields: resp })
      // lokiLogger.info({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'sendMoneyRevert', fields: resp })

      // Reverse BBPS Surcharges, incentives and TDS txn if failed in settlement
      const incentiveDetailsQuery = `SELECT * from ma_points_ledger_master where parent_id  = '${fields.orderid}' and transaction_type in (14, 9, 11, 18) order by mode,ma_points_ledger_master_id desc`
      const incentiveDetails = await pointsLedger.rawQuery(incentiveDetailsQuery, connection)
      if (incentiveDetails.length > 0) {
        for (let i = 0; i < incentiveDetails.length; i++) {
          const descTypeObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: incentiveDetails[i].transaction_type }, connection)
          const descType = descTypeObj.code_desc ? descTypeObj.code_desc : ''

          const reverseTxnDetails = {
            ma_user_id: incentiveDetails[i].ma_user_id,
            corresponding_id: incentiveDetails[i].corresponding_id,
            amount: -incentiveDetails[i].amount,
            userid: incentiveDetails[i].userid,
            orderid: transaction_status + '-' + incentiveDetails[i].orderid,
            mode: 'cr',
            transaction_type: incentiveDetails[i].transaction_type,
            ma_status: transaction_status,
            description: 'Credit - ' + descType + ' - Reversed',
            connection: connection
          }

          const resppoint = await pointsLedger.createEntry(null, reverseTxnDetails)
          if (resppoint.status === 400) return resppoint
        }
      }
 
      let paymodeChargeLedgerQuery = `SELECT * from ma_points_ledger_master where parent_id  = '${fields.orderid}' and transaction_type in ('70','71','72') and ma_status = "S" ORDER BY ma_points_ledger_master_id desc limit 3`
      
      const paymodeChargeLedgerQueryResult = await this.rawQuery(paymodeChargeLedgerQuery, connection)
      console.log("paymodeChargeLedgerQueryResult", paymodeChargeLedgerQueryResult);
      log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'paymodeChargeLedgerQueryResult', fields: paymodeChargeLedgerQueryResult })

      if (paymodeChargeLedgerQueryResult.length > 0) {
        for (let i = 0; i < paymodeChargeLedgerQueryResult.length; i++) {
          const mode = paymodeChargeLedgerQueryResult[i].mode == 'cr' ? 'dr' : 'cr'
          const amount = mode == 'cr' ? paymodeChargeLedgerQueryResult[i].amount * -1 : paymodeChargeLedgerQueryResult[i].amount
          const reverseTxnDetails = {
            ma_user_id: paymodeChargeLedgerQueryResult[i].ma_user_id,
            corresponding_id: paymodeChargeLedgerQueryResult[i].corresponding_id,
            amount,
            mode,
            userid: paymodeChargeLedgerQueryResult[i].userid,
            orderid: 'REV -' + paymodeChargeLedgerQueryResult[i].orderid,
            transaction_type: paymodeChargeLedgerQueryResult[i].transaction_type,
            ma_status: transaction_status,
            description: paymodeChargeLedgerQueryResult[i].description + '- Reversed',
            connection: connection
          }

          const resppoint = await pointsLedgerController.createEntry(null, reverseTxnDetails)
          if (resppoint.status === 400) return resppoint
        }
        const bankPaymodeChargesSql = `SELECT * from ma_bank_paymode_charges where order_id = '${fields.orderid}' ORDER BY ma_bank_paymode_charges_id limit 2`
      const bankPaymodeChargesSqlResult = await this.rawQuery(bankPaymodeChargesSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'reversePaymodeCharges', type: 'bankPaymodeChargesSqlResult', fields: bankPaymodeChargesSqlResult })
 
      if (bankPaymodeChargesSqlResult?.length) {
        for (let i = 0; i < bankPaymodeChargesSqlResult.length; i++) {
          const sql = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
          const params = [bankPaymodeChargesSqlResult[i].ma_user_id, bankPaymodeChargesSqlResult[i].order_id, bankPaymodeChargesSqlResult[i].charges_type, bankPaymodeChargesSqlResult[i].bank_name, bankPaymodeChargesSqlResult[i].transaction_type, bankPaymodeChargesSqlResult[i].sub_transaction_type, bankPaymodeChargesSqlResult[i].transaction_amount, -bankPaymodeChargesSqlResult[i].charges, 'REV']
          await this.secureRawQuery(sql, { params, connection: connection })
        }
      }
 
      }

      if (resp.status == 200) {
        log.debug({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'mark R for point bank payment', fields: txnDetails[0].ma_billpay_transactionid })
        // lokiLogger.info({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'mark R for point bank payment', fields: txnDetails[0].ma_billpay_transactionid })
        await billPayTransactionCtrl.updateWhereData(connection, {
          data: { payment_status: 'R' },
          id: txnDetails[0].ma_billpay_transactionid,
          where: 'ma_billpay_transactionid'
        })
      }
      return resp
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'reverseBBPSLedgers', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  /**
   * @cron getProvidersCron description - Calls MS API and stores data in appropriate format
   * @updated on 30-06-2022
   * <AUTHOR> Andrew
   * @returns {Promise<void>}
   */
  static async getProvidersCron(utilitiesObj = undefined) {
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      let utilities = []
      const insert_utility_providers_cron_log = []
      const insertUtilityData = []
      const parsedUtilityKeys = []
      const getUtilitiesDBQuery = "SELECT utility_name as value, utility_id, page, providers_data from ma_bbps_utility_providers_cron_log where is_synced = 'N' and updatedon > now() - interval 1 day"
      utilities = utilitiesObj || await this.rawQuery(getUtilitiesDBQuery, connection)
      log.debug({ pagename: basename(__filename), action: 'getProvidersCron', type: 'check utility pending', fields: utilities })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'check utility pending', fields: utilities })
      if (utilities.length <= 0) {
        /**
         * Fetches list of utilities from MS.
         * @type {{ key: string|number, value: string }[]}
         */
        //  [{ value: 'Mutual Fund', key: '7' }, { value: 'Municipal Taxes', key: '14' }, { value: 'Education', key: '19' }]
        utilities = await MsBBPSIntegration.getUtilities()
        log.debug({ pagename: basename(__filename), action: 'getProvidersCron', type: 'get utilities from api', fields: utilities })
        // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'get utilities from api', fields: utilities })
        utilities.forEach(e => {
          if (e.key == undefined) {
            e.key = e.utility_id
          }
          insert_utility_providers_cron_log.push([e.value, e.key])
          parsedUtilityKeys.push(e.key)
        })

        if (insert_utility_providers_cron_log.length > 0) await this.insertBulkWithTableName(connection, 'ma_bbps_utility_providers_cron_log', '(utility_name, utility_id)', insert_utility_providers_cron_log)

        // CHECK IF SOME UTILITIES ARE MISSING FROM MS API AND INACTIVATE THEM IF ANY
        if (parsedUtilityKeys.length > 0) {
          log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: 'parsedUtilityKeys', fields: parsedUtilityKeys.join(',') })
          // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'parsedUtilityKeys', fields: parsedUtilityKeys.join(',') })
          const missingUtilUpdateQuery = `Update ma_billpayment_utility_providers set active_status = 'N' where CAST(provider_key AS DECIMAL) not in (${parsedUtilityKeys.join(',')})`
          log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: 'UPDATE QUERY', fields: missingUtilUpdateQuery })
          // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'UPDATE QUERY', fields: missingUtilUpdateQuery })
          await this.rawQuery(missingUtilUpdateQuery, connection)
        }
      }

      if (utilitiesObj == undefined) {
        for (const utility of utilities) {
          if (utility.key == undefined) { utility.key = utility.utility_id }
          if (utility.key) {
            // parsedUtilityKeys.push(utility.key)

            // Check if Utility already exist.
            const utilityCheckQuery = `Select * from ma_billpayment_utility_providers where provider_name = '${utility.value}'`
            const utilityCheck = await this.rawQuery(utilityCheckQuery, connection)

            // Insert Utility if not present in db OR update provider_key of utility
            // Fetched utilities based on provider_name, now check if the utility exist or is it inactive, insert if not exist else update it's status
            if (utilityCheck.length <= 0) insertUtilityData.push([utility.key, utility.value, 'https://retailappdocs.s3.ap-south-1.amazonaws.com/web_icons_v1/bill.svg', 'https://retailappdocs.s3.ap-south-1.amazonaws.com/app_icons/1653546366_Bbps.png', 'Y'])
            else if (utilityCheck[0].provider_key !== utility.key || utilityCheck[0].active_status == 'N') {
              log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: '', fields: { utility, utilityDB: utilityCheck[0].ma_billpayment_utility_provider_id } })
              // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: '', fields: { utility, utilityDB: utilityCheck[0].ma_billpayment_utility_provider_id } })
              const utilityUpdateQuery = `Update ma_billpayment_utility_providers set active_status = 'Y', provider_key = '${utility.key}' where ma_billpayment_utility_provider_id = '${utilityCheck[0].ma_billpayment_utility_provider_id}'`
              await this.rawQuery(utilityUpdateQuery, connection)
              // } else if (utilityCheck[0].provider_key !== utility.key) {
              //   const utilityUpdateQuery = `Update ma_billpayment_utility_providers set provider_name = '${utility.value}', provider_key = '${utility.key}' where provider_name = '${utility.value}'`
              //   await this.rawQuery(utilityUpdateQuery, connection)
            }
            utility.provider_key = utility.key
          }
        }

        // * Update unparsed utilities from db to Not active status.
        /*
        if (parsedUtilityKeys.length > 0) {
          log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: 'parsedUtilityKeys', fields: parsedUtilityKeys.join(',') })
          const missingUtilUpdateQuery = `Update ma_billpayment_utility_providers set active_status = 'N' where CAST(provider_key AS DECIMAL) not in (${parsedUtilityKeys.join(',')})`
          log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: 'UPDATE QUERY', fields: missingUtilUpdateQuery })
          await this.rawQuery(missingUtilUpdateQuery, connection)
        }
        */

        if (insertUtilityData.length > 0) await this.insertBulkWithTableName(connection, 'ma_billpayment_utility_providers', '(provider_key, provider_name, web_icon, app_icon, active_status)', insertUtilityData)

        log.debug({ pagename: basename(__filename), action: 'getProvidersCron', type: 'insert Query', fields: { insertUtilityData, updateMissingUtil: parsedUtilityKeys } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'insert Query', fields: { insertUtilityData, updateMissingUtil: parsedUtilityKeys } })
      }
      if (utilitiesObj && utilitiesObj.length > 0) {
        const getUtilitiesDBQuery = `SELECT utility_name as value, utility_id, page, providers_data from ma_bbps_utility_providers_cron_log where is_synced = 'N' and  utility_id = ${utilitiesObj[0].key} and updatedon > now() - interval 1 day`
        const checkUtilityIsSynced = await this.rawQuery(getUtilitiesDBQuery, connection)
        if (checkUtilityIsSynced.length == 0) {
          insert_utility_providers_cron_log.push([utilitiesObj[0].value, utilitiesObj[0].key, (utilitiesObj[0].page || 1)])
          await this.insertBulkWithTableName(connection, 'ma_bbps_utility_providers_cron_log', '(utility_name, utility_id, page)', insert_utility_providers_cron_log)
        }
      }

      // Check utility table for utility id
      const utility_query = "Select ma_billpayment_utility_provider_id, provider_name, CAST(provider_key AS CHAR) as provider_key from ma_billpayment_utility_providers mbup where provider_name = 'Mutual Fund' limit 1"
      const utility_query_resp = await this.rawQuery(utility_query, connection)
      log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'utility_data', fields: { utility_query, utility_query_resp } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'utility_data', fields: { utility_query, utility_query_resp } })

      for (const utility of utilities) {
        if ((util.bbpsProvidersExclude).includes(utility) && (typeof utilitiesObj === 'undefined')) {
          log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: 'utility_skip', fields: utility })
          // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'utility_skip', fields: utility })
          continue
        }
        if (typeof utilitiesObj === 'undefined') {
          const updateQuery = `UPDATE ma_bbps_providers_validations SET is_visible = 'N' where utility_id = ${(utility.provider_key || utility.key)}`
          await this.rawQuery(updateQuery, connection)
        }
        const getProviderResponse = await this.getProvidersInBatchWise({ utility_id: (utility.provider_key || utility.key), page: utility.page || 1 }, connection, utility_query_resp)
        log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'response', fields: getProviderResponse })
        // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'response', fields: getProviderResponse })

        const update_cron_log = `Update ma_bbps_utility_providers_cron_log set is_synced = 'Y' where utility_id = ${utility.provider_key || utility.key} order by ma_bbps_utility_providers_cron_log_id desc limit 1`
        const update_cron_log_resp = await this.rawQuery(update_cron_log, connection)
        log.debug({ pagename: basename(__filename), action: 'getProvidersCron', type: 'update cron log', fields: { update_cron_log, update_cron_log_resp } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersCron', type: 'update cron log', fields: { update_cron_log, update_cron_log_resp } })
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: basename(__filename), action: 'getProvidersCron', type: 'err', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'getProvidersCron', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (connection) connection.release()
    }
  }

  /**
   * @callbackapi Gets called when biller registration is successful
   * <AUTHOR> Andrew
   * @param {{USER_ID: number, CUSTOMER_ID: number, ACCOUNTID: number, REQUESTNUMBER: number, STATUS: number}} fields
   * @returns {Promise <{STATUS: number, MSG: string}>}
   */
  static async registerStatus(fields) {
    log.debug({ pagename: basename(__filename), action: 'registerStatus', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'registerStatus', type: 'request', fields })
    const validationResponse = validator.validateFields(fields, ['USER_ID', 'CUSTOMER_ID', 'ACCOUNTID', 'REQUESTNUMBER', 'STATUS'])
    if (validationResponse.status !== 200) {
      validationResponse.STATUS = validationResponse.status
      validationResponse.MSG = validationResponse.message
      delete validationResponse.status
      delete validationResponse.message
      delete validationResponse.respcode
      return validationResponse
    }

    if (fields.STATUS != 200) return { STATUS: 400, MSG: 'Biller Registration Failed' }

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const updateStatusQuery = `UPDATE ma_billpay_registered_merchant SET registration_status = 'Y' WHERE orderid = '${fields.REQUESTNUMBER}'`
      await this.rawQuery(updateStatusQuery, connection)
      return { STATUS: 200, MSG: 'Billers Registered Successfully.' }
    } catch (err) {
      return { STATUS: 400, MSG: 'Biller Registration Failed' }
    } finally {
      connection.release()
    }
  }

  /**
   * @callbackapi Gets called when biller registration is successful
   * <AUTHOR> Andrew
   * @param {{
   *  USER_ID: number,
   *  CUSTOMER_ID: number,
   *  ACCOUNTID: string,
   *  BILLAMOUNT: string,
   *  BILLID: string,
   *  BILLDUEDATE: string,
   *  BILLNUMBER: string,
   *  BILLERNAME: string,
   *  REGISTERID: string,
   *  BILLRSPID: string,
   *  REQUESTNUMBER: string,
   *  BILLPERIOD?: string,
   *  EARLY_BILLDUEDATE?: string,
   *  EARLY_BILLDISCOUNT: string,
   *  EARLY_BILLAMOUNT: string,
   *  LATE_PAYMENT_CHARGES: string,
   *  LATE_PAYMENT_AMOUNT: string,
   *  url: string,
   *  CHECKSUM: string,
   *  PRIVATEKEY: string
   * }} fields
   * @returns {Promise <{STATUS: number, MSG: string}>}
   */
  static async newbill(fields) {
    log.debug({ pagename: basename(__filename), action: 'newbill', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'newbill', type: 'request', fields })
    const validationResponse = validator.validateFields(fields, ['CHECKSUM', 'USER_ID', 'ACCOUNTID', 'REQUESTNUMBER', 'BILLAMOUNT', 'BILLID'])
    if (validationResponse.status !== 200) return { STATUS: validationResponse.status, MSG: validationResponse.message }
    if (!md5Compare(fields.CHECKSUM, `${fields.USER_ID || ''}~${fields.CUSTOMER_ID || ''}~${fields.AUTHENTICATOR || ''}~${fields.BILLAMOUNT || ''}~${fields.BILLID || ''}~${fields.BILLDUEDATE || ''}~${fields.BILLNUMBER || ''}~${fields.BILLERNAME || ''}~${fields.BBPS_REGISTER_RES_ID || ''}~${fields.VIEW_BILL_RSP_ID || ''}~${fields.REQUESTNUMBER || ''}`)) return { STATUS: 400, MSG: 'Invalid Checksum' }
    if (fields.STATUS == 400) return { STATUS: 400, MSG: 'Notification Failed' }

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const notificationQuery = `SELECT
        brm.notification_status as status,
        brm.mobile_number,
        bpv.provider_name
      FROM
        ma_billpay_registered_merchant brm
      INNER JOIN
        ma_bbps_providers_validations bpv
      ON
        bpv.utility_id = brm.utility_id and
        bpv.provider_id = brm.provider_id
      WHERE
        orderid = '${fields.REQUESTNUMBER}' or
        brm.account_id = '${fields.ACCOUNTID}' and
        brm.registration_status = 'Y'
      LIMIT 1`
      /**
       * @type {{ status: 'ON' | 'OFF', mobile_number: string, provider_name: string }[]}
       */
      const notification = await this.rawQuery(notificationQuery, connection)
      log.debug({ pagename: basename(__filename), action: 'newbill', type: 'notification', fields: notification })
      // lokiLogger.info({ pagename: basename(__filename), action: 'newbill', type: 'notification', fields: notification })
      if (notification.length <= 0) return { STATUS: 400, MSG: 'Biller Not Registered.' }
      if (notification[0].status == 'ON' && validator.validateField(notification[0].mobile_number)) {
        // send sms
        await sms.sentSmsAsync(bbpsConfig.BBPS_NEW_BILL_SMS.replace('<Provider>', notification[0].provider_name).replace('<Amount>', 'Rs.' + fields.BILLAMOUNT), notification[0].mobile_number, bbpsConfig.BBPS_NEW_BILL_SMS_TEMPLATEID)
      }
      if (fields.ACCOUNTID) {
        const fetchBill = `Select ma_bbps_register_bill_details_id, utility_id, biller_master_id, provider_id, account_id, amount, bill_due_date, status from ma_bbps_register_bill_details where account_id = '${fields.ACCOUNTID}' ORDER by ma_bbps_register_bill_details_id DESC limit 1`
        const fetchResp = await this.rawQuery(fetchBill, connection)
        log.debug({ pagename: basename(__filename), action: 'newbill', type: 'fetch register_bill_details', fields: { fetchBill, fetchResp } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'newbill', type: 'fetch register_bill_details', fields: { fetchBill, fetchResp } })
        if (fetchResp.length > 0) {
          try {
            const check_bill_due_date = moment(fields.BILLDUEDATE, ['DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY-MM-DDTH:i:s:a', 'DD/MM/YYYY', 'YYYY/MM/DD', 'YYYY/MM/DDTH:i:s:a'])

            if (!check_bill_due_date.isValid()) {
              throw new Error('Invalid Date Format')
            }
            if (fetchResp[0].amount != null && fetchResp[0].amount != undefined) {
              // insert
              const sql_insert_bill_details = `insert into ma_bbps_register_bill_details (utility_id, biller_master_id, provider_id, account_id, amount, bill_due_date, bill_request) values (${fetchResp[0].utility_id}, ${fetchResp[0].biller_master_id}, ${fetchResp[0].provider_id}, '${fields.ACCOUNTID}', ${fields.BILLAMOUNT}, ${fields.BILLDUEDATE ? `'${check_bill_due_date.format('YYYY-MM-DD 23:59:59')}'` : 'DATE_ADD(NOW(), INTERVAL 1 MONTH)'}, '${JSON.stringify(fields)}')`
              const insert_bill_resp = await this.rawQuery(sql_insert_bill_details, connection)
              log.debug({ pagename: basename(__filename), action: 'newbill', type: 'insert register_bill_details', fields: { sql_insert_bill_details, insert_bill_resp } })
              // lokiLogger.info({ pagename: basename(__filename), action: 'newbill', type: 'insert register_bill_details', fields: { sql_insert_bill_details, insert_bill_resp } })
            } else {
              const update_bill_details = `update ma_bbps_register_bill_details set amount = ${fields.BILLAMOUNT}, bill_due_date = ${fields.BILLDUEDATE ? `'${check_bill_due_date.format('YYYY-MM-DD 23:59:59')}'` : 'DATE_ADD(NOW(), INTERVAL 1 MONTH)'}, bill_request = '${JSON.stringify(fields)}' where account_id = '${fields.ACCOUNTID}' and status = 'UNPAID' ORDER by ma_bbps_register_bill_details_id DESC limit 1`
              const update_billresp = await this.rawQuery(update_bill_details, connection)
              log.debug({ pagename: basename(__filename), action: 'newbill', type: 'update bill details', fields: { update_bill_details, update_billresp } })
              // lokiLogger.info({ pagename: basename(__filename), action: 'newbill', type: 'update bill details', fields: { update_bill_details, update_billresp } })
            }
          } catch (error) {
            log.debug({ pagename: basename(__filename), action: 'newbill', type: 'error', fields: error })
            // lokiLogger.error({ pagename: basename(__filename), action: 'newbill', type: 'error', fields: error })
            const sql_insert_bbps_new_bill_log = `insert into ma_bbps_new_bill_log (utility_id, biller_master_id, account_id, bill_request, remark) values (${fetchResp[0].utility_id}, ${fetchResp[0].biller_master_id}, '${fields.ACCOUNTID}', '${JSON.stringify(fields)}', "${(error.message + ' ' + error.stack).replace(/"/gm, "'")}")`
            const insert_bbps_new_bill_log = await this.rawQuery(sql_insert_bbps_new_bill_log, connection)
            log.debug({ pagename: basename(__filename), action: 'newbill', type: 'insert bill log', fields: { sql_insert_bbps_new_bill_log, insert_bbps_new_bill_log } })
            // lokiLogger.error({ pagename: basename(__filename), action: 'newbill', type: 'insert bill log', fields: { sql_insert_bbps_new_bill_log, insert_bbps_new_bill_log } })
            return { STATUS: 200, MSG: 'Biller Notified Successfully.' }
          }
        }
      }
      return { STATUS: 200, MSG: 'Biller Notified Successfully.' }
    } catch (err) {
      log.debug({ pagename: basename(__filename), action: 'newbill', type: 'catch error', fields: err })
      // lokiLogger.error({ pagename: basename(__filename), action: 'newbill', type: 'catch error', fields: err })
      return { STATUS: 400, MSG: 'Biller Notification Failed' }
    } finally {
      connection.release()
    }
  }

  /**
   * @api Check Biller Availability
   * <AUTHOR> Gupta
   * @param {*|null} _
   * @param {{ ma_user_id: number }} fields
   * @returns {Promise<{ status: number, message: string, respcode: number}>}
   */
  static async checkBillerAvailable(_, fields) {
    log.debug({ pagename: basename(__filename), action: 'checkBillerAvailable', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'checkBillerAvailable', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Field Validation
      const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
      if (validationResponse.status !== 200) return validationResponse

      let fluctuatingBiller = []; let offlineBiller = []; const billerDetails = {}

      const fluctuatingBillerResp = []; const offlineBillerResp = []

      const sql_biller_availability = 'Select ma_bbps_biller_availability_id, response, updatedon, TIMESTAMPDIFF(MINUTE, updatedon ,CURRENT_TIMESTAMP) as lastupdatedon from ma_bbps_biller_availability limit 1'

      const sql_biller_resp = await this.rawQuery(sql_biller_availability, connection)

      log.logger({ pagename: basename(__filename), action: 'sql_biller_availability', type: 'response', fields: sql_biller_resp })
      // lokiLogger.info({ pagename: basename(__filename), action: 'sql_biller_availability', type: 'response', fields: sql_biller_resp })

      let billerresp = {}

      if (sql_biller_resp.length == 0 || sql_biller_resp[0].lastupdatedon >= 15) {
        const merchantDetails = await this.getBuyersData(fields, connection)

        if (!merchantDetails) return { status: 400, message: `${errorMsg.responseCode[1002]} - Merchant details not found`, respcode: 1002 }

        fields.username = merchantDetails.username
        fields.password = merchantDetails.password
        fields.secret = merchantDetails.secret

        billerresp = await MaBBPSIntegration.billAvailabilityCheck(fields)
        log.logger({ pagename: basename(__filename), action: 'billAvailabilityCheck', type: 'response', fields: billerresp })
        // lokiLogger.info({ pagename: basename(__filename), action: 'billAvailabilityCheck', type: 'response', fields: billerresp })

        log.logger({ pagename: basename(__filename), action: 'billAvailabilityCheck billerresp', type: 'response', fields: billerresp })
        // lokiLogger.info({ pagename: basename(__filename), action: 'billAvailabilityCheck billerresp', type: 'response', fields: billerresp })
        if (billerresp.status != 200) {
          if (fields.billerid) {
            return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, billerDetails: null }
          } else {
            return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, fluctuatingBiller: [], offlineBiller: [] }
          }
        }

        if (sql_biller_resp.length == 0) {
          const sql_insert_billeravail = `insert into ma_bbps_biller_availability (response) values ('${JSON.stringify({ biller_availability: billerresp.biller_availability })}')`
          const insert_biller_resp = await this.rawQuery(sql_insert_billeravail, connection)

          log.logger({ pagename: basename(__filename), action: 'ma_bbps_biller_availability insert', type: 'response', fields: { insert_biller_resp } })
          // lokiLogger.info({ pagename: basename(__filename), action: 'ma_bbps_biller_availability insert', type: 'response', fields: { insert_biller_resp } })
        } else {
          const sql_update_billeravail = `update ma_bbps_biller_availability  set response = '${JSON.stringify({ biller_availability: billerresp.biller_availability })}', updatedon = CURRENT_TIMESTAMP where ma_bbps_biller_availability_id = ${sql_biller_resp[0].ma_bbps_biller_availability_id}`
          const update_biller_resp = await this.rawQuery(sql_update_billeravail, connection)

          log.logger({ pagename: basename(__filename), action: 'ma_bbps_biller_availability insert', type: 'response', fields: { update_biller_resp } })
          // lokiLogger.info({ pagename: basename(__filename), action: 'ma_bbps_biller_availability insert', type: 'response', fields: { update_biller_resp } })
        }
      } else {
        billerresp = JSON.parse(sql_biller_resp[0].response)
      }

      if (fields.billerid && billerresp.biller_availability.data) {
        billerresp.biller_availability.data.BILLERDATA.forEach(element => {
          if (element.BILLER_MASTER_ID == fields.billerid) {
            if (element.BILLERSTATE.includes('INACTIVE')) {
              billerDetails.message = 'Biller Offline. Please check later.'
              billerDetails.status = 'Inactive'
            } else if (element.BILLERSTATE.includes('FLUCTUATING')) {
              billerDetails.message = 'Biller experiencing low success rate of payment.'
              billerDetails.status = 'Fluctuating'
            } else {
              billerDetails.message = 'Success'
              billerDetails.status = 'Active'
            }
          }
        })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, billerDetails: Object.keys(billerDetails).length > 0 ? billerDetails : null }
      }

      log.logger({ pagename: basename(__filename), action: 'billerresp', type: 'response', fields: billerresp })
      // lokiLogger.info({ pagename: basename(__filename), action: 'billerresp', type: 'response', fields: billerresp })
      const fluctuatingBillerId = []; const offlineBillerId = []

      if (billerresp.biller_availability.data && billerresp.biller_availability.data.BILLERDATA && billerresp.biller_availability.data.BILLERDATA.length > 0) {
        billerresp.biller_availability.data.BILLERDATA.forEach(element => {
          if (element.BILLERSTATE == 'FLUCTUATING') {
            fluctuatingBillerId.push(element.BILLER_MASTER_ID)
            fluctuatingBiller.push(element)
          } else if (element.BILLERSTATE == 'INACTIVE') {
            offlineBillerId.push(element.BILLER_MASTER_ID)
            offlineBiller.push(element)
          }
        })
      }
      log.logger({ pagename: basename(__filename), action: 'fluctuate biller', type: 'response', fields: fluctuatingBiller })
      // lokiLogger.info({ pagename: basename(__filename), action: 'fluctuate biller', type: 'response', fields: fluctuatingBiller })
      log.logger({ pagename: basename(__filename), action: 'offline biller', type: 'response', fields: offlineBiller })
      // lokiLogger.info({ pagename: basename(__filename), action: 'offline biller', type: 'response', fields: offlineBiller })
      const sql_fetch_biller = 'Select DISTINCT biller_master_id as BILLER_MASTER_ID, utility_id as UTILITY_ID,  provider_name as BILLER_NAME from ma_bbps_providers_validations'
      if (fluctuatingBillerId.length > 0) {
        const sql_fluctuate_fetch_biller = sql_fetch_biller + ` where biller_master_id in (${fluctuatingBillerId})`
        log.logger({ pagename: basename(__filename), action: 'fluctuate', type: 'sql query', fields: sql_fluctuate_fetch_biller })
        // lokiLogger.info({ pagename: basename(__filename), action: 'fluctuate', type: 'sql query', fields: sql_fluctuate_fetch_biller })
        const sql_fluctuate_biller_resp = await this.rawQuery(sql_fluctuate_fetch_biller, connection)
        log.logger({ pagename: basename(__filename), action: 'fluctuate biller', type: 'response', fields: sql_fluctuate_biller_resp })
        // lokiLogger.info({ pagename: basename(__filename), action: 'fluctuate biller', type: 'response', fields: sql_fluctuate_biller_resp })
        if (sql_fluctuate_biller_resp.length == 0) {
          fluctuatingBiller = []
        } else {
          const obj_fluctuate_biller = Object.assign({}, ...sql_fluctuate_biller_resp.map(item => ({ [item.BILLER_MASTER_ID]: item })))
          fluctuatingBiller.forEach(element => {
            if (obj_fluctuate_biller[element.BILLER_MASTER_ID]) {
              fluctuatingBillerResp.push({ ...element, ...obj_fluctuate_biller[element.BILLER_MASTER_ID] })
            }
          })
        }
      }
      if (offlineBillerId.length > 0) {
        const sql_offline_fetch_biller = sql_fetch_biller + ` where biller_master_id in (${offlineBillerId})`
        log.logger({ pagename: basename(__filename), action: 'offline', type: 'sql query', fields: sql_offline_fetch_biller })
        // lokiLogger.info({ pagename: basename(__filename), action: 'offline', type: 'sql query', fields: sql_offline_fetch_biller })
        const sql_offline_biller_resp = await this.rawQuery(sql_offline_fetch_biller, connection)
        log.logger({ pagename: basename(__filename), action: 'offline biller', type: 'response', fields: sql_offline_biller_resp })
        // lokiLogger.info({ pagename: basename(__filename), action: 'offline biller', type: 'response', fields: sql_offline_biller_resp })
        if (sql_offline_biller_resp.length == 0) {
          offlineBiller = []
        } else {
          const obj_offline_biller = Object.assign({}, ...sql_offline_biller_resp.map(item => ({ [item.BILLER_MASTER_ID]: item })))
          offlineBiller.forEach(element => {
            if (obj_offline_biller[element.BILLER_MASTER_ID]) {
              offlineBillerResp.push({ ...element, ...obj_offline_biller[element.BILLER_MASTER_ID] })
            }
          })
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, fluctuatingBiller: fluctuatingBillerResp, offlineBiller: offlineBillerResp }
    } catch (error) {
      log.logger({ pagename: basename(__filename), action: 'checkBillerAvailable', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: basename(__filename), action: 'checkBillerAvailable', type: 'catcherror', fields: error })
      // return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, fluctuatingBiller: [], activeBiller: [] }
      if (fields.billerid) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, billerDetails: null }
      } else {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, fluctuatingBiller: [], offlineBiller: [] }
      }
    } finally {
      if (connection) connection.release()
    }
  }

  /**
   * @api Update BBPS Providers in batch
   * <AUTHOR> Gupta
   * @param {{ utility_id: number, key: number }} utilityObj
   * @param { MySQL.connection } connection
   * @param {{ ma_billpayment_utility_provider_id: number, provider_name: number, provider_key: number}[]} utility_query_resp
   * @returns {Promise<{ status: number, message: string, respcode: number}>}
   */
  static async getProvidersInBatchWise(utilityObj, connection, utility_query_resp) {
    try {
      log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'request', fields: utilityObj })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'request', fields: utilityObj })
      let providersResp = {}
      let providerdata = []
      const validationInsertData = []
      providersResp = await MsBBPSIntegration.getProviders(utilityObj)
      if (providersResp.status !== 200 || !('data' in providersResp) || Object.keys(providersResp.data).length == 0) return []

      // Looping through providers data
      for (let params of providersResp.data) {
        log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'params', fields: { params, utilityObj } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'params', fields: { params, utilityObj } })
        const rawFields = params.raw_fields || {}
        const rawFieldArr = []
        let mobileFlag = true

        // Additional Params added for mutual funds
        // ? Are we sure to use provider key as an optional condition?
        if (utility_query_resp.length > 0 && (utilityObj.utility_id == utility_query_resp[0].provider_key || params.provider_name.toLowerCase().includes('mutual fund'))) {
          params.raw_fields.bank_account_number = {
            name: 'Bank Account Number',
            validation: '^[a-zA-Z0-9]*$',
            postKey: 'bank_account_number'
          }
          params.raw_fields.ifsc_number = {
            name: 'IFSC Number',
            validation: '^[A-Z]{4}0[0-9]{6}$',
            postKey: 'ifsc_number'
          }
        }

        // Loop through raw fields and format data as required
        for (const rawFieldKey in rawFields) {
          // removes trailing comma from validation regex if available.
          rawFields[rawFieldKey] = { ...rawFields[rawFieldKey], validationWithComma: (rawFields[rawFieldKey].validation || '').replace(/\\/g, '\\\\'), postKey: rawFieldKey }
          rawFields[rawFieldKey].validation = (rawFields[rawFieldKey].validationWithComma || '').replace(/,\$$/g, '$')

          // Converts raw field object into array of an object. APP V2
          rawFieldArr.push({ name: params.raw_fields[rawFieldKey].name, postKey: rawFieldKey, type: typeof rawFieldKey, validation: (rawFields[rawFieldKey].validation || '') })

          if (rawFieldKey == 'mobile_number') mobileFlag = false
        }

        params = { ...params, show_mobile: mobileFlag, raw_fields: { ...rawFields } }
        /** APP V2 params */
        const newParam = {
          ...params,
          fields_validation: (params.fields_validation || '').replace(/\\/g, '\\\\'),
          raw_fields: rawFieldArr,
          provider_name: params.provider_name.replace(/[^A-Za-z0-9\- ,.+&']/gm, '')
        }

        // Check if provider validation exist
        const query = `select * from ma_bbps_providers_validations where biller_master_id = ${params.BILLER_MASTER_ID} limit 1`
        const providerValidationData = await this.rawQuery(query, connection)
        log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'providerValidationData', fields: { query, providerValidationData, rawFields } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'providerValidationData', fields: { query, providerValidationData, rawFields } })

        // If validation exists update validation
        if (providerValidationData.length > 0) {
          // update validation
          const updateQuery = `UPDATE ma_bbps_providers_validations SET validations = '${JSON.stringify(rawFields).replace(/'/g, "\\'")}', account_id = '${(newParam.required_fields || (Object.keys(rawFields) || [''])[0]).split('|')[0].trim()}', utility_id = ${utilityObj.utility_id}, provider_id = ${newParam.provider_id}, action_type='${newParam.action_type}', providers_data = '${JSON.stringify(newParam).replace(/'/g, "\\'")}', provider_name = '${newParam.provider_name}', is_visible = 'Y' where ma_bbps_providers_validations_id = ${providerValidationData[0].ma_bbps_providers_validations_id}`
          log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'updateQuery', fields: updateQuery })
          // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'updateQuery', fields: updateQuery })
          await this.rawQuery(updateQuery, connection)
        } else {
          // insert validation
          validationInsertData.push([utilityObj.utility_id, params.provider_id, JSON.stringify(rawFields).replace(/'/g, "\\'"), (newParam.required_fields || '').split('|')[0].trim(), newParam.provider_name, newParam.BILLER_MASTER_ID, newParam.action_type, JSON.stringify(newParam).replace(/'/g, "\\'")])
        }
        delete newParam.required_fields
      }
      // * Bulk Insert Providers, Providers Validations & New Utilities.
      if (validationInsertData.length > 0) await this.insertBulkWithTableName(connection, 'ma_bbps_providers_validations', '(utility_id, provider_id, validations, account_id, provider_name, biller_master_id, action_type, providers_data)', validationInsertData)

      providerdata = providerdata.concat(providersResp.data)

      const update_cron_log = `Update ma_bbps_utility_providers_cron_log set page = ${utilityObj.page}, providers_data = '${JSON.stringify(providersResp.data).replace(/'/g, "\\'")}' where utility_id = ${utilityObj.utility_id} order by ma_bbps_utility_providers_cron_log_id desc limit 1`
      const update_cron_log_resp = await this.rawQuery(update_cron_log, connection)
      log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'update cron log', fields: { update_cron_log, update_cron_log_resp } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'update cron log', fields: { update_cron_log, update_cron_log_resp } })

      if (providersResp.number_of_page == utilityObj.page) return providerdata
      utilityObj.page = utilityObj.page + 1
      await this.getProvidersInBatchWise(utilityObj, connection, utility_query_resp)
      log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'insert Query', fields: { validationInsertData } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'insert Query', fields: { validationInsertData } })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.debug({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'error', fields: error })
      // lokiLogger.error({ pagename: basename(__filename), action: 'getProvidersInBatchWise', type: 'error', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   *
   * @param {JSON Object} data
   * @returns {parse json}
   */
  static parseJson(data) {
    try {
      JSON.parse(data)
    } catch (error) {
      return false
    }
    return true
  }

  static addRupeeInAmount(amount) {
    return
  }
  static async checkSMS(_, fields) {

    const getBillTxnDetailsQuery = `SELECT * from ma_billpay_transaction_master where order_id='${fields.aggregator_order_id}' LIMIT 1`
    const getBillTxnDetails = await this.rawQuery(getBillTxnDetailsQuery)
    console.log("getBillTxnDetails", getBillTxnDetails)

    if (getBillTxnDetails.length > 0) {
      let message = ''
      let template = ''
      if (fields.bill_status == 'S') {
        message = bbpsConfig.BBPS_BILLPAYSUCCESS_SMS
        template = bbpsConfig.BBPS_BILLPAYSUCCESS_SMS_TEMPLATEID
      } else {
        message = bbpsConfig.BBPS_BILLPAYFAILURE_SMS
        template = bbpsConfig.BBPS_BILLPAYFAILURE_SMS_TEMPLATEID
      }
      const billTxnDetail = getBillTxnDetails[0]
      const bank_response = JSON.parse(billTxnDetail.bank_response)

      const dt = new Date(billTxnDetail.updatedon)
      const datetimeformat = dt.toLocaleString('en-IN')
      message = message.replace('<Provider name>', billTxnDetail.provider_name)
      message = message.replace('<amount>', 'Rs'.concat(' ', billTxnDetail.amount))
      message = message.replace('<date time>', datetimeformat)
      message = message.replace('<bConnectId>', bank_response?.response?.response?.bbps_ref_no)

      await sms.sentSmsAsync(message, fields.mobile_number, template)
    }

    return { status: 200 }
  }


}

module.exports = BBPSController
