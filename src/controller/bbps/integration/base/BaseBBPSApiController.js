const path = require('path')
const { default: Axios } = require('axios')
const axios = require('axios')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const errorMsg = require('../../../../util/error')
const log = require('../../../../util/log')
const DAO = require('../../../../lib/dao')
const sms = require('../../../../util/sms')
const util = require('../../../../util/util')
// const { lokiLogger } = require('../../../../util/lokiLogger')

class BaseBBPSApiClass extends DAO {
  static get TABLE_NAME () {
    return 'ma_bbps_api_log'
  }

  static get PRIMARY_KEY () {
    return 'ma_bbps_api_log_id'
  }

  /**
   * @private
   * @description This function is used to make axios api call and log the response in ma_bbps_api_log
   * @param {{ api_name: string, provider_name: string, method: string, url: string, config: { 'Content-Type': string, timeout: number }, data: { [key: string]: string|number } }} requestInfo contains request information for axios call
   * @param {'get'|'post'|'put'|'patch'|'delete'} requestInfo.method define which type of axio call to make GET, POST, etc.
   * @param {string} requestInfo.url define which url to make call to
   * @param {Object} requestInfo.config contains information what kind of format you're calling in and credentials
   * @param {Object} requestInfo.data that is to be sent with the api call
   * @param {Object|null} con connection
  */
  static async doApiCall (requestInfo, con) {
    log.debug({ pagename: path.basename(__filename), action: 'doApiCall', type: 'request Information', fields: requestInfo })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'doApiCall', type: 'request Information', fields: requestInfo })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    const requestParams = { api_name: requestInfo.api_name, url: requestInfo.url || this.URL, data: requestInfo.data, config: requestInfo.config || this.CONFIG, method: requestInfo.method }

    let response
    try {
      switch (requestInfo.method.toLowerCase()) {
        case 'post':
          response = await Axios.post(requestInfo.url || this.URL, requestInfo.data, requestInfo.config || this.CONFIG)
          break
        case 'get':
          response = await Axios.get(requestInfo.url || this.URL, requestInfo.data, requestInfo.config || this.CONFIG)
          break
        case 'put':
          response = await Axios.put(requestInfo.url || this.URL, requestInfo.data, requestInfo.config || this.CONFIG)
          break
        case 'patch':
          response = await Axios.patch(requestInfo.url || this.URL, requestInfo.data, requestInfo.config || this.CONFIG)
          break
        case 'delete':
          response = await Axios.delete(requestInfo.url || this.URL, requestInfo.data, requestInfo.config || this.CONFIG)
          break
        default:
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      log.debug({ pagename: path.basename(__filename), action: 'doApiCall', type: 'response', fields: { data: response.data } })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'doApiCall', type: 'response', fields: { data: response.data } })
      log.logger({
        pagename: path.basename(__filename),
        action: 'doApiCall',
        type: 'response',
        fields: {
          data: {
            request: JSON.stringify(requestParams),
            response: JSON.stringify(response.data)
          }
        }
      })
      // lokiLogger.info({
      //   pagename: path.basename(__filename),
      //   action: 'doApiCall',
      //   type: 'response',
      //   fields: {
      //     data: {
      //       request: JSON.stringify(requestParams),
      //       response: JSON.stringify(response.data)
      //     }
      //   }
      // })

      await this.insert(connection, {
        data: {
          api_name: requestInfo.api_name,
          provider_name: requestInfo.provider_name || this.PROVIDER_NAME,
          request: JSON.stringify(requestParams).replace(/'/gm, '\'').replace(/\\/gm, '\\\\'),
          response: JSON.stringify(response.data).replace(/'/gm, '\'').replace(/\\/gm, '\\\\')
        }
      })

      if (response.status == 200) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], response: response.data }
      return { status: 400, respcode: 1144, message: errorMsg.responseCode[1144] }
    } catch (error) {
      log.debug({ pagename: path.basename(__filename), action: 'doApiCall', type: 'error', fields: error })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'doApiCall', type: 'error', fields: error })

      if (error.response && error.response.data) return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], response: error.response.data }
      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        await this.insert(connection, {
          data: {
            api_name: requestInfo.api_name,
            provider_name: requestInfo.provider_name || this.PROVIDER_NAME,
            request: JSON.stringify(requestParams).replace(/'/gm, '\'').replace(/\\/gm, '\\\\'),
            response: JSON.stringify({ error: 'CONNECTION_TIMEOUT' })
          }
        })
        return { status: 301, respcode: 1145, message: errorMsg.responseCode[1145] }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }
}

module.exports = BaseBBPSApiClass
