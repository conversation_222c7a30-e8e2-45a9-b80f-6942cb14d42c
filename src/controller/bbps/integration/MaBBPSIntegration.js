const parser = require('fast-xml-parser')
const qs = require('qs')
const { basename } = require('path')
const common = require('../../../util/common')
const errorMsg = require('../../../util/error')
const validator = require('../../../util/validator')
const BaseBBPSApiClass = require('./base/BaseBBPSApiController')
const { payIndexchecksum } = require('../../../util/checksum.js')
const log = require('../../../util/log')
const bbpsConfig = require('../bbpsconfig')
// const { lokiLogger } = require('../../../util/lokiLogger.js')

module.exports = class MaBBPSIntegration extends BaseBBPSApiClass {
  static get PROVIDER_NAME () {
    return 'MA'
  }

  static get URL () {
    return bbpsConfig.BBPS_MA_PAYMENTS_URL
  }

  /**
   * Calls ma add biller api
   * <AUTHOR>
   * @param {{
   *  ma_user_id: number,
   *  userid: number,
   *  action: 'register' | 'instapay' | 'recharge' | 'adhoc',
   *  bill_data: {
   *    account_id: string,
   *    mobile_number: string,
   *    fname: string,
   *    lname: string,
   *    email: string,
   *    details: string,
   *    billerid: number,
   *    remark: string,
   *    requestid: string
   *  },
   *  secret: string,
   *  username: string,
   *  password: string,
   *  utility_id: string,
   *  provider_id: string
   * }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, request: string, response: string, bill_response: {
   *  objectid: string,
   *  billid: string,
   *  billstatus: string,
   *  billnumber: string,
   *  billperiod: string,
   *  net_billamount: string,
   *  customer_name: string,
   *  billduedate: string,
   *  billamount: string,
   *  billdate: string
   * }}>}
   */
  static async addBiller (fields) {
    const privatekey = this.generatePrivateKey(fields)

    const request = {
      privatekey,
      mercid: bbpsConfig.BBPS_MERCHANT_DETAILS_MERCID,
      checksum: this.generateChecksum(bbpsConfig.BBPS_MERCHANT_DETAILS_MERCID, privatekey),
      api: 'add_biller',
      returnurl: bbpsConfig.BBPS_MA_RETURN_URL,
      callbackurl: bbpsConfig.BBPS_MA_CALLBACK_URL,
      customerid: fields.ma_user_id,
      action: fields.action,
      action_type: fields.action,
      bill_data: [fields.bill_data],
      utility_id: fields.utility_id,
      provider_id: fields.provider_id
    }

    switch (fields.action) {
      case 'adhoc':
      case 'recharge':
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], request, response: JSON.stringify({ noAPICall: 'action recharge' }) }
      case 'register':
        request.action = 'register'
        break
      case 'instapay':
        request.action = 'INSTA'
    }

    const response = await this.doApiCall({
      url: this.URL + 'bbps/bbps.php',
      api_name: 'add_biller',
      data: request,
      method: 'post'
    })

    delete request.privatekey
    delete request.checksum

    /*
     ! TESTING PURPOSE STATIC RESPONSE. <START>
     * Remove before production merge.
     */
     if (fields.bill_data.mobile_number == '**********' || fields.bill_data.mobile_number == '**********' || fields.bill_data.mobile_number == '**********' || fields.bill_data.mobile_number == '**********' || fields.bill_data.mobile_number == '**********' || fields.bill_data.mobile_number == '9821191280') {
      response.response = {
        STATUS: '200',
        MSG: 'Insta Bills Listed Successfully.',
        BILLS: [{
          objectid: 'bill',
          billid: 'HGA1V0B2720000091285B',
          billstatus: 'UNPAID',
          billnumber: '98500686481',
          billperiod: '2019/01',
          net_billamount: '840.00',
          customer_name: 'SATYABHAMA R K TRIPATHI .',
          billduedate: '30-01-2019',
          billamount: '840.00',
          billdate: '09-01-2019'
        }]
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bill_response: response.response.BILLS[0], request, response: JSON.stringify(response.response) }
    }
    // ! TESTING PURPOSE STATIC RESPONSE. <END>
    if (response.status != 200) return { ...response, request }

    if ((validator.validateField(response.response.status) && response.response.status != 200) || (validator.validateField(response.response.STATUS) && response.response.STATUS != 200)) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + (response.response.msg || response.response.MSG || response.response.MESSAGE || response.response.message), request, response: JSON.stringify(response.response) }
    }

    if (!validator.validateField(response.response.BILLS)) return { status: 400, respcode: 14001, message: errorMsg.responseCode[14001], request, response: JSON.stringify(response.response) }
    if (response.response.BILLS.length <= 0) return { status: 400, respcode: 14000, message: errorMsg.responseCode[14000], request, response: JSON.stringify(response.response) }

    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bill_response: response.response.BILLS[0], request, response: JSON.stringify(response.response) }
  }

  /**
   * Calls MA's pay index API
   * <AUTHOR> Andrew
   * @param {{
   *  ma_user_id: number,
   *  email_id: string,
   *  mobile_id: string,
   *  firstname: string,
   *  lastname: string,
   *  address: string,
   *  city: string,
   *  state: string,
   *  country: string,
   *  pincode: string,
   *  transaction_amount: number,
   *  aggregator_order_id: string,
   *  security_pin: string,
   *  merchant_details: {
   *    mercid: number,
   *    secret: string,
   *    password: string,
   *    username: string
   *  }
   * }} fields
   */
  static async payIndex (fields) {
    const alldata = {
      email_id: fields.email_id,
      firstname: fields.firstname,
      lastname: fields.lastname,
      address: fields.address,
      city: fields.city,
      state: fields.state,
      country: fields.country,
      transaction_amount: (+fields.transaction_amount).toFixed(2),
      aggregator_order_id: fields.aggregator_order_id,
      merchantDet: fields.merchant_details
    }

    const data = {
      privatekey: this.generatePrivateKey(fields.merchant_details),
      checksum: payIndexchecksum(alldata),
      mercid: fields.merchant_details.mercid,
      orderid: fields.aggregator_order_id,
      currency: '356',
      isocurrency: 'INR',
      chmod: 'apcrdt',
      channel: 'apcrdt',
      buyerEmail: fields.email_id,
      buyerPhone: fields.mobile_id,
      buyerFirstName: fields.firstname,
      buyerLastName: fields.lastname,
      buyerAddress: fields.address,
      buyerCity: fields.city,
      buyerState: fields.state,
      buyerCountry: fields.country,
      buyerPinCode: fields.pincode,
      amount: (+fields.transaction_amount).toFixed(2),
      apcrdtpin: fields.security_pin,
      customvar: '',
      mer_dom: Buffer.from('https://apmerchantapp.nowpay.co.in').toString('base64'),
      arpyVer: bbpsConfig.BBPS_MA_PAY_INDEX_VERSION
    }

    return this.doApiCall({
      api_name: 'payindex',
      url: this.URL + 'pay/payindexapi.php',
      data: qs.stringify(data),
      method: 'post'
    })
  }

  /**
   * <AUTHOR> Andrew
   * @param {{
   *  aggregator_order_id: string
   *  merchant_details: {
   *    mercid: number,
   *    secret: string,
   *    password: string,
   *    username: string
   *  }
   * }} fields
   */
  static async orderConfirmation (fields) {
    const data = {
      privatekey: this.generatePrivateKey(fields.merchant_details),
      mercid: fields.merchant_details.mercid,
      merchant_txnId: fields.aggregator_order_id || '',
      checksum: common.createHash((fields.merchant_details.mercid || '') + (fields.aggregator_order_id || '') + new Date().toISOString().split('T')[0]),
      airpayId: ''
    }

    const response = await this.doApiCall({
      api_name: 'order verification',
      url: this.URL + 'order/verify.php',
      data: qs.stringify(data),
      method: 'post',
      config: {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      }
    })

    log.debug({ pagename: basename(__filename), action: 'orderConfirmation', type: 'request', fields: response })
    // lokiLogger.info({ pagename: basename(__filename), action: 'orderConfirmation', type: 'request', fields: response })
    if (response.status !== 200) return { status: 400, respcode: 14007, message: errorMsg.responseCode[14007] + '' + response.status }
    const transactionObj = parser.parse(response.data)
    if (typeof transactionObj !== 'object' && typeof transactionObj.RESPONSE !== 'object') return { status: 400, respcode: 1012, message: errorMsg.responseCode[1012] }

    let transaction_reason = ''
    let transaction_status = ''
    let transaction_status_text = ''

    const rrn = (transactionObj.RESPONSE.TRANSACTION.RRN) ? transactionObj.RESPONSE.TRANSACTION.RRN : ''
    const aggregator_txn_id = (transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID) ? transactionObj.RESPONSE.TRANSACTION.APTRANSACTIONID : 0

    // Update aeps transaction according to transaction status return from order confirmation
    switch (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONSTATUS) {
      case 200:
        // Transaction is success
        transaction_status = 'S'
        transaction_reason = 'Success'
        transaction_status_text = 'Success'
        break
      case 400:
        // Transaction failed
        transaction_status = 'F'
        transaction_reason = (transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON) ? transactionObj.RESPONSE.TRANSACTION.TRANSACTIONREASON : 'Failed'
        transaction_status_text = 'Failed'
        break
      case 201:
      case 211:
      default:
        // Transaction in pending state
        transaction_status = 'P'
        transaction_reason = 'Initiated'
        transaction_status_text = 'Initiated'
    }

    if (!validator.validateField(transaction_status)) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + transactionObj.RESPONSE.TRANSACTION.MESSAGE }

    const updateParams = {
      ma_user_id: fields.ma_user_id,
      orderid: fields.aggregator_order_id,
      aggregator_txn_id: aggregator_txn_id,
      transaction_status: transaction_status,
      amount: fields.transaction_amount,
      rrn: rrn,
      userid: fields.userid,
      transaction_reason
    }

    log.debug({ pagename: basename(__filename), action: 'orderConfirmation', type: 'response', fields: updateParams, transaction_status })
    // lokiLogger.info({ pagename: basename(__filename), action: 'orderConfirmation', type: 'response', fields: updateParams, transaction_status })
    const message = `Your Previous Txn ${fields.aggregator_order_id} is now ${transaction_status_text}`
    return { status: 200, respcode: 1000, message, updateParams, transaction_status, aggregator_txn_id, rrn, transaction_reason, transactionObj }
  }

  /**
   * <AUTHOR> Andrew
   * @param {{
   *  ma_user_id: number
   *  action_type: 'recharge' | 'register' | 'adhoc' | 'instapay',
   *  merchant_details: {
   *    mercid: number,
   *    secret: string,
   *    password: string,
   *    username: string
   *  },
   *  buyerData: {
   *    firstname: string,
   *    lastname: string,
   *    email_id: string
   *  }
   *  amount: number,
   *  aggregator_txn_id: string,
   *  aggregator_order_id: string,
   *  mobile_number: string,
   *  details: string,
   *  BILLER_MASTER_ID: number
   * }} fields
   * @returns {Promise<{ status: number, message: string, respcode: number, bank_response: { message: string, response: any }, addition_details: { parameter_name: string, value: string }[] }>}
   */
  static async makepayment (fields) {
    log.logger({ pagename: basename(__filename), action: 'makepayment', type: 'request', fields })
    // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'request', fields })

    try {
      const postParams = {
        private_key: this.generatePrivateKey(fields.merchant_details),
        checksum: '',
        mercid: fields.mercid,
        callbackurl: bbpsConfig.BBPS_MA_CALLBACK_URL,
        api: 'makePayment',
        airpay_id: fields.aggregator_txn_id,
        amountsum: (+fields.amount).toFixed(2),
        paymentamount_validation: fields.action_type == 'instapay' ? 'N' : 'Y', // recharge case
        action: fields.action_type,
        customerid: fields.ma_user_id,
        payment_type: fields.action_type == 'instapay' ? 'billpay' : 'instapay'
      }

      // billpayment case
      switch (fields.action_type) {
        case 'instapay':
          postParams.makepaymentdata = [
            {
              viewbillresponseid: fields.aggregator_order_id || 0,
              amount: fields.amount,
              billerid: fields.BILLER_MASTER_ID
            }
          ]
          break
        case 'register':
          postParams.makepaymentdata = [
            {
              viewbillresponseid: fields.aggregator_order_id || 0,
              amount: fields.amount,
              billerid: fields.BILLER_MASTER_ID
            }
          ]
          if (fields.bank_account_number && fields.ifsc_number) {
            postParams.makepaymentdata[0].account_number = fields.bank_account_number
            postParams.makepaymentdata[0].ifsc = fields.ifsc_number
            postParams.makepaymentdata[0].txntype = 'netbank'
          }
          break
        default:
          // recharge case
          postParams.makepaymentdata = [
            {
              billerid: fields.BILLER_MASTER_ID,
              rechargeid: '',
              amount: fields.amount,
              circle_name: '',
              circleid: '',
              planid: '',
              plan_category_name: '',
              first_name: fields.buyerData.firstname,
              last_name: fields.buyerData.lastname,
              email: fields.buyerData.email_id,
              account_id: fields.account_id,
              details: fields.details,
              mobile_number: fields.mobile_number
            }
          ]
      }

      // Call make payments api for order confirmation
      const response = await this.doApiCall({
        api_name: 'makepayment',
        method: 'post',
        url: this.URL + 'bbps/bbps.php',
        data: JSON.stringify(postParams),
        config: { headers: { 'Content-Type': 'application/json' } }
      })

      log.logger({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: response })
      // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: response })

      if (response.status !== 200) {
        log.logger({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: { status: response.status } })
        // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: { status: response.status } })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving payment response details', bank_response: { message: 'Server error', response: { status: response.status } }, billerStatus: 'P' }
      }

      // const transactionObj = typeof response.data == 'object' ? response.data : JSON.parse(response.data)

      const transactionObj = JSON.parse(response.response)

      let addition_details = []

      // console.log(transactionObj)
      let billerStatus = 'P'
      if (typeof transactionObj === 'object' && Object.keys(transactionObj).length > 0 && (('200' in transactionObj) || ('400' in transactionObj))) {
        try {
          transactionObj.response = JSON.parse(transactionObj.response)
        } catch (e) {
          log.debug({ pagename: basename(__filename), action: 'makepayment', type: 'transactionObj.response catcherror', fields: e })
          // lokiLogger.error({ pagename: basename(__filename), action: 'makepayment', type: 'transactionObj.response catcherror', fields: e })
        }

        // Adds key to object with parameter_name and value
        addition_details = ((transactionObj.response || {}).additional_info || []).map(ad => ({ ...ad, key: ad.parameter_name }))

        log.debug({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: {} })
        // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: {} })
        if (transactionObj['200']) {
          const message = transactionObj['200']
          if (transactionObj.recharge_status == 'PAID') {
            billerStatus = 'S'
          } else {
            billerStatus = 'P'
          }
          log.debug({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: transactionObj })
          // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: transactionObj })
          return { status: 200, respcode: 1000, message: transactionObj.msg, bank_response: { message: message, response: transactionObj }, billerStatus, addition_details }
        }
        billerStatus = 'P' // immediate failure mark as pending
        const message = transactionObj['400']
        log.debug({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: transactionObj })
        // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: transactionObj })
        return { status: 200, respcode: 1131, message: errorMsg.responseCode[1131] + ': ' + message, bank_response: { message: message, response: transactionObj }, billerStatus, addition_details }
      }
      log.debug({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: { notanobject: transactionObj } })
      // lokiLogger.info({ pagename: basename(__filename), action: 'makepayment', type: 'response', fields: { notanobject: transactionObj } })
      return { status: 400, respcode: 1132, message: errorMsg.responseCode[1132], bank_response: { message: 'Unknown response', response: transactionObj }, billerStatus, addition_details }
    } catch (err) {
      log.debug({ pagename: basename(__filename), action: 'makepayment', type: 'catcherror', fields: { codeerror: err.message } })
      // lokiLogger.error({ pagename: basename(__filename), action: 'makepayment', type: 'catcherror', fields: { codeerror: err.message } })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], bank_response: { codeerror: err.message }, billerStatus: 'P' }
    }
  }

  /**
   * billAvailabilityCheck description - fetch the biller availability ?
   * <AUTHOR> Gupta
   * @param {{ ma_user_id: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number, biller_availability: {BILLER_MASTER_ID: string, OBJECTID: string, BILLERALERTID: string, SOURCEID: string, BILLER_ID: string, BILLERSTATE: string, STARTTIME: string, UPDATETIME: string, ENDTIME: string}[] }>}
   */
  static async billAvailabilityCheck (fields) {
    log.logger({ pagename: 'MaBBPSIntegration.js', action: 'billAvailabilityCheck', type: 'request', fields })
    // lokiLogger.info({ pagename: 'MaBBPSIntegration.js', action: 'billAvailabilityCheck', type: 'request', fields })

    try {
      const privatekey = this.generatePrivateKey(fields)

      const data = {
        privatekey: privatekey,
        mercid: fields.ma_user_id,
        checksum: this.generateChecksum(fields.ma_user_id, privatekey),
        api: 'billerAvailabilityCheck',
        action: 'biller_availability'
      }

      const response = await this.doApiCall({
        url: this.URL + 'bbps/bbps.php',
        api_name: 'billerAvailabilityCheck',
        data: data,
        method: 'post'
      })

      if (response.status != 200 || (response.data && response.data.RESPONSE_MESSAGE == 'false') || (response.data && response.data.MESSAGE && response.data.MESSAGE == 'No bills found')) {
        return { status: 400, respcode: 1002, message: 'No data found', biller_availability: {} }
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], biller_availability: response.response }
    } catch (err) {
      log.logger({ pagename: 'MaBBPSIntegration.js', action: 'billAvailabilityCheck', type: 'err', fields: err })
      // lokiLogger.error({ pagename: 'MaBBPSIntegration.js', action: 'billAvailabilityCheck', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], biller_availability: {} }
    }
  }

  /**
   * Generates Privatekey
   * <AUTHOR> Andrew
   * @param {{ secret: string, username: string, password: string }} fields
   * @returns {string}
   */
  static generatePrivateKey (fields) {
    // Create hash private key for order confirmation api
    const keyData = `${fields.secret}@${fields.username}:|:${fields.password}`

    const privatekey = common.createHash(keyData)
    return privatekey
  }

  /**
   * Generates Checksum
   * <AUTHOR> Andrew
   * @param {number} ma_user_id
   * @param {string} privatekey
   * @returns {string}
   */
  static generateChecksum (ma_user_id, privatekey) {
    return common.createHash(new Date().toISOString().split('T')[0].replace(/-/g, '') + ma_user_id + privatekey)
  }
}
