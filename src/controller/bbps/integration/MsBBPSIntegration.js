const qs = require('qs')
const { basename } = require('path')
const BaseBBPSApiClass = require('./base/BaseBBPSApiController')
const log = require('../../../util/log')
const errorMsg = require('../../../util/error')
const { md5checksum } = require('../../../util/checksum')
const bbpsConfig = require('../bbpsconfig')
// const { lokiLogger } = require('../../../util/lokiLogger')

module.exports = class MsBBPSIntegration extends BaseBBPSApiClass {
  static get PROVIDER_NAME () {
    return 'MS'
  }

  static get URL () {
    return bbpsConfig.BBPS_MS_PAYMENTS_URL
  }

  /**
   * Calls MS getProvider API
   * @param {{ utility_id: number }} fields
   * @returns {Promise<{status: 200, respcode: number, message: string, data: { BILLER_MASTER_ID: string|number, provider_name: string, provider_id: string|number, required_fields: string, fields_validation: RegExp|string, biller_logo: string, biller_bill_copy: null, check_amount_req: string, raw_fields: { [key: string]: { name: string, validation: RegExp|string } }, utility_id: string|number, partial_pay: 'Y'|'N', payment_methods: { payment_method: string, min_limit: number|string, autopay_allowed: 'Y'|'N', paylater_allowed: 'Y'|'N' }[], action_type: 'instapay'|'register'|'recharge'}[]}>}
   */
  static async getProviders (fields) {
    const response = await this.doApiCall({
      method: 'post',
      data: qs.stringify({
        checksum: Buffer.from(fields.utility_id + '' + md5checksum('airpay'), 'utf8').toString('base64'),
        utility_id: fields.utility_id,
        page: fields.page
      }),
      url: this.URL + 'bbps/bbpsapi/get-providers',
      config: { timeout: 60000 }
    })

    log.logger({ pagename: basename(__filename), action: 'getProviders', type: 'providers response', fields: response.response })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getProviders', type: 'providers response', fields: response.response })
    const getProviderObj = typeof (response.response) == 'string' ? JSON.parse(response.response) : response.response
    if (response.status !== 200) {
      if ('msg' in getProviderObj) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + getProviderObj.msg }
      return { status: 400, respcode: 14000, message: errorMsg.responseCode[14000] }
    }
    if (response.status == 200 && getProviderObj.status == 200) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: getProviderObj.data, number_of_records: getProviderObj.number_of_records, number_of_page: getProviderObj.number_of_page }
    return { status: 400, respcode: 14001, message: errorMsg.responseCode[14001] }
  }

  /**
   * Calls MS getUtility API
   */
  static async getUtilities () {
    const response = await this.doApiCall({
      method: 'post',
      data: qs.stringify({
        checksum: Buffer.from('' + md5checksum('airpay'), 'utf8').toString('base64')
      }),
      url: this.URL + 'bbps/bbpsapi/get-utility',
      config: { timeout: 60000 }
    })
    log.debug({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'utility response', fields: response.data })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'utility response', fields: response.data })

    if (response.status !== 200) return

    const getUtilityObj = typeof (response.response) == 'string' ? JSON.parse(response.response) : response.response
    log.debug({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'getBillPaymentUtilityResponseObj', fields: getUtilityObj })
    // lokiLogger.info({ pagename: basename(__filename), action: 'getBillPaymentProviders', type: 'getBillPaymentUtilityResponseObj', fields: getUtilityObj })

    if (!(typeof getUtilityObj === 'object' && ('status' in getUtilityObj))) return
    log.debug({ pagename: require('path').basename(__filename), action: 'getBillPaymentUtility', type: 'response', fields: getUtilityObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBillPaymentUtility', type: 'response', fields: getUtilityObj })
    if (getUtilityObj.status != 200) return
    let utility_data = []
    if (!(('data' in getUtilityObj) && Object.keys(getUtilityObj.data).length > 0)) return
    utility_data = getUtilityObj.data

    // Remove Recharge from BBPS utility
    utility_data = utility_data.filter(function (params) {
      if (params.value != 'Recharge') {
        return params
      }
    })

    return utility_data
  }
}
