const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const path = require('path')
const util = require('../../util/util')
var crypto = require('crypto')
const md5 = require('md5')
const common = require('../../util/common')
// const { lokiLogger } = require('../../util/lokiLogger')

class BankList extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_aeps_bank_down_logs'
  }

  static get PRIMARY_KEY () {
    return 'ma_aeps_bank_down_logs_id'
  }

  static async getBanklist () {
    // await this.checkbankdown()
    // if (1) {
    //   return 200
    // }
    // log.logger({ pagename: require('path').basename(__filename), action: 'getBanklist', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      console.time('Timer_BANK_LIST')
      // eslint-disable-next-line quotes
      const bankstatuscond = ` WHERE bank_status = 'ACTIVE'`
      let sql = ''
      // if (typeof fields.bank_status != 'undefined' && fields.bank_status != null) {
      //   bankstatuscond = ` WHERE bank_status = '${fields.bank_status}'`
      // }
      console.log('bankstatuscond', bankstatuscond)
      const sqlQuery1 = `SELECT
      B.ma_aeps_bank_list_id,
      B.ma_bank_name,
      B.ma_iinno,
      B.bank_status
  FROM
      ma_aeps_bank_list B
  LEFT JOIN
      ma_aeps_popular_banklist C
  ON
      B.ma_bank_name = C.bank_name
  WHERE
      B.bank_status = 'ACTIVE'
  ORDER BY
      C.success_count DESC,
      B.ma_bank_name ASC;`
      // const sqlQuery2 = `SELECT ma_aeps_bank_list_id,ma_bank_name,ma_iinno,bank_status FROM ma_aeps_bank_list ${bankstatuscond} ORDER BY ma_bank_name ASC`
      const sqlQuery2 = `SELECT ma_aeps_bank_list_id,ma_bank_name,ma_iinno,bank_status FROM ma_aeps_bank_list where bank_status = 'ACTIVE'
      ORDER BY FIELD(ma_bank_name,'Indian Bank Erstwhile Allahabad Bank','Bank Of India','Punjab National Bank Erstwhile Oriental Bank Of Commerce', 'Union Bank Of India Erstwhile Corporation Bank Erstwhile Andhra Bank', 'State Bank Of India') DESC`
      const bankListVal = await common.getSystemCodes(this, util.banklistaepscheck, connection)
      console.log('banklistval', bankListVal)
      if (bankListVal == 'Y') {
        // eslint-disable-next-line quotes
        const truncateQuery = `TRUNCATE TABLE ma_aeps_popular_banklist`
        const truncateResult = await this.rawQuery(truncateQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getBankid', type: 'response', fields: truncateResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBankid', type: 'response', fields: truncateResult })
        const getLogsQuery = `SELECT bank_id, COUNT(*) AS success_count FROM ma_aeps_transaction_logs
          WHERE ma_status = 'S' AND aeps_mode ='CW'
          AND addedon >= DATE_SUB(CURDATE(), INTERVAL 2 HOUR)
          GROUP BY bank_id
          ORDER BY success_count DESC
          LIMIT 10;`
        const getLogsQueryResult = await this.rawQuery(getLogsQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getBankid', type: 'response', fields: getLogsQueryResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBankid', type: 'response', fields: getLogsQueryResult })
        // const bankid = getLogsQueryResult[0].bank_id;
        // const successCount = getLogsQueryResult[0].success_count;
        const bankIds = getLogsQueryResult.map(result => result.bank_id)
        if (bankIds == '' || bankIds == null) {
          const sqlQuery3 = `SELECT ma_aeps_bank_list_id,ma_bank_name,ma_iinno,bank_status FROM ma_aeps_bank_list ${bankstatuscond} ORDER BY ma_bank_name ASC`
          const sqlQuery3Result = await this.rawQuery(sqlQuery3, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'getBankid', type: 'response', fields: sqlQuery3Result })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBankid', type: 'response', fields: sqlQuery3Result })
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], aeps_bank_list: sqlQuery3Result }
        }
        const getBankNameQuery = `SELECT ma_iinno, ma_bank_name
      FROM ma_aeps_bank_list
      WHERE ma_iinno IN (${bankIds.join(', ')});`
        const getBankNameResult = await this.rawQuery(getBankNameQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getBanklist', type: 'response', fields: getBankNameResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanklist', type: 'response', fields: getBankNameResult })
        // const insertDataQuery = `INSERT INTO ma_aeps_popular_banklist (bank_name, success_count,addedon,updatedon) VALUES ('${bankName}', '${successCount}',now(),now())`;
        for (let i = 0; i < getBankNameResult.length; i++) {
          const bankName = getBankNameResult[i].ma_bank_name
          const successCount = getLogsQueryResult[i].success_count
          const insertQuery = `INSERT INTO  ma_aeps_popular_banklist(bank_name,success_count,addedon,updatedon)VALUES ('${bankName}','${successCount}',now(),now());`
          const LogResult = await this.rawQuery(insertQuery, connection)
        }
        sql = sqlQuery1
      } else {
        sql = sqlQuery2
      }
      console.log('---------------- sql ---------------', sql)
      const result = await this.rawQuery(sql, connection)
      console.log('result', result)
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanklist', type: 'response', fields: result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanklist', type: 'response', fields: result })
      const failureSql = 'Select distinct(bank_id) from ma_aeps_bank_down_logs WHERE addedon BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 15 MINUTE) AND DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 0 MINUTE);'
      log.logger({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'failureSql', fields: failureSql })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'failureSql', fields: failureSql })
      const failureSqlData = await this.rawQuery(failureSql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'failureSqlData', fields: failureSqlData })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'failureSqlData', fields: failureSqlData })
      // Map array for bank_id
      const failureData = failureSqlData.map(x => x.bank_id)
      console.log('Data:', failureData)
      // Map two arrays with bank_id condition
      const finalList = result.map(bank => {
        let network_status = 'UP'

        if (failureData.includes(bank.ma_iinno)) {
          network_status = 'DOWN'
        }

        return { ...bank, network_status }
      })
      console.timeEnd('Timer_BANK_LIST')
      if (result.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], aeps_bank_list: finalList }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      // const sql = `SELECT ma_aeps_bank_list_id,ma_bank_name,ma_iinno,bank_status FROM ma_aeps_bank_list ${bankstatuscond} ORDER BY ma_bank_name ASC`
      // const sql = `SELECT B.ma_aeps_bank_list_id,B.ma_bank_name,B.ma_iinno,B.bank_status,
      // IFNULL(C.success_count, 0) AS success_count
      // FROM ma_aeps_bank_list B
      // LEFT JOIN (
      //       SELECT bank_id, COUNT(*) AS success_count
      //       FROM ma_aeps_transaction_logs
      //     WHERE ma_status = 'S' AND aeps_mode ='CW'
      //         AND addedon >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
      //         GROUP BY bank_id
      //       ORDER BY success_count DESC
      //       LIMIT 10
      //   ) C ON B.ma_iinno = C.bank_id
      //   WHERE B.bank_status = 'ACTIVE'
      //   ORDER BY
      //       success_count DESC,
      //       B.ma_bank_name ASC`;
      // //console.log('aepsBankList', sql);
      // const result = await this.rawQuery(sql, connection)
      // log.logger({ pagename: require('path').basename(__filename), action: 'getBanklist', type: 'response', fields: result })
      // Fetch failure data for last 15  mins
      // const failureSql = 'Select distinct(bank_id) from ma_aeps_bank_down_logs WHERE addedon BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 15 MINUTE) AND DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 0 MINUTE);'
      // log.logger({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'failureSql', fields: failureSql })
      // const failureSqlData = await this.rawQuery(failureSql, connection)
      // log.logger({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'failureSqlData', fields: failureSqlData })
      // // Map array for bank_id
      // const failureData = failureSqlData.map(x => x.bank_id)
      // console.log('Data:', failureData)
      // // Map two arrays with bank_id condition
      // const finalList = result.map(bank => {
      //   let network_status = 'UP'

      //   if (failureData.includes(bank.ma_iinno)) {
      //     network_status = 'DOWN'
      //   }

      //   return { ...bank, network_status }
      // })
      // console.timeEnd('Timer_BANK_LIST')
      // if (result.length > 0) {
      //   return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], aeps_bank_list: finalList }
      // } else {
      //   return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      // }
    } catch (err) {
      console.log('getBanklistError>>', err)
      log.logger({ pagename: 'getBanklist.js', action: 'getBanklist', type: 'error', fields: err })
      // lokiLogger.error({ pagename: 'getBanklist.js', action: 'getBanklist', type: 'error', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + err }
    } finally {
      connection.release()
    }
  }

  // Disclaimers for Aeps
  static async getBankListDisclaimer (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'length', fields: fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'length', fields: fields })
    try {
      const connection = await mySQLWrapper.getConnectionFromPool()
      const disclaimerEnglish = util.communication.AEPSDISCLAIMER
      const disclaimerHindi = util.communication.AEPSDISCLAIMERHINDI
      let disclaimer_english, disclaimer_hindi
      const priorityBankNameSql = 'SELECT bank_name FROM ma_aeps_bank_master WHERE active_status = "A" ORDER BY priority ASC limit 1'
      const priorityBankNameData = await this.rawQuery(priorityBankNameSql, connection)
      console.log('Priority Bank:', priorityBankNameData)
      if (priorityBankNameData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      if (fields.partner_bank_name == '' || fields.partner_bank_name == 'undefined') {
        disclaimer_english = disclaimerEnglish.replace(/<Bank Name>/g, priorityBankNameData[0].bank_name)
        disclaimer_hindi = disclaimerHindi.replace(/<Bank Name>/g, priorityBankNameData[0].bank_name)
        log.logger({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'getBankListDisclaimer', fields: { disclaimer_english, disclaimer_hindi } })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'getBankListDisclaimer', fields: { disclaimer_english, disclaimer_hindi } })
      } else {
        disclaimer_english = disclaimerEnglish.replace(/<Bank Name>/g, fields.partner_bank_name)
        disclaimer_hindi = disclaimerHindi.replace(/<Bank Name>/g, fields.partner_bank_name)
        log.logger({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'getBankListDisclaimer', fields: { disclaimer_english, disclaimer_hindi } })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'getBankListDisclaimer', fields: { disclaimer_english, disclaimer_hindi } })
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], disclaimer_english, disclaimer_hindi }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'getBankListDisclaimer', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /** Internal Function
   * generateRamdomId description
   * @param {{ requestData: String }} fields
   * @returns {Promise < ecryptedData: String >}
   */
  static async generateRandomId (length) {
    log.logger({ pagename: path.basename(__filename), action: 'generateRamdomId', type: 'length', fields: length })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'generateRamdomId', type: 'length', fields: length })
    try {
      let id = ''
      const characters = '0123456789abcdefghijklmnopqrstuvwxyz'
      for (let i = 0; i < length; i++) id += characters.charAt(Math.floor(Math.random() * characters.length))
      log.logger({ pagename: path.basename(__filename), action: 'generateRamdomId', type: 'id', fields: id })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'generateRamdomId', type: 'id', fields: id })
      return id
    } catch (axiosErr) {
      log.logger({ pagename: path.basename(__filename), action: 'generateRamdomId', type: 'axios catcherror', fields: axiosErr })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'generateRamdomId', type: 'axios catcherror', fields: axiosErr })
      return ''
    }
  }

  // Cron for AEPS bank down notification
  static async checkbankdown () {
    log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'request', fields: 'request' })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'request', fields: 'request' })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Fetch code val from system_codes table to check the threshold.
      const systemCodeSql = `SELECT code_val FROM system_codes WHERE code_id = ${util.bank_failure_threshold.code} limit 1`
      log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'systemCodeSql', fields: systemCodeSql })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'systemCodeSql', fields: systemCodeSql })
      const systemCodeSqlData = await this.rawQuery(systemCodeSql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'systemCodeSqlData', fields: systemCodeSqlData })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'systemCodeSqlData', fields: systemCodeSqlData })
      if (systemCodeSqlData.length < 0) return { status: 400, respcode: 1001, message: 'No record found in system_code Tbl' }
      // Fetch data from ma_aeps_transaction_logs tbl to find the total count of failed transaction having bank down responsecode.
      // Fetch failed txn data for against each bankid
      const bankWiseFailureSql = `SELECT
      count(matl.bank_id) as failure_count,
      matl.bank_id,
      mabl.ma_bank_name,
      mabl.bank_status
    FROM
      ma_aeps_transaction_logs matl
    JOIN ma_aeps_bank_list mabl ON
      matl.bank_id = mabl.ma_iinno
    WHERE
      matl.bank_response_code in ('08','12','0008','0U28','0028')
      and matl.aeps_mode = 'CW'
      and matl.ma_status = 'F'
      and matl.addedon BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 15 MINUTE) AND DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 0 MINUTE)
    GROUP BY
      matl.bank_id`
      log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'bankWiseFailureSql', fields: bankWiseFailureSql })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'bankWiseFailureSql', fields: bankWiseFailureSql })
      const bankWiseFailureSqlData = await this.rawQuery(bankWiseFailureSql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'bankWiseFailureSqlData', fields: bankWiseFailureSqlData })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'bankWiseFailureSqlData', fields: bankWiseFailureSqlData })
      // Check for records greater than 0
      if (bankWiseFailureSqlData.length <= 0) return { status: 400, respcode: 1002, message: `${errorMsg.responseCode[1002]}` }

      const totalFailedTxnCount = bankWiseFailureSqlData.reduce((acc, val) => acc + parseInt(val.failure_count), 0)
      log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'totalFailedTxnCount', fields: totalFailedTxnCount })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'totalFailedTxnCount', fields: totalFailedTxnCount })
      // console.log('TOTAL COUNT', totalFailedTxnCount)
      // **** calculatePercentage() ****
      // Calculate percentage base on fail txn and Total failed txn wrt to bank_id
      const calculatePercentage = await this.calculatePercentage(systemCodeSqlData[0].code_val, totalFailedTxnCount, bankWiseFailureSqlData, connection)
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'checkbankdown', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  // Calculate percentage
  static async calculatePercentage (systemCodeSqlData, totalFailedTxnCount, bankWiseFailureSqlData, con) {
    log.logger({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'request', fields: totalFailedTxnCount, bankWiseFailureSqlData })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'request', fields: totalFailedTxnCount, bankWiseFailureSqlData })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('Threshold:', systemCodeSqlData)
      // Map the array
      const map1 = bankWiseFailureSqlData.map(x => ({ ...x, failurePercentage: parseInt(x.failure_count) / parseInt(totalFailedTxnCount) * 100 }))
      console.log('Mapped array:', map1)
      // Filter the array : Above threshold
      var aboveThreshold = map1.filter(function (el) {
        return el.failurePercentage > systemCodeSqlData
      }
      )
      console.log('Above threshold:', aboveThreshold)
      const random_id = md5(new Date()) + await this.generateRandomId(4)

      const insert_data = []
      const aboveThresholdLen = aboveThreshold.length
      for (let index = 0; index < aboveThresholdLen; index++) {
        const aboveThresholdData = aboveThreshold[index]
        console.log('Test:', aboveThresholdData)
        insert_data.push(`('${random_id}','${aboveThresholdData.ma_bank_name}',${aboveThresholdData.bank_id}, '${aboveThresholdData.failurePercentage}', ${aboveThresholdData.failure_count},${systemCodeSqlData})`)
      }
      // Insert bulk data in ma_aeps_bank_down_logs
      const insertbFailedCasesResult = await this.insertBulk(con, { fields: '(`session_id`,`bank_name`,`bank_id`,`ratio`,`failure_count`,`threshold`)', data: insert_data.join(',') })
      console.log(insertbFailedCasesResult)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'calculatePercentage', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }
}

module.exports = BankList
