const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')

class Blacklisting extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_blacklisting'
  }

  static get PRIMARY_KEY () {
    return 'ma_blacklisting_id'
  }

  /**
   * Blacklisted values will be checked here
   * @param {fields} combinedFields
   * account_number, mobilenumber, PAN, AADHAR number
   */
  static async checkBlackListing (fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    const sql = `SELECT * FROM ma_blacklisted_values 
                WHERE (blacklisted_value = '${fields.account_number}' AND value_type='ACCNO')
                OR (blacklisted_value = '${fields.mobile_number}' AND value_type='MOBNO')`
    const blacklist = await this.rawQuery(sql, connection)
    console.log('Found Blacklisted Values', blacklist)
    connection.release()
    if (blacklist !== undefined && blacklist !== null && blacklist.length > 0) {
      return true
    }
    return false
  }
}

module.exports = Blacklisting
