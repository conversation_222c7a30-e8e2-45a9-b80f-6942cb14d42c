const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const crypto = require('crypto')
const util = require('../../util/util')
const jwt = require('jsonwebtoken')
const bcrypt = require('bcryptjs')
// const { lokiLogger } = require('../../util/lokiLogger')

class auth extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_api_user_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_api_user_master_id'
  }

  static async authenticate (_, fields, airpaykey) {
    log.logger({ pagename: require('path').basename(__filename), action: 'authenticate', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'authenticate', type: 'request', fields: fields })
    fields = JSON.parse(fields)
    fields.ma_user_id = fields.ma_user_id || fields.merchant_id
    fields.user_id = fields.user_id || fields.client_id
    console.log('fields after parse', fields)
    console.log('mauserid', fields.ma_user_id)
    if (fields.ma_user_id == null || fields.ma_user_id == undefined || fields.ma_user_id == '') {
      return { status: 400, respcode: 1028, message: 'Merchant UserId is mandatory' }
    }
    if (fields.password == null || fields.password == undefined || fields.password == '') {
      return { status: 400, respcode: 1028, message: 'Password is mandatory' }
    }
    if (fields.purpose == null || fields.purpose == undefined || fields.purpose == '') {
      return { status: 400, respcode: 1028, message: 'Purpose is mandatory' }
    }
    if (fields.checksum == null || fields.checksum == undefined || fields.checksum == '') {
      return { status: 400, respcode: 1028, message: 'Checksum is mandatory' }
    }
    const stringHere = fields.ma_user_id + fields.password + fields.purpose + airpaykey.checksumkey
    const generatedchecksum = await crypto.createHash('md5').update(stringHere).digest('hex')
    if (fields.checksum != generatedchecksum) {
      return { status: 400, respcode: 1028, message: 'Checksum Mismatch' }
    }
    if (fields.purpose != airpaykey.purpose) {
      return { status: 400, respcode: 1028, message: 'Invalid Purpose' }
    }
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const password = Buffer.from(fields.password, 'base64').toString('ascii')
      const sqlauth = `SELECT * from ma_api_user_master where ma_user_id = '${fields.ma_user_id}'  AND purpose = '${fields.purpose}'`

      const dataauth = await this.rawQuery(sqlauth, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'authenticate', type: 'query-res', fields: dataauth })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'authenticate', type: 'query-res', fields: dataauth })
      if (dataauth.length > 0) {
        const passwordMatch = await bcrypt.compareSync(password, dataauth[0].password)
        if (passwordMatch == true) {
          const profileid = dataauth[0].ma_user_id
          const userid = dataauth[0].userid
          const statuscode = 200
          const token = await this.signToken(userid, profileid)
          this.TABLE_NAME = 'ma_user_authentication'
          await this.insert(connection, { data: { jwt_token: token, user_id: userid } })
          var checksumstr = statuscode + '' + profileid + '' + userid + '' + airpaykey.checksumkey
          const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
          return { status: statuscode, status_code: 1000, message: errorMsg.responseCode[1000], ma_user_id: profileid, userid: userid, accessToken: token, checksum: checksum }
        } else {
          return { status: 400, respcode: 1028, message: 'Fail: Invalid Password' }
        }
      } else {
        return { status: 400, respcode: 1028, message: 'Fail: Invalid User' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'authenticate', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'authenticate', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
    } finally {
      connection.release()
    }
  }

  static async signToken (publickey, profileid) {
    return jwt.sign({ publickey, profileid }, util.jwtKey, {
      algorithm: 'HS256',
      expiresIn: util.jwtApiExpirySeconds
    })
  }

  static async confirmorder (_, fields, airpaykey) {
    log.logger({ pagename: require('path').basename(__filename), action: 'confirmorder', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'confirmorder', type: 'request', fields: fields })
    fields = JSON.parse(fields)
    if (fields.txn_referenceid == null || fields.txn_referenceid == undefined || fields.txn_referenceid == '') {
      return { status: 400, respcode: 1028, message: 'Transaction Reference Id is mandatory' }
    }
    if (fields.purpose == null || fields.purpose == undefined || fields.purpose == '') {
      return { status: 400, respcode: 1028, message: 'Purpose is mandatory' }
    }
    if (fields.checksum == null || fields.checksum == undefined || fields.checksum == '') {
      return { status: 400, respcode: 1028, message: 'Checksum is mandatory' }
    }
    const stringHere = fields.txn_referenceid + fields.purpose + airpaykey.checksumkey
    const generatedchecksum = await crypto.createHash('md5').update(stringHere).digest('hex')
    if (fields.checksum != generatedchecksum) {
      return { status: 400, respcode: 1028, message: 'Checksum Mismatch' }
    }
    if (fields.purpose != airpaykey.purpose) {
      return { status: 400, respcode: 1028, message: 'Invalid Purpose' }
    }
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sqlorder = `select a.transaction_status,a.aggregator_order_id,a.transaction_id,a.amount,b.bank_name,a.addedon,c.aggregator_user_id from  ma_transaction_master a,ma_transaction_master_details b,users c where a.ma_transaction_master_id = b.ma_transaction_master_id AND a.userid = c.id AND a.transaction_id =  '${fields.txn_referenceid}'`
      const dataorder = await this.rawQuery(sqlorder, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'confirmorder', type: 'query-res', fields: dataorder })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'confirmorder', type: 'query-res', fields: dataorder })
      if (dataorder.length > 0) {
        var status = ''
        var status_code = ''
        var message = ''
        if (dataorder[0].transaction_status == 'S') {
          status = 200
          status_code = 1000
          message = errorMsg.responseCode[1000]
        } else if (dataorder[0].transaction_status == 'P') {
          status = 200
          status_code = 1170
          message = errorMsg.responseCode[1170]
        } else {
          status = 400
          status_code = 1028
          message = errorMsg.responseCode[1028]
        }
        var checksumstr = dataorder[0].transaction_id + '' + dataorder[0].aggregator_order_id + '' + airpaykey.checksumkey
        const checksum = await crypto.createHash('md5').update(checksumstr).digest('hex')
        return { status: status, status_code: status_code, message: errorMsg.responseCode[1000], airpay_txn_id: dataorder[0].aggregator_order_id, txn_referenceid: dataorder[0].transaction_id, SSOID: dataorder[0].aggregator_user_id, amount: dataorder[0].amount, txn_datetime: dataorder[0].addedon, bank_name: dataorder[0].bank_name, checksum: checksum }
      } else {
        return { status: 400, respcode: 1028, message: 'Record Not Found' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'confirmorder', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'confirmorder', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
    } finally {
      connection.release()
    }
  }
}

module.exports = auth
