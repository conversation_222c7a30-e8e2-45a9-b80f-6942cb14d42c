const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class commissionReportController extends DAO {
  get TABLE_NAME () {
    return 'ma_orderwise_taxes'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_id'
  }

  static async getCommissionReport (_, fields) {
    log.logger({ pagename: 'commissionReportController.js', action: 'getCommissionReport', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT 
  mot.ma_user_id,
  mum.company,
  mum.user_type,
  mum.firstname,
  mum.lastname,
  mttm.display_name,
  mot.orderid,
  mtm.amount AS transaction_amount,
  mot.amount AS tax_amount,
  mot.tds_amount,
  mot.ma_status,
  DATE_FORMAT(mot.addedon,'%d-%m-%Y %h:%i:%s %p') AS addedon
  FROM ma_orderwise_taxes mot
  LEFT JOIN ma_user_master mum ON mot.ma_user_id = mum.profileid
  LEFT JOIN ma_transaction_master mtm ON mtm.transaction_id = mot.orderid
  LEFT JOIN ma_transaction_type_master mttm ON mttm.transaction_type = mot.transaction_type
  WHERE mot.addedon BETWEEN '${fields.start_date} 00:00:01' AND '${fields.end_date} 23:59:59'
  AND mot.orderid = '${fields.orderid}'
  AND mot.transaction_type = '${fields.transaction_type}'
  AND mot.ma_user_id = '${fields.ma_user_id}'`

      const commissionReport = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getCommissionReport', type: 'request', commissionReport })
      const commissionReponse = commissionReport.map(item => ({
        ma_user_id: item.ma_user_id,
        company: item.company,
        user_type: item.user_type,
        merchant_name: item.firstname + ' ' + item.lastname,
        transaction_type: item.display_name,
        order_id: item.orderid,
        transaction_amount: item.transaction_amount,
        amount: item.tax_amount,
        tds_amount: item.tds_amount,
        tds_percent: '',
        specific_person: '',
        record_status: item.ma_status,
        addedon: item.addedon
      }))

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, commission_report: commissionReponse }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCommissionReport', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (connection) {
        connection.release()
      }
    }
  }
}
module.exports = commissionReportController
