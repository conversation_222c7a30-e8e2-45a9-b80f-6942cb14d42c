const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
// const sms = require('../../util/sms')
// const commissionController = require('../commission/commissionController')
// const pointsRateController = require('../pointsRate/pointsRateController')
// const transactionController = require('../transaction/transactionController')
// const pointsLedgerController = require('../creditDebit/pointsLedgerController')
// const pointsAccountController = require('../creditDebit/pointsAccountController')
// const incentiveController = require('../incentive/incentiveController')
// const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const log = require('../../util/log')
// const { lokiLogger } = require('../../util/lokiLogger')
// const common = require('../../util/common')
// const billpayProviderCtrl = require('../billpayProvider/billpayProviderController')
// const validator = require('../../util/validator')
// const Billpay = require('../billpay/billpay')

class BillpayTransaction extends DAO {
  static get TABLE_NAME () {
    return 'ma_billpay_transaction_master'
  }

  static get PRIMARY_KEY () {
    return 'ma_billpay_transactionid'
  }

  // static async generateRequestNumber (code, uniqueId) {
  //   let prefix = 'D'
  //   let requestNumber = ''
  //   const env = process.env.NODE_ENV
  //   if (typeof (env) !== 'undefined' && env != null && env !== '') {
  //     if (env === 'production') {
  //       prefix = 'P'
  //     } else if (env === 'staging') {
  //       prefix = 'S'
  //     }
  //   }
  //   requestNumber = prefix + code + uniqueId
  //   requestNumber = requestNumber.toUpperCase()
  //   return requestNumber
  // }

  static async createEntry (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })

    try {
      const transactionFields = {
        order_id: fields.aggregator_order_id,
        ma_transaction_master_id: fields.ma_transaction_master_id,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        amount: fields.amount,
        consent_flag: fields.consent_flag,
        transaction_status: fields.transaction_status,
        customer_mobile_number: fields.mobile_number,
        utility_name: fields.utility_name,
        utility_id: fields.utility_id,
        provider_id: fields.provider_id,
        provider_name: fields.provider_name,
        action_type: fields.action_type,
        mer_dtls: typeof (fields.mer_dtls) == 'object' ? JSON.stringify(fields.mer_dtls) : fields.mer_dtls,
        makepayment_request_id: fields.makepayment_request_id,
        invoice_id: fields.invoice_id ? fields.invoice_id : 0,
        payment_status: 'I',
        payment_response: typeof (fields.payment_response) == 'object' ? fields.payment_response : '{}',
        bank_response: typeof (fields.bank_response) == 'object' ? fields.bank_response : '{}'
      }

      const _result = await this.insert(fields.connection, {
        data: transactionFields
      })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_billpay_transactionid: _result.insertId }
    } catch (err) {
      console.log(err)
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async updateWhereData (connection, { data, id, where }) {
    if (this.TABLE_NAME != 'ma_billpay_transaction_master') {
      this.TABLE_NAME = 'ma_billpay_transaction_master'
    }
    const updateQueue = await this.updateWhere(connection, { data, id: id, where: where })
    return updateQueue
  }

  // static async initiateBillpayTransaction (_, fields) {
  //   log.logger({ pagename: require('path').basename(__filename), action: 'initiateBillpayTransaction', type: 'request', fields: fields })
  //   const connection = await mySQLWrapper.getConnectionFromPool()
  //   try {
  //     const validate = validator.validateMobile(fields.customer_mobile_number, 'IN')
  //     if (validate.status === false) {
  //       return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033] }
  //     }

  //     // Decimal amount is not allowed
  //     if (fields.amount % 1 !== 0) {
  //       return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid amount, Decimal not allowed' }
  //     }

  //     const minimumAmount = await common.getSystemCodes(this, util.billpay.minimum_recharge, connection)
  //     console.log('minimumAmount', minimumAmount)

  //     if (fields.amount < parseInt(minimumAmount)) {
  //       return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Minimum amount ' + minimumAmount + ' required' }
  //     }

  //     const maximumAmount = await common.getSystemCodes(this, util.billpay.maximum_recharge, connection)
  //     console.log('maximumAmount', maximumAmount)

  //     if (fields.amount > parseInt(maximumAmount)) {
  //       return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Maximum amount ' + maximumAmount + ' allowed ' }
  //     }

  //     const provideDetailsArr = await billpayProviderCtrl.getProviderDetails(fields, connection)
  //     if (provideDetailsArr.length === 0) {
  //       return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid provider provided ' }
  //     }

  //     const sufficentBalanceResponse = await billpayProviderCtrl.checkBalanceForRecharge(null, {
  //       connection: connection,
  //       ma_billpay_provider_id: fields.ma_billpay_provider_id,
  //       amount: fields.amount
  //     })
  //     if (sufficentBalanceResponse.status === 400) {
  //       return sufficentBalanceResponse
  //     }

  //     await mySQLWrapper.beginTransaction(connection)

  //     const commissionVal = 0
  //     // Points rate
  //     let pointsFactor = 1
  //     const pointsFactorData = await pointsRateController.getGlobalPointsRate(_, { connection })
  //     if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
  //       pointsFactor = pointsFactorData.points_value
  //     }

  //     // Create transaction with I status
  //     const transferFields = {
  //       ma_user_id: fields.ma_user_id,
  //       userid: fields.userid,
  //       amount: fields.amount,
  //       aggregator_order_id: fields.aggregator_order_id,
  //       transaction_id: fields.aggregator_order_id,
  //       commission_amount: commissionVal,
  //       points_factor: pointsFactor,
  //       transaction_status: 'I',
  //       transaction_type: '15',
  //       mobile_number: fields.customer_mobile_number
  //     }

  //     const transaction = await transactionController.createTransaction(_, transferFields, connection)
  //     if (transaction.status === 400) {
  //       await mySQLWrapper.rollback(connection)
  //       return transaction
  //     }

  //     fields.ma_transaction_master_id = transaction.transaction_id
  //     fields.connection = connection

  //     console.log('provideDetailsArr', provideDetailsArr)
  //     fields.ma_billpay_gateway_id = provideDetailsArr[0].ma_billpay_gateway_id
  //     fields.ma_billpay_provider_id = provideDetailsArr[0].ma_billpay_provider_id
  //     fields.ma_billpay_appid = provideDetailsArr[0].ma_billpay_appid
  //     fields.shortname = provideDetailsArr[0].shortname

  //     const transactionEntry = await this.createEntry(_, fields)
  //     if (transactionEntry.status === 400) {
  //       // ----- SEND TRANSFER FAILURE MESSAGE
  //       await mySQLWrapper.rollback(connection)
  //       return transactionEntry
  //     }

  //     console.log('.........................Committing Finally............')
  //     await mySQLWrapper.commit(connection)

  //     const response = { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, order_id: fields.aggregator_order_id, ma_transaction_master_id: fields.ma_transaction_master_id }

  //     return response
  //   } catch (err) {
  //     // console.log('Here catched', err)
  //     log.logger({ pagename: require('path').basename(__filename), action: 'initiateBillpayTransaction', type: 'request', fields: err })
  //     return { status: 400, message: errorMsg.responseCode[1001] + err, respcode: 1001 }
  //   } finally {
  //     connection.release()
  //   }
  // }

  // static async doRecharge (_, fields) {
  //   log.logger({ pagename: require('path').basename(__filename), action: 'doRecharge', type: 'request', fields: fields })
  //   var isSet = false
  //   try {
  //     if (fields.transaction_status !== 'S') {
  //       return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' Transacton is not success' }
  //     }

  //     // Create connection if not passed
  //     if (fields.connection === null || fields.connection === undefined) {
  //       fields.connection = await mySQLWrapper.getConnectionFromPool()
  //       isSet = true
  //       // await mySQLWrapper.beginTransaction(fields.connection)
  //     }
  //     const sql = `SELECT tm.amount,tm.ma_transaction_master_id,tm.ma_user_id,tm.userid,
  //     btm.customer_mobile_number,tm.transaction_status,
  //     btm.billpay_transaction_id,btm.ma_billpay_gateway_id,btm.ma_billpay_provider_id,
  //     btm.ma_billpay_transactionid
  //     FROM ma_transaction_master as tm
  //     JOIN ma_billpay_transaction_master as btm on btm.ma_transaction_master_id = tm.ma_transaction_master_id
  //     where tm.aggregator_order_id='${fields.orderid}'
  //     AND tm.transaction_status IN('I') AND tm.transaction_type = '15'`
  //     const txndetails = await this.rawQuery(sql, fields.connection)

  //     console.log('txndetailssql', sql)
  //     console.log('txndetails', txndetails)
  //     // Return error if transaction not found
  //     if (txndetails.length <= 0) {
  //       // if (isSet) await mySQLWrapper.rollback(fields.connection)
  //       return { status: 400, respcode: 1020, message: errorMsg.responseCode[1020] }
  //     }
  //     log.logger({ pagename: require('path').basename(__filename), action: 'doRecharge', type: 'response', fields: txndetails })

  //     // If airpay id is not provided
  //     if (fields.aggregator_txn_id === undefined || fields.aggregator_txn_id === null || !(fields.aggregator_txn_id > 0)) {
  //       // if (isSet) await mySQLWrapper.rollback(fields.connection)
  //       return { status: 400, respcode: 1021, message: errorMsg.responseCode[1021] }
  //     }

  //     // If amount mismatches
  //     if (fields.amount != txndetails[0].amount) {
  //       // if (isSet) await mySQLWrapper.rollback(fields.connection)
  //       return { status: 400, respcode: 1015, message: errorMsg.responseCode[1015] }
  //     }

  //     if (txndetails[0].transaction_status === 'I') {
  //       const updateRes = await transactionController.updateTransaction(_, {
  //         aggregator_order_id: fields.orderid,
  //         aggregator_txn_id: fields.aggregator_txn_id,
  //         transaction_status: 'P',
  //         amount: fields.amount,
  //         bank_rrn: fields.rrn,
  //         connection: fields.connection
  //       })

  //       if (updateRes.status === 200) {
  //         const sql = `
  //         SELECT pm.*,gm.*,am.*
  //         FROM ma_billpay_provider_master as pm
  //         JOIN ma_billpay_gateway_master as gm on gm.ma_billpay_gateway_id = pm.ma_billpay_gateway_id AND  gm.ma_billpay_appid = pm.ma_billpay_appid
  //         JOIN ma_billpay_app_master as am on am.ma_billpay_appid = gm.ma_billpay_appid
  //         WHERE pm.provider_status = 'Y' AND gm.gateway_status = 'Y' AND am.app_status = 'Y'
  //         AND pm.ma_billpay_provider_id = ${txndetails[0].ma_billpay_provider_id} limit 1
  //         `
  //         const gatewayDetails = await this.rawQuery(sql, fields.connection)
  //         console.log('gatewayDetails', gatewayDetails)
  //         if (gatewayDetails.length > 0) {
  //           const currentGateway = gatewayDetails[0]
  //           const currentUser = txndetails[0]
  //           console.log('currentUser', currentUser)
  //           let message = ''
  //           const shortname = currentGateway.shortname

  //           const checkBalanceObj = new Billpay(shortname, fields.connection)
  //           console.log('checkBalanceObj', checkBalanceObj)

  //           const apiPayload = {
  //             ma_billpay_transactionid: currentUser.ma_billpay_transactionid,
  //             api_token: currentGateway.api_token,
  //             customer_mobile_number: currentUser.customer_mobile_number,
  //             operator_code: currentGateway.operator_code,
  //             amount: currentUser.amount,
  //             gateway_user_id: currentGateway.gateway_user_id,
  //             billpay_transaction_id: currentUser.billpay_transaction_id
  //           }

  //           const resultRechargeResponse = await checkBalanceObj.requestToProvider('INIT_TRANSACTION', apiPayload)
  //           console.log('resultRechargeResponse', resultRechargeResponse)

  //           if (resultRechargeResponse.status === 200) {
  //             const updateTrfRequestData = {
  //               billpay_ref_id: resultRechargeResponse.apiData.billpay_ref_id,
  //               transaction_status: resultRechargeResponse.apiStatus,
  //               biller_input: JSON.stringify(resultRechargeResponse.request),
  //               biller_response: JSON.stringify(resultRechargeResponse.response)
  //             }
  //             const updateRequestNo = await this.updateWhere(fields.connection, { data: updateTrfRequestData, id: currentUser.ma_billpay_transactionid, where: 'ma_billpay_transactionid' })
  //             if (resultRechargeResponse.apiStatus === 'S') {
  //               message = errorMsg.responseCode[1000] + ' Recharge Successfully'
  //             } else if (resultRechargeResponse.apiStatus === 'P' && resultRechargeResponse.apiData.billpay_ref_id) {
  //               message = errorMsg.responseCode[1000] + ' Recharge Pending'
  //               apiPayload.billpay_ref_id = resultRechargeResponse.apiData.billpay_ref_id
  //               const resultPendingResponse = await checkBalanceObj.requestToProvider('DOUBLE_VERIFICATION', apiPayload)
  //               console.log('resultPendingResponse', resultPendingResponse)
  //               if (resultPendingResponse.status === 200) {
  //                 const updateTrfRequestDataPending = {}
  //                 updateTrfRequestDataPending.transaction_status = resultPendingResponse.apiStatus
  //                 updateTrfRequestDataPending.biller_response = updateTrfRequestData.biller_response + ':::' + resultPendingResponse.biller_response
  //                 if (resultPendingResponse.apiStatus === 'S') {
  //                   message = ' Recharge Successfully'
  //                 } else if (resultPendingResponse.apiStatus === 'P') {
  //                   message = ' Recharge Pending'
  //                 } else if (resultPendingResponse.apiStatus === 'F') {
  //                   message = ' Recharge Failed. Refund Initited '
  //                   await this.refundTransaction({
  //                     order_id: fields.orderid,
  //                     rrn: fields.rrn
  //                   })
  //                 } else {
  //                   message = ' Recharge Unknow Status'
  //                 }

  //                 const updateRequestNo = await this.updateWhere(fields.connection, { data: updateTrfRequestDataPending, id: currentUser.ma_billpay_transactionid, where: 'ma_billpay_transactionid' })
  //               }
  //             } else if (resultRechargeResponse.apiStatus === 'F') {
  //               message = ' Recharge Failed. Refund Initited '
  //               await this.refundTransaction({
  //                 order_id: fields.orderid,
  //                 rrn: fields.rrn
  //               })
  //             }
  //             const masterBillPayData = await transactionController.updateMasterBillpay(null, {
  //               ma_transaction_master_id: currentUser.ma_transaction_master_id
  //             }, fields.connection)
  //             console.log('masterBillPayData', masterBillPayData)

  //             if (isSet) await mySQLWrapper.commit(fields.connection)
  //             return { status: 200, respcode: 1000, message: message }
  //           } else {
  //             // if (isSet) await mySQLWrapper.rollback(fields.connection)

  //             const updateTrfRequestData = {
  //               transaction_status: 'F',
  //               biller_input: apiPayload,
  //               biller_response: resultRechargeResponse
  //             }
  //             const updateRequestNo = await this.updateWhere(fields.connection, { data: updateTrfRequestData, id: currentUser.ma_billpay_transactionid, where: 'ma_billpay_transactionid' })
  //             await this.refundTransaction({
  //               order_id: fields.orderid,
  //               rrn: fields.rrn
  //             })

  //             const masterBillPayData = await transactionController.updateMasterBillpay(null, {
  //               ma_transaction_master_id: currentUser.ma_transaction_master_id
  //             }, fields.connection)
  //             console.log('masterBillPayData', masterBillPayData)

  //             if (isSet) await mySQLWrapper.commit(fields.connection)
  //             return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' Recharge Failed. Refund Initited ' }
  //           }
  //         } else {
  //           if (isSet) await mySQLWrapper.rollback(fields.connection)
  //           await this.refundTransaction({
  //             order_id: fields.aggregator_order_id,
  //             rrn: fields.rrn
  //           })
  //           return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' Provider not found. Refund Initited' }
  //         }
  //         // if (isSet) await mySQLWrapper.commit(fields.connection)
  //         // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
  //       }
  //       return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + updateRes }
  //     } else {
  //       return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013] }
  //     }
  //   } catch (err) {
  //     console.log('doRechargeError', err)
  //     if (isSet) await mySQLWrapper.rollback(fields.connection)
  //     log.logger({ pagename: require('path').basename(__filename), action: 'topup', type: 'response', fields: err })
  //     return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.message }
  //   } finally {
  //     // release connection
  //     if (isSet) fields.connection.release()
  //   }
  // }

  // static async refundTransaction (fields) {

  // }

  // static async revalidationRecharge (_, fields) {
  //   log.logger({ pagename: require('path').basename(__filename), action: 'revalidationRecharge', type: 'request', fields: fields })
  //   let isSet = false
  //   let connection = null
  //   try {
  //     // Create connection if not passed
  //     if (fields.connection === null || fields.connection === undefined) {
  //       connection = await mySQLWrapper.getConnectionFromPool()
  //       isSet = true
  //       // await mySQLWrapper.beginTransaction(fields.connection)
  //     } else {
  //       connection = fields.connection
  //     }

  //     const sql = `SELECT tm.amount,tm.ma_transaction_master_id,tm.ma_user_id,tm.userid,
  //     btm.customer_mobile_number,tm.transaction_status,
  //     btm.billpay_transaction_id,btm.ma_billpay_gateway_id,btm.ma_billpay_provider_id,
  //     btm.ma_billpay_transactionid,
  //     btm.billpay_ref_id,
  //     gm.shortname,
  //     gm.api_token,
  //     gm.gateway_user_id,
  //     btm.biller_response
  //     FROM ma_billpay_transaction_master as btm
  //     JOIN ma_transaction_master as tm on btm.ma_transaction_master_id = tm.ma_transaction_master_id
  //     JOIN ma_billpay_provider_master as pm on pm.ma_billpay_provider_id = btm.ma_billpay_provider_id
  //     JOIN ma_billpay_gateway_master as gm on gm.ma_billpay_gateway_id = btm.ma_billpay_gateway_id
  //     where btm.transaction_status = 'P' AND gm.gateway_status = 'Y' AND pm.provider_status = 'Y' AND btm.cron_settlement = 0 `
  //     const txndetails = await this.rawQuery(sql, connection)

  //     let message = ''
  //     if (txndetails.length > 0) {
  //       console.log('txndetailsCron', txndetails)
  //       for (let index = 0; index < txndetails.length; index++) {
  //         const currentTransacation = txndetails[index]

  //         const billpay_ref_id = currentTransacation.billpay_ref_id
  //         if (currentTransacation.billpay_ref_id.toString().length === 0) {
  //           console.log('Record skips as billpay_ref_id empty')
  //           continue
  //         }

  //         const checkBalanceObj = new Billpay(currentTransacation.shortname, connection)
  //         console.log('checkBalanceObj', checkBalanceObj)
  //         const apiPayload = {
  //           api_token: currentTransacation.api_token,
  //           gateway_user_id: currentTransacation.gateway_user_id,
  //           billpay_ref_id: billpay_ref_id
  //         }
  //         const resultPendingResponse = await checkBalanceObj.requestToProvider('DOUBLE_VERIFICATION', apiPayload)
  //         console.log('resultPendingResponseCron', resultPendingResponse)

  //         if (resultPendingResponse.status === 200) {
  //           const updateTrfRequestDataPending = {}
  //           updateTrfRequestDataPending.transaction_status = resultPendingResponse.apiStatus
  //           updateTrfRequestDataPending.biller_response = currentTransacation.biller_response + '::CRON::'
  //           updateTrfRequestDataPending.cron_settlement = 1
  //           if (resultPendingResponse.apiStatus === 'S') {
  //             message = ' Recharge Successfully'
  //           } else if (resultPendingResponse.apiStatus === 'P') {
  //             message = ' Recharge Pending'
  //           } else if (resultPendingResponse.apiStatus === 'F') {
  //             message = ' Recharge Failed. Refund Initited '
  //             await this.refundTransaction({
  //               order_id: fields.orderid,
  //               rrn: fields.rrn
  //             })
  //           } else {
  //             message = ' Recharge Unknow Status'
  //           }

  //           const updateRequestNo = await this.updateWhere(connection, { data: updateTrfRequestDataPending, id: currentTransacation.ma_billpay_transactionid, where: 'ma_billpay_transactionid' })

  //           const masterBillPayData = await transactionController.updateMasterBillpay(null, {
  //             ma_transaction_master_id: currentTransacation.ma_transaction_master_id
  //           }, connection)
  //           console.log('masterBillPayDataCron', masterBillPayData)
  //         } else {
  //           continue
  //         }
  //       }
  //       if (isSet) await mySQLWrapper.commit(connection)
  //       return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
  //     } else {
  //       return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
  //     }
  //   } catch (error) {
  //     console.log('revalidationRechargeError', error)
  //     return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + error.message }
  //   } finally {
  //     // release connection
  //     if (isSet) connection.release()
  //   }
  // }
}

module.exports = BillpayTransaction
