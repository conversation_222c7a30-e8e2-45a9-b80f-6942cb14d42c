const DAO = require('../../lib/dao.js')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const AdminRouter = require('./routers/AdminRouter')
const errorEmail = require('../../util/errorHandler')
const validator = require('../../util/validator')
const path = require('path')
const { ENDPOINTS } = require('./routers/config')
const fs = require('fs')
const AWS = require('aws-sdk')
/* TODO :  */
AWS.config.update({
  region: 'ap-south-1',
  credentials: {
    accessKeyId: '********************',
    secretAccessKey: 'KC6eMGlgmLHe6Bt+BinsdrRgJzg6IwYD5cksuI+9'
  }
})
class FastagPdfUploadController extends DAO {
  /**
   *
   * @param {null} _
   * @param {{ source: app|web,customer_id: string, rc_front: string,rc_back: string }} fields
   * @returns {Promise <any>}
   * @returns
   */
  static async rCPdfUpload (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'rCPdfUpload', type: 'request', fields: { fields } })
    try {
      /* validate the fields */
      const validatorResponse = validator.validateFields(fields, ['customer_id', 'rc_front', 'rc_back', 'source'])
      if (validatorResponse.status != 200) return validatorResponse

      const validatorEnumResponse = validator.validateEnum(fields.source, ['web', 'app'], true)
      if (validatorEnumResponse.status != 200) return validatorEnumResponse

      const { filename, mimetype: RcFrontMimeType, createReadStream: rcFrontStream } = await fields.rc_front
      const { mimetype: RcBackMimeType, createReadStream: rcBackStream } = await fields.rc_back

      if (!RcFrontMimeType.match(/image/) || !RcBackMimeType.match(/image/)) {
        return { status: 400, message: `${errorMsg.responseCode[1028]} : Invalid file type`, respcode: 1028, action_code: 1001 }
      }

      const s3 = new AWS.S3({ apiVersion: '2006-03-01', params: { Bucket: 'retailappdocs' } })
      const data = await s3.upload({ Key: filename, Body: rcFrontStream() }).promise()

      log.logger({ pagename: path.basename(__filename), action: 'rCPdfUpload', type: 'S3 Result', fields: { data } })
      // await this.storeFS({ stream: rcFrontStream(), filename })
      // const rc_front = await this.readableToString(rcFrontStream(), RcFrontMimeType)
      // const rc_back = await this.readableToString(rcBackStream(), RcBackMimeType)

      /* makeRequest for external End point and return result */
      // const query = {
      //   ma_user_id: fields.ma_user_id,
      //   userid: fields.userid,
      //   customer_id: fields.customer_id,
      //   rc_front: rc_front,
      //   rc_back: rc_back
      // }

      // const adminRouter = new AdminRouter()
      // const response = await adminRouter
      //   .setEndpoint(ENDPOINTS.RC_PDF_UPLOAD)
      //   .setHeaders({ source: 'api', mimeType: 'multipart/form-data' })
      //   .setRequestBody({ query })
      //   .post()
      // log.logger({ pagename: path.basename(__filename), action: 'rCPdfUpload', type: 'adminRouter', fields: response })

      // if (response.status != 200) {
      //   return response
      // }

      // if (response.data === undefined || Object.keys(response.data).length == 0) {
      //   return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      // }

      // const { data } = response
      // const { data } = response
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data }
    } catch (error) {
      log.logger({ pagename: path.basename(__filename), action: 'rCPdfUpload', type: 'catcherror', fields: error })
      errorEmail.notifyCatchErrorEmail({
        function: ENDPOINTS.RC_PDF_UPLOAD,
        data: { ...fields },
        error: error
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async readableToString (readable, mimeType) {
    let result = ''
    for await (const chunk of readable) {
      result += chunk.toString('base64')
    }
    return `data:${mimeType};base64,${result}`
  }
}

module.exports = FastagPdfUploadController
