const DAO = require('../../lib/dao.js')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const AdminRouter = require('./routers/AdminRouter')
const errorEmail = require('../../util/errorHandler')
const validator = require('../../util/validator')
const path = require('path')
const { ENDPOINTS } = require('./routers/config')

class GoldHistoryController extends DAO {
  /**
   *
   * @param {null} _
   * @param {{ source: app|web,dateFrom: string,dateTo: string extCustomerId: string }} fields
   * @returns {Promise <any>}
   */
  static async downloadGoldTransactionHistory (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'downloadGoldTransactionHistory', type: 'request', fields: { fields } })
    try {
      /* validate the fields */
      const validatorResponse = validator.validateFields(fields, ['dateFrom', 'dateTo', 'source', 'extCustomerId'])
      if (validatorResponse.status != 200) return validatorResponse

      const validatorEnumResponse = validator.validateEnum(fields.source, ['web', 'app'], true)
      if (validatorEnumResponse.status != 200) return validatorEnumResponse
      /* makeRequest for external End point and return result */
      const query = {
        date_from: fields.dateFrom,
        date_to: fields.dateTo,
        extCustomerId: fields.extCustomerId
      }

      const adminRouter = new AdminRouter()
      const response = await adminRouter
        .setEndpoint(ENDPOINTS.DOWNLOAD_GOLDTRANSACTION_HISTORYAPI)
        .setHeaders({ source: fields.source })
        .setRequestBody({ query })
        .get()
      log.logger({ pagename: path.basename(__filename), action: 'downloadGoldTransactionHistory', type: 'adminRouter', fields: response })
      return response
    } catch (error) {
      log.logger({ pagename: path.basename(__filename), action: 'downloadGoldTransactionHistory', type: 'catcherror', fields: error })
      errorEmail.notifyCatchErrorEmail({
        function: ENDPOINTS.DOWNLOAD_GOLDTRANSACTION_HISTORYAPI,
        data: { ...fields },
        error: error
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }
}

module.exports = GoldHistoryController
