const path = require('path')
const crypto = require('crypto')
const { default: Axios } = require('axios')

const logs = require('../../../util/log')
const util = require('../../../util/util')
const common = require('../../../util/common')
const errorMsg = require('../../../util/error')
const errorEmail = require('../../../util/errorHandler')
const { ADMIN_BASE_URL, KEYS } = require('./config')
const env = process.env.NODE_ENV || 'development'

/**
 * @private variables for AdminRouter
 */
let _headers, _data, _endpoint
/**
 * @private variables for AdminRouter
 */
const _APP_ENCRYPTION_KEY = KEYS[env].APP_ENCRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
const _APP_DECRYPTION_KEY = KEYS[env].APP_DECRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
const _WEB_ENCRYPTION_KEY = KEYS[env].WEB_ENCRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
const _WEB_DECRYPTION_KEY = KEYS[env].WEB_DECRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
const _API_ENCRYPTION_KEY = KEYS[env].API_ENCRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
const _API_DECRYPTION_KEY = KEYS[env].API_DECRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
let _encryptionKey = _WEB_ENCRYPTION_KEY
/**
 * @private variables for AdminRouter
 */
let _decryptionKey = _WEB_DECRYPTION_KEY

class AdminRouter {
  get URL () {
    return ADMIN_BASE_URL
  }

  get ENCRYPTION_KEY () {
    return _encryptionKey
  }

  get DECRYPTION_KEY () {
    return _decryptionKey
  }

  /**
   * @param {string} key
   */
  set ENCRYPTION_KEY (key) {
    _encryptionKey = key
    return _encryptionKey
  }

  /**
   * @param {string} key
   */
  set DECRYPTION_KEY (key) {
    _decryptionKey = key
    return _decryptionKey
  }

  get CONFIG () {
    return {
      headers: _headers,
      timeout: 20000
    }
  }

  /**
   * @param {any} headers
   */
  set HEADERS (headers) {
    _headers = headers
  }

  /**
   * @return _data
   */
  get REQUEST_BODY () {
    return _data
  }

  /**
   * @param {{ query: any }} data
   */
  set REQUEST_BODY (data) {
    _data = data
  }

  /**
   * @param {string} endpoint
   */
  set ENDPOINT (endpoint) {
    _endpoint = endpoint
  }

  /**
   * Sets Header and Encryption for Admin router
   * @param {{ source: 'web'|'app', [key: string]: any }} headers
   * @returns {AdminRouter}
   */
  setHeaders (headers) {
    this.HEADERS = { ...headers, source: Buffer.from(headers.source || '').toString('base64') }
    switch (headers.source) {
      case 'web':
        this.ENCRYPTION_KEY = _WEB_ENCRYPTION_KEY
        this.DECRYPTION_KEY = _WEB_DECRYPTION_KEY
        break
      case 'app':
        this.ENCRYPTION_KEY = _APP_ENCRYPTION_KEY
        this.DECRYPTION_KEY = _APP_DECRYPTION_KEY
        break
      case 'api':
        this.ENCRYPTION_KEY = _API_ENCRYPTION_KEY
        this.DECRYPTION_KEY = _API_DECRYPTION_KEY
        break
    }
    return this
  }

  /**
   * Sets Request Body
   * @param {{ query: any }} requestBody
   * @returns {AdminRouter}
   */
  setRequestBody (requestBody) {
    this.REQUEST_BODY = requestBody
    return this
  }

  setEndpoint (endpoint) {
    this.ENDPOINT = endpoint
    return this
  }

  /**
   * encrypt description - Encrypt API Request of admin
   * @private
   * @param {string} data
   * @returns {Promise <string>}
   */
  async encrypt (data) {
    logs.logger({ pagename: path.basename(__filename), action: 'encrypt', type: 'request', fields: data })

    try {
      const iv = '4l9nz7c92m2q7c8h'
      logs.debug({ pagename: path.basename(__filename), action: 'encrypt', type: 'iv', fields: { iv } })
      const cipher = crypto.createCipheriv(util.algorithm, this.ENCRYPTION_KEY, iv)
      let encrypted = cipher.update(data, 'utf8', 'base64')
      encrypted += cipher.final('base64')
      logs.debug({ pagename: path.basename(__filename), action: 'encrypt', type: 'enc type of', fields: typeof encrypted })
      return iv + encrypted
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'encrypt', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'encrypt',
        data: { data: _data },
        error: err
      })
      throw err
    }
  }

  /**
   * decrypt description - Decrypts API Response of admin
   * @private
   * @param {string} data
   * @returns {Promise <string>}
   */
  async decrypt (data) {
    logs.logger({ pagename: path.basename(__filename), action: 'decrypt', type: 'request', fields: data })

    try {
      const iv = '4l9nz7c92m2q7c8h'
      logs.debug({ pagename: path.basename(__filename), action: 'decrypt', type: 'iv', fields: iv })
      const decipher = crypto.createDecipheriv(util.algorithm, this.DECRYPTION_KEY, iv)
      let decrypted = decipher.update(data, 'base64')
      decrypted += decipher.final()
      return decrypted
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'decrypt', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'decrypt',
        data: { data },
        error: err
      })
      throw err
    }
  }

  /**
   * makeRequest description - Make API Request to admin
   * @param {'get'|'post'|'put'|'delete'|'patch'} method
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  async makeRequest (method) {
    try {
      const requestData = { query: (await this.encrypt(JSON.stringify((this.REQUEST_BODY || {}).query || ''))) }
      // const requestData =

      logs.debug({ pagename: path.basename(__filename), action: 'makeRequest', type: 'requestData', fields: { headers: _headers, body: requestData, url: this.URL + (_endpoint || '') } })

      let response
      switch (method) {
        case 'post':
          response = await Axios.post(this.URL + (_endpoint || ''), requestData, this.CONFIG)
          break
        case 'get':
          response = await Axios.get(this.URL + (_endpoint || ''), requestData, this.CONFIG)
          break
        case 'put':
          response = await Axios.put(this.URL + (_endpoint || ''), requestData, this.CONFIG)
          break
        case 'patch':
          response = await Axios.patch(this.URL + (_endpoint || ''), requestData, this.CONFIG)
          break
        case 'delete':
          response = await Axios.delete(this.URL + (_endpoint || ''), requestData, this.CONFIG)
          break
        default:
          throw errorMsg[130001]
      }

      logs.debug({ pagename: path.basename(__filename), action: 'makeRequest', type: 'requestData', fields: { headers: _headers, body: requestData, url: this.URL + (_endpoint || ''), response: response.data } })

      return response.data
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'makeRequest', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'makeRequest',
        data: { data: _data, headers: _headers, url: this.URL + (_endpoint || '') },
        error: err
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  async get () {
    return this.makeRequest('get')
  }

  async post () {
    return this.makeRequest('post')
  }
}

module.exports = AdminRouter
