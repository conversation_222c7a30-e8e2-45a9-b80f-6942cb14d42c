const util = require('../../../util/util')

module.exports = {
  ADMIN_BASE_URL: util.isProduction() ? 'https://retaila.airpay.co.in/api/' : 'https://retaila.airpay.ninja/api/',
  ENDPOINTS: {
    REPORT_DOWNLOAD_API: 'ReportdownloadAPI-api',
    E_VALUE: 'e-value-api',
    RC_PDF_UPLOAD: 'idfc-fastag/rc-pdf-upload-api',
    DOWNLOAD_GOLDTRANSACTION_HISTORYAPI: 'downloadGoldTransactionHistoryAPI',
    INVALIDATE: 'invalidate',
    AUTHENTICATE: 'authenticate'
  },
  KEYS: {
    development: {
      APP_ENCRYPTION_KEY: '73h3DYqctmrPdN7PeDUjmXRM6A9JIzVP',
      APP_DECRYPTION_KEY: '73h3DYqctmrPdN7PeDUjmXRM6A9JIzVP',
      WEB_ENCRYPTION_KEY: 'OlorK9nZbeqBoG1oTJilbPl6S4sb5cyT',
      WEB_DECRYPTION_KEY: 'OlorK9nZbeqBoG1oTJilbPl6S4sb5cyT',
      API_ENCRYPTION_KEY: 'JKaPdSgVkXp2s5v8y3B4E5H9MbQeThWm',
      API_DECRYPTION_KEY: 'JKaPdSgVkXp2s5v8y3B4E5H9MbQeThWm'
    },
    staging: {
      APP_ENCRYPTION_KEY: '73h3DYqctmrPdN7PeDUjmXRM6A9JIzVP',
      APP_DECRYPTION_KEY: '73h3DYqctmrPdN7PeDUjmXRM6A9JIzVP',
      WEB_ENCRYPTION_KEY: 'OlorK9nZbeqBoG1oTJilbPl6S4sb5cyT',
      WEB_DECRYPTION_KEY: 'OlorK9nZbeqBoG1oTJilbPl6S4sb5cyT',
      API_ENCRYPTION_KEY: 'JKaPdSgVkXp2s5v8y3B4E5H9MbQeThWm',
      API_DECRYPTION_KEY: 'JKaPdSgVkXp2s5v8y3B4E5H9MbQeThWm'
    },
    production: {
      APP_ENCRYPTION_KEY: 'V1v3oKCrBqkvt90f65cBy9oajkjmkmlb',
      APP_DECRYPTION_KEY: 'V1v3oKCrBqkvt90f65cBy9oajkjmkmlb',
      WEB_ENCRYPTION_KEY: 'N1C5dgRpifQ7Y2D6W0PjQW7y1R8ybtIh',
      WEB_DECRYPTION_KEY: 'N1C5dgRpifQ7Y2D6W0PjQW7y1R8ybtIh',
      API_ENCRYPTION_KEY: 'DkFmJaNdRgUkXp2s5v8y8B7E3H8KbPeS',
      API_DECRYPTION_KEY: 'DkFmJaNdRgUkXp2s5v8y8B7E3H8KbPeS'
    }
  }
}
