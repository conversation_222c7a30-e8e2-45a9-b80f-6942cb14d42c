const path = require('path')
const DAO = require('../../lib/dao')
const logs = require('../../util/log')
const errorMsg = require('../../util/error')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorEmail = require('../../util/errorHandler')
const validator = require('../../util/validator')
const AdminRouter = require('./routers/AdminRouter')

class LoginApiController extends DAO {
  get TABLE_NAME () {
    return 'ma_table_name'
  }

  get PRIMARY_KEY () {
    return 'ma_table_name_id'
  }

  /**
   * forgotPassword description - Calls Admin's forgotpassword api and forwards the response to front end
   * @param {null} _
   * @param {{ username: number, mobile: number }} fields
   * @returns {Promise <any>}
   */
  static async forgotPassword (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'forgotPassword', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['username', 'mobile', 'source'])
    if (validatorResponse.status != 200) return validatorResponse

    const validatorEnumResponse = validator.validateEnum(fields.source, ['web', 'app'], true)
    if (validatorEnumResponse.status != 200) return validatorEnumResponse

    try {
      const adminRouter = new AdminRouter()
      const response = await adminRouter
        .setEndpoint('forgotpassword')
        .setHeaders({ source: fields.source })
        .setRequestBody({ query: { username: fields.username, mobile: fields.mobile } })
        .post()
      console.log(response)
      return response
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'forgotPassword', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'forgotPassword',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   * changePassword description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number, oldPassword: string, newPassword: string, confirmNewPassword: string, authstatus: string }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async changePassword (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'changePassword', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'oldPassword', 'newPassword', 'confirmNewPassword', 'authstatus'])
    if (validatorResponse.status != 200) return validatorResponse

    try {
      const adminRouter = new AdminRouter()
      const response = await adminRouter
        .setEndpoint('changepassword')
        .setRequestBody({
          query: {
            userid: fields.userid,
            oldPassword: fields.oldPassword,
            newPassword: fields.newPassword,
            confirmNewPassword: fields.confirmNewPassword,
            authstatus: fields.authstatus
          }
        })
        .post()
      return response
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'changePassword', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'changePassword',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }
}

module.exports = LoginApiController
