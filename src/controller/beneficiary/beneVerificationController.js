const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const validator = require('../../util/validator')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const util = require('../../util/util')
const beneVerifyIncentiveCtrl = require('../incentive/beneVerifyIncentiveController')
const common = require('../../util/common')
const queueLib = require('../../util/sqs')
const bankOnBoardDetails = require('../bankHandler/bankOnBoardingDetails')
// const { lokiLogger } = require('../../util/lokiLogger')
class BeneVerification extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_bene_verification'
  }

  get PRIMARY_KEY () {
    return 'ma_bene_verification_id'
  }

  static async getOrCreateBeneVerify (_, fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getOrCreateBeneVerify', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getOrCreateBeneVerify', type: 'request', fields: fields })
    try {
      this.TABLE_NAME = 'ma_bene_verification'
      const tmpbenData = fields.beneficiaryDetailData
      const sql = `SELECT * FROM ma_bene_verification WHERE account_number = '${tmpbenData.accountnumber}' AND ifsc_code = '${tmpbenData.ifscode}' AND bene_verify_status = 'S' limit 1 `
      const queryResp = await this.rawQuery(sql, connection)
      if (queryResp.length > 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: queryResp[0] })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: queryResp[0] })

        const verified = { ...queryResp[0], allreadyverified: true }
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: verified }
      } else {
        console.log('tmpbenData>>', tmpbenData)
        const insertData = {
          ma_transaction_master_id: fields.ma_transaction_master_id,
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          uic: fields.uic,
          account_number: tmpbenData.accountnumber,
          ma_bank_on_boarding: fields.verifyBankOnBoardingId,
          bene_mobile_number: tmpbenData.ben_mobile_number,
          ifsc_code: tmpbenData.ifscode,
          validation_amount: 0, // later it will  update from bank response
          bene_verify_status: 'I',
          bank_benename: ''
        }

        const _result = await this.insert(connection, {
          data: insertData
        })

        const verified = { ...insertData, ma_bene_verification_id: _result.insertId, allreadyverified: false }
        log.logger({ pagename: require('path').basename(__filename), action: 'getOrCreateBeneVerify', type: 'response', fields: queryResp })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getOrCreateBeneVerify', type: 'response', fields: queryResp })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: verified }
      }
    } catch (err) {
      console.log('getOrCreateBeneVerifyError', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'getOrCreateBeneVerify', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getOrCreateBeneVerify', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async beneVerificationWithBank (_, fields, handler, connection, sessionRQ) {
    log.logger({ pagename: require('path').basename(__filename), action: 'beneVerificationWithBank', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'beneVerificationWithBank', type: 'request', fields: fields })
    let isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const tmpbenData = fields.beneficiaryDetailData
      let detail_bank_rrn = ''
      const bankRemark = ''
      // let requestNumber = fields.otp_request_id ? fields.otp_request_id : ''
      // if (handler.BANK_ON_BOARDING_ID == 5) {
      //   if (requestNumber == '') {
      //     requestNumber = await this.generateRequestNumber(fields.ma_bene_verification_id)
      //   }
      // } else {
      //   requestNumber = await this.generateRequestNumber(fields.ma_bene_verification_id)
      // }
      const requestNumber = await this.generateRequestNumber(fields.ma_bene_verification_id)

      const apiAPIPayload = {
        ma_user_id: fields.ma_user_id,
        ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
        senderid: tmpbenData.remitter_id,
        receivername: tmpbenData.beneficiary_name,
        receivermobilenumber: tmpbenData.ben_mobile_number,
        receiveremailid: '', // Optional not yet decided to pass or not
        ifsccode: tmpbenData.ifscode,
        accountnumber: tmpbenData.accountnumber,
        request_number: requestNumber,
        sendermobilenumber: tmpbenData.sendermobilenumber,
        bankname: tmpbenData.bank,
        bankifsc: tmpbenData.ifscode,
        token: fields.token
      }

      const senderData = await bankOnBoardDetails.getSenderAddressDetails(null, { userid: fields.userid }, connection)

      if (senderData.status !== 200 && senderData.data.length == 0) {
        return senderData
      }

      const custDetailsSql = `SELECT remitter_name FROM ma_customer_details where uic='${fields.uic}' limit 1`
      const custDetailsData = await this.rawQuery(custDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'beneVerificationWithBank', type: 'custDetailsData', fields: custDetailsData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'beneVerificationWithBank', type: 'custDetailsData', fields: custDetailsData })

      if (custDetailsData.length == 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      const remitterDetail = custDetailsData[0]
      const senderDetails = senderData.data[0]
      const extraAPIPayload = {
        beneMobNo: tmpbenData.ben_mobile_number,
        beneAccNo: tmpbenData.accountnumber,
        ifsc_code: tmpbenData.ifscode,
        bank_name: tmpbenData.bank,
        pincode: senderDetails.pincode,
        address: senderDetails.address,
        remitter_name: remitterDetail.remitter_name
      }

      const reqUpdateData = {
        request_number: requestNumber,
        bank_request: JSON.stringify(apiAPIPayload)
      }

      // Update request here
      const updateRes = await this.updateWhere(connection, { data: reqUpdateData, id: fields.ma_bene_verification_id, where: 'ma_bene_verification_id' })

      if (updateRes.affectedRows <= 0) {
        return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + ' Failed to Update bank request ' }
      }

      const Payment = require('./../bankHandler/payment')
      const transactionController = require('../transaction/transactionController')
      try {
        const payment = new Payment(handler.BANK_NAME, null, connection, handler.BANK_ON_BOARDING_ID)
        const resBeneVerify = await payment.requestToBank('VALIDATE_BENEFICIARY_BEFORE_ADD', { ...apiAPIPayload, ...extraAPIPayload }, sessionRQ, connection)
        console.log('resBeneVerify>>', resBeneVerify)
        const TransferApi = {}
        if ('status' in resBeneVerify) {
          if (resBeneVerify.status === 1) {
            TransferApi.bene_verify_status = 'S'
          } else if (resBeneVerify.status === -1) {
            TransferApi.bene_verify_status = 'P'
          } else if (resBeneVerify.status === 0) {
            TransferApi.bene_verify_status = 'F'
          } else {
            // Other than case include Session Error or API Error or Login Error consider as failed case
            TransferApi.bene_verify_status = 'F'
          }

          const bank_response = resBeneVerify.bank_response ? resBeneVerify.bank_response : resBeneVerify
          const updateBeneData = {
            bene_verify_status: TransferApi.bene_verify_status,
            bank_response: JSON.stringify(bank_response)
          }
          let bank_benename = ''
          if ('bankTransactionid' in resBeneVerify) {
            updateBeneData.validation_amount = resBeneVerify.amount
            updateBeneData.utr_number = resBeneVerify.bankTransactionid
            updateBeneData.bank_service_charges = resBeneVerify.bank_service_charges
            updateBeneData.bank_gross_amount = resBeneVerify.bank_gross_amount
            updateBeneData.bank_benename = resBeneVerify.benename
            bank_benename = resBeneVerify.benename
            updateBeneData.bank_rrn = resBeneVerify.rrn ? resBeneVerify.rrn : ''

            try {
              if (detail_bank_rrn == '' && updateBeneData.bank_rrn != '' && typeof (updateBeneData.bank_rrn) != 'undefined') {
                detail_bank_rrn = updateBeneData.bank_rrn
              }
            } catch (e) {

            }
          }

          console.log('updateBeneData', updateBeneData, TransferApi)
          const updateRes = await this.updateWhere(connection, { data: updateBeneData, id: fields.ma_bene_verification_id, where: 'ma_bene_verification_id' })

          if (detail_bank_rrn != '') {
            const dataDetails = { bank_rrn: detail_bank_rrn, aggregator_order_id: fields.aggregator_order_id }
            if (bank_benename != '') {
              dataDetails.receiver_name = bank_benename
            }
            const transactionController = require('../transaction/transactionController')
            const updateTransactionDetails = await transactionController.updateTransactionDetails(dataDetails, connection)
            console.log('detail_bank_rrn', detail_bank_rrn)
          }

          if (bank_response && Object.keys(bank_response).length > 0) {
            let data = {}
            if (('remarks' in bank_response) && bank_response.remarks != '') {
              data = { transaction_reason: bank_response.remarks }
            } else {
              data = { transaction_reason: resBeneVerify.message }
            }
            // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })

            /* RISK MANAGEMENT Changes */
            const updateTransactionResult = await transactionController.updateWhereData(connection, {
              data,
              id: fields.ma_transaction_master_id,
              where: 'ma_transaction_master_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
          }

          if (('apiFailed' in resBeneVerify) && resBeneVerify.apiFailed == true) {
            return { status: 200, message: resBeneVerify.message, respcode: 1000, apiFailed: true, bene_verify_status: TransferApi.bene_verify_status, bank_benename: bank_benename }
          } else {
            return { status: 200, message: resBeneVerify.message, respcode: 1000, apiFailed: false, bene_verify_status: TransferApi.bene_verify_status, bank_benename: bank_benename }
          }
        } else {
          return resBeneVerify
        }
      } catch (error) {
        console.log('Bank Module Load Error', error)
        return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
      }
    } catch (error) {
      console.log('beneVerificationWithBankError', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'transferDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'transferDetails', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001] + ' With bene verification', respcode: 1001 }
    } finally {
      if (isSet) {
        connection.release()
      }
    }
  }

  static async generateRequestNumber (uniqueId) {
    const prefix = !util.isProduction() ? util.nsdlRequestNoPrefix : 'P'
    const requestNumber = prefix + 'BN' + uniqueId
    return requestNumber
  }

  static async reCheckBeneVerifyStatus (_, fields) {
    let isSet = false
    try {
      if (fields.connection === null || fields.connection === undefined) {
        fields.connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      let pendingBeneVerifySql = `
      SELECT 
        t.ma_bene_verification_id,
        t.ma_transaction_master_id,
        tm.aggregator_order_id,
        t.ma_user_id,
        t.userid,
        t.uic,
        t.request_number,
        t.bene_verify_status,
        mbob.bank_name,
        mbob.ma_bank_on_boarding_id,
        t.bank_response,
        tm.remarks,
        tm.aggregator_order_id,
        tm.transaction_reason
        FROM ma_bene_verification as t 
        JOIN ma_transaction_master as tm on tm.ma_transaction_master_id = t.ma_transaction_master_id   
        JOIN ma_bank_on_boarding as mbob ON mbob.ma_bank_on_boarding_id = t.ma_bank_on_boarding
        WHERE ((t.bene_verify_status IN ('P') AND TIMESTAMPDIFF(MINUTE,t.updatedon,CURRENT_TIMESTAMP) >= 5) OR (t.bene_verify_status IN ('I') AND TIMESTAMPDIFF(MINUTE,t.updatedon,CURRENT_TIMESTAMP) >= 15)) AND mbob.onboarding_status='Y'
        ORDER BY t.ma_bene_verification_id DESC
      `
      if (fields.limit && fields.limit > 0) {
        pendingBeneVerifySql += ` LIMIT ${fields.limit} `
      }

      const sqsenable = await common.getSystemCodes(this, util.sqsenable, fields.connection)
      const SQSENABLE = (sqsenable == 'true')

      console.log('pendingBeneVerifySql', pendingBeneVerifySql)
      const pendingBeneVerifyData = await this.rawQuery(pendingBeneVerifySql, fields.connection)

      let SQSDATA = [] // Initialize again
      // SQSENABLE = false

      if (pendingBeneVerifyData.length > 0) {
        console.log('FoundNoOfRecords>>', pendingBeneVerifyData.length)
        /**
          * Load Bank Modules
          */
        if (SQSENABLE == true) {
          for (let index = 0; index < pendingBeneVerifyData.length; index++) {
            const currentPending = pendingBeneVerifyData[index]
            const sqsPayload = {
              queue_name: 'requery_messages',
              queue_msg_grp_id: 'ReQueryBatch' + currentPending.aggregator_order_id,
              queue_payload: {
                fields: currentPending,
                functions: 'doBeneBankRequery',
                controller: 'beneVerificationController'
              }
            }
            SQSDATA.push(sqsPayload)
          }

          if (SQSENABLE === true && SQSDATA && SQSDATA.length > 0) {
            console.log(')this.SQSDATA>>', SQSDATA)
            console.log('SQS Calling....')

            const MessageData = SQSDATA
            console.time('TIMER_BULK_SQS_PROCESS')
            await queueLib.SinglePushToQueue(MessageData, 'requery_messages', fields.connection)
            console.timeEnd('TIMER_BULK_SQS_PROCESS')
            SQSDATA = []
          }
        } else {
          console.time('TIMER_BULK_REQUERY_PROCESS')
          // await Promise.all(pendingBeneVerifyData.map(async (currentPending) => {
          //   await that.doBeneBankRequery('', currentPending, connection)
          // }))
          for (let index = 0; index < pendingBeneVerifyData.length; index++) {
            const currentPending = pendingBeneVerifyData[index]
            await this.doBeneBankRequery('', currentPending, fields.connection)
          }
          console.timeEnd('TIMER_BULK_REQUERY_PROCESS')
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'reCheckBeneVerifyStatus', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'reCheckBeneVerifyStatus', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        fields.connection.release()
      }
    }
  }

  static async doBeneBankRequery (_, fields, conn = null) {
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    const transactionController = require('../transaction/transactionController')
    const beneficiaryController = require('./beneficiaryController')
    const Payment = require('./../bankHandler/payment')
    const detailsUpdated = []
    try {
      const sql = `SELECT bene_verify_status FROM ma_bene_verification WHERE ma_bene_verification_id = '${fields.ma_bene_verification_id}' limit 1`
      const beneVerifyExist = await this.rawQuery(sql, connection)
      if (beneVerifyExist.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' Transfer Data' }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'doBeneBankRequery', type: 'beneVerifyExist', fields: beneVerifyExist })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doBeneBankRequery', type: 'beneVerifyExist', fields: beneVerifyExist })

      if (!(beneVerifyExist[0].bene_verify_status == 'I' || beneVerifyExist[0].bene_verify_status == 'P')) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doBeneBankRequery', type: 'beneVerifyExist', fields: { status: 'Transaciton Already Updated!' } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doBeneBankRequery', type: 'beneVerifyExist', fields: { status: 'Transaciton Already Updated!' } })
        return { status: 400, respcode: 1013, message: errorMsg.responseCode[1013] }
      }
      await mySQLWrapper.beginTransaction(connection)
      const currentPending = fields
      let detail_bank_rrn = ''
      let detail_bank_name = ''
      console.log('CronCurrentPendingTransfer ' + (new Date()).toUTCString() + '::=>', currentPending)
      console.log(currentPending)
      if (currentPending.request_number === null || currentPending.request_number === '' || currentPending.request_number === 'null') {
        console.log('Request No not found ')
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' Request No not found' }
      }

      const payment = new Payment(currentPending.bank_name, currentPending.transaction_type, connection)
      const apiAPIPayload = {
        ma_user_id: currentPending.ma_user_id,
        ma_bank_on_boarding_id: currentPending.ma_bank_on_boarding_id,
        bcpartnerrefno: currentPending.request_number,
        ma_transfers_id: currentPending.ma_bene_verification_id,
        connection: connection
      }

      console.time('TIMER_DOUBLE_VERIFICATION')
      const beneVerifyData = await payment.requestToBank('DOUBLE_VERIFICATION_BENE', apiAPIPayload, currentPending.aggregator_order_id, connection)
      console.timeEnd('TIMER_DOUBLE_VERIFICATION')
      console.log('beneVerifyData>>', beneVerifyData)

      const updateTrfData = {}

      if (beneVerifyData.status === 1) {
        updateTrfData.bene_verify_status = 'S'
      } else if (beneVerifyData.status === 0) {
        updateTrfData.bene_verify_status = 'F'
      } else {
        updateTrfData.bene_verify_status = 'P'
        console.log('Transaction Status Pending Found Ignored >> ', beneVerifyData)
        await mySQLWrapper.commit(connection)
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' Transaction Status Pending Found Ignored >>' }
      }

      if (('bankTransactionid' in beneVerifyData) && Object.keys(beneVerifyData).length > 0) {
        updateTrfData.utr_number = beneVerifyData.bankTransactionid

        // if (beneVerifyData.bankrefno !== null && beneVerifyData.bankrefno !== '') {
        //   updateTrfData.brn = beneVerifyData.bankrefno
        // } // This is not present at the time of validation so ignored in pending crons
        updateTrfData.bank_service_charges = beneVerifyData.bank_service_charges
        updateTrfData.bank_gross_amount = beneVerifyData.bank_gross_amount
        updateTrfData.bank_benename = beneVerifyData.benename
        updateTrfData.bank_rrn = beneVerifyData.rrn

        try {
          if (detail_bank_rrn == '' && updateTrfData.bank_rrn != '' && typeof (updateTrfData.bank_rrn) != 'undefined') {
            detail_bank_rrn = updateTrfData.bank_rrn
            detail_bank_name = updateTrfData.bank_benename ? updateTrfData.bank_benename : ''
          }
        } catch (e) {

        }
      }

      if (currentPending.bank_response === null) {
        currentPending.bank_response = {}
      } else {
        currentPending.bank_response = JSON.parse(currentPending.bank_response)
      }
      const cronLable = 'CRON_' + new Date().getTime()
      var cronObj = {}
      cronObj[cronLable] = JSON.stringify(beneVerifyData)
      // updateTrfData.bank_response = { ...currentPending.bank_response }
      updateTrfData.bank_response = { ...currentPending.bank_response, ...cronObj }
      // updateTrfData.bank_response[cronLable] = beneVerifyData.message // apend new crons logs
      updateTrfData.bank_response = JSON.stringify(updateTrfData.bank_response)

      if (currentPending.ma_bene_verification_id > 0) {
        if (updateTrfData.bene_verify_status === 'F') {
          this.TABLE_NAME = 'ma_bene_verification'

          console.log('Pending To Failure Refunding>>', currentPending.ma_bene_verification_id)
          console.log('RefundOnlyForAirpaySide>>', currentPending.ma_bene_verification_id)

          if (currentPending.ma_transaction_master_id > 0) {
            const data = { transaction_status: 'F' }
            const updateTransaction = await transactionController.updateWhere(connection, { data, id: currentPending.ma_transaction_master_id, where: 'ma_transaction_master_id' })
            // Revert debit entries
            // Check Point Ledger Entry check
            const pointLedgerEntryRevDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'cr' AND transaction_type = '21' AND parent_id = '${currentPending.aggregator_order_id}'`
            console.log('pointLedgerEntryRevDataSql', pointLedgerEntryRevDataSql)
            const pointLedgerEntryRevData = await this.rawQuery(pointLedgerEntryRevDataSql, connection)

            if (pointLedgerEntryRevData.length == 0) {
              console.log('BankFailureREVERSEDDDDDCRON')
              // await mySQLWrapper.beginTransaction(connection)
              const reverseDebitLedger = await beneficiaryController.reverseDebitLedger('_', {
                connection: connection,
                aggregator_order_id: currentPending.aggregator_order_id,
                ma_user_id: currentPending.ma_user_id
              }, connection)

              if (reverseDebitLedger.status != 200) {
                await mySQLWrapper.rollback(connection)
                return reverseDebitLedger
              }
            } else {
              console.log('BankFailureREVERSEDDDDDCRON SKIPPED')
            }

            this.TABLE_NAME = 'ma_bene_verification'
            const updateResMa = await this.updateWhere(connection, { data: updateTrfData, id: currentPending.ma_bene_verification_id, where: 'ma_bene_verification_id' })

            if (updateResMa.affectedRows <= 0) {
              // Rollback
              console.log('bene verification Data Update Failed Else>>', currentPending.ma_transfers_id)
              await mySQLWrapper.rollback(connection)
              return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' bene verification Data Update Failed Else' }
            }
            await mySQLWrapper.commit(connection)
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' ma_transaction_master_id not found' }
          }
        } else {
          this.TABLE_NAME = 'ma_bene_verification'
          const updateResMa = await this.updateWhere(connection, { data: updateTrfData, id: currentPending.ma_bene_verification_id, where: 'ma_bene_verification_id' })

          if (updateResMa.affectedRows <= 0) {
            // Rollback
            console.log('bene verification Data Update Failed Else>>', currentPending.ma_transfers_id)
            await mySQLWrapper.rollback(connection)
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' bene verification Data Update Failed Else' }
          }
        }

        const data = { transaction_status: updateTrfData.bene_verify_status }

        if (('remark' in beneVerifyData) && beneVerifyData.remark && beneVerifyData.remark != '') {
          data.transaction_reason = currentPending.transaction_reason + '::CRON::' + beneVerifyData.remark
        } else if (('message' in beneVerifyData) && beneVerifyData.message && beneVerifyData.message != '') {
          data.transaction_reason = currentPending.transaction_reason + '::CRON::' + beneVerifyData.message
        }

        const updateTransaction = await transactionController.updateWhere(connection, { data, id: currentPending.ma_transaction_master_id, where: 'ma_transaction_master_id' })
        console.log('update_bene_verification', updateTransaction)

        if (updateTrfData.bene_verify_status == 'S') {
          let customer_charges = 0

          // Check Point Ledger Entry check
          const pointLedgerEntryDataSql = `SELECT ma_points_ledger_master_id FROM ma_points_ledger_master WHERE mode = 'cr' AND transaction_type = '22' AND parent_id = '${currentPending.aggregator_order_id}'`
          console.log('pointLedgerEntryDataSql', pointLedgerEntryDataSql)
          const pointLedgerEntryData = await this.rawQuery(pointLedgerEntryDataSql, connection)
          if (pointLedgerEntryData.length === 0) {
            const globalValues = await beneVerifyIncentiveCtrl.globalValues({
              ma_user_id: currentPending.ma_user_id,
              userid: currentPending.userid,
              ma_bank_on_boarding_id: currentPending.ma_bank_on_boarding_id
            }, connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })

            if (!(typeof (globalValues) === 'boolean') && Object.keys(globalValues).length > 0) {
              customer_charges = globalValues.customer_charges
              console.log('globalSetting ==============', globalValues, 'here', globalValues.customer_charges)
              fields.customer_charges = customer_charges
              console.log('cust charges ==============', customer_charges)

              if (customer_charges > 0) {
                console.log('INCENTIVEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV')
                const distribution = await beneVerifyIncentiveCtrl.incentiveDistribution({
                  ma_user_id: currentPending.ma_user_id,
                  userid: currentPending.userid,
                  connection: connection,
                  customer_charges: customer_charges,
                  aggregator_order_id: currentPending.aggregator_order_id,
                  ma_bank_on_boarding_id: currentPending.ma_bank_on_boarding_id
                }, connection)

                if (distribution.status === 400) {
                  // return distribution
                  console.log('IncentiveFailed>>>', distribution)
                  console.log('bene verification Data Update Failed Else>> reversing')
                  await mySQLWrapper.rollback(connection)
                  return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' bene verification Data Update Failed Else>> reversing' }
                } else {
                  console.log('IncentiveSuccess>>>', distribution)
                }
              }
            }
          } else {
            console.log('IncentiveAlreadyGiven >>', pointLedgerEntryData)
          }
        }

        await mySQLWrapper.commit(connection)
        console.log('ma_transaction_master_id Updated ::', currentPending.ma_transaction_master_id)

        console.log('detail_bank_rrn', detail_bank_rrn)
        try {
          if (detail_bank_rrn != '') {
            const dataDetails = { bank_rrn: detail_bank_rrn, aggregator_order_id: currentPending.aggregator_order_id, customer_name: detail_bank_name }
            await mySQLWrapper.beginTransaction(connection)
            const updateTransactionDetails = await transactionController.updateTransactionDetails(dataDetails, connection)
            console.log('detail_bank_rrn', detail_bank_rrn)
            if (updateTransactionDetails.status == 200) {
              detailsUpdated.push(currentPending.aggregator_order_id)
            }
            await mySQLWrapper.commit(connection)
          } else {
            console.log('detailsUpdated >>', detailsUpdated)
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] + ' detailsUpdated' }
          }
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
        } catch (e) {
          console.log('updateTransactionDetailsError >>', e)
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' updateTransactionDetailsError' }
        }
      } else {
        console.log('ma_bene_verification_id is not valid ')
        await mySQLWrapper.rollback(connection)
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' a_bene_verification_id is not valid ' }
      }
    } catch (err) {
      console.log('reCheckTransferStatusLoopError', err)
      console.log('Error Transfer ReQuery Cron ', err.message)
      // await mySQLWrapper.rollback(connection)

      // throw (err)

      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' ma_transfers_id is not valid' }
    } finally {
      if (tmpConn) connection.release()
    }
  }
}

module.exports = BeneVerification
