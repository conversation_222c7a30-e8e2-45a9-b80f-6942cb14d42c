const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const validator = require('../../util/validator')
const errorMsg = require('../../util/error')
const bankBranch = require('../bankDetails/bankBranchController')
const bankMaster = require('../bankDetails/bankMasterController')
const Routing = require('../bankRouting/routing')
const aml = require('../aml/amlController')
const otp = require('../otp/otpController')
const log = require('../../util/log')
const bankOnBoardDetails = require('../bankHandler/bankOnBoardingDetails')
const util = require('../../util/util')
const sms = require('../../util/sms')
const integrated = require('../integrated/integratedController')
const { reverseLedgerEntries } = require('../transfers/transfersController')
const dmtSession = require('../session/dmt_session')
const beneficiaryBankMap = require('./beneficiaryBankMapController')
const Payment = require('./../bankHandler/payment')
const { getTryBankArray } = require('../../util/common_fns')
const customBankMap = require('../customer/customerBankMapController')
const common_fns = require('../../util/common_fns')
const path = require('path')
/* EXTERNAL API CHANGES */
const AffiliateController = require('../externalApi/affliliateController')
/* RISK MANAGEMENT Changes : NEW CHANGES */
const RiskManagementController = require('../riskManagement/riskManagementController')
// const { lokiLogger } = require('../../util/lokiLogger')
class Beneficiary extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_beneficiaries'
  }

  get PRIMARY_KEY () {
    return 'ma_beneficiaries_id'
  }

  /**
     * Creates a beneficiary
     */
  static async addBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: fields })
    var isInsert = true
    fields.beneficiary_status = 'N'

    // addFrombank flag is used for internally adding beneficiaries

    const isValidAccountNo = validator.validateAccNoAlphaNumeric(fields.account_number)
    if (!isValidAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ' }
    }

    const isValidReAccountNo = validator.validateAccNoAlphaNumeric(fields.re_account_number)
    if (!isValidReAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':re_account_number ' }
    }

    // Mobile number validation
    if (fields.countrycode === null || fields.countrycode === undefined) {
      fields.countrycode = 'IN'
    }

    const isUICDefined = validator.definedVal(fields.uic)
    if (!isUICDefined) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    const isValidMoNumber = validator.validInput('indianmobile', fields.mobile_number)
    if (fields.uic != '' && !isValidMoNumber && !isNaN(fields.uic)) {
      const custDetailsSql = `SELECT mobile_number FROM ma_customer_details where uic='${fields.uic}' limit 1`
      const custDetailsData = await this.rawQuery(custDetailsSql, connection)
      console.log('custDetailsData', custDetailsData)
      if (custDetailsData.length > 0) {
        fields.mobile_number = custDetailsData[0].mobile_number
      } else {
        return { status: 400, respcode: 1041, message: errorMsg.responseCode[1041] }
      }
    } else {
      const validateMobileNo = validator.validateMobile(fields.mobile_number, fields.countrycode)
      if (validateMobileNo.status === false) {
        return { status: 400, respcode: 1111, message: validateMobileNo.message }
      }
    }

    // Mobile number validation beneficiary
    if (fields.ben_mobile_number) {
      if (fields.countrycode === null || fields.countrycode === undefined) {
        fields.countrycode = 'IN'
      }
      const validate = validator.validateMobile(fields.ben_mobile_number, fields.countrycode)
      if (validate.status === false) {
        return { status: 400, respcode: 1111, message: validate.message }
      }
    } else {
      fields.ben_mobile_number = 0
    }

    // IFSC code verification
    const isIFSCDefined = validator.definedVal(fields.ifsc_code)
    if (!isIFSCDefined) {
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
    }

    const beneficiaryDetails = await this.checkBeneficiary(fields)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: beneficiaryDetails })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: beneficiaryDetails })
    if (beneficiaryDetails !== null && beneficiaryDetails !== undefined && beneficiaryDetails.data.length > 0) {
      // Check status of beneficiary
      if (beneficiaryDetails.data[0].beneficiary_status !== 'N') {
        return { status: 400, respcode: 1032, message: errorMsg.responseCode[1032] }
      } else {
        isInsert = false
      }
    } else {
      // Bank name validation
      const verifyBank = await bankMaster.checkBank(fields.ma_bank_master_id)
      if (!verifyBank) {
        return { status: 400, respcode: 1035, message: errorMsg.responseCode[1035] }
      }

      // Account number and Reaccount number
      if (fields.account_number !== fields.re_account_number) {
        return { status: 400, respcode: 1034, message: errorMsg.responseCode[1034] }
      }
      // IFSC code verification
      const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code)
      if (!verifyIFSC) {
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
      }
      // Benificiary name validation
      fields.beneficiary_name = fields.beneficiary_name.trim()
      const validateBenificiaryName = validator.validInput('alphasinglespace', fields.beneficiary_name)
      if (validateBenificiaryName === false) {
        return { status: 400, respcode: 1111, message: errorMsg.responseCode[1111] }
      }
    }

    // check if beneficiary status  in ma_bene_verification
    const beneVerificationDetails = await this.checkBeneVerification(fields)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: beneVerificationDetails })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: beneVerificationDetails })
    if (beneVerificationDetails !== null && beneVerificationDetails !== undefined && beneVerificationDetails.length > 0) {
      // Check status of beneficiary
      if (beneVerificationDetails[0].bene_verify_status === 'I' || beneVerificationDetails[0].bene_verify_status === 'P') {
        return { status: 400, respcode: 1143, message: errorMsg.responseCode[1143] }
      }
    }

    // AML Check
    const amlparams = {
      ma_user_id: fields.ma_user_id,
      userid: fields.userid,
      uic: fields.uic,
      aml_type: ['blacklisting'],
      calltype: 'BEN',
      accountno: fields.account_number,
      mobileno: fields.ben_mobile_number
    }
    const amlValidation = await aml.getAmlDetails('', amlparams)
    if (amlValidation.status === 200) {
      if (amlValidation.blacklisted === 'Y') {
        return { status: 400, respcode: 1066, message: errorMsg.responseCode[1066] }
      }
    } else {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + amlValidation.message }
    }

    // Call routing logic and add beneficiary at bank

    // [START] Added by Majid N. for Beneficiary threshold
    const amlThresholdbene = {
      ma_user_id: fields.ma_user_id,
      uic: fields.uic,
      beneficiary_name: fields.beneficiary_name,
      account_number: fields.account_number,
      mobile_number: fields.ben_mobile_number,
      ifsc_code: fields.ifsc_code,
      bank_master_id: fields.ma_bank_master_id
    }
    const amlThresholdbeneRes = await aml.beneThreshold(amlThresholdbene, connection)
    if (amlThresholdbeneRes.status === 400) {
      return amlThresholdbeneRes
    }
    // [END] Added by Majid N. for Beneficiary threshold

    try {
      var BeneficiaryResult = {}
      var beneficiaryId
      if (isInsert) {
      // Create entry
        if (fields.addFrombank !== null && fields.addFrombank !== undefined) {
          fields.beneficiary_status = 'Y'
        }

        BeneficiaryResult = await this.createEntry(_, fields)
        beneficiaryId = BeneficiaryResult.id
      } else {
      // Update status as Initiated
        // BeneficiaryResult.status = 200
        beneficiaryId = beneficiaryDetails[0].ma_beneficiaries_id

        const agentDetails = { ma_user_id: fields.ma_user_id, userid: fields.userid, ma_bank_master_id: fields.ma_bank_master_id, ifsc_code: fields.ifsc_code, beneficiary_name: fields.beneficiary_name, ben_mobile_number: fields.ben_mobile_number, mobile_number: fields.mobile_number, country_code: fields.countrycode, relationship: fields.relationship }
        const updateAgentDetails = await this.updateWhere(connection, { data: agentDetails, id: beneficiaryId, where: 'ma_beneficiaries_id' })
        BeneficiaryResult.status = 200

        // Update receiver id
        if (fields.addFrombank !== null && fields.addFrombank !== undefined) {
          const bankDetails = { receiver_id: fields.receiver_id, beneficiary_status: 'Y' }
          const updateRes = await this.updateWhere(connection, { data: bankDetails, id: beneficiaryId, where: 'ma_beneficiaries_id' })
          BeneficiaryResult.status = (updateRes.affected_rows > 0) ? 200 : 400
        }
      }

      if (fields.bank_verify === undefined || typeof fields.bank_verify != 'boolean') {
        fields.bank_verify = false
      }

      // Verification Benficiary Process
      fields.ma_bene_verification_id = 0
      let isVerified = false
      let verification_message = ''
      let bank_bene_name = null
      let bank_account_number = 0
      let bene_verify_status = 'U'
      let ma_transaction_master_id = 0
      if (fields.bank_verify === true) {
        fields.ma_beneficiaries_id = beneficiaryId
        const verifyBeneFromBankRes = await this.verifyBeneFromBank(_, fields)
        console.log('verifyBeneFromBankRes>>', verifyBeneFromBankRes)
        if (verifyBeneFromBankRes.status === 200) {
          isVerified = true
          fields.ma_bene_verification_id = verifyBeneFromBankRes.ma_bene_verification_id
          verification_message = verifyBeneFromBankRes.verification_message
          bank_bene_name = verifyBeneFromBankRes.bank_bene_name
          bank_account_number = verifyBeneFromBankRes.bank_account_number
          bene_verify_status = verifyBeneFromBankRes.bene_verify_status
          ma_transaction_master_id = verifyBeneFromBankRes.ma_transaction_master_id ? verifyBeneFromBankRes.ma_transaction_master_id : 0

          const verifyBeneficiaryOtpRes = await this.verifyBeneficiaryOtp(_, {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            orderid: null,
            mobile_number: fields.mobile_number,
            ma_beneficiaries_id: beneficiaryId,
            uic: fields.uic,
            bank_verify: true,
            otp: ''
          })

          if (verifyBeneficiaryOtpRes.status != 200) {
            // if (ma_transaction_master_id > 0) {
            //   const data = { transaction_status: 'F' }
            //   const transactionController = require('../transaction/transactionController')
            //   const updateTransaction = await transactionController.updateWhere(fields.connection, { data, id: ma_transaction_master_id, where: 'ma_transaction_master_id' })
            //   // Revert debit entries
            //   console.log('BankFailureREVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
            //   await mySQLWrapper.beginTransaction(fields.connection)
            //   const reverseDebitLedger = await this.reverseDebitLedger(_, {
            //     connection: fields.connection,
            //     orderid: fields.aggregator_order_id
            //   })
            //   await mySQLWrapper.commit(fields.connection)
            //   if (reverseDebitLedger.status != 200) {
            //     return reverseDebitLedger
            //   }
            // }
            return verifyBeneficiaryOtpRes
          }
        } else {
          if ('message' in verifyBeneFromBankRes) {
            verifyBeneFromBankRes.message += ' [' + fields.aggregator_order_id + ']'
          }
          return verifyBeneFromBankRes
        }
      }

      if (BeneficiaryResult.status === 200) {
        // addFrombank flag exists
        if (fields.addFrombank !== null && fields.addFrombank !== undefined) {
          const BBM = await this.bankBeneficiaryMapping({ beneficiaryId, bank_on_boarding_id: fields.ma_bank_on_boarding_id, connection })
          if (BBM.status === 400) {
            return BBM
          }
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
        }

        // Bank Integration called
        let res = {}
        if (fields.addFrombank !== null && fields.addFrombank !== undefined && fields.addFrombank === true) {
          res = await this.addBeneficiaryBank(beneficiaryId, connection, null, fields.ma_user_id)
        } else {
          res = {
            status: 200,
            bankName: 'NSDL' // Static need ask how to pass this to different bank
          }
        }

        if (res.status === 200) {
        // if success Internal OTP send
          const retailerDetails = await this.getRetailerDetails(fields.ma_user_id, fields.userid, connection)
          let retailerName = ''
          if (retailerDetails.status === 200) {
            retailerName = retailerDetails.details.username
          }
          fields.account_number = fields.account_number.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
          // Bank verification is true then directly activate account
          if (fields.bank_verify === true) {
            const response = {}
            response.status = 200
            response.respcode = 1000
            response.message = errorMsg.responseCode[1000]
            response.ma_beneficiaries_id = beneficiaryId
            response.ma_bene_verification_id = fields.ma_bene_verification_id
            response.verification_message = verification_message
            response.bank_bene_name = bank_bene_name
            response.bank_account_number = bank_account_number
            response.bene_verify_status = bene_verify_status
            return response
          }
          // var otpResp = await otp.sentOtp(fields.ma_user_id, fields.userid, fields.mobile_number, 'BE', { account_number: fields.account_number, agent_name: retailerName, bankName: res.bankName })
          var response = {}
          var otpResp = {
            status: true
          }
          if (otpResp.status === true) {
            response.status = 200
            response.respcode = 1000
            response.message = errorMsg.responseCode[1000]
            response.orderid = fields.aggregator_order_id
            response.ma_beneficiaries_id = beneficiaryId
            response.ma_bene_verification_id = fields.ma_bene_verification_id
            if (fields.bank_verify === true && isVerified === true) {
              response.message += ': Beneficiary Verification Succeeded'
            }
          } else {
            response.status = 400
            response.respcode = otpResp.respcode
            response.message = otpResp.message
          }
          return response
        } else {
          return res
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: errorMsg.responseCode[1042] })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: errorMsg.responseCode[1042] })
        return { status: 400, respcode: 1042, message: errorMsg.responseCode[1042] }
      }
    } catch (err) {
      console.log(err)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + err }
    } finally {
      connection.release()
    }
  }

  /**
   * Creates a beneficiary for multiple banks
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async addBeneficiaryV2 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'request', fields: { _, fields } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'request', fields: { _, fields } })
    let isInsert = true
    fields.beneficiary_status = 'N'

    // addFrombank flag is used for internally adding beneficiaries

    /* DMT SESSION DATA */
    /* RISK MANAGEMENT Changes : NEW CHANGES */
    if (_ && _.ip_address) {
      fields.ip_address = _.ip_address
    }

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
    }

    // Beneficiary name validation
    if (!validator.definedVal(fields.beneficiary_name)) {
      return { status: 400, respcode: 1111, message: errorMsg.responseCode[1111] + ':beneficiary_name ' }
    }

    fields.beneficiary_name = fields.beneficiary_name.trim()
    const validateBenificiaryName = validator.validInput('alphasinglespace', fields.beneficiary_name)
    if (validateBenificiaryName === false) {
      return { status: 400, respcode: 1111, message: errorMsg.responseCode[1111] }
    }

    const isValidAccountNo = validator.validateAccountNo(fields.account_number)
    if (!isValidAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ' }
    }

    const isValidReAccountNo = validator.validateAccountNo(fields.re_account_number)
    if (!isValidReAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':re_account_number ' }
    }

    // Account number and Reaccount number
    if (fields.account_number !== fields.re_account_number) {
      return { status: 400, respcode: 1034, message: errorMsg.responseCode[1034] }
    }

    // Mobile number validation
    if (!validator.definedVal(fields.countrycode)) {
      fields.countrycode = 'IN'
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    // IFSC code verification
    if (!validator.definedVal(fields.ifsc_code)) {
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
    }

    if (!validator.definedVal(fields.aggregator_order_id)) {
      return { status: 400, respcode: 1104, message: errorMsg.responseCode[1104] }
    }

    if (!validator.definedVal(fields.addFrombank)) {
      fields.addFrombank = false
    }

    if (!validator.definedVal(fields.bank_verify)) {
      fields.bank_verify = false
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    const isValidMoNumber = validator.validInput('indianmobile', fields.mobile_number)
    if (validator.definedVal(fields.uic) && !isValidMoNumber && !isNaN(fields.uic)) {
      const custDetailsSql = `SELECT mobile_number FROM ma_customer_details where uic='${fields.uic}' limit 1`
      const custDetailsData = await this.rawQuery(custDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'custDetailsData', fields: custDetailsData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'custDetailsData', fields: custDetailsData })

      if (custDetailsData.length > 0) {
        fields.mobile_number = custDetailsData[0].mobile_number
      } else {
        return { status: 400, respcode: 1041, message: errorMsg.responseCode[1041] }
      }
    } else {
      const validateMobileNo = validator.validateMobile(fields.mobile_number, fields.countrycode)
      if (validateMobileNo.status === false) {
        return { status: 400, respcode: 1111, message: validateMobileNo.message }
      }
    }

    // Mobile number validation beneficiary
    if (validator.definedVal(fields.ben_mobile_number)) {
      const validate = validator.validateMobile(fields.ben_mobile_number, fields.countrycode)
      if (validate.status === false) {
        return { status: 400, respcode: 1111, message: 'Fail: Invalid mobile number provided :mobile_number' }
      }
    } else {
      fields.ben_mobile_number = 0
    }

    /* BENE BLACKLIST CHANGES */
    if (!fields.skipBeneficiaryBlacklistCheck) {
      const isBeneficaryBlackListed = await this.isBeneficaryBlackListed({
        account_number: fields.account_number,
        ifsc_code: fields.ifsc_code,
        connection
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isBeneficaryBlackListed', fields: isBeneficaryBlackListed })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isBeneficaryBlackListed', fields: isBeneficaryBlackListed })

      if (isBeneficaryBlackListed.status != 200) return { status: 400, message: `${errorMsg.responseCode[1028]}: We do not premit money transfers to this account; the inconvenience is regrated `, respcode: 1001, action_code: 1001 }
    }
    // Check beneficiary exist in master list
    const beneficiaryDetails = await this.checkBeneficiary(fields, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'beneficiaryDetails', fields: beneficiaryDetails })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'beneficiaryDetails', fields: beneficiaryDetails })

    if (beneficiaryDetails.status == 400) {
      return beneficiaryDetails
    }

    if (beneficiaryDetails.status === 200 && beneficiaryDetails.data.length > 0) {
      // Check status of beneficiary
      if (beneficiaryDetails.data[0].beneficiary_status !== 'N') {
        return { status: 400, respcode: 1032, message: errorMsg.responseCode[1032] }
      } else {
        isInsert = false
      }
    }

    // Bank name validation
    const verifyBank = await bankMaster.checkBank(fields.ma_bank_master_id, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyBank', fields: verifyBank })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyBank', fields: verifyBank })

    if (verifyBank === 400) {
      return { status: 400, respcode: 1035, message: errorMsg.responseCode[1035] }
    }

    // IFSC code verification
    const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyIFSC', fields: verifyIFSC })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyIFSC', fields: verifyIFSC })

    if (verifyIFSC.status === 400) {
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
    }

    // check if beneficiary status  in ma_bene_verification
    const beneVerificationDetails = await this.checkBeneVerification(fields, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'beneVerificationDetails', fields: beneVerificationDetails })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'beneVerificationDetails', fields: beneVerificationDetails })
    if (beneVerificationDetails.status == 200) {
      // Check status of beneficiary
      if (beneVerificationDetails.data.length > 0) {
        if (beneVerificationDetails.data[0].bene_verify_status === 'I' || beneVerificationDetails.data[0].bene_verify_status === 'P') {
          return { status: 400, respcode: 1143, message: errorMsg.responseCode[1143] }
        }
      }
    } else {
      return beneVerificationDetails
    }

    // AML Check
    const amlparams = {
      ma_user_id: fields.ma_user_id,
      userid: fields.userid,
      uic: fields.uic,
      aml_type: ['blacklisting'],
      calltype: 'BEN',
      accountno: fields.account_number,
      mobileno: fields.ben_mobile_number
    }
    const amlValidation = await aml.getAmlDetails('', amlparams, connection)
    if (amlValidation.status === 200) {
      if (amlValidation.blacklisted === 'Y') {
        return { status: 400, respcode: 1066, message: errorMsg.responseCode[1066] }
      }
    } else {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + amlValidation.message }
    }

    // Call routing logic and add beneficiary at bank
    const sessionParams = {
      ma_user_id: fields.ma_user_id,
      user_id: fields.userid,
      uic: fields.uic
    }

    if (validator.definedVal(fields.ma_bank_on_boarding_id) && fields.ma_bank_on_boarding_id > 0) {
      sessionParams.ma_bank_on_boarding_id = fields.ma_bank_on_boarding_id
    } else if (fields._beneVerifyOtp) {
      // emitra
      const common = require('../../util/common')
      sessionParams.ma_bank_on_boarding_id = await common.getSystemCodes(this, util.default_bank_for_aeps_kiosk, connection)
      fields.ma_bank_verification_id = sessionParams.ma_bank_on_boarding_id
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'default_bank_for_aeps_kiosk', fields: { default_bank_for_aeps_kiosk: sessionParams.ma_bank_on_boarding_id } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'default_bank_for_aeps_kiosk', fields: { default_bank_for_aeps_kiosk: sessionParams.ma_bank_on_boarding_id } })
    }

    const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, sessionParams)
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'sessionData', fields: sessionData })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'sessionData', fields: sessionData })

    if (sessionData.status == 400) {
      return sessionData
    }

    const { bank_name, ma_bank_on_boarding_id, uicSession, bank_priority_json } = sessionData.data
    const handler = sessionData.handler

    try {
      let BeneficiaryResult = {}
      let beneficiaryId
      let beneficiaryDetailDataIp = {}
      if (isInsert) {
        // Create entry
        // This flag will be Y as we fetch details from Bank Side Bene List which are already added
        if (fields.addFrombank === true) {
          fields.beneficiary_status = 'Y'
        }

        BeneficiaryResult = await this.createEntry(_, fields, connection)
        if (BeneficiaryResult === 400) {
          return BeneficiaryResult
        }
        beneficiaryId = BeneficiaryResult.id
        beneficiaryDetailDataIp = BeneficiaryResult.data
      } else {
        // Update status as Initiated
        // BeneficiaryResult.status = 200
        beneficiaryId = beneficiaryDetails.data[0].ma_beneficiaries_id

        const agentDetails = { ma_user_id: fields.ma_user_id, userid: fields.userid, ma_bank_master_id: fields.ma_bank_master_id, ifsc_code: fields.ifsc_code, beneficiary_name: fields.beneficiary_name, ben_mobile_number: fields.ben_mobile_number, mobile_number: fields.mobile_number, country_code: fields.countrycode, relationship: fields.relationship }

        if (fields.addFrombank === true) {
          agentDetails.receiver_id = fields.receiver_id
          agentDetails.beneficiary_status = 'Y'
        }
        beneficiaryDetailDataIp = agentDetails
        const updateAgentDetails = await this.updateWhere(connection, { data: agentDetails, id: beneficiaryId, where: 'ma_beneficiaries_id' })
        BeneficiaryResult.status = 200
      }

      if (fields.bank_verify === undefined || typeof fields.bank_verify != 'boolean') {
        fields.bank_verify = false
      }

      // Verification Benficiary Process
      fields.ma_bene_verification_id = 0
      let isVerified = false
      let verification_message = ''
      let bank_bene_name = null
      let bank_account_number = 0
      let bene_verify_status = 'U'
      let ma_transaction_master_id = 0
      // PENNY DROP
      if (fields.bank_verify === true) {
        // Airtel verify otp internal call
        if (handler.BANK_ON_BOARDING_ID == 10) {
          const customerController = require('../customer/customerController')
          // session token api
          const sessionTokenResult = await customerController.airtelGenerateSessionTokenApi(fields)
          log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'sessionTokenResult', fields: sessionTokenResult })
          if (sessionTokenResult.status != 200) return sessionTokenResult
          const statusResultApi = await customerController.airtelGetCustomerApi(fields, sessionTokenResult.data.token)
          log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'statusResultApi', fields: statusResultApi })
          if (statusResultApi.status != 200) return statusResultApi
          if (statusResultApi.data.meta.status == 0 && statusResultApi.data.meta.code == '000') {
            // transaction verify otp
            const request_number = await this.generateClientId()
            const obj = {
              otp_type: 5,
              amount: '1',
              mobile_number: fields.mobile_number,
              otp_pin: fields.otp_pin,
              otp_id: fields.otp_request_id,
              requestNumber: request_number
            }
            log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyOTPReq', fields: obj })
            const Payment = require('./../bankHandler/payment')
            const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
            console.time('Timer_BANK_INIT_TRANSACTION')
            const resRemit = await payment.requestToBank('VERIFY_OTP', obj)
            console.timeEnd('Timer_BANK_INIT_TRANSACTION')
            log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyOTPRes', fields: resRemit })
            if (resRemit.status != 200) {
              return resRemit
            }
            fields.token = resRemit.token
          } else {
            return { status: 400, message: statusResultApi.data.meta.description || 'Fail: API Error', respcode: 1001, action_code: 1001 }
          }
        }
        // fino changes
        /* CHECK INTERBANK CASE */

        const interBankResp = await this.isInterbankAllowed({
          bankHandler: handler,
          receiverBank: verifyBank.data.bank_name,
          connection
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isInterbankAllowed', fields: interBankResp })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isInterbankAllowed', fields: interBankResp })

        if (interBankResp.status != 200) return interBankResp
        fields.ma_beneficiaries_id = beneficiaryId
        fields.sessionData = sessionData
        const verifyBeneFromBankRes = await this.verifyBeneFromBank(_, fields, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyBeneFromBankRes', fields: verifyBeneFromBankRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'verifyBeneFromBankRes', fields: verifyBeneFromBankRes })

        if (verifyBeneFromBankRes.status === 200) {
          isVerified = true
          fields.ma_bene_verification_id = verifyBeneFromBankRes.ma_bene_verification_id
          verification_message = verifyBeneFromBankRes.verification_message
          bank_bene_name = verifyBeneFromBankRes.bank_bene_name
          bank_account_number = verifyBeneFromBankRes.bank_account_number
          bene_verify_status = verifyBeneFromBankRes.bene_verify_status
          ma_transaction_master_id = verifyBeneFromBankRes.ma_transaction_master_id ? verifyBeneFromBankRes.ma_transaction_master_id : 0

          if (fields._beneVerifyOtp == true) {
            const verifyBeneficiaryOtpRes = await this.verifyBeneficiaryOtpV2(_, {
              ma_user_id: fields.ma_user_id,
              userid: fields.userid,
              orderid: null,
              mobile_number: fields.mobile_number,
              ma_beneficiaries_id: beneficiaryId,
              uic: fields.uic,
              bank_verify: true,
              otp: '',
              sessionRQ: fields.sessionRQ,
              sessionData: fields.sessionData
            }, connection)

            if (verifyBeneficiaryOtpRes.status != 200) {
              return verifyBeneficiaryOtpRes
            }
          }
        } else {
          if ('message' in verifyBeneFromBankRes) {
            verifyBeneFromBankRes.message += ' [' + fields.aggregator_order_id + ']'
          }

          /** TRY WITH ANOTHER BANK ARRAY  */

          if (validator.validJson(bank_priority_json)) {
            // verifyBeneFromBankRes.tryWithOtherBank = await beneficiaryBankMap.getTryWithOtherBankArray({
            //   bank_priority_json: bank_priority_json,
            //   skipBankId: handler.BANK_ON_BOARDING_ID,
            //   uic: fields.uic
            // }, connection)
            verifyBeneFromBankRes.tryWithOtherBank = []
          } else {
            verifyBeneFromBankRes.tryWithOtherBank = []
          }

          return verifyBeneFromBankRes
        }
      }

      if (BeneficiaryResult.status === 200) {
        // Bank Integration called here if direct beneaddition required then add
        let res = {}
        if (fields.addFrombank === true) {
          res = await beneficiaryBankMap.addBeneficiaryBank(handler, { ma_beneficiaries_id: fields.ma_beneficiaries_id, ma_user_id: fields.ma_user_id, user_id: fields.userid }, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'addBeneficiaryBank', fields: res })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'addBeneficiaryBank', fields: res })
          if (res == 400) {
            return res
          }
        } else {
          res = {
            status: 200,
            bankName: bank_name // Static need ask how to pass this to different bank
          }
        }
        // ################## here we come
        if (res.status === 200) {
          const isOtpRequired = await this.isAddBeneficiaryOTPRequired({ bankHandler: handler, connection })

          // to do add new Flag for next step
          // if success Internal OTP send {}

          fields.account_number = fields.account_number.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
          // Bank verification is true then directly activate account
          if (fields.bank_verify === true) {
            const response = {}
            response.status = 200
            response.respcode = 1000
            response.message = errorMsg.responseCode[1000]
            response.ma_beneficiaries_id = beneficiaryId
            response.ma_bene_verification_id = fields.ma_bene_verification_id
            response.verification_message = verification_message
            response.bank_bene_name = bank_bene_name
            response.bank_account_number = bank_account_number
            response.bene_verify_status = bene_verify_status
            response.ifsc_code = fields.ifsc_code
            response.orderid = fields.aggregator_order_id
            response.tryWithOtherBank = []
            response.isOtpRequired = isOtpRequired
            return response
          }
          const response = {}
          response.status = 200
          response.respcode = 1000
          response.message = errorMsg.responseCode[1000]
          response.orderid = fields.aggregator_order_id
          response.ma_beneficiaries_id = beneficiaryId
          response.ma_bene_verification_id = fields.ma_bene_verification_id
          response.ifsc_code = fields.ifsc_code
          if (fields.bank_verify === true && isVerified === true) {
            response.message += ': Beneficiary Verification Succeeded'
          }
          response.isOtpRequired = isOtpRequired
          /* Send Add Benficiary OTP  */
          if (isOtpRequired === true) {
            fields.bank_name = verifyBank.data.bank_name
            const sendOtpResult = await otp.sentBeneficiaryOtp({
              fields,
              connection,
              handler,
              otp_type: 'BE',
              bankOtp: true,
              BANK_OP: 'SEND_OTP_ADD_BENEFICIARY'
            })
            // if bene register at bank end then register at our end
            if (sendOtpResult.status == 400) {
              return sendOtpResult
            }
            response.orderid = sendOtpResult.data.aggregator_order_id
            response.respcode = 2002
            response.message = errorMsg.responseCode[2002]
          }

          return response
        } else {
          return res
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'response', fields: errorMsg.responseCode[1042] })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'response', fields: errorMsg.responseCode[1042] })
        return { status: 400, respcode: 1042, message: errorMsg.responseCode[1042] }
      }
    } catch (err) {
      console.log(err)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'response', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[ADD_BEN]' }
    } finally {
      connection.release()
    }
  }

  /**
   * External API "Creates a beneficiary for multiple banks"
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async addAffiliateRemitterBeneficiary ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    let isInsert = true
    fields.beneficiary_status = 'N'
    try {
      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (fields.ipAddress) {
        fields.ip_address = fields.ipAddress
      }
      /* BENE BLACKLIST CHANGES */
      const isBeneficaryBlackListed = await this.isBeneficaryBlackListed({
        account_number: fields.account_number,
        ifsc_code: fields.ifsc_code,
        connection: conn
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isBeneficaryBlackListed', fields: isBeneficaryBlackListed })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isBeneficaryBlackListed', fields: isBeneficaryBlackListed })

      if (isBeneficaryBlackListed.status != 200) return { status: 400, message: `${errorMsg.responseCode[1028]}: We do not permit money transfers to this account; the inconvenience is regretted.`, respcode: 1001, action_code: 1001 }

      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (fields.ipAddress) {
        fields.ip_address = fields.ipAddress
      }
      const fieldsValidationResponse = await this.fieldsValidation({ fields, connection: conn, connectionRead: connRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'fieldsValidationResponse', fields: fieldsValidationResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'fieldsValidationResponse', fields: fieldsValidationResponse })

      if (fieldsValidationResponse.status != 200) return fieldsValidationResponse

      /* UPDATE THE REQUEST */
      fields = { ...fields, ...fieldsValidationResponse.fields }

      // Check beneficiary exist in master list
      const beneficiaryDetails = await this.checkBeneficiary(fields, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'beneficiaryDetails', fields: beneficiaryDetails })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'beneficiaryDetails', fields: beneficiaryDetails })

      if (beneficiaryDetails.status == 400) return beneficiaryDetails

      if (beneficiaryDetails.status === 200 && beneficiaryDetails.data.length > 0) {
      // Check status of beneficiary
        isInsert = false
        if (beneficiaryDetails.data[0].beneficiary_status !== 'N') return { status: 400, respcode: 1032, message: errorMsg.responseCode[1032] }
      }

      // Call routing logic and add beneficiary at bank
      const sessionParams = {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic
      }

      if (validator.definedVal(fields.ma_bank_on_boarding_id) && fields.ma_bank_on_boarding_id > 0) {
        sessionParams.ma_bank_on_boarding_id = fields.ma_bank_on_boarding_id
      }

      const sessionData = await dmtSession.getDMTSessionData(conn, fields.sessionRQ, sessionParams)
      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'sessionData', fields: sessionData })

      if (sessionData.status == 400) return sessionData

      const { bank_name, ma_bank_on_boarding_id, uicSession, bank_priority_json } = sessionData.data
      const handler = sessionData.handler

      let BeneficiaryResult = {}
      let beneficiaryId
      let beneficiaryDetailDataIp = {}
      // Create entry
      if (isInsert) {
        // This flag will be Y as we fetch details from Bank Side Bene List which are already added
        if (fields.addFrombank === true) {
          fields.beneficiary_status = 'Y'
        }

        BeneficiaryResult = await this.createEntry('_', fields, conn)
        if (BeneficiaryResult === 400) return BeneficiaryResult

        beneficiaryId = BeneficiaryResult.id
        beneficiaryDetailDataIp = BeneficiaryResult.data
      } else {
        // Update status as Initiated
        // BeneficiaryResult.status = 200
        beneficiaryId = beneficiaryDetails.data[0].ma_beneficiaries_id

        const agentDetails = { ma_user_id: fields.ma_user_id, userid: fields.userid, ma_bank_master_id: fields.ma_bank_master_id, ifsc_code: fields.ifsc_code, beneficiary_name: fields.beneficiary_name, ben_mobile_number: fields.ben_mobile_number, mobile_number: fields.mobile_number, country_code: fields.countrycode, relationship: fields.relationship }

        if (fields.addFrombank === true) {
          agentDetails.receiver_id = fields.receiver_id
          agentDetails.beneficiary_status = 'Y'
        }
        beneficiaryDetailDataIp = agentDetails
        const updateAgentDetails = await this.updateWhere(conn, { data: agentDetails, id: beneficiaryId, where: 'ma_beneficiaries_id' })
        BeneficiaryResult.status = 200
      }

      if (fields.bank_verify === undefined || typeof fields.bank_verify != 'boolean') {
        fields.bank_verify = false
      }

      fields.ma_beneficiaries_id = beneficiaryId
      // Verification Benficiary Process
      fields.ma_bene_verification_id = 0
      let isVerified = false
      let verification_message = ''
      let bank_bene_name = null
      let bank_account_number = 0
      let bene_verify_status = 'U'
      let ma_transaction_master_id = 0
      // PENNY DROP
      if (fields.bank_verify === true) {
        // fino changes
        /* CHECK INTERBANK CASE */

        const interBankResp = await this.isInterbankAllowed({
          bankHandler: handler,
          receiverBank: fields.verifyBank.data.bank_name,
          connection
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isInterbankAllowed', fields: interBankResp })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'isInterbankAllowed', fields: interBankResp })

        if (interBankResp.status != 200) return interBankResp
        fields.ma_beneficiaries_id = beneficiaryId
        fields.sessionData = sessionData
        const verifyBeneFromBankRes = await this.affiliateVerifyBeneFromBank({ fields, connection: conn, connectionRead: connRead })

        log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'verifyBeneFromBankRes', fields: verifyBeneFromBankRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'verifyBeneFromBankRes', fields: verifyBeneFromBankRes })

        await this.updateWhere(conn, { data: { beneficiary_status: 'N' }, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })

        if (verifyBeneFromBankRes.status === 200) {
          isVerified = true
          fields.ma_bene_verification_id = verifyBeneFromBankRes.ma_bene_verification_id
          verification_message = verifyBeneFromBankRes.verification_message
          bank_bene_name = verifyBeneFromBankRes.bank_bene_name
          bank_account_number = verifyBeneFromBankRes.bank_account_number
          bene_verify_status = verifyBeneFromBankRes.bene_verify_status
          ma_transaction_master_id = verifyBeneFromBankRes.ma_transaction_master_id ? verifyBeneFromBankRes.ma_transaction_master_id : 0
        } else {
          if ('message' in verifyBeneFromBankRes) {
            verifyBeneFromBankRes.message += ' [' + fields.aggregator_order_id + ']'
          }
        }
        verifyBeneFromBankRes.ma_beneficiaries_id = fields.ma_beneficiaries_id
        verifyBeneFromBankRes.order_id = fields.aggregator_order_id
        verifyBeneFromBankRes.transaction_id = fields.transaction_id
        return verifyBeneFromBankRes
      }

      if (BeneficiaryResult.status != 200) return { status: 400, respcode: 1042, message: errorMsg.responseCode[1042] }

      const isOtpRequired = await this.isAddBeneficiaryOTPRequired({ bankHandler: handler, connection: connRead })

      // to do add new Flag for next step
      // if success Internal OTP send {}

      fields.account_number = fields.account_number.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*')
      // Bank verification is true then directly activate account
      const response = {}
      if (fields.bank_verify === true) {
        response.status = 200
        response.respcode = 1000
        response.message = errorMsg.responseCode[1000]
        response.ma_beneficiaries_id = beneficiaryId
        response.ma_bene_verification_id = fields.ma_bene_verification_id
        response.verification_message = verification_message
        response.bank_bene_name = bank_bene_name
        response.bank_account_number = bank_account_number
        response.bene_verify_status = bene_verify_status
        response.ifsc_code = fields.ifsc_code
        response.orderid = fields.aggregator_order_id
        response.tryWithOtherBank = []
        response.isOtpRequired = isOtpRequired
        // return response
      }

      response.status = 200
      response.respcode = 1000
      response.message = errorMsg.responseCode[1000]
      response.orderid = fields.aggregator_order_id
      response.ma_beneficiaries_id = beneficiaryId
      response.ma_bene_verification_id = fields.ma_bene_verification_id
      response.ifsc_code = fields.ifsc_code
      if (fields.bank_verify === true && isVerified === true) {
        response.message += ': Beneficiary Verification Succeeded'
      }
      response.isOtpRequired = isOtpRequired
      /* Send Add Benficiary OTP  */
      if (isOtpRequired === true) {
        fields.bank_name = fields.verifyBank.data.bank_name
        const sendOtpResult = await otp.sentBeneficiaryOtp({
          fields,
          conn,
          handler,
          otp_type: 'BE',
          bankOtp: true,
          BANK_OP: 'SEND_OTP_ADD_BENEFICIARY'
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'sentBeneficiaryOtp', type: 'response', fields: sendOtpResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'sentBeneficiaryOtp', type: 'response', fields: sendOtpResult })
        // if bene register at bank end then register at our end
        if (sendOtpResult.status == 400) {
          await this.updateWhere(conn, { data: { beneficiary_status: 'N' }, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })
          return sendOtpResult
        }
        response.orderid = sendOtpResult.data.aggregator_order_id
        response.respcode = 2202
        response.message = 'OTP Send Successfully'
      }

      if (fields._beneVerifyOtp == true) {
        const verifyBeneficiaryOtpRes = await this.verifyBeneficiaryOtpV2('_', {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          orderid: null,
          mobile_number: fields.mobile_number,
          ma_beneficiaries_id: beneficiaryId,
          uic: fields.uic,
          bank_verify: true,
          otp: '',
          sessionRQ: fields.sessionRQ,
          sessionData: fields.sessionData
        }, conn)

        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'response', fields: verifyBeneficiaryOtpRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'response', fields: verifyBeneficiaryOtpRes })

        if (verifyBeneficiaryOtpRes.status != 200) {
          return verifyBeneficiaryOtpRes
        }
      }

      // Bank Integration called here if direct beneaddition required then add
      let res = {}
      if (fields.addFrombank === true && !isOtpRequired) {
        res = await beneficiaryBankMap.addBeneficiaryBank(handler, { ma_beneficiaries_id: fields.ma_beneficiaries_id, ma_user_id: fields.ma_user_id, user_id: fields.userid }, conn)
        log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'addBeneficiaryBank', fields: res })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'addBeneficiaryBank', fields: res })
        if (res == 400) {
          return res
        }
      } else {
        res = {
          status: 200,
          bankName: bank_name // Static need ask how to pass this to different bank
        }
      }

      if (isOtpRequired || res.status !== 200) {
        await this.updateWhere(conn, { data: { beneficiary_status: 'N' }, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })
      }

      if (res.status !== 200) return res

      return response
    } catch (err) {
      console.log(err)
      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'response', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'response', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[ADD_BEN]' }
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   * EXTERNAL API
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async fieldsValidation ({ fields, connection, connectionRead }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* DMT SESSION DATA */
      if (!validator.definedVal(fields.sessionRQ)) {
        return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
      }

      // Beneficiary name validation
      if (!validator.definedVal(fields.beneficiary_name)) {
        return { status: 400, respcode: 1111, message: errorMsg.responseCode[1111] + ':beneficiary_name ' }
      }

      fields.beneficiary_name = fields.beneficiary_name.trim()
      const validateBenificiaryName = validator.validInput('alphasinglespace', fields.beneficiary_name)
      if (validateBenificiaryName === false) {
        return { status: 400, respcode: 1111, message: errorMsg.responseCode[1111] }
      }

      const isValidAccountNo = validator.validateAccNoAlphaNumeric(fields.account_number)
      if (!isValidAccountNo) {
        return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ' }
      }

      const isValidReAccountNo = validator.validateAccNoAlphaNumeric(fields.re_account_number)
      if (!isValidReAccountNo) {
        return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':re_account_number ' }
      }

      // Account number and Reaccount number
      if (fields.account_number !== fields.re_account_number) {
        return { status: 400, respcode: 1034, message: errorMsg.responseCode[1034] }
      }

      // Mobile number validation
      if (!validator.definedVal(fields.countrycode)) {
        fields.countrycode = 'IN'
      }

      if (!validator.definedVal(fields.uic)) {
        return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
      }

      // IFSC code verification
      if (!validator.definedVal(fields.ifsc_code)) {
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
      }

      if (!validator.definedVal(fields.addFrombank)) {
        fields.addFrombank = false
      }

      if (!validator.definedVal(fields.bank_verify)) {
        fields.bank_verify = false
      }
      const isValidMoNumber = validator.validInput('indianmobile', fields.mobile_number)
      if (validator.definedVal(fields.uic) && !isValidMoNumber && !isNaN(fields.uic)) {
        const custDetailsSql = `SELECT mobile_number FROM ma_customer_details where uic='${fields.uic}' limit 1`
        const custDetailsData = await this.rawQuery(custDetailsSql, connRead)
        log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'custDetailsData', fields: custDetailsData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'custDetailsData', fields: custDetailsData })

        if (custDetailsData.length > 0) {
          fields.mobile_number = custDetailsData[0].mobile_number
        } else {
          return { status: 400, respcode: 1041, message: errorMsg.responseCode[1041] }
        }
      } else {
        const validateMobileNo = validator.validateMobile(fields.mobile_number, fields.countrycode)
        if (validateMobileNo.status === false) {
          return { status: 400, respcode: 1111, message: validateMobileNo.message }
        }
      }

      // Mobile number validation beneficiary
      if (validator.definedVal(fields.ben_mobile_number)) {
        const validate = validator.validateMobile(fields.ben_mobile_number, fields.countrycode)
        if (validate.status === false) {
          return { status: 400, respcode: 1111, message: 'Fail: Invalid mobile number provided :mobile_number' }
        }
      } else {
        fields.ben_mobile_number = 0
      }

      // Bank name validation
      const verifyBank = await bankMaster.checkBank(fields.ma_bank_master_id, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'verifyBank', fields: verifyBank })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'verifyBank', fields: verifyBank })

      if (verifyBank === 400) {
        return { status: 400, respcode: 1035, message: errorMsg.responseCode[1035] }
      }

      fields.verifyBank = verifyBank

      // IFSC code verification
      const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'verifyIFSC', fields: verifyIFSC })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'verifyIFSC', fields: verifyIFSC })

      if (verifyIFSC.status === 400) {
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] }
      }

      // check if beneficiary status  in ma_bene_verification
      const beneVerificationDetails = await this.checkBeneVerification(fields, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'beneVerificationDetails', fields: beneVerificationDetails })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addAffiliateRemitterBeneficiary', type: 'beneVerificationDetails', fields: beneVerificationDetails })

      if (beneVerificationDetails.status != 200) {
        return beneVerificationDetails
      }

      // Check status of beneficiary
      if (beneVerificationDetails.data.length > 0) {
        if (beneVerificationDetails.data[0].bene_verify_status === 'I' || beneVerificationDetails.data[0].bene_verify_status === 'P') {
          return { status: 400, respcode: 1143, message: errorMsg.responseCode[1143] }
        }
      }

      // AML Check
      const amlparams = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        uic: fields.uic,
        aml_type: ['blacklisting'],
        calltype: 'BEN',
        accountno: fields.account_number,
        mobileno: fields.ben_mobile_number
      }
      const amlValidation = await aml.getAmlDetails('', amlparams, conn)

      if (amlValidation.status != 200) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : ' + amlValidation.message }
      }
      if (amlValidation.blacklisted === 'Y') {
        return { status: 400, respcode: 1066, message: errorMsg.responseCode[1066] }
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], fields }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'fieldsValidation', type: 'catch-error', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'fieldsValidation', type: 'catch-error', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  static async isAddBeneficiaryOTPRequired ({ bankHandler, connection }) {
    return await this.getMethodFlag({
      ma_bank_on_boarding_id: bankHandler.BANK_ON_BOARDING_ID,
      method: 'BENEFICIARY',
      connection
    })
  }

  static async addBeneficiaryResendOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryResendOtp', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryResendOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (!validator.definedVal(fields.countrycode)) {
        fields.countrycode = 'IN'
      }

      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
      if (validate.status === false) {
        return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033] }
      }

      if (!validator.definedVal(fields.sessionRQ)) {
        return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
      }

      if (!validator.definedVal(fields.orderid)) {
        return { status: 400, respcode: 1104, message: errorMsg.responseCode[1104] }
      }

      const otpBankSQL = `select ma_bank_on_boarding_id FROM ma_otp_master WHERE aggregator_order_id = "${fields.orderid}" limit 1`
      const otpBankData = await this.rawQuery(otpBankSQL, connection)

      if (otpBankData.length == 0) {
        return { status: 400, respcode: 1104, message: errorMsg.responseCode[1104] }
      }

      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        ma_bank_on_boarding_id: otpBankData[0].ma_bank_on_boarding_id
      })

      if (sessionData.status == 400) {
        return sessionData
      }
      const bankHandler = sessionData.handler

      const isOtpRequired = await this.isAddBeneficiaryOTPRequired({ bankHandler, connection })

      if (isOtpRequired) {
        const sendOtpResult = await otp.resentBeneficiaryOtp({
          fields,
          connection,
          handler: bankHandler,
          otp_type: 'BE',
          BANK_OP: 'RESEND_OTP_ADD_BENEFICIARY'
        })

        if (sendOtpResult.status == 400) {
          return sendOtpResult
        }
        return { status: 200, message: errorMsg.responseCode[2002], respcode: 2002 }
      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryResendOtp', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiaryResendOtp', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async isBeneMobileMandatory ({ handler, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const bankDetailData = await bankOnBoardDetails.getBanksDetails(null, { ma_bank_on_boarding_id: handler.ma_bank_on_boarding_id, entity_type: 'IS_BENEMOBILE_MANDATORY' }, conn)

      let isBeneMobileMandatory = false
      log.logger({ pagename: require('path').basename(__filename), action: 'isBeneMobileMandatory', type: 'bankDetailData', fields: bankDetailData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneMobileMandatory', type: 'bankDetailData', fields: bankDetailData })

      if (bankDetailData.status == 200 && bankDetailData.data.length > 0) {
        const bankData = bankDetailData.data
        if (bankData[0].otp_required === 'YES') {
          isBeneMobileMandatory = true
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, flag: isBeneMobileMandatory }
    } catch (error) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  // static async verifyBeneficiaryOtp (_, fields) {
  //   log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtp', type: 'request', fields: fields })
  //   this.TABLE_NAME = 'ma_beneficiaries'
  //   const verify = {}
  //   let isSet = false
  //   let connection = null
  //   try {
  //     if (fields.connection === null || fields.connection === undefined) {
  //       connection = await mySQLWrapper.getConnectionFromPool()
  //       isSet = true
  //     } else {
  //       connection = fields.connection
  //     }
  //     if (fields.bank_verify != undefined && fields.bank_verify === true) {
  //       verify.status = true
  //     } else {
  //       // verify = await otp.verifyOtp(fields.orderid, 'BE', fields.otp)
  //       const securePinCtrl = require('../securityPin/securityPinController')
  //       const securePinData = await securePinCtrl.verifySecurePin(null, {
  //         ma_user_id: fields.ma_user_id,
  //         userid: fields.userid,
  //         security_pin: fields.security_pin,
  //         connection: connection
  //       })

  //       console.log('securePinData', securePinData)

  //       if (securePinData.status === 400) {
  //         return securePinData
  //       }
  //       verify.status = true
  //     }

  //     if (verify.status === true) {
  //       // update Beneficiary status as Verified

  //       const res = await this.addBeneficiaryBank(fields.ma_beneficiaries_id, connection, null, fields.ma_user_id)

  //       if (res.status === 200) {
  //         const updated = await this.updateBeneficiary('Y', fields.ma_beneficiaries_id, 0, connection)
  //         if (updated.status === 400) {
  //           console.log('Updated details in beneficiary', updated)
  //           return updated
  //         }
  //       } else {
  //         return res
  //       }

  //       const beneSql = `SELECT *, concat(bank_name,', ',branch) as bank_name FROM ma_beneficiaries b join ma_bank_master mbm on b.ma_bank_master_id = mbm.ma_bank_master_id join ma_bank_branch mbb on mbb.ifsc = b.ifsc_code where uic='${fields.uic}' and beneficiary_status='Y' and ma_beneficiaries_id=${fields.ma_beneficiaries_id}`
  //       const beneficiary_list = await this.rawQuery(beneSql, connection)
  //       console.log('Details', beneficiary_list)
  //       // ------- SEND BENIFICIARY SUCCESS MESSAGE ------
  //       const benfData = await this.getBeneficiaryDetails(fields.ma_beneficiaries_id, connection)
  //       console.log(benfData)
  //       if (benfData.status === 200 && benfData.details.mobile_number) {
  //         const BankSignature = util.bankNameCommunication[benfData.details.bank_name]
  //         let message = util.communication.BENFSUCCESS
  //         message = message.replace('<benf_name>', benfData.details.beneficiary_name)
  //         message = message.replace('<Customer>', 'Customer')
  //         const accNumber = benfData.details.account_number.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '')
  //         message = message.replace('<account_number>', accNumber)
  //         // message = message.replace('<account_number>', benfData.details.account_number)
  //         message = message.replace('<ifsc>', benfData.details.ifsc_code)
  //         message = message.replace('<Salutation>', util.communication.Signature + BankSignature)
  //         await sms.sentSmsAsync(message, benfData.details.mobile_number, util.templateid.BENFSUCCESS)
  //       }
  //       return { status: 200, respcode: 1087, message: errorMsg.responseCode[1087], beneficiary_list }
  //     }
  //     return { status: 400, respcode: verify.respcode, message: verify.message }
  //   } catch (error) {
  //     console.log('verifyBeneficiaryOtpError>>', error)
  //     return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' at Verify beneficiary' }
  //   } finally {
  //     if (isSet) {
  //       connection.release()
  //     }
  //   }
  // }

  static async verifyBeneficiaryOtpV2 (_, fields, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiaries'
    const verify = {}
    const bank_verify = validator.definedVal(fields.bank_verify) && fields.bank_verify === true

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161], action_code: 1001 }
    }

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161], action_code: 1001 }
    }

    const tmpConn = !(validator.definedVal(conn) && validator.definedVal(conn.threadId))
    const connection = tmpConn ? await mySQLWrapper.getConnectionFromPool() : conn

    let sessionData = {}
    try {
      // isOtpRequired fields missing case handled for old app
      // eslint-disable-next-line no-prototype-builtins
      if (!fields.hasOwnProperty('isOtpRequired')) {
        fields.isOtpRequired = false
      }
      // Call routing logic and add beneficiary at bank

      if (validator.definedVal(fields.sessionData)) {
        sessionData = fields.sessionData
      } else {
        let ma_bank_on_boarding_id = null
        if (bank_verify === true && validator.definedVal(fields.ma_bene_verification_id) && fields.ma_bene_verification_id > 0) {
          const verifySql = `SELECT b.ma_beneficiaries_id,mbb.ma_bene_verification_id,
          mbb.ma_bank_on_boarding FROM ma_beneficiaries b join ma_bene_verification mbb on mbb.ma_bene_verification_id = b.ma_bene_verification_id where b.uic='${fields.uic}' and b.beneficiary_status='N' and mbb.bene_verify_status IN ('S','P') and b.ma_beneficiaries_id=${fields.ma_beneficiaries_id} and b.ma_bene_verification_id=${fields.ma_bene_verification_id} limit 1`
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'verifySql', fields: { verifySql } })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'verifySql', fields: { verifySql } })
          const verifiedData = await this.rawQuery(verifySql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'verifiedData', fields: { verifiedData } })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'verifiedData', fields: { verifiedData } })
          if (verifiedData.length <= 0) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Verification details not found!', action_code: 1001 }
          }
          ma_bank_on_boarding_id = null
        } else {
          if (bank_verify === true) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Verification details required!', action_code: 1001 }
          }
        }
        sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
          ma_user_id: fields.ma_user_id,
          user_id: fields.userid,
          uic: fields.uic,
          ma_bank_on_boarding_id: ma_bank_on_boarding_id
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'verifyBeneFromBank', fields: sessionData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'verifyBeneFromBank', fields: sessionData })

        if (sessionData.status == 400) {
          sessionData.action_code = 1001
          return sessionData
        }
      }

      const { bank_name, ma_bank_on_boarding_id, uicSession } = sessionData.data
      const handler = sessionData.handler

      if (bank_verify && !fields.isOtpRequired) {
        verify.status = true
      } else {
        const isVerified = await this.verifiedBeneficiaryFields({ fields, connection })
        if (isVerified.status === 400) {
          isVerified.action_code = 1001
          return isVerified
        }
        verify.status = isVerified.isVerified
      }

      if (verify.status === true) {
        // update Beneficiary status as Verified

        const res = await beneficiaryBankMap.addBeneficiaryBank(handler, { ma_beneficiaries_id: fields.ma_beneficiaries_id, ma_user_id: fields.ma_user_id, user_id: fields.userid, isOtpRequired: fields.isOtpRequired, orderid: fields.orderid, otp: fields.otp || '' }, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'addBeneficiaryBank', fields: res })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneficiaryOtpV2', type: 'addBeneficiaryBank', fields: res })
        if (res == 400) {
          res.action_code = 1001
          return res
        }

        if (res.status === 200) {
          const updated = await this.updateBeneficiary('Y', fields.ma_beneficiaries_id, 0, connection)
          if (updated.status === 400) {
            console.log('Updated details in beneficiary', updated)
            updated.action_code = 1001
            return updated
          }
        } else {
          res.action_code = 1000
          return res
        }

        /* EXTERNAL API CHANGES */
        const beneSql = `SELECT b.ma_beneficiaries_id,if(vrfy.bene_verify_status = 'S',IF(vrfy.bank_benename!='',vrfy.bank_benename,b.beneficiary_name),b.beneficiary_name) as beneficiary_name,b.beneficiary_name as bene_name,b.account_number,concat(bank_name,', ',branch) as bank_name,b.ben_mobile_number,b.ifsc_code,b.ma_bene_verification_id,b.relationship FROM ma_beneficiaries b join ma_bank_master mbm on b.ma_bank_master_id = mbm.ma_bank_master_id join ma_bank_branch mbb on mbb.ifsc = b.ifsc_code LEFT JOIN ma_bene_verification as vrfy ON vrfy.ma_bene_verification_id = b.ma_bene_verification_id AND b.ma_bene_verification_id > 0 where b.uic='${fields.uic}' and beneficiary_status='Y' and ma_beneficiaries_id=${fields.ma_beneficiaries_id} limit 1`
        const beneficiary_list = await this.rawQuery(beneSql, connection)
        console.log('Details', beneficiary_list)

        return { status: 200, respcode: 1087, message: errorMsg.responseCode[1087], beneficiary_list, action_code: 1000 }
      }
      return { status: 400, respcode: verify.respcode, message: verify.message, action_code: 1001 }
    } catch (error) {
      console.log('verifyBeneficiaryOtpError>>', error)
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' at Verify beneficiary', action_code: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async verifiedBeneficiaryFields ({ fields, connection }) {
    try {
      if (fields.isOtpRequired === false) {
        return await this.pinVerify({ fields, connection })
      }
      return { status: 200, isVerified: true }
    } catch (error) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' at Verify beneficiary' }
    }
  }

  static async pinVerify ({ fields, connection }) {
    try {
      const securePinCtrl = require('../securityPin/securityPinController')
      const securePinData = await securePinCtrl.verifySecurePin(null, {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        security_pin: fields.security_pin,
        connection: connection
      })

      if (securePinData.status === 400) {
        return securePinData
      }

      return { status: 200, isVerified: true }
    } catch (error) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' at Verify beneficiary' }
    }
  }

  static async resendBeneficiaryOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resendBeneficiaryOtp', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resendBeneficiaryOtp', type: 'request', fields: fields })
    const otpResponse = await otp.resentOtp(fields.mobile_number, fields.orderid, 'BE')
    if (otpResponse.status === true) {
      return { status: 200, respcode: 2002, message: errorMsg.responseCode[2002] }
    } else {
      return { status: 400, respcode: otpResponse.respcode, message: otpResponse.message }
    }
  }

  // static async addBeneficiaryBank (beneficiaryId, connection, reRegister = null, ma_user_id = null) {
  //   log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryBank', type: 'request', fields: beneficiaryId })
  //   this.TABLE_NAME = 'ma_beneficiaries'
  //   try {
  //     // call rounting
  //     const routingObj = new Routing(null, connection)
  //     const handler = await routingObj.getHandler(null, { transferId: null, orderId: null, transferMode: 'ADDBENEFICIARY', beneficiaryId: beneficiaryId })
  //     if (handler.status === 200) {
  //       // call bank api integration
  //       // BANK API CODE STARTS
  //       const beneficiaryDetailData = await bankOnBoardDetails.getBeneficiaryDetails(null, { beneficiaryId: beneficiaryId })
  //       if (beneficiaryDetailData.status === 200) {
  //         const tmpbenData = beneficiaryDetailData.data[0]

  //         const apiAPIPayload = {
  //           ma_user_id: ma_user_id,
  //           ma_bank_on_boarding_id: handler.handler.BANK_ON_BOARDING_ID,
  //           senderid: tmpbenData.remitter_id,
  //           receivername: tmpbenData.beneficiary_name,
  //           receivermobilenumber: tmpbenData.ben_mobile_number,
  //           receiveremailid: '',
  //           bank: tmpbenData.bank,
  //           state: tmpbenData.state,
  //           city: tmpbenData.city,
  //           branch: tmpbenData.branch,
  //           address: tmpbenData.address,
  //           ifscode: tmpbenData.ifscode,
  //           accountnumber: tmpbenData.accountnumber,
  //           flag: handler.handler.BANK_TPYE === 'NEFT' ? 0 : 2, // IMPS
  //           bcpartnerrefno: tmpbenData.uic,
  //           beneficiaryId: beneficiaryId,
  //           sendermobilenumber: tmpbenData.sendermobilenumber
  //         }

  //         /**
  //         * Load Bank Modules
  //         */

  //         const Payment = require('./../bankHandler/payment')
  //         try {
  //           const payment = new Payment(handler.handler.BANK_NAME, handler.handler.BANK_TPYE, connection, handler.handler.BANK_ON_BOARDING_ID)
  //           const resRemit = await payment.requestToBank('ADD_BENEFICIARY', apiAPIPayload)
  //           if (('beneficiaryid' in resRemit) && reRegister === null) {
  //             const BBM = await this.bankBeneficiaryMapping({ beneficiaryId, bank_on_boarding_id: handler.handler.BANK_ON_BOARDING_ID, connection })
  //             if (BBM.status === 400) {
  //               return BBM
  //             }
  //             return { status: 200, message: errorMsg.responseCode[2002], respcode: 2002, bankName: handler.handler.BANK_NAME }
  //           } else {
  //             return resRemit
  //           }
  //         } catch (error) {
  //           console.log('Bank Module Load Error', error)
  //           return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
  //         }
  //       } else {
  //         return beneficiaryDetailData
  //       }
  //     } else { // check if routing failed return error
  //       return handler
  //     }
  //   } catch (err) {
  //     console.log('Details error ||||||||||', err)
  //     return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + err }
  //   }
  // }

  static async updateBeneficiary (status, id, verificationId = 0, prevconnection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateBeneficiary', type: 'request', fields: { status, id } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateBeneficiary', type: 'request', fields: { status, id } })
    this.TABLE_NAME = 'ma_beneficiaries'
    // this.PRIMARY_KEY = 'ma_beneficiaries_id'
    let connection = null
    let isSet = false
    if (prevconnection === null || prevconnection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    } else {
      connection = prevconnection
    }

    try {
      const data = { beneficiary_status: status }
      const updateRes = await this.updateWhere(connection, { data, id, where: 'ma_beneficiaries_id' })
      log.logger({ pagename: require('path').basename(__filename), action: 'updateBeneficiary', type: 'response', fields: updateRes })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateBeneficiary', type: 'response', fields: updateRes })
      updateRes.status = 200
      updateRes.message = errorMsg.responseCode[1000]
      updateRes.respcode = 1000
      return updateRes
    } catch (err) {
      console.log('Error here', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateBeneficiary', type: 'response', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'updateBeneficiary', type: 'response', fields: err })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + err }
    } finally {
      if (isSet) {
        connection.release()
      }
    }
  }

  static async checkBeneficiary (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiaries'
    // this.PRIMARY_KEY = 'ma_beneficiaries_id'

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_beneficiaries_id,beneficiary_status,ben_mobile_number FROM ma_beneficiaries WHERE account_number = '${fields.account_number}' and ifsc_code = '${fields.ifsc_code}' and uic= '${fields.uic}' AND beneficiary_status != 'D' `

      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'checkBeneficiarySQL', fields: sql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'checkBeneficiarySQL', fields: sql })

      const beneficiaryDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'response', fields: beneficiaryDetails })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'response', fields: beneficiaryDetails })
      if (beneficiaryDetails.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: beneficiaryDetails }
      } else {
        return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], data: [] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'checkBeneficiary', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [CHECK_BENE]' }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async checkBeneVerification (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneVerification', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneVerification', type: 'request', fields: fields })
    // this.TABLE_NAME = 'ma_bene_verification'
    // this.PRIMARY_KEY = 'ma_beneficiaries_id'
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_bene_verification_id,bene_verify_status,bank_benename FROM ma_bene_verification WHERE account_number = '${fields.account_number}' and ifsc_code = '${fields.ifsc_code}' and uic= '${fields.uic}' `
      const beneficiaryDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneVerification', type: 'response', fields: beneficiaryDetails })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneVerification', type: 'response', fields: beneficiaryDetails })
      if (beneficiaryDetails.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: beneficiaryDetails[0] }
      } else {
        return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], data: [] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneVerification', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'checkBeneVerification', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [CHECK_BENE_VERIFY]' }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async createEntry (_, fields, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiaries'
    // this.PRIMARY_KEY = 'ma_beneficiaries_id'
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      const beneData = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        ma_bank_master_id: fields.ma_bank_master_id,
        beneficiary_name: fields.beneficiary_name,
        account_number: fields.account_number,
        mobile_number: fields.mobile_number,
        ben_mobile_number: fields.ben_mobile_number,
        ifsc_code: fields.ifsc_code,
        uic: fields.uic,
        beneficiary_status: fields.beneficiary_status,
        relationship: fields.relationship,
        country_code: fields.countrycode,
        receiver_id: fields.receiver_id || 0
      }

      const _result = await this.insert(connection, { data: beneData })
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'response', fields: _result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'response', fields: _result })
      const insertedID = { id: _result.insertId }
      insertedID.status = 200
      insertedID.message = errorMsg.responseCode[1000]
      insertedID.respcode = 1000
      beneData.ma_beneficiaries_id = insertedID
      insertedID.data = beneData
      return insertedID
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001] + ' [ADD_BEN_TBL]', respcode: 1001 }
    } finally {
      // Releases the connection only if it is created in this function
      if (tmpConn) connection.release()
    }
  }

  static async listBeneficiaries (_, fields = null, conn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiaries'
    const tmpConn = !(validator.definedVal(conn) && validator.definedVal(conn.threadId))

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    const connection = tmpConn ? await mySQLWrapper.getConnectionFromPool() : conn

    let handler = {}

    let priorityBankList = []
    if (!validator.definedVal(fields.handler)) {
      if (!validator.definedVal(fields.sessionRQ)) {
        return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
      }

      const sessionDataTmp = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id || null
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'sessionDataTmp', fields: sessionDataTmp })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'sessionDataTmp', fields: sessionDataTmp })
      if (sessionDataTmp.status == 400) {
        return sessionDataTmp
      }

      const { bank_name, ma_bank_on_boarding_id, uicSession, bank_priority_json } = sessionDataTmp.data
      priorityBankList = JSON.parse(bank_priority_json)
      handler = sessionDataTmp.handler
    } else {
      handler = fields.handler
      priorityBankList = JSON.parse(fields.bank_priority_json)
    }

    try {
      let beneficiaries = []
      let bankPriorityListArr = priorityBankList.map((currentBank) => currentBank.ma_bank_on_boarding_id)

      const removeDuplicates = new Set(bankPriorityListArr)
      bankPriorityListArr = [...removeDuplicates]

      if (!validator.definedVal(fields.uic)) {
        return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
      }
      // Only with uic - beneficiary listing

      // sync beneficiaries
      // condition add only sync while fetching bene not while searching
      // eslint-disable-next-line no-prototype-builtins

      // DISABLING BENE SYNC. BENE IMPORT ADDED
      // if (!(fields.hasOwnProperty('search_type') && fields.hasOwnProperty('search_text'))) {
      //   console.time('TIMER_syncBeneficiariesFromBank')
      //   const requestStart = process.hrtime()
      //   fields.bankHandler = handler
      //   const syncBeneficiariesFromBankResult = await this.syncBeneficiariesFromBank(fields, connection)
      //   console.timeEnd('TIMER_syncBeneficiariesFromBank')
      //   const requestEnd = process.hrtime(requestStart)
      //   const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      //   log.logger({ pagename: require('path').basename(__filename), action: 'TIMER_syncBeneficiariesFromBank', type: 'TIMER', fields: timeInMs })
      //   /* if (syncBeneficiariesFromBankResult.status != 200) {
      //     return syncBeneficiariesFromBankResult
      //   } */
      //   log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiariesFromBank', type: 'syncBeneficiariesFromBankResult', fields: syncBeneficiariesFromBankResult })
      // }

      // sync + delete beneficiaries
      // eslint-disable-next-line no-prototype-builtins
      if (!(fields.hasOwnProperty('search_type') && fields.hasOwnProperty('search_text'))) {
        fields.bankHandler = handler
        const syncBeneDelete = await this.syncBeneDelete(fields, connection)

        log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'syncBeneDeleteResult', fields: syncBeneDelete })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'syncBeneDeleteResult', fields: syncBeneDelete })
      }

      // sync beneficiaries end

      fields.limit = !validator.definedVal(fields.limit) ? 0 : fields.limit
      fields.offset = !validator.definedVal(fields.offset) ? 0 : fields.offset
      let searchSql = ''
      if (validator.definedVal(fields.search_type)) {
        if (fields.search_type === 'name') {
          searchSql += ` AND b.beneficiary_name like '%${fields.search_text}%'`
        } else if (fields.search_type === 'acc_no') {
          searchSql += ` AND b.account_number like '%${fields.search_text}%'`
        } else if (fields.search_type === 'mob_no') {
          searchSql += ` AND b.ben_mobile_number like '%${fields.search_text}%'`
        }
      }

      let sql = `SELECT
          SQL_CALC_FOUND_ROWS
          b.ma_beneficiaries_id,
          b.beneficiary_name,
          b.account_number,
          b.ma_bank_master_id,
          b.ifsc_code,
          b.country_code,
          b.mobile_number,
          b.uic,
          b.relationship,
          b.ma_user_id,
          b.userid,
          b.receiver_id,
          b.ma_bene_verification_id,
          b.beneficiary_status,
          IF(
              (b.ben_mobile_number IS NULL) OR(b.ben_mobile_number = '0') OR(b.ben_mobile_number = ''),
              'N/A',
              b.ben_mobile_number
          ) AS ben_mobile_number,
          CONCAT(mbm.bank_name, ', ', mbb.branch) AS bank_name,
          IF(
              b.ma_bene_verification_id > 0,
              vrfy.bene_verify_status,
              'U'
          ) AS bene_verify_status,
          IF(
              vrfy.bene_verify_status = 'S',
              IF(
                  vrfy.bank_benename != '',
                  vrfy.bank_benename,
                  b.beneficiary_name
              ),
              b.beneficiary_name
          ) AS beneficiary_name,
          'U' as bank_status
      FROM
          ma_beneficiaries b
      JOIN ma_bank_master mbm ON
          b.ma_bank_master_id = mbm.ma_bank_master_id
      JOIN ma_bank_branch mbb ON
          mbb.ifsc = b.ifsc_code
      LEFT JOIN ma_bene_verification AS vrfy
      ON
          vrfy.ma_bene_verification_id = b.ma_bene_verification_id 
          AND b.ma_bene_verification_id > 0
      WHERE
          b.uic = '${fields.uic}' AND b.beneficiary_status = 'Y' 
          AND mbm.bank_status = 1 AND mbb.branch_status = 1
          ${searchSql}
      ORDER BY
          b.ma_beneficiaries_id
      DESC
          `
      if (fields.limit > 0) {
        sql += ` LIMIT ${fields.offset},${fields.limit}`
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'sql', fields: sql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'sql', fields: sql })

      beneficiaries = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'benSQLResult', fields: beneficiaries })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'benSQLResult', fields: beneficiaries })

      let nextFlag = false

      if (beneficiaries.length == 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, beneficiary_list: beneficiaries, nextFlag: nextFlag }
      }

      const countSql = 'SELECT FOUND_ROWS() AS total'
      const countResult = await this.rawQuery(countSql, connection)
      if (countResult.length > 0) {
        const total = countResult[0].total
        console.log('Total : ' + total)
        console.log('Current : ' + (fields.limit + fields.offset))
        if ((fields.limit + fields.offset) < total) {
          nextFlag = true
        }
      }

      /* NEW DELETE CHANGES */

      const beneIds = beneficiaries.map((bene) => bene.ma_beneficiaries_id)
      const beneMapSQL = `
        SELECT
        bmap.ma_beneficiary_bank_mapping_id,
        bmap.bank_status,
        bmap.ma_beneficiaries_id,
        b.bank_name,
        b.ma_bank_on_boarding_id
        FROM ma_beneficiary_bank_mapping as bmap
        JOIN ma_bank_on_boarding as b ON b.ma_bank_on_boarding_id = bmap.ma_bank_on_boarding_id
        WHERE ma_beneficiaries_id IN (${beneIds.join(',')})
        AND bmap.ma_bank_on_boarding_id IN (${bankPriorityListArr.join(',')})
        AND b.onboarding_status = 'Y'
        AND bmap.bank_status in ('S','P')
        order by b.priority ASC
        `

      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'beneMapSQL', fields: beneMapSQL })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'beneMapSQL', fields: beneMapSQL })

      const beneficiaryMapData = await this.rawQuery(beneMapSQL, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'beneficiaryMapData', fields: beneficiaryMapData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'beneficiaryMapData', fields: beneficiaryMapData })

      const isMappingPresent = new Map()
      for (let index = 0; index < beneficiaryMapData.length; index++) {
        const inProgressBank = beneficiaryMapData[index]
        isMappingPresent.set(inProgressBank.ma_beneficiaries_id, inProgressBank)
      }

      const isOtpRequired = await this.getMethodFlag({ ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, method: 'DEL_BENEFICIARY', connection })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'isOtpRequired', fields: isOtpRequired })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'isOtpRequired', fields: isOtpRequired })

      if (isOtpRequired.status == 400) return isOtpRequired

      beneficiaries.forEach(current_bank => {
        current_bank.boarding_bank_name = handler.BANK_NAME
        current_bank.delBankList = []
        console.log('current_bank >>', current_bank)
        // let bCode = [5, 9, 10, 11]
        // beneficiaryMapData.forEach(beneMapData => {
        //   if (bCode.includes(beneMapData.ma_bank_on_boarding_id)) {
        //     current_bank.delBankList.push({
        //       bank_name: beneMapData.bank_name,
        //       ma_bank_on_boarding_id: beneMapData.ma_bank_on_boarding_id,
        //       otp_flag: 'Y'
        //     })
        //     bCode = bCode.filter(function (bank) {
        //       return bank !== beneMapData.ma_bank_on_boarding_id
        //     })
        //   }
        // })
        // bCode.forEach((list) => {
        //   current_bank.delBankList.push({
        //     bank_name: list == 5 ? 'NSDL' : list == 9 ? 'PAYTM' : list == 10 ? 'AIRTEL' : list == 11 ? 'FINO' : null,
        //     ma_bank_on_boarding_id: list == 5 ? 5 : list == 9 ? 9 : list == 10 ? 10 : list == 11 ? 11 : null,
        //     otp_flag: 'N'
        //   })
        // })
        // console.log("beneficiaries--->",beneficiaries[0])
        current_bank.delBankList.push({
          bank_name: current_bank.boarding_bank_name,
          ma_bank_on_boarding_id: bankPriorityListArr.join(','),
          otp_flag: 'N'
        })
        for (let index = 0; index < beneficiaryMapData.length; index++) {
          const inProgressBank = beneficiaryMapData[index]
          console.log('inProgressBank>>>', inProgressBank)
          console.log('value', current_bank.ma_beneficiaries_id === inProgressBank.ma_beneficiaries_id)
          if (current_bank.ma_beneficiaries_id === inProgressBank.ma_beneficiaries_id && isOtpRequired) {
            current_bank.delBankList[0].otp_flag = 'Y'
          }

        //   if (current_bank.ma_beneficiaries_id === inProgressBank.ma_beneficiaries_id) {
        //     current_bank.bank_status = inProgressBank.bank_status
        //     if (current_bank.delBankList) {
        //       current_bank.delBankList.push({
        //         bank_name: inProgressBank.bank_name,
        //         ma_bank_on_boarding_id: inProgressBank.ma_bank_on_boarding_id
        //       })
        //     } else {
        //       current_bank.delBankList = []
        //       current_bank.delBankList.push({
        //         bank_name: inProgressBank.bank_name,
        //         ma_bank_on_boarding_id: inProgressBank.ma_bank_on_boarding_id
        //       })
        //     }
        //   }
        }
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'response', fields: beneficiaries })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'response', fields: beneficiaries })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, beneficiary_list: beneficiaries, nextFlag: nextFlag }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async beneficiariesList ({ fields, connectionRead, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      this.TABLE_NAME = 'ma_beneficiaries'
      let priorityBankList = []
      let handler = {}

      if (!validator.definedVal(fields.handler)) {
        if (!validator.definedVal(fields.sessionRQ)) {
          return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
        }

        const sessionDataTmp = await dmtSession.getDMTSessionData(conn, fields.sessionRQ, {
          ma_user_id: fields.ma_user_id,
          user_id: fields.userid,
          ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id || null
        })

        if (sessionDataTmp.status == 400) {
          return sessionDataTmp
        }

        const { bank_name, ma_bank_on_boarding_id, uicSession, bank_priority_json } = sessionDataTmp.data
        priorityBankList = JSON.parse(bank_priority_json)
        handler = sessionDataTmp.handler
      } else {
        handler = fields.handler
        priorityBankList = JSON.parse(fields.bank_priority_json)
      }
      let beneficiaries = []
      let bankPriorityListArr = priorityBankList.map((currentBank) => currentBank.ma_bank_on_boarding_id)

      const removeDuplicates = new Set(bankPriorityListArr)
      bankPriorityListArr = [...removeDuplicates]

      if (!validator.definedVal(fields.uic)) {
        return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
      }
      // Only with uic - beneficiary listing

      // sync beneficiaries
      // condition add only sync while fetching bene not while searching
      // eslint-disable-next-line no-prototype-builtins
      if (!(fields.hasOwnProperty('search_type') && fields.hasOwnProperty('search_text'))) {
        fields.bankHandler = handler
        const syncBeneficiariesFromBankResult = await this.syncBeneficiariesFromBank(fields, conn)
        /* if (syncBeneficiariesFromBankResult.status != 200) {
          return syncBeneficiariesFromBankResult
        } */
        log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiariesFromBank', type: 'syncBeneficiariesFromBankResult', fields: syncBeneficiariesFromBankResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiariesFromBank', type: 'syncBeneficiariesFromBankResult', fields: syncBeneficiariesFromBankResult })
      }

      // sync + delete beneficiaries
      // eslint-disable-next-line no-prototype-builtins
      if (!(fields.hasOwnProperty('search_type') && fields.hasOwnProperty('search_text'))) {
        fields.bankHandler = handler
        const syncBeneDelete = await this.syncBeneDelete(fields, conn)

        log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'syncBeneDeleteResult', fields: syncBeneDelete })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'syncBeneDeleteResult', fields: syncBeneDelete })
      }
      // sync beneficiaries end

      fields.limit = !validator.definedVal(fields.limit) ? 0 : fields.limit
      fields.offset = !validator.definedVal(fields.offset) ? 0 : fields.offset
      let searchSql = ''
      if (validator.definedVal(fields.search_type)) {
        if (fields.search_type === 'name') {
          searchSql += ` AND b.beneficiary_name like '%${fields.search_text}%'`
        } else if (fields.search_type === 'acc_no') {
          searchSql += ` AND b.account_number like '%${fields.search_text}%'`
        } else if (fields.search_type === 'mob_no') {
          searchSql += ` AND b.ben_mobile_number like '%${fields.search_text}%'`
        }
      }

      if (validator.definedVal(fields.limit)) {
        fields.limit = 10
      }
      if (validator.definedVal(fields.offset)) {
        fields.offset = 0
      }

      const sql = `SELECT
          SQL_CALC_FOUND_ROWS
          b.ma_beneficiaries_id,
          b.beneficiary_name,
          b.account_number,
          b.ma_bank_master_id,
          b.ifsc_code,
          b.country_code,
          b.mobile_number,
          b.uic,
          b.relationship,
          b.ben_mobile_number,
          CONCAT(mbm.bank_name, ', ', mbb.branch) AS bank_name,
          b.beneficiary_name
      FROM
          ma_beneficiaries b
      JOIN ma_bank_master mbm ON
          b.ma_bank_master_id = mbm.ma_bank_master_id
      JOIN ma_bank_branch mbb ON
          mbb.ifsc = b.ifsc_code
      WHERE
          b.uic = '${fields.uic}' AND b.beneficiary_status = 'Y' 
          AND mbm.bank_status = 1 AND mbb.branch_status = 1
          ${searchSql}
      ORDER BY
          b.ma_beneficiaries_id
      DESC LIMIT ${fields.offset},${fields.limit}
          `
      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'response', fields: sql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'listBeneficiariesSQL', type: 'response', fields: sql })

      beneficiaries = await this.rawQuery(sql, connRead)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, beneficiaries }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'listBeneficiaries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  static async bankBeneficiaryMapping (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiary_bank_mapping'
    try {
      const _result = await this.insert(fields.connection, {
        data: {
          ma_beneficiaries_id: fields.beneficiaryId,
          ma_bank_on_boarding_id: fields.bank_on_boarding_id
        }
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'response', fields: _result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'response', fields: _result })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getRetailerDetails (maUserId, userid, connection) {
    try {
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      const sql = `SELECT ma_user_master_id,
      profileid,
      userid,
      user_password,
      roleid,
      distributer_user_master_id,
      reseller_user_master_id,
      email_id,
      mobile_id,
      loginkey,
      firstname,
      lastname,
      address,
      city,
      state,
      country,
      pincode,
      user_type,
      user_status,
      username,
      password,
      security_pin,
      security_pin_attempts,
      security_lock_expiry,
      security_pin_expiry,
      apikey,
      last_login,
      imei,
      login_ip,
      gst_number,
      CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,
      aadhar_number,
      company,
      sales_id,
      zone,
      addedon,
      updatedon,
      jwtkey,
      logo,
      mer_user,
      esign_link FROM ma_user_master WHERE profileid = ${maUserId} AND userid = ${userid} limit 1`
      const queryResp = await this.rawQuery(sql, connection)
      if (queryResp.length > 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: queryResp[0] })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: queryResp[0] })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: queryResp[0] }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: queryResp })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: queryResp })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getRetailerDetails', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.message }
    }
  }

  static async getBeneficiaryDetails (beneficiaryId, connection) {
    try {
      const sql = `SELECT bf.beneficiary_name,
                    bf.account_number,
                    bf.ifsc_code,
                    bf.mobile_number,
                    bf.ben_mobile_number
                  FROM ma_beneficiaries AS bf
                  WHERE bf.ma_beneficiaries_id = ${beneficiaryId} limit 1`
      const queryResp = await this.rawQuery(sql, connection)
      if (queryResp.length > 0) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'response', fields: queryResp[0] })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'response', fields: queryResp[0] })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: queryResp[0] }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'response', fields: queryResp })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'response', fields: queryResp })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'response', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.message }
    }
  }

  /* static async deleteBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiaries'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const delBenSql = `
      SELECT
        mcd.ma_user_id,
        mcd.mobile_number,
        mcd.remitter_id,
        mcd.uic,
        b.ben_mobile_number,
        b.receiver_id,
        mbob.bank_name,
        mbob.ma_bank_on_boarding_id
        FROM ma_beneficiaries b
        JOIN ma_beneficiary_bank_mapping as mbbm ON mbbm.ma_beneficiaries_id = b.ma_beneficiaries_id
        JOIN ma_bank_on_boarding as mbob ON mbob.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
        JOIN ma_customer_details mcd ON mcd.uic = b.uic
        WHERE b.ma_beneficiaries_id=${fields.ma_beneficiaries_id} AND
        mcd.uic= '${fields.uic}' AND
        b.beneficiary_status='Y' AND
        mcd.customer_status='Y' AND
        mbob.onboarding_status='Y'
         LIMIT 1
      `

      const delBenResult = await this.rawQuery(delBenSql, connection)
      console.log('deleteBeneficiaryDetails', delBenResult)

      if (delBenResult.length > 0) {

        if (delBenResult[0].receiver_id > 0 && delBenResult[0].remitter_id > 0) {
          const apiAPIPayload = {
            ma_user_id: fields.ma_user_id,
            ma_bank_on_boarding_id: delBenResult[0].ma_bank_on_boarding_id,
            senderid: delBenResult[0].remitter_id,
            receiverid: delBenResult[0].receiver_id,
            beneficiaryId: fields.ma_beneficiaries_id
          }
          const Payment = require('./../bankHandler/payment')
          try {
            const payment = new Payment(delBenResult[0].bank_name, null, connection, delBenResult[0].ma_bank_on_boarding_id)
            const resRemit = await payment.requestToBank('DELETE_BENEFICIARY', apiAPIPayload)
            if ('receiverid' in resRemit) {
              return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: delBenResult[0].bank_name }
            } else {
              return resRemit
            }
          } catch (error) {
            console.log('Bank Module Load Error', error)
            return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
          }
        } else {
          const sql = `UPDATE ma_beneficiaries set beneficiary_status= "D" where ma_beneficiaries_id = ${fields.ma_beneficiaries_id} `
          console.log('deleteBeneficiaryId : ', sql)
          await this.rawQuery(sql, connection)
          return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: delBenResult[0].bank_name }
        }
      } else {
        return { status: 400, respcode: 1091, message: errorMsg.responseCode[1091] }
      }
    } catch (err) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + err.message }
    } finally {
      connection.release()
    }
  } */

  static async deleteBeneficiaryV2 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'request', fields: fields })

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    fields.ma_bank_on_boarding_id = parseInt(fields.ma_bank_on_boarding_id)
    fields.ma_bank_on_boarding_id = isNaN(fields.ma_bank_on_boarding_id) ? 0 : fields.ma_bank_on_boarding_id

    this.TABLE_NAME = 'ma_beneficiaries'
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Call routing logic and add beneficiary at bank
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sessionData', fields: sessionData })

      if (sessionData.status == 400) {
        return sessionData
      }

      const { bank_priority_json, mobileNumber } = sessionData.data
      const handler = sessionData.handler

      if (!validator.validJson(bank_priority_json)) {
        return {
          status: 200,
          respcode: 1001,
          message: errorMsg.responseCode[1001] + ' [PRIORITY_BNK]'
        }
      }

      const delBeneData = await this.checkBeneIdExistData({
        ma_beneficiaries_id: fields.ma_beneficiaries_id
      }, connection)

      if (delBeneData.status == 400) {
        return delBeneData
      }

      const BankPriorityArr = JSON.parse(bank_priority_json)

      const sqlStrMap = `AND mbbm.bank_status in ('S','P') AND mbbm.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}`

      const delBenSql = `
      SELECT
    custmap.remitter_id,
    mbbm.receiver_id,
    mbob.bank_name,
    mbob.ma_bank_on_boarding_id,
    custmap.mobile_number,
    mbbm.ma_bank_on_boarding_id
    FROM
      ma_beneficiary_bank_mapping AS mbbm
    JOIN ma_beneficiaries AS b
    ON
    mbbm.ma_beneficiaries_id = b.ma_beneficiaries_id AND b.uic = '${fields.uic}'
    JOIN ma_customer_details_bank_mapping AS custmap
    ON
    custmap.uic = b.uic AND custmap.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
    JOIN ma_bank_on_boarding AS mbob
    ON
    mbob.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
    WHERE
    b.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} 
    AND b.uic = '${fields.uic}' 
    AND b.beneficiary_status = 'Y'
    ${sqlStrMap}
      `

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'delBenSql', fields: delBenSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'delBenSql', fields: delBenSql })

      const delBenResult = await this.rawQuery(delBenSql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'delBenResult', fields: delBenResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'delBenResult', fields: delBenResult })

      let customOTP = false
      if (delBenResult.length > 0) {
        const foundBoardingIdArr = delBenResult.map((currentBank) => currentBank.ma_bank_on_boarding_id)

        const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'DEL_BENEFICIARY' }, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'bankDetailData', fields: bankDetailData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'bankDetailData', fields: bankDetailData })

        let CurrentBankDetailConfig = null
        if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
          const bankData = bankDetailData.data
          CurrentBankDetailConfig = bankData.find((currentBank) => currentBank.ma_bank_on_boarding_id === handler.BANK_ON_BOARDING_ID)
          // ########## here
          log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'foundBoardingIdArr', fields: foundBoardingIdArr })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'foundBoardingIdArr', fields: foundBoardingIdArr })
        } else {
          // Invalid bank if data not found in db table ma_bank_on_boarding_details
          return bankDetailData
        }

        let currentBankBene = null
        for (let index = 0; index < delBenResult.length; index++) {
          if (handler.BANK_ON_BOARDING_ID === delBenResult[index].ma_bank_on_boarding_id) {
            currentBankBene = delBenResult[index]
            break
          }
        }

        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'currentBankBene', fields: { currentBankBene } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'currentBankBene', fields: { currentBankBene } })

        // process delete from bank end
        if (validator.definedVal(currentBankBene) && validator.definedVal(CurrentBankDetailConfig)) {
          try {
            const BANK_OTP = 'SEND_OTP_DELETE_BENEFICIARY'

            const bankOtp = CurrentBankDetailConfig.otp_required === 'YES'

            console.time('TIMER_SENT_OTP_DELETE_BENEFICIARY')
            const resp = await otp.sentBankOtp(fields.ma_user_id, fields.userid, currentBankBene.mobile_number, 'DBE', { handler: handler, BANK_OP: BANK_OTP, bankName: handler.BANK_NAME, bankOtp: bankOtp, sessionRQ: fields.sessionRQ }, connection)
            console.timeEnd('TIMER_SENT_OTP_DELETE_BENEFICIARY')

            log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'respSentBankOtp', fields: resp })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'respSentBankOtp', fields: resp })

            const response = {}
            if (resp.status === true) {
              response.status = 200
              response.respcode = 2002
              response.message = errorMsg.responseCode[2002]
              response.orderid = resp.data.aggregator_order_id
              return response
            } else {
              response.status = 400
              response.respcode = 1001
              response.message = resp.message || errorMsg.responseCode[1001] + ' [DEL_BEN_OTP]'
              return response
            }
          } catch (error) {
            console.log('Bank Module Load Error', error)
            return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
          }
        } else {
          customOTP = true // bank not found so do delete from normal otp process
        }
      } else {
        customOTP = false // bank not found so do delete from normal otp process
        /* NEW DELETE CHANGES */
        const sqlQuery = `SELECT account_number,ifsc_code FROM ma_beneficiaries WHERE ma_beneficiaries_id = ${fields.ma_beneficiaries_id} AND uic='${fields.uic}'`
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sqlQuery', fields: sqlQuery })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sqlQuery', fields: sqlQuery })
        const sqlResult = await this.rawQuery(sqlQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sqlResult', fields: sqlResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sqlResult', fields: sqlResult })
        if (sqlResult < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
        const updateData = { beneficiary_status: 'N' }
        this.TABLE_NAME = 'ma_beneficiaries'
        const updateRes = await this.updateWhere(connection, { data: updateData, id: fields.ma_beneficiaries_id.toString(), where: 'ma_beneficiaries_id' })
        if (updateRes.affectedRows > 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[2005] }
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + '~[DEL_BE]' }
      }

      // bank not found so do delete from normal otp process
      if (customOTP) {
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'currentBankBene', fields: { customOTP } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'currentBankBene', fields: { customOTP } })
        const resp = await otp.sentOtp(fields.ma_user_id, fields.userid, mobileNumber, 'DBE')
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sentOtpresp', fields: { resp } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sentOtpresp', fields: { resp } })
        const response = {}
        if (resp.status === true) {
          response.status = 200
          response.respcode = 2002
          response.message = errorMsg.responseCode[2002]
          response.orderid = resp.data.aggregator_order_id
          return response
        } else {
          response.status = 400
          response.respcode = 1001
          response.message = errorMsg.responseCode[1001] + ' [DEL_BEN_OTP]'
          return response
        }
      } else {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + '~[DEL_BE]' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[DEL_BE]' }
    } finally {
      connection.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {Object} fields
   * @returns
   */
  static async deleteBeneficiaryV3 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      this.TABLE_NAME = 'ma_beneficiaries'
      if (!validator.definedVal(fields.sessionRQ)) {
        return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
      }

      if (!validator.definedVal(fields.uic)) {
        return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
      }

      fields.ma_bank_on_boarding_id = parseInt(fields.ma_bank_on_boarding_id)
      fields.ma_bank_on_boarding_id = isNaN(fields.ma_bank_on_boarding_id) ? 0 : fields.ma_bank_on_boarding_id
      // Call routing logic and add beneficiary at bank
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'sessionData', fields: sessionData })

      if (sessionData.status == 400) return sessionData

      const { mobileNumber } = sessionData.data
      const handler = sessionData.handler

      const delBeneData = await this.checkBeneIdExistData({ ma_beneficiaries_id: fields.ma_beneficiaries_id }, connectionRead)

      if (delBeneData.status == 400) return delBeneData

      const delBenSql = `
        SELECT
          custmap.remitter_id,
          mbbm.receiver_id,
          mbob.bank_name,
          mbob.ma_bank_on_boarding_id,
          custmap.mobile_number,
          mbbm.ma_bank_on_boarding_id
        FROM
          ma_beneficiary_bank_mapping AS mbbm JOIN ma_beneficiaries AS b
        ON mbbm.ma_beneficiaries_id = b.ma_beneficiaries_id AND b.uic = '${fields.uic}'
          JOIN ma_customer_details_bank_mapping AS custmap
        ON custmap.uic = b.uic AND custmap.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
          JOIN ma_bank_on_boarding AS mbob
        ON mbob.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
        WHERE
          b.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} 
          AND b.uic = '${fields.uic}' 
          AND b.beneficiary_status = 'Y'
          AND mbbm.bank_status in ('S','P') AND mbbm.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}
      `

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'delBenSql', fields: delBenSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'delBenSql', fields: delBenSql })

      const delBenResult = await this.rawQuery(delBenSql, connectionRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'delBenResult', fields: delBenResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'delBenResult', fields: delBenResult })

      if (delBenResult.length == 0) {
        // NEW DELETE CHANGES
        const sqlQuery = `SELECT account_number,ifsc_code FROM ma_beneficiaries WHERE ma_beneficiaries_id = ${fields.ma_beneficiaries_id} AND uic='${fields.uic}'`
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'sqlQuery', fields: sqlQuery })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'sqlQuery', fields: sqlQuery })
        const sqlResult = await this.rawQuery(sqlQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'sqlResult', fields: sqlResult })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'sqlResult', fields: sqlResult })
        if (sqlResult.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
        const updateData = { beneficiary_status: 'N' }
        this.TABLE_NAME = 'ma_beneficiaries'
        const updateRes = await this.updateWhere(connection, { data: updateData, id: fields.ma_beneficiaries_id.toString(), where: 'ma_beneficiaries_id' })
        if (updateRes.affectedRows > 0) return { status: 200, respcode: 1000, message: errorMsg.responseCode[2005] }
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + '~[DEL_BE]' }
      }
      const isOtpRequired = await this.getMethodFlag({ ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id, method: 'DEL_BENEFICIARY', connection: connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'isOtpRequired', fields: isOtpRequired })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'isOtpRequired', fields: isOtpRequired })

      if (isOtpRequired.status == 400) return isOtpRequired

      if (isOtpRequired) {
        try {
          const BANK_OTP = 'SEND_OTP_DELETE_BENEFICIARY'
          console.time('TIMER_SENT_OTP_DELETE_BENEFICIARY')
          const resp = await otp.sentBankOtp(fields.ma_user_id, fields.userid, mobileNumber, 'DBE', { handler: handler, BANK_OP: BANK_OTP, bankName: handler.BANK_NAME, bankOtp: isOtpRequired, sessionRQ: fields.sessionRQ }, connection)
          console.timeEnd('TIMER_SENT_OTP_DELETE_BENEFICIARY')

          log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'respSentBankOtp', fields: resp })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'respSentBankOtp', fields: resp })

          if (resp.status === true) {
            return { status: 200, respcode: 2002, message: errorMsg.responseCode[2002], orderid: resp.data.aggregator_order_id }
          }
          return { status: 400, respcode: 1001, message: resp.message || errorMsg.responseCode[1001] + ' [DEL_BEN_OTP]' }
        } catch (error) {
          console.log('Bank Module Load Error', error)
          return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
        }
      }

      if (!isOtpRequired) {
        const apiAPIPayload = {
          ma_user_id: fields.ma_user_id,
          ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id,
          senderid: delBenResult[0].remitter_id,
          receiverid: delBenResult[0].receiver_id,
          beneficiaryId: fields.ma_beneficiaries_id
        }

        const payment = new Payment(handler.BANK_NAME, null, connection, handler.BANK_ON_BOARDING_ID)
        const resRemit = await payment.requestToBank('DELETE_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'resRemit', fields: resRemit })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'resRemit', fields: resRemit })

        if (resRemit.status != 200) return resRemit

        const responseBen = await this.deleteBeneFromDB({ ma_beneficiaries_id: fields.ma_beneficiaries_id, ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID }, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })

        if (responseBen.status != 200) return responseBen
        return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: handler.BANK_NAME, finalDelete: responseBen.finalDelete || false }
      }

      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + '~[DEL_BE]' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[DEL_BE]' }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object,connection:Promise<>,connectionRead:Promise<>}} param0
   */
  static async deleteAffiliateBeneficiary ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteAffiliateBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteAffiliateBeneficiary', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* BENE VALIDATION */
      const isBeneExist = `SELECT mb.account_number,mb.ifsc_code,mbbm.ma_bank_on_boarding_id,mbbm.bank_status,mb.beneficiary_status FROM ma_beneficiaries mb LEFT JOIN ma_beneficiary_bank_mapping mbbm ON mb.ma_beneficiaries_id = mbbm.ma_beneficiaries_id WHERE mb.uic = '${fields.uic}' AND mb.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} AND mb.beneficiary_status='Y'`
      const isBeneExistResult = await this.rawQuery(isBeneExist, connRead)

      if (isBeneExistResult.length == 0) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid beneficiary id' }

      let isMappingPresent = false

      isBeneExistResult.forEach(mapping => {
        if (mapping.ma_bank_on_boarding_id == fields.ma_bank_on_boarding_id) {
          isMappingPresent = true
        }
      })

      const deleteBeneFlagResponse = await this.getMethodFlag({
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id,
        method: 'SEND_OTP_DELETE_BENEFICIARY',
        connection: connRead
      })

      if (deleteBeneFlagResponse.status == 400) return deleteBeneFlagResponse

      /* CUSTOM OTP */
      if ((deleteBeneFlagResponse.status == 200 && deleteBeneFlagResponse.respcode == 1002) || !deleteBeneFlagResponse || !isMappingPresent) {
        const resp = await otp.sentOtp(fields.ma_user_id, fields.userid, fields.mobile_number, 'DBE')
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sentOtpresp', fields: { resp } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'sentOtpresp', fields: { resp } })
        const response = {}
        if (resp.status === true) {
          response.status = 200
          response.respcode = 2002
          response.message = errorMsg.responseCode[2002]
          response.orderid = resp.data.aggregator_order_id
          return response
        } else {
          response.status = 400
          response.respcode = 1001
          response.message = errorMsg.responseCode[1001]
        }
        return response
      }

      /* BANK SIDE OTP */
      if (deleteBeneFlagResponse) {
        try {
          console.time('TIMER_SENT_OTP_DELETE_BENEFICIARY')
          const resp = await otp.sentBankOtp(fields.ma_user_id, fields.userid, fields.mobile_number, 'DBE', { handler: fields.handler, BANK_OP: 'SEND_OTP_DELETE_BENEFICIARY', bankName: fields.handler.BANK_NAME, bankOtp: deleteBeneFlagResponse, sessionRQ: fields.sessionRQ }, connection)
          console.timeEnd('TIMER_SENT_OTP_DELETE_BENEFICIARY')

          log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'respSentBankOtp', fields: resp })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV2', type: 'respSentBankOtp', fields: resp })

          const response = {}
          if (resp.status === true) {
            response.status = 200
            response.respcode = 2002
            response.message = errorMsg.responseCode[2002]
            response.orderid = resp.data.aggregator_order_id
            return response
          } else {
            response.status = 400
            response.respcode = 1001
            response.message = resp.message || errorMsg.responseCode[1001]
          }
          return response
        } catch (error) {
          console.log('Bank Module Load Error', error)
          return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteAffiliateBeneficiary', type: 'error', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteAffiliateBeneficiary', type: 'error', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  static async deleteBeneFromDB (fields, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)

      const delBenSql = `UPDATE ma_beneficiary_bank_mapping 
      SET bank_status = 'D'
      WHERE ma_beneficiaries_id = ${fields.ma_beneficiaries_id}
      AND ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}
      `

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'delBenSql', fields: delBenSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'delBenSql', fields: delBenSql })

      const delBenResult = await this.rawQuery(delBenSql, connection)

      /* const delBenNotSql = `UPDATE ma_beneficiary_bank_mapping
      SET bank_status = 'DA'
      WHERE ma_beneficiaries_id = ${fields.ma_beneficiaries_id}
      AND ma_bank_on_boarding_id != ${fields.ma_bank_on_boarding_id}
      AND bank_status != 'D'
      `

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'delBenNotSql', fields: delBenNotSql })

      const delBenNotResult = await this.rawQuery(delBenNotSql, connection) */

      const AnyBenExistSql = ` SELECT count(ma_beneficiaries_id) as cnt
      FROM ma_beneficiary_bank_mapping
      WHERE ma_beneficiaries_id = ${fields.ma_beneficiaries_id}
      AND bank_status = 'S'
      `

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'AnyBenExistSql', fields: AnyBenExistSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'AnyBenExistSql', fields: AnyBenExistSql })

      const delBAnyBenExistResult = await this.rawQuery(AnyBenExistSql, connection)
      let finalDelete = false
      if (delBAnyBenExistResult.length > 0 && delBAnyBenExistResult[0].cnt == 0) {
        const updateData = { beneficiary_status: 'D' }
        this.TABLE_NAME = 'ma_beneficiaries'
        const updateRes = await this.updateWhere(connection, { data: updateData, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })
        finalDelete = true
      }

      /* NEW DELETE BENE CHANGES */
      if (delBAnyBenExistResult.length > 0 && delBAnyBenExistResult[0].cnt > 0) {
        const updateData = { beneficiary_status: 'N' }
        this.TABLE_NAME = 'ma_beneficiaries'
        const updateRes = await this.updateWhere(connection, { data: updateData, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })
      }

      await mySQLWrapper.commit(connection)

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], finalDelete }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneFromDB', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_DB]' }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async verifyDeleteBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'request', fields: fields })

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    if (!validator.definedVal(fields.otp)) {
      return { status: 400, respcode: 1007, message: errorMsg.responseCode[1007] }
    }

    if (!validator.definedVal(fields.orderid)) {
      return { status: 400, respcode: 1104, message: errorMsg.responseCode[1104] }
    }

    fields.ma_bank_on_boarding_id = parseInt(fields.ma_bank_on_boarding_id)
    fields.ma_bank_on_boarding_id = isNaN(fields.ma_bank_on_boarding_id) ? 0 : fields.ma_bank_on_boarding_id

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })
      if (sessionData.status == 400) {
        return sessionData
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'sessionData', fields: sessionData })

      const { uicSession } = sessionData.data
      const handler = sessionData.handler
      const ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID

      const delBeneData = await this.checkBeneIdExistData({
        ma_beneficiaries_id: fields.ma_beneficiaries_id
      }, connection)

      if (delBeneData.status == 400) {
        return delBeneData
      }

      let customOTPVerify = false

      const sqlStrMap = `AND mbbm.bank_status in ('S','P') AND mbbm.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}`

      const beneExistSql = `
      SELECT
    custmap.remitter_id,
    mbbm.receiver_id,
    mbob.bank_name,
    mbob.ma_bank_on_boarding_id,
    custmap.mobile_number,
    mbbm.ma_bank_on_boarding_id
    FROM
      ma_beneficiary_bank_mapping AS mbbm
    JOIN ma_beneficiaries AS b
    ON
    mbbm.ma_beneficiaries_id = b.ma_beneficiaries_id AND b.uic = '${fields.uic}'
    JOIN ma_customer_details_bank_mapping AS custmap
    ON
    custmap.uic = b.uic AND custmap.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
    JOIN ma_bank_on_boarding AS mbob
    ON
    mbob.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
    WHERE
    b.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} 
    AND b.uic = '${fields.uic}'
    ${sqlStrMap}
    AND b.beneficiary_status = 'Y'
    limit 1
      `
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'benExistDataSQL', fields: beneExistSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'benExistDataSQL', fields: beneExistSql })

      const benExistData = await this.rawQuery(beneExistSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'benExistData', fields: benExistData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'benExistData', fields: benExistData })

      if (benExistData.length === 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' [VFY_BEN]' }
        // customOTPVerify = true
      } else {
        const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'DEL_BENEFICIARY' }, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'bankDetailData', fields: bankDetailData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'bankDetailData', fields: bankDetailData })

        let CurrentBankDetailConfig = null
        if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
          const bankData = bankDetailData.data
          CurrentBankDetailConfig = bankData[0]
        } else {
          return bankDetailData
        }

        if (validator.definedVal(CurrentBankDetailConfig) && benExistData.length > 0) {
          if (CurrentBankDetailConfig.otp_required == 'YES' || CurrentBankDetailConfig.otp_required == 'NO') {
            console.time('TIMER_VERIFY_DELETE_BENEFICIARY')
            const bankOtp = CurrentBankDetailConfig.otp_required == 'YES'
            const verify = await otp.verifyOnlyBankOtp(fields.orderid, 'DBE', fields.otp, connection, bankOtp)
            console.timeEnd('TIMER_VERIFY_DELETE_BENEFICIARY')

            log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'respVerifyBankOtp', fields: verify })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'respVerifyBankOtp', fields: verify })

            if (verify.status != 200) {
              return verify
            }

            const apiAPIPayload = {
              ma_user_id: fields.ma_user_id,
              ma_bank_on_boarding_id: benExistData[0].ma_bank_on_boarding_id,
              senderid: benExistData[0].remitter_id,
              receiverid: benExistData[0].receiver_id,
              beneficiaryId: fields.ma_beneficiaries_id,
              bankOTPRsponse: verify.response,
              otp: fields.otp
            }

            const BANK_OTP = 'DELETE_BENEFICIARY'

            const payment = new Payment(handler.BANK_NAME, null, connection, handler.BANK_ON_BOARDING_ID)
            const resRemit = await payment.requestToBank(BANK_OTP, apiAPIPayload, fields.sessionRQ, connection)

            log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'resRemit', fields: resRemit })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'resRemit', fields: resRemit })

            if (resRemit.status == 200) {
              const responseBen = await this.deleteBeneFromDB({
                ma_beneficiaries_id: fields.ma_beneficiaries_id,
                ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID
              },
              connection
              )

              log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })

              if (responseBen.status == 200) {
                return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: handler.BANK_NAME, finalDelete: responseBen.finalDelete || false }
              }

              return responseBen
            } else {
              return resRemit
            }
          } else {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[NON_OTP]' }
          }
        } else {
          customOTPVerify = true
        }
      }

      const response = {}
      if (customOTPVerify) {
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'customOTPVerify', fields: { customOTPVerify } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'customOTPVerify', fields: { customOTPVerify } })
        const verify = await otp.verifyOtp(fields.orderid, 'DBE', fields.otp)

        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'verify', fields: { verify } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'verify', fields: { verify } })

        if (verify.status === true) {
          const responseBen = await this.deleteBeneFromDB({
            ma_beneficiaries_id: fields.ma_beneficiaries_id,
            ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID
          },
          connection
          )
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })

          if (responseBen.status == 200) {
            return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: handler.BANK_NAME }
          }

          return responseBen
        } else {
          response.status = 400
          response.message = verify.message
          response.respcode = verify.respcode
          return response
        }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_VFY]' }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_VFY]' }
    } finally {
      connection.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {Object} fields
   * @returns
   */
  static async verifyDeleteBeneficiaryV2 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      if (!validator.definedVal(fields.sessionRQ)) {
        return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
      }

      if (!validator.definedVal(fields.uic)) {
        return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
      }

      if (!validator.definedVal(fields.otp)) {
        return { status: 400, respcode: 1007, message: errorMsg.responseCode[1007] }
      }

      if (!validator.definedVal(fields.orderid)) {
        return { status: 400, respcode: 1104, message: errorMsg.responseCode[1104] }
      }

      fields.ma_bank_on_boarding_id = parseInt(fields.ma_bank_on_boarding_id)
      fields.ma_bank_on_boarding_id = isNaN(fields.ma_bank_on_boarding_id) ? 0 : fields.ma_bank_on_boarding_id

      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })
      if (sessionData.status == 400) return sessionData

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'sessionData', fields: sessionData })

      const handler = sessionData.handler

      const delBeneData = await this.checkBeneIdExistData({ ma_beneficiaries_id: fields.ma_beneficiaries_id }, connectionRead)

      if (delBeneData.status == 400) return delBeneData

      const beneExistSql = `
      SELECT
        custmap.remitter_id,
        mbbm.receiver_id,
        mbob.bank_name,
        mbob.ma_bank_on_boarding_id,
        custmap.mobile_number,
        mbbm.ma_bank_on_boarding_id
      FROM
        ma_beneficiary_bank_mapping AS mbbm JOIN ma_beneficiaries AS b
      ON mbbm.ma_beneficiaries_id = b.ma_beneficiaries_id AND b.uic = '${fields.uic}'
      JOIN ma_customer_details_bank_mapping AS custmap
      ON
      custmap.uic = b.uic AND custmap.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
      JOIN ma_bank_on_boarding AS mbob
      ON
      mbob.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
      WHERE
      b.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} 
      AND b.uic = '${fields.uic}'
      AND mbbm.bank_status in ('S','P') AND mbbm.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}
      AND b.beneficiary_status = 'Y'
      LIMIT 1
      `
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'benExistDataSQL', fields: beneExistSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'benExistDataSQL', fields: beneExistSql })

      const benExistData = await this.rawQuery(beneExistSql, connectionRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'benExistData', fields: benExistData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'benExistData', fields: benExistData })

      if (benExistData.length == 0) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' [VFY_BEN]' }

      const isOtpRequired = await this.getMethodFlag({ ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id, method: 'DEL_BENEFICIARY', connection: connectionRead })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'isOtpRequired', fields: isOtpRequired })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryV3', type: 'isOtpRequired', fields: isOtpRequired })

      if (isOtpRequired.status == 400) return isOtpRequired

      if (!isOtpRequired) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_VFY]' }

      const verify = await otp.verifyOnlyBankOtp(fields.orderid, 'DBE', fields.otp, connection, isOtpRequired)
      console.timeEnd('TIMER_VERIFY_DELETE_BENEFICIARY')

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'respVerifyBankOtp', fields: verify })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'respVerifyBankOtp', fields: verify })

      if (verify.status != 200) return verify

      const apiAPIPayload = {
        ma_user_id: fields.ma_user_id,
        ma_bank_on_boarding_id: benExistData[0].ma_bank_on_boarding_id,
        senderid: benExistData[0].remitter_id,
        receiverid: benExistData[0].receiver_id,
        beneficiaryId: fields.ma_beneficiaries_id,
        bankOTPRsponse: verify.response,
        otp: fields.otp
      }

      const payment = new Payment(handler.BANK_NAME, null, connection, handler.BANK_ON_BOARDING_ID)
      const resRemit = await payment.requestToBank('DELETE_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'resRemit', fields: resRemit })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'resRemit', fields: resRemit })

      if (resRemit.status != 200) return resRemit

      const responseBen = await this.deleteBeneFromDB({ ma_beneficiaries_id: fields.ma_beneficiaries_id, ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID }, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'deleteBeneFromDB', fields: responseBen })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'deleteBeneFromDB', fields: responseBen })

      if (responseBen.status != 200) return responseBen
      return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: handler.BANK_NAME, finalDelete: responseBen.finalDelete || false }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiaryV2', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_VFY]' }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  /**
   * External API Verify OTP And Delete Beneficiary
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   */
  static async verifyDeleteAffiliateBeneficiary ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteAffiliateBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteAffiliateBeneficiary', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      /* BENE VALIDATION */
      const isBeneExist = `SELECT mb.account_number,mb.ifsc_code,mbbm.ma_bank_on_boarding_id,mbbm.bank_status,mb.beneficiary_status,mbbm.receiver_id FROM ma_beneficiaries mb LEFT JOIN ma_beneficiary_bank_mapping mbbm ON mb.ma_beneficiaries_id = mbbm.ma_beneficiaries_id WHERE mb.uic = '${fields.uic}' AND mb.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} AND mb.beneficiary_status='Y'`
      const isBeneExistResult = await this.rawQuery(isBeneExist, connRead)

      if (isBeneExistResult.length == 0) return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }

      let isMappingPresent = false
      let data = {}

      isBeneExistResult.forEach(mapping => {
        if (mapping.ma_bank_on_boarding_id == fields.ma_bank_on_boarding_id) {
          isMappingPresent = true
          data = mapping
        }
      })

      const deleteBeneFlagResponse = await this.getMethodFlag({
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id,
        method: 'SEND_OTP_DELETE_BENEFICIARY',
        connection: connRead
      })

      if (deleteBeneFlagResponse.status == 400) return deleteBeneFlagResponse

      /* CUSTOM OTP */
      if ((deleteBeneFlagResponse.status == 200 && deleteBeneFlagResponse.respcode == 1002) || !deleteBeneFlagResponse || !isMappingPresent) {
        const verify = await otp.verifyOtp(fields.orderid, 'DBE', fields.otp)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'verify', fields: { verify } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'verify', fields: { verify } })
        const response = {}
        if (verify.status === true) {
          if (isMappingPresent) {
            const responseBen = await this.deleteBeneFromDB({
              ma_beneficiaries_id: fields.ma_beneficiaries_id,
              ma_bank_on_boarding_id: fields.handler.BANK_ON_BOARDING_ID
            },
            connection
            )
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })

            if (responseBen.status == 200) {
              return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: fields.handler.BANK_NAME }
            }

            return responseBen
          }

          if (!isMappingPresent) {
            this.TABLE_NAME = 'ma_beneficiaries'
            const updateData = { beneficiary_status: 'N' }
            const updateRes = await this.updateWhere(connection, { data: updateData, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteAffiliateBeneficiary', type: 'updateWhere', fields: updateRes })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteAffiliateBeneficiary', type: 'updateWhere', fields: updateRes })
            return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: fields.handler.BANK_NAME }
          }
        } else {
          response.status = 400
          response.message = verify.message
          response.respcode = verify.respcode
        }
        return response
      }

      /* BANK SIDE OTP */
      if (deleteBeneFlagResponse) {
        const verify = await otp.verifyOnlyBankOtp(fields.orderid, 'DBE', fields.otp, connection, deleteBeneFlagResponse)
        if (verify.status != 200) return verify

        const remitterBankDetailQuery = `SELECT remitter_id FROM ma_customer_details_bank_mapping WHERE uic='${fields.uic}' AND ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id} LIMIT 1`

        const remitterBankDetail = await this.rawQuery(remitterBankDetailQuery, connRead)

        if (remitterBankDetail.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }

        const apiAPIPayload = {
          ma_user_id: fields.ma_user_id,
          ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id,
          senderid: remitterBankDetail[0].remitter_id,
          receiverid: data.receiver_id,
          beneficiaryId: fields.ma_beneficiaries_id,
          bankOTPRsponse: verify.response,
          otp: fields.otp
        }

        const payment = new Payment(fields.handler.BANK_NAME, null, connection, fields.handler.BANK_ON_BOARDING_ID)
        const resRemit = await payment.requestToBank('DELETE_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'resRemit', fields: resRemit })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'resRemit', fields: resRemit })

        if (resRemit.status == 200) {
          const responseBen = await this.deleteBeneFromDB({
            ma_beneficiaries_id: fields.ma_beneficiaries_id,
            ma_bank_on_boarding_id: fields.handler.BANK_ON_BOARDING_ID
          },
          connection
          )

          log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyDeleteBeneficiary', type: 'deleteBeneFromDB', fields: responseBen })

          if (responseBen.status == 200) {
            return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, bankName: fields.handler.BANK_NAME, finalDelete: responseBen.finalDelete || false }
          }

          return responseBen
        } else {
          return resRemit
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyDeleteAffiliateBeneficiary', type: 'error', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'verifyDeleteAffiliateBeneficiary', type: 'error', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  static async debitLedgerEntries (_, fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'debitLedgerEntries', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'debitLedgerEntries', type: 'request', fields: fields })
    try {
      /* LEDGER DEBIT ENTRIES */
      const balanceController = require('../balance/balanceController')
      const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.customer_charges,
        transactionType: '1',
        connection: connection
      })
      if (pointsDetailsEntries.status === 400) {
        return pointsDetailsEntries
      }

      const pointsLedger = require('../creditDebit/pointsLedgerController')
      const retailerDr = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.customer_charges,
        mode: 'dr',
        transaction_type: '21',
        description: 'Debit - ' + util.BeneValidationCharges,
        ma_status: 'S',
        orderid: fields.aggregator_order_id,
        userid: fields.userid,
        corresponding_id: util.airpayCommissionId,
        connection: connection
      })
      if (retailerDr.status === 400) {
        return retailerDr
      }

      const pointsDetailsController = require('../creditDebit/pointsDetailsController')
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: retailerDr.id,
          orderid: fields.aggregator_order_id,
          ma_status: 'S',
          connection: connection
        })
        if (entry.status === 400) {
          return entry
        }
      }
      /* END LEDGER DEBIT ENTRIES */
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      return { status: 400, message: errorMsg.responseCode[1022] + err, respcode: 1022 }
    }
  }

  static async reverseDebitLedger (_, fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'reverseDebitLedger', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'reverseDebitLedger', type: 'request', fields: fields })
    /* const pointsLedger = require('../creditDebit/pointsLedgerController')
    const retailerCr = await pointsLedger.createEntry('_', {
      ma_user_id: fields.ma_user_id,
      amount: fields.customer_charges,
      mode: 'cr',
      transaction_type: '21',
      description: 'Credit - ' + util.BeneValidationCharges + ' - Reversed',
      ma_status: 'REV',
      orderid: 'REV-' + fields.aggregator_order_id,
      userid: fields.userid,
      corresponding_id: util.airpayCommissionId,
      connection: fields.connection
    }) */
    const incentiveFields = {
      connection: fields.connection,
      orderid: fields.aggregator_order_id,
      ma_user_id: fields.ma_user_id
    }
    const beneIncentive = require('../incentive/beneVerifyIncentiveController')
    const rev = await beneIncentive.reverseIncentive(_, incentiveFields, connection)
    return rev
  }

  static async verifyBeneFromBank (_, fields, conn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'request', fields: fields })

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    if (!validator.definedVal(fields.aggregator_order_id)) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid order id' }
    }

    if (!validator.definedVal(fields.ma_beneficiaries_id)) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid beneficiary id' }
    }

    const tmpConn = !(validator.definedVal(conn) && validator.definedVal(conn.threadId))
    const connection = tmpConn ? await mySQLWrapper.getConnectionFromPool() : conn

    let sessionData = {}
    try {
      // Call routing logic and add beneficiary at bank

      if (validator.definedVal(fields.sessionData)) {
        sessionData = fields.sessionData
      } else {
        sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
          ma_user_id: fields.ma_user_id,
          user_id: fields.userid,
          uic: fields.uic
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'verifyBeneFromBank', fields: sessionData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'verifyBeneFromBank', fields: sessionData })

        if (sessionData.status == 400) {
          return sessionData
        }
      }

      const { bank_name, uicSession } = sessionData.data
      const handler = sessionData.handler
      const ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID

      /* NEW CHANGE : USE PRIORITY BANK FOR BENE VERIFICATION */
      fields.ma_bank_verification_id = fields.ma_bank_verification_id || ma_bank_on_boarding_id

      const beneficiaryDetailData = await bankOnBoardDetails.getBeneficiaryDetails(null, { beneficiaryId: fields.ma_beneficiaries_id, ma_bank_on_boarding_id: fields.ma_bank_verification_id }, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryDetailData', fields: beneficiaryDetailData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryDetailData', fields: beneficiaryDetailData })

      if (beneficiaryDetailData.status != 200) {
        return beneficiaryDetailData
      }

      fields.beneficiaryDetailData = beneficiaryDetailData.data[0]
      console.log('fields.beneficiaryDetailData>>', fields.beneficiaryDetailData)

      if (fields.beneficiaryDetailData.ma_bene_verification_id != null && fields.beneficiaryDetailData.ma_bene_verification_id > 0) {
        let verificationMessage = ''
        let bank_bene_name = null
        let bank_account_number = 0
        let bene_verify_status = 'U'

        if (fields.beneficiaryDetailData.bene_verify_status === 'P') {
          verificationMessage = 'Verification Pending from bank side!'
          bank_bene_name = null
          bank_account_number = 0
          bene_verify_status = 'P'
        } else if (fields.beneficiaryDetailData.bene_verify_status === 'S') {
          if (fields.beneficiaryDetailData.bank_benename != null && fields.beneficiaryDetailData != '') {
            verificationMessage = 'Bank Verified'
            bank_bene_name = fields.beneficiaryDetailData.bank_benename
            bank_account_number = fields.beneficiaryDetailData.accountnumber
            bene_verify_status = 'S'
          } else {
            verificationMessage = 'Bank Verified'
            bank_bene_name = fields.beneficiaryDetailData.beneficiary_name
            bank_account_number = fields.beneficiaryDetailData.accountnumber
            bene_verify_status = 'S'
          }
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] + ': Beneficiary Already verified', ma_bene_verification_id: fields.beneficiaryDetailData.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status }
      }

      const bankOnboardSql = `SELECT bene_verification as bank_bene_verification,bene_charges FROM ma_bank_on_boarding where onboarding_status='Y' and ma_bank_on_boarding_id=${fields.ma_bank_verification_id} limit 1 `
      const bankOnboardData = await this.rawQuery(bankOnboardSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'bankOnboardData', fields: bankOnboardData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'bankOnboardData', fields: bankOnboardData })

      if (bankOnboardData.length === 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1192] }
      }

      fields.bank_bene_verification = bankOnboardData[0].bank_bene_verification
      fields.bene_charges = bankOnboardData[0].bene_charges

      const beneVerifySql = `SELECT boardbank.ma_bank_on_boarding_id,boardbank.bank_name as BANK_NAME FROM ma_user_on_boarding_bank_mapping as mapp 
      JOIN ma_bank_on_boarding as boardbank on boardbank.ma_bank_on_boarding_id = mapp.ma_bank_on_boarding_id
      where mapp.ma_user_id='${fields.ma_user_id}' and mapp.ma_bank_on_boarding_id = ${fields.ma_bank_verification_id}  and mapp.onBoarding_status='Y' AND boardbank.onboarding_status='Y' AND mapp.bene_verification = 'Y' AND boardbank.bene_verification = 'Y' limit 1 `
      const beneficiaryVerifyData = await this.rawQuery(beneVerifySql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryVerifyData', fields: beneficiaryVerifyData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryVerifyData', fields: beneficiaryVerifyData })

      console.log('beneVerifySql', beneVerifySql)
      if (beneficiaryVerifyData.length > 0) {
        fields.isBeneVerifyRequird = 'Y'
        fields.verifyBankOnBoardingId = beneficiaryVerifyData[0].ma_bank_on_boarding_id
        fields.bankName = beneficiaryVerifyData[0].BANK_NAME
      } else {
        fields.isBeneVerifyRequird = 'N'
        fields.verifyBankOnBoardingId = 0
      }
      /* Check if the customer is register with partner bank */
      const isRegisterData = await customBankMap.isCustomerRegister(fields.uic, fields.ma_bank_verification_id, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'isCustomerRegister', type: 'isRegisterData', fields: isRegisterData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isCustomerRegister', type: 'isRegisterData', fields: isRegisterData })
      if (isRegisterData.status != 200) {
        return isRegisterData
      }

      if (isRegisterData.respcode === 1002) {
        return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077], uic: fields.uic }
      }

      if (fields.bank_bene_verification === 'Y' && fields.isBeneVerifyRequird === 'Y') {
        /**
         * Check balance for Retailer
         */
        const balanceController = require('../balance/balanceController')
        const availableBalance = await balanceController.getWalletBalancesDirect(_, {
          ma_user_id: fields.ma_user_id,
          ma_status: 'ACTUAL',
          balance_flag: 'SUMMARY',
          connection: connection
        })

        const beneVerifyIncentiveCtrl = require('../incentive/beneVerifyIncentiveController')

        let customer_charges = 0
        fields.ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID
        const globalValues = await beneVerifyIncentiveCtrl.globalValues(fields, connection)
        log.logger({ pagename: 'transfersDistributionController.js', action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })
        // lokiLogger.info({ pagename: 'transfersDistributionController.js', action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })

        if (typeof (globalValues) === 'boolean') {
          return { status: 400, respcode: 1112, message: errorMsg.responseCode[1112] }
        }

        const globalSettings = Object.keys(globalValues).length > 0
        console.log('globalSetting ==============', globalValues, 'here', globalValues.customer_charges)
        if (globalSettings) {
          customer_charges = globalValues.customer_charges
        }
        fields.customer_charges = customer_charges
        console.log('cust charges ==============', customer_charges)

        // Points rate
        let pointsFactor = 1
        const pointsRateController = require('../pointsRate/pointsRateController')
        const pointsFactorData = await pointsRateController.getGlobalPointsRate(_, { connection: connection })
        if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
          pointsFactor = pointsFactorData.points_value
        }

        const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid} limit 1`
        console.log(userSQL)
        const integratedMer = await this.rawQuery(userSQL, connection)

        if (customer_charges > 0 && availableBalance.amount < customer_charges && !(integratedMer.length > 0)) {
        // ----- SEND TRANSFER FAILURE MESSAGE
          return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
        }

        const commissionVal = 0

        /* MA BENE VALIDATION/PENNY DROP CASE HANDLED  */
        let bankBeneHandler = handler
        if (fields.isBeneValidationBankDifferent) {
          if (sessionData.data.bene_bank_priority_json) {
            JSON.parse(sessionData.data.bene_bank_priority_json).forEach(bank => {
              if (bank.ma_bank_on_boarding_id == fields.ma_bank_verification_id) {
                bankBeneHandler = {
                  BANK_NAME: bank.bank_name,
                  BANK_ON_BOARDING_ID: bank.ma_bank_on_boarding_id
                }
              }
            })
          }

          if (!bankBeneHandler.BANK_ON_BOARDING_ID) {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Beneficiary Verification failed!' }
          }
        }

        // Create transaction with I status
        const transactionController = require('../transaction/transactionController')
        if (customer_charges > 0) {
          const transferFields = {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            amount: customer_charges, // To do  check amount from
            bank_charges: fields.bene_charges, // To do ch This fiedl from ma_bank_on_boarding bene_charges
            aggregator_order_id: fields.aggregator_order_id, // TO do Need Ask to generate From Frontend
            transaction_id: fields.aggregator_order_id,
            commission_amount: commissionVal,
            points_factor: pointsFactor,
            transaction_status: 'P',
            transaction_type: '21', // BENEFICICARY VALIDATION
            mobile_number: fields.mobile_number,
            remarks: 'Bank Beneficiary verification Charges ' + customer_charges, // Added bank charges for reference
            ma_bank_on_boarding_id: fields.ma_bank_verification_id
          }

          const transaction = await transactionController.createTransaction(_, transferFields, connection)
          if (transaction.status === 400 || transaction.status == 401) {
            //
            // const data = { transaction_status: 'F', remarks: transferFields.remarks + '::' + transaction.message }
            // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.aggregator_order_id, where: 'aggregator_order_id' })
            const remarksUpdate = transferFields.remarks + '::' + transaction.message
            // const UpdateRes = await this.rawQuery(`UPDATE ma_transaction_master set transaction_status='F',remarks='${remarksUpdate}' where aggregator_order_id = '${fields.aggregator_order_id}' AND transaction_type = 21 `, connection)

            /* RISK MANAGEMENT Changes */
            const data = {
              remarks: remarksUpdate,
              transaction_status: 'F'
            }

            const updateTransactionResult = await transactionController.updateWhereData(connection, {
              data,
              id: fields.aggregator_order_id,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            return transaction
          }

          fields.ma_transaction_master_id = transaction.transaction_id

          console.time('Timer_CreateTransactionDetails')
          transferFields.customer_name = fields.beneficiaryDetailData.remitter_name || 'NA'
          transferFields.bank_name = fields.beneficiaryDetailData.bank ? fields.beneficiaryDetailData.bank : 'NA'
          transferFields.ma_transaction_master_id = transaction.transaction_id
          transferFields.utility_name = 'DMT'
          transferFields.customer_mobile = fields.mobile_number || 'NA'
          transferFields.provider_name = bankBeneHandler.BANK_NAME || 'NA'
          transferFields.receiver_account_number = fields.beneficiaryDetailData.accountnumber || 'NA'
          transferFields.receiver_name = fields.beneficiary_name || 'NA'

          const transactionDetails = await transactionController.createTransactionDetails(_, transferFields, connection)
          console.log('transactionDetailsResponse >>', transactionDetails)
          if (transactionDetails.status === 400) {
            return transactionDetails
          }
          fields.transaction_details_id = transactionDetails.transaction_details_id
          console.timeEnd('Timer_CreateTransactionDetails')
        } else {
          fields.ma_transaction_master_id = 0
        }

        /* RISK MANAGEMENT Changes : NEW CHANGES */
        if (fields.ma_transaction_master_id > 0) {
          const requestParams = {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            aggregator_order_id: fields.aggregator_order_id,
            transaction_status: 'P',
            transaction_type: '21', // BENEFICICARY VALIDATION
            amount: customer_charges,
            customer_name: fields.beneficiaryDetailData.remitter_name || '',
            customer_phone: fields.mobile_number,
            account_number: fields.beneficiaryDetailData.accountnumber || '',
            ben_mobile_number: fields.beneficiaryDetailData.ben_mobile_number || '',
            beneficiary_name: fields.beneficiary_name || '',
            bank_name: fields.beneficiaryDetailData.bank ? fields.beneficiaryDetailData.bank : '',
            ip_address: fields.ip_address
          }
          const checkRiskAnalysisResp = await RiskManagementController.checkTransactionRiskAnalysis({ requestParams, connection })
          if (checkRiskAnalysisResp.status != 200) return checkRiskAnalysisResp
        }

        // LEDGER ENTRIES
        if (fields.ma_transaction_master_id > 0) {
          await mySQLWrapper.beginTransaction(connection)
          const debitLedgerEntries = await this.debitLedgerEntries(_, fields, connection)
          if (debitLedgerEntries.status != 200) {
            await mySQLWrapper.rollback(connection)
            return debitLedgerEntries
          }
          console.log('Debit doneeeeeeeeeeeeeeeeeeeee', debitLedgerEntries)
          await mySQLWrapper.commit(connection)
        }

        // Verifcation charges applicable in this block success or failure
        const beneVerificationCtrl = require('./beneVerificationController')

        const checkBeneVerifyData = await beneVerificationCtrl.getOrCreateBeneVerify(_, fields, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'checkBeneVerifyData', fields: checkBeneVerifyData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'checkBeneVerifyData', fields: checkBeneVerifyData })

        if (checkBeneVerifyData.status != 200) {
          return checkBeneVerifyData
        }

        const isallreadyverified = checkBeneVerifyData.details.allreadyverified
        fields.ma_bene_verification_id = checkBeneVerifyData.details.ma_bene_verification_id

        let walletDeduction = true
        let bankValidatedSuccess = false
        let verificationMessage = ''
        let bank_bene_name = null
        let bank_account_number = 0
        let bene_verify_status = 'U'
        let bankFailReason = errorMsg.responseCode[1028] + ': Beneficiary validation failed'

        if (isallreadyverified === false) {
          const bankBeneVerifyRes = await beneVerificationCtrl.beneVerificationWithBank(_, fields, bankBeneHandler, connection, fields.sessionRQ)
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneVerificationWithBank', fields: bankBeneVerifyRes })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneVerificationWithBank', fields: bankBeneVerifyRes })

          bankFailReason = bankBeneVerifyRes.message
          if (bankBeneVerifyRes.status === 200) {
            if (bankBeneVerifyRes.apiFailed == true) {
              walletDeduction = false
            }
            if (bankBeneVerifyRes.bene_verify_status == 'S' || bankBeneVerifyRes.bene_verify_status == 'P') {
              bankValidatedSuccess = true
              if (bankBeneVerifyRes.bene_verify_status == 'S') {
                if (bankBeneVerifyRes.bank_benename != null && bankBeneVerifyRes.bank_benename != '') {
                  verificationMessage = 'Bank Verified'
                  bank_bene_name = bankBeneVerifyRes.bank_benename
                  bank_account_number = checkBeneVerifyData.details.account_number
                  bene_verify_status = 'S'
                } else {
                  verificationMessage = 'Bank Verified & Beneficiary name empty'
                  bank_bene_name = fields.beneficiary_name
                  bank_account_number = checkBeneVerifyData.details.account_number
                  bene_verify_status = 'S'
                }
              } else if (bankBeneVerifyRes.bene_verify_status == 'P') {
                verificationMessage = 'Verification Pending from bank side!'
                bank_bene_name = null
                bank_account_number = 0
                bene_verify_status = 'P'
              }
            } else if (bankBeneVerifyRes.bene_verify_status == 'F') {
              walletDeduction = false
            }
          } else {
            walletDeduction = false
          }
        } else {
          if (checkBeneVerifyData.details.bene_verify_status === 'P') {
            verificationMessage = 'Verification Pending from bank side!'
            bank_bene_name = null
            bank_account_number = 0
            bene_verify_status = 'P'
          } else if (checkBeneVerifyData.details.bene_verify_status === 'S') {
            if (checkBeneVerifyData.details.bank_benename != null && checkBeneVerifyData.details.bank_benename != '') {
              verificationMessage = 'Bank Verified'
              bank_bene_name = checkBeneVerifyData.details.bank_benename
              bank_account_number = checkBeneVerifyData.details.account_number
              bene_verify_status = 'S'
            } else {
              verificationMessage = 'Bank Verified & Beneficiary name empty'
              bank_bene_name = fields.beneficiaryDetailData.beneficiary_name
              bank_account_number = fields.beneficiaryDetailData.accountnumber
              bene_verify_status = 'S'
            }
          }
          bankValidatedSuccess = true
        }

        console.log('walletDeduction>>', walletDeduction)
        console.log('bankValidatedSuccess>>', bankValidatedSuccess)

        if (walletDeduction === false && fields.ma_transaction_master_id > 0) {
          // revert deudction entries of ledgers
          // LEDGER ENTRIES Revert
          const data = { transaction_status: 'F' }
          // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
          /* RISK MANAGEMENT Changes */
          const updateTransactionResult = await transactionController.updateWhereData(connection, {
            data,
            id: fields.ma_transaction_master_id,
            where: 'ma_transaction_master_id'
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
          console.log('WalletREVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
          await mySQLWrapper.beginTransaction(connection)
          const reverseDebitLedger = await this.reverseDebitLedger(_, fields, connection)
          console.log('reverseDebitLedger>>>', reverseDebitLedger)
          await mySQLWrapper.commit(connection)
          if (reverseDebitLedger.status != 200) {
            return reverseDebitLedger
          }
        }

        if (bankValidatedSuccess === true) {
          // incentive distribution function call
          if (fields.ma_transaction_master_id > 0) {
            await mySQLWrapper.beginTransaction(connection)
            console.log('INCENTIVEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV')
            const distribution = await beneVerifyIncentiveCtrl.incentiveDistribution(fields, connection)
            await mySQLWrapper.commit(connection)

            // Check if integrated merchant
            // const userSQL =  `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid}`
            // const integratedMer = await this.rawQuery(userSQL, connection)
            if (integratedMer.length > 0) {
              // Post receipt
              var receiptData = {}
              receiptData.action = 'POSTRECEIPT'
              receiptData.aggregator_user_id = integratedMer[0].aggregator_user_id
              receiptData.aggregator_order_id = fields.aggregator_order_id
              receiptData.ma_user_id = fields.ma_user_id

              console.time('TIMER_BENE_POSTRECEIPT')
              const responsePostReceipt = await integrated.index(receiptData, connection)
              console.log('integrated returned this', responsePostReceipt)
              console.timeEnd('TIMER_BENE_POSTRECEIPT')
            }

            if (distribution.status === 400) {
              return distribution
            }
          }

          const data = { ma_bene_verification_id: fields.ma_bene_verification_id }
          console.log('DataUpdate>>', data, '>>ma_beneficiaries_id', fields.ma_beneficiaries_id)
          this.TABLE_NAME = 'ma_beneficiaries'
          const updateRes = await this.updateWhere(connection, { data, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })

          // updateRes.affectedRows = 0 // for test reverse entries
          if (updateRes.affectedRows > 0) {
            if (fields.ma_transaction_master_id > 0) {
              const data = { transaction_status: 'S' }
              // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
              /* RISK MANAGEMENT Changes */

              const updateTransactionResult = await transactionController.updateWhereData(connection, {
                data,
                id: fields.ma_transaction_master_id,
                where: 'ma_transaction_master_id'
              })

              log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
            }

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_bene_verification_id: fields.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status, ma_transaction_master_id: fields.ma_transaction_master_id }
          } else {
            if (fields.ma_transaction_master_id > 0) {
              const data = { transaction_status: 'F' }
              // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
              /* RISK MANAGEMENT Changes */
              const updateTransactionResult = await transactionController.updateWhereData(connection, {
                data,
                id: fields.ma_transaction_master_id,
                where: 'ma_transaction_master_id'
              })

              log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
              // Revert debit entries
              console.log('REVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
              await mySQLWrapper.beginTransaction(connection)
              const reverseDebitLedger = await this.reverseDebitLedger('_', fields, connection)
              await mySQLWrapper.commit(connection)
              if (reverseDebitLedger.status != 200) {
                return reverseDebitLedger
              }
            }

            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Beneficiary Verification failed!' }
          }
        } else {
          return { status: 400, respcode: 1028, message: bankFailReason }
        }
      } else {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Bank verification is not available !' }
      }
    } catch (error) {
      console.log('verifyBeneFromBank', error)
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Verify beneficiary:' + error.message }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  /**
   * External API : Beneficiary Verify
   * @param {{fields:Object,connection:Promise<mySQLWrapper.getConnectionFromPool()>, connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async affiliateVerifyBeneFromBank ({ fields, connection, connectionRead }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      if (!validator.definedVal(fields.uic)) {
        return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
      }

      if (!validator.definedVal(fields.aggregator_order_id)) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid order id' }
      }

      if (!validator.definedVal(fields.ma_beneficiaries_id)) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid beneficiary id' }
      }

      let sessionData = fields.sessionData
      // Call routing logic and add beneficiary at bank

      if (!validator.definedVal(fields.sessionData)) {
        sessionData = await dmtSession.getDMTSessionData(conn, fields.sessionRQ, {
          ma_user_id: fields.ma_user_id,
          user_id: fields.userid,
          uic: fields.uic
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'verifyBeneFromBank', fields: sessionData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'verifyBeneFromBank', fields: sessionData })

        if (sessionData.status == 400) {
          return sessionData
        }
      }

      const { bank_name, uicSession } = sessionData.data
      const handler = sessionData.handler
      const ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID

      // ma_bank_verification_id fields missing case handled for old app
      fields.ma_bank_verification_id = ma_bank_on_boarding_id

      const beneficiaryDetailData = await bankOnBoardDetails.getBeneficiaryDetails(null, { beneficiaryId: fields.ma_beneficiaries_id, ma_bank_on_boarding_id: fields.ma_bank_verification_id }, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryDetailData', fields: beneficiaryDetailData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryDetailData', fields: beneficiaryDetailData })

      if (beneficiaryDetailData.status != 200) {
        return beneficiaryDetailData
      }

      fields.beneficiaryDetailData = beneficiaryDetailData.data[0]

      if (fields.beneficiaryDetailData.ma_bene_verification_id != null && fields.beneficiaryDetailData.ma_bene_verification_id > 0) {
        let verificationMessage = ''
        let bank_bene_name = null
        let bank_account_number = 0
        let bene_verify_status = 'U'

        if (fields.beneficiaryDetailData.bene_verify_status === 'P') {
          verificationMessage = 'Verification Pending from bank side!'
          bank_bene_name = null
          bank_account_number = 0
          bene_verify_status = 'P'
        } else if (fields.beneficiaryDetailData.bene_verify_status === 'S') {
          if (fields.beneficiaryDetailData.bank_benename != null && fields.beneficiaryDetailData != '') {
            verificationMessage = 'Bank Verified'
            bank_bene_name = fields.beneficiaryDetailData.bank_benename
            bank_account_number = fields.beneficiaryDetailData.accountnumber
            bene_verify_status = 'S'
          } else {
            verificationMessage = 'Bank Verified'
            bank_bene_name = fields.beneficiaryDetailData.beneficiary_name
            bank_account_number = fields.beneficiaryDetailData.accountnumber
            bene_verify_status = 'S'
          }
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] + ': Beneficiary Already verified', ma_bene_verification_id: fields.beneficiaryDetailData.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status }
      }

      const bankOnboardSql = `SELECT bene_verification as bank_bene_verification,bene_charges FROM ma_bank_on_boarding where onboarding_status='Y' and ma_bank_on_boarding_id=${fields.ma_bank_verification_id} LIMIT 1`
      const bankOnboardData = await this.rawQuery(bankOnboardSql, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'bankOnboardData', fields: bankOnboardData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'bankOnboardData', fields: bankOnboardData })

      if (bankOnboardData.length === 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1192] }
      }

      fields.bank_bene_verification = bankOnboardData[0].bank_bene_verification
      fields.bene_charges = bankOnboardData[0].bene_charges

      const beneVerifySql = `SELECT boardbank.ma_bank_on_boarding_id,boardbank.bank_name as BANK_NAME FROM ma_user_on_boarding_bank_mapping as mapp 
      JOIN ma_bank_on_boarding as boardbank on boardbank.ma_bank_on_boarding_id = mapp.ma_bank_on_boarding_id
      where mapp.ma_user_id='${fields.ma_user_id}' and mapp.ma_bank_on_boarding_id = ${fields.ma_bank_verification_id}  and mapp.onBoarding_status='Y' AND boardbank.onboarding_status='Y' AND mapp.bene_verification = 'Y' AND boardbank.bene_verification = 'Y' LIMIT 1`
      const beneficiaryVerifyData = await this.rawQuery(beneVerifySql, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryVerifyData', fields: beneficiaryVerifyData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneficiaryVerifyData', fields: beneficiaryVerifyData })

      console.log('beneVerifySql', beneVerifySql)
      if (beneficiaryVerifyData.length > 0) {
        fields.isBeneVerifyRequird = 'Y'
        fields.verifyBankOnBoardingId = beneficiaryVerifyData[0].ma_bank_on_boarding_id
        fields.bankName = beneficiaryVerifyData[0].BANK_NAME
      } else {
        fields.isBeneVerifyRequird = 'N'
        fields.verifyBankOnBoardingId = 0
      }
      /* Check if the customer is register with partner bank */
      const isRegisterData = await customBankMap.isCustomerRegister(fields.uic, fields.ma_bank_verification_id, connRead)

      log.logger({ pagename: require('path').basename(__filename), action: 'isCustomerRegister', type: 'isRegisterData', fields: isRegisterData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isCustomerRegister', type: 'isRegisterData', fields: isRegisterData })
      if (isRegisterData.status != 200) {
        return isRegisterData
      }

      if (isRegisterData.respcode === 1002) {
        return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077], uic: fields.uic }
      }

      if (fields.bank_bene_verification !== 'Y' && fields.isBeneVerifyRequird !== 'Y') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Bank verification is not available !' }
      }

      const isMerchantAffiliatedMerchantResponse = await AffiliateController.isMerchantAffiliatedMerchant({ merchant_id: fields.ma_user_id, connectionRead: connRead })

      if (isMerchantAffiliatedMerchantResponse.status != 200) return isMerchantAffiliatedMerchantResponse

      const { affiliate_ma_user_id, affiliate_userid } = isMerchantAffiliatedMerchantResponse

      // CHECK AFFILIATE BALANCE
      const balanceController = require('../balance/balanceController')
      const availableBalance = await balanceController.getWalletBalancesDirect('_', {
        ma_user_id: affiliate_ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection: conn
      })

      const beneVerifyIncentiveCtrl = require('../incentive/beneVerifyIncentiveController')

      let customer_charges = 0
      fields.ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID
      const globalValues = await beneVerifyIncentiveCtrl.globalValues(fields, connRead)
      log.logger({ pagename: 'transfersDistributionController.js', action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })
      // lokiLogger.info({ pagename: 'transfersDistributionController.js', action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })

      if (typeof (globalValues) === 'boolean') {
        return { status: 400, respcode: 1112, message: errorMsg.responseCode[1112] }
      }

      const globalSettings = Object.keys(globalValues).length > 0
      console.log('globalSetting ==============', globalValues, 'here', globalValues.customer_charges)
      if (globalSettings) {
        customer_charges = globalValues.customer_charges
      }
      fields.customer_charges = customer_charges
      console.log('cust charges ==============', customer_charges)

      // Points rate
      let pointsFactor = 1
      const pointsRateController = require('../pointsRate/pointsRateController')
      const pointsFactorData = await pointsRateController.getGlobalPointsRate('_', { connection: conn })
      if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
        pointsFactor = pointsFactorData.points_value
      }

      if (customer_charges > 0 && availableBalance.amount < customer_charges) {
        // ----- SEND TRANSFER FAILURE MESSAGE
        return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
      }

      const commissionVal = 0

      // Create transaction with I status
      const transactionController = require('../transaction/transactionController')
      if (customer_charges > 0) {
        const transferFields = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          amount: customer_charges, // To do  check amount from
          bank_charges: fields.bene_charges, // To do ch This fiedl from ma_bank_on_boarding bene_charges
          aggregator_order_id: fields.aggregator_order_id, // AUTO Generate
          transaction_id: fields.transaction_id, // referID from API side
          commission_amount: commissionVal,
          points_factor: pointsFactor,
          transaction_status: 'P',
          transaction_type: '21', // BENEFICICARY VALIDATION
          mobile_number: fields.mobile_number,
          remarks: 'Bank Beneficiary verification Charges ' + customer_charges, // Added bank charges for reference
          ma_bank_on_boarding_id: fields.ma_bank_verification_id
        }

        const transaction = await transactionController.createTransaction('_', transferFields, conn)
        if (transaction.status === 400 || transaction.status == 401) {
          const remarksUpdate = transferFields.remarks + '::' + transaction.message
          const UpdateRes = await this.rawQuery(`UPDATE ma_transaction_master set transaction_status='F',remarks='${remarksUpdate}' where aggregator_order_id = '${fields.aggregator_order_id}' AND transaction_type = 21 `, conn)
          return transaction
        }

        fields.ma_transaction_master_id = transaction.transaction_id

        console.time('Timer_CreateTransactionDetails')
        transferFields.customer_name = fields.beneficiaryDetailData.remitter_name || 'NA'
        transferFields.bank_name = fields.beneficiaryDetailData.bank ? fields.beneficiaryDetailData.bank : 'NA'
        transferFields.ma_transaction_master_id = transaction.transaction_id
        transferFields.utility_name = 'DMT'
        transferFields.customer_mobile = fields.mobile_number || 'NA'
        transferFields.provider_name = handler.BANK_NAME || 'NA'
        transferFields.receiver_account_number = fields.beneficiaryDetailData.accountnumber || 'NA'
        transferFields.receiver_name = fields.beneficiary_name || 'NA'

        const transactionDetails = await transactionController.createTransactionDetails('_', transferFields, conn)
        console.log('transactionDetailsResponse >>', transactionDetails)
        if (transactionDetails.status === 400) {
          return transactionDetails
        }
        fields.transaction_details_id = transactionDetails.transaction_details_id
        console.timeEnd('Timer_CreateTransactionDetails')
      } else {
        fields.ma_transaction_master_id = 0
      }

      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (fields.ma_transaction_master_id > 0) {
        const requestParams = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          aggregator_order_id: fields.aggregator_order_id,
          transaction_status: 'P',
          transaction_type: '21', // BENEFICICARY VALIDATION
          amount: customer_charges,
          customer_name: fields.beneficiaryDetailData.remitter_name || '',
          customer_phone: fields.mobile_number,
          account_number: fields.beneficiaryDetailData.accountnumber || '',
          ben_mobile_number: fields.beneficiaryDetailData.ben_mobile_number || '',
          beneficiary_name: fields.beneficiary_name || '',
          bank_name: fields.beneficiaryDetailData.bank ? fields.beneficiaryDetailData.bank : '',
          ip_address: fields.ip_address || ''
        }
        const checkRiskAnalysisResp = await RiskManagementController.checkTransactionRiskAnalysis({ requestParams, connection })
        if (checkRiskAnalysisResp.status != 200) return checkRiskAnalysisResp
      }

      // LEDGER ENTRIES
      if (fields.ma_transaction_master_id > 0) {
        await mySQLWrapper.beginTransaction(conn)
        const debitLedgerEntries = await this.debitLedgerEntries('_', fields, conn)
        if (debitLedgerEntries.status != 200) {
          await mySQLWrapper.rollback(conn)
          return debitLedgerEntries
        }
        console.log('Debit doneeeeeeeeeeeeeeeeeeeee', debitLedgerEntries)
        await mySQLWrapper.commit(conn)
      }

      // Verifcation charges applicable in this block success or failure
      const beneVerificationCtrl = require('./beneVerificationController')

      const checkBeneVerifyData = await beneVerificationCtrl.getOrCreateBeneVerify('_', fields, conn)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'checkBeneVerifyData', fields: checkBeneVerifyData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'checkBeneVerifyData', fields: checkBeneVerifyData })

      if (checkBeneVerifyData.status != 200) {
        return checkBeneVerifyData
      }

      const isallreadyverified = checkBeneVerifyData.details.allreadyverified
      fields.ma_bene_verification_id = checkBeneVerifyData.details.ma_bene_verification_id

      let walletDeduction = true
      let bankValidatedSuccess = false
      let verificationMessage = ''
      let bank_bene_name = null
      let bank_account_number = 0
      let bene_verify_status = 'U'
      let bankFailReason = errorMsg.responseCode[1028] + ': Beneficiary validation failed'
      if (isallreadyverified === false) {
        const bankBeneVerifyRes = await beneVerificationCtrl.beneVerificationWithBank('_', fields, handler, conn, fields.sessionRQ)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneVerificationWithBank', fields: bankBeneVerifyRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyBeneFromBank', type: 'beneVerificationWithBank', fields: bankBeneVerifyRes })

        bankFailReason = bankBeneVerifyRes.message
        if (bankBeneVerifyRes.status === 200) {
          if (bankBeneVerifyRes.apiFailed == true) {
            walletDeduction = false
          }
          if (bankBeneVerifyRes.bene_verify_status == 'S' || bankBeneVerifyRes.bene_verify_status == 'P') {
            bankValidatedSuccess = true
            if (bankBeneVerifyRes.bene_verify_status == 'S') {
              if (bankBeneVerifyRes.bank_benename != null && bankBeneVerifyRes.bank_benename != '') {
                verificationMessage = 'Bank Verified'
                bank_bene_name = bankBeneVerifyRes.bank_benename
                bank_account_number = checkBeneVerifyData.details.account_number
                bene_verify_status = 'S'
              } else {
                verificationMessage = 'Bank Verified & Beneficiary name empty'
                bank_bene_name = fields.beneficiary_name
                bank_account_number = checkBeneVerifyData.details.account_number
                bene_verify_status = 'S'
              }
            } else if (bankBeneVerifyRes.bene_verify_status == 'P') {
              verificationMessage = 'Verification Pending from bank side!'
              bank_bene_name = null
              bank_account_number = 0
              bene_verify_status = 'P'
            }
          } else if (bankBeneVerifyRes.bene_verify_status == 'F') {
            walletDeduction = false
          }
        } else {
          walletDeduction = false
        }
      } else {
        if (checkBeneVerifyData.details.bene_verify_status === 'P') {
          verificationMessage = 'Verification Pending from bank side!'
          bank_bene_name = null
          bank_account_number = 0
          bene_verify_status = 'P'
        } else if (checkBeneVerifyData.details.bene_verify_status === 'S') {
          if (checkBeneVerifyData.details.bank_benename != null && checkBeneVerifyData.details.bank_benename != '') {
            verificationMessage = 'Bank Verified'
            bank_bene_name = checkBeneVerifyData.details.bank_benename
            bank_account_number = checkBeneVerifyData.details.account_number
            bene_verify_status = 'S'
          } else {
            verificationMessage = 'Bank Verified & Beneficiary name empty'
            bank_bene_name = fields.beneficiaryDetailData.beneficiary_name
            bank_account_number = fields.beneficiaryDetailData.accountnumber
            bene_verify_status = 'S'
          }
        }
        bankValidatedSuccess = true
      }
      console.log('walletDeduction>>', walletDeduction)
      console.log('bankValidatedSuccess>>', bankValidatedSuccess)

      if (walletDeduction === false && fields.ma_transaction_master_id > 0) {
        // revert deudction entries of ledgers
        // LEDGER ENTRIES Revert
        const data = { transaction_status: 'F' }
        const updateTransaction = await transactionController.updateWhereData(conn, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
        console.log('WalletREVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
        await mySQLWrapper.beginTransaction(conn)
        const reverseDebitLedger = await this.reverseDebitLedger('_', fields, conn)
        console.log('reverseDebitLedger>>>', reverseDebitLedger)
        await mySQLWrapper.commit(conn)
        if (reverseDebitLedger.status != 200) {
          return reverseDebitLedger
        }
      }

      if (bankValidatedSuccess !== true) return { status: 400, respcode: 1028, message: bankFailReason }
      // incentive distribution function call
      if (fields.ma_transaction_master_id > 0) {
        await mySQLWrapper.beginTransaction(conn)
        console.log('INCENTIVEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV')
        const distribution = await beneVerifyIncentiveCtrl.incentiveDistribution(fields, conn)
        await mySQLWrapper.commit(conn)

        if (distribution.status === 400) {
          return distribution
        }
      }

      const data = { ma_bene_verification_id: fields.ma_bene_verification_id }
      console.log('DataUpdate>>', data, '>>ma_beneficiaries_id', fields.ma_beneficiaries_id)
      this.TABLE_NAME = 'ma_beneficiaries'
      const updateRes = await this.updateWhere(conn, { data, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })

      // updateRes.affectedRows = 0 // for test reverse entries
      if (updateRes.affectedRows > 0) {
        if (fields.ma_transaction_master_id > 0) {
          const data = { transaction_status: 'S' }
          const updateTransaction = await transactionController.updateWhereData(conn, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_bene_verification_id: fields.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status, ma_transaction_master_id: fields.ma_transaction_master_id }
      } else {
        if (fields.ma_transaction_master_id < 0) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Beneficiary Verification failed!' }

        const data = { transaction_status: 'F' }
        const updateTransaction = await transactionController.updateWhereData(conn, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
        // Revert debit entries
        console.log('REVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
        await mySQLWrapper.beginTransaction(conn)
        const reverseDebitLedger = await this.reverseDebitLedger('_', fields, conn)
        await mySQLWrapper.commit(conn)
        if (reverseDebitLedger.status != 200) {
          return reverseDebitLedger
        }
      }
    } catch (error) {
      console.log('verifyBeneFromBank', error)
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Verify beneficiary:' + error.message }
    } finally {
      if (isSetRead) connRead.release()
      if (isSet) conn.release()
    }
  }

  /**
   * EMITRA ACCOUNT VERIFICATION
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async beneverify (_, fields) {
    log.logger({ pagename: 'beneficiaryController.js', action: 'beneverify', type: 'request', fields: { _, fields } })
    // lokiLogger.info({ pagename: 'beneficiaryController.js', action: 'beneverify', type: 'request', fields: { _, fields } })
    try {
      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (_ && _.ip_address) {
        fields.ip_address = _.ip_address
      }
      const account_number = fields.account_number
      console.log('accno', account_number)
      /* BENE BLACKLIST CHANGES */
      fields.skipBeneficiaryBlacklistCheck = true
      const beneresp = await this.addBeneficiaryV2('', { ...fields, _beneVerifyOtp: true })
      log.logger({ pagename: 'beneficiaryController.js', action: 'beneverify', type: 'beneaddition-response', fields: beneresp })
      // lokiLogger.info({ pagename: 'beneficiaryController.js', action: 'beneverify', type: 'beneaddition-response', fields: beneresp })
      if (beneresp.status == 200) {
        const businessbank = require('../businessProfiles/businessProfilesController')

        const bankdata = {
          ma_user_id: fields.ma_user_id,
          bank_name: fields.bank_name,
          account_type: fields.account_type,
          ifsc_code: fields.ifsc_code,
          account_name: fields.beneficiary_name

        }
        if (beneresp.bene_verify_status == 'S') {
          bankdata.bank_status = 'Y'
          bankdata.account_no = beneresp.bank_account_number
        } else {
          bankdata.bank_status = 'P'
          bankdata.account_no = account_number
        }
        const resp = await businessbank.createEntrySingle('', bankdata)
        if (resp.status == 200 && beneresp.bene_verify_status == 'S') {
          if (fields.service_id == util.aeps_emitra_reg_service) {
            const connection = await mySQLWrapper.getConnectionFromPool()
            const aepsflagstatus = await this.rawQuery(`UPDATE ma_integration_user_master set aeps_registration_type = 'Y' where ma_user_id = '${fields.ma_user_id}'`, connection)
            connection.release()
          }
        }
        if (beneresp.bene_verify_status == 'S') {
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
        } else {
          return { status: 200, message: errorMsg.responseCode[1170], respcode: 1170 }
        }
      } else {
        return beneresp
      }
    } catch (error) {
      log.logger({ pagename: 'beneficiaryController.js', action: 'beneverify', type: 'response-error', fields: error })
      // lokiLogger.error({ pagename: 'beneficiaryController.js', action: 'beneverify', type: 'response-error', fields: error })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] }
    } finally {

    }
  }

  static async bankstatuscron (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      var bankdata = await this.rawQuery('SELECT * from ma_business_profiles_bank where  bank_status = \'P\'', connection)
      log.logger({ pagename: 'beneficiaryController.js', action: 'bankstatuscron', type: 'response', fields: bankdata })
      // lokiLogger.info({ pagename: 'beneficiaryController.js', action: 'bankstatuscron', type: 'response', fields: bankdata })
      if (bankdata.length > 0) {
        for (var k = 0; k < bankdata.length; k++) {
          var benedata = await this.rawQuery(`SELECT * FROM ma_bene_verification WHERE account_number = '${bankdata[k].account_no}' AND ifsc_code = '${bankdata[k].ifsc_code}'`, connection)
          if (benedata[0].bene_verify_status == 'S') {
            // update to S
            const UpdateResStatus = await this.rawQuery(`UPDATE ma_business_profiles_bank set bank_status= 'Y' where ma_business_profiles_bank_id = '${bankdata[k].ma_business_profiles_bank_id}'`, connection)
            const aepsflagstatus = await this.rawQuery(`UPDATE ma_integration_user_master set aeps_registration_type = 'Y' where ma_user_id = '${bankdata[k].ma_user_id}'`, connection)
          } else if (benedata[0].bene_verify_status == 'F') {
            const UpdateResStatus = await this.rawQuery(`UPDATE ma_business_profiles_bank set bank_status= 'F' where ma_business_profiles_bank_id = '${bankdata[k].ma_business_profiles_bank_id}'`, connection)
            // update to fail
          }
        }
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
    } catch (error) {
      log.logger({ pagename: 'beneficiaryController.js', action: 'bankstatuscron', type: 'response-error', fields: error })
      // lokiLogger.error({ pagename: 'beneficiaryController.js', action: 'bankstatuscron', type: 'response-error', fields: error })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] }
    } finally {
      connection.release()
    }
  }

  static async resentOtpDeleteBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'request', fields: fields })

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    if (!validator.definedVal(fields.orderid)) {
      return { status: 400, respcode: 1104, message: errorMsg.responseCode[1104] }
    }

    fields.ma_bank_on_boarding_id = parseInt(fields.ma_bank_on_boarding_id)
    fields.ma_bank_on_boarding_id = isNaN(fields.ma_bank_on_boarding_id) ? 0 : fields.ma_bank_on_boarding_id

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })
      if (sessionData.status == 400) {
        return sessionData
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'sessionData', fields: sessionData })

      const { uicSession, mobileNumber } = sessionData.data
      const handler = sessionData.handler
      const ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID

      const delBeneData = await this.checkBeneIdExistData({
        ma_beneficiaries_id: fields.ma_beneficiaries_id
      }, connection)

      if (delBeneData.status == 400) {
        return delBeneData
      }

      let customOTP = false
      const sqlStrMap = `AND mbbm.bank_status = 'S' AND mbbm.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}`

      const beneExistSql = `
      SELECT
    custmap.remitter_id,
    mbbm.receiver_id,
    mbob.bank_name,
    mbob.ma_bank_on_boarding_id,
    custmap.mobile_number,
    mbbm.ma_bank_on_boarding_id
    FROM
      ma_beneficiary_bank_mapping AS mbbm
    JOIN ma_beneficiaries AS b
    ON
    mbbm.ma_beneficiaries_id = b.ma_beneficiaries_id AND b.uic = '${fields.uic}'
    JOIN ma_customer_details_bank_mapping AS custmap
    ON
    custmap.uic = b.uic AND custmap.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
    JOIN ma_bank_on_boarding AS mbob
    ON
    mbob.ma_bank_on_boarding_id = mbbm.ma_bank_on_boarding_id
    WHERE
    b.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} 
    AND b.uic = '${fields.uic}' 
    AND b.beneficiary_status = 'Y'
    ${sqlStrMap}
    limit 1
      `
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'benExistDataSQL', fields: beneExistSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'benExistDataSQL', fields: beneExistSql })

      const benExistData = await this.rawQuery(beneExistSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'benExistData', fields: benExistData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'benExistData', fields: benExistData })

      if (benExistData.length === 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' [VFY_BEN]' }
      } else {
        const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'DEL_BENEFICIARY' }, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'bankDetailData', fields: bankDetailData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpDeleteBeneficiary', type: 'bankDetailData', fields: bankDetailData })

        let CurrentBankDetailConfig = null
        if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
          const bankData = bankDetailData.data
          CurrentBankDetailConfig = bankData[0]
        } else {
          return bankDetailData
        }

        if (validator.definedVal(CurrentBankDetailConfig) && benExistData.length > 0) {
          if (CurrentBankDetailConfig.otp_required == 'YES' || CurrentBankDetailConfig.otp_required == 'NO') {
            const bankOtp = CurrentBankDetailConfig.otp_required == 'YES'
            console.time('TIMER_RESEND_OTP_DELETE_BENEFICIARY')
            const resp = await otp.resentBankOtp(handler, 'RESEND_OTP_DELETE_BENEFICIARY', benExistData[0].mobile_number, fields.orderid, 'DBE', connection, fields.sessionRQ, 2002, bankOtp)
            console.timeEnd('TIMER_RESEND_OTP_DELETE_BENEFICIARY')
            // todo handling yes -- Call Sent bank otp api {}
            log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'resentBankOtp', fields: resp })

            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'resentBankOtp', fields: resp })

            const response = {}
            if (resp.status === true) {
              response.status = 200
              response.respcode = 1000
              response.message = errorMsg.responseCode[2002]
            } else {
              response.status = 400
              response.respcode = resp.respcode
              response.message = resp.message
            }

            return response
          } else {
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[NON_OTP]' }
          }
        } else {
          customOTP = true
        }
      }

      if (customOTP) {
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'customOTP', fields: { customOTP } })

        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'customOTP', fields: { customOTP } })

        const response = {}
        var resp = await otp.resentOtp(mobileNumber, fields.orderid, 'DBE')
        log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'resentOtp', fields: { resp } })

        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'resentOtp', fields: { resp } })

        if (resp.status === true) {
          response.status = 200
          response.respcode = 1000
          response.message = errorMsg.responseCode[1000]
        } else {
          response.status = 400
          response.respcode = resp.respcode
          response.message = resp.message
        }
        return response
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_VFY]' }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'resentOtpV2', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [DEL_BEN_VFY]' }
    } finally {
      connection.release()
    }
  }

  static async trywithAddBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'trywithAddBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'trywithAddBeneficiary', type: 'request', fields: fields })

    try {
      const trywithaddBeneficiaryV2 = await this.addBeneficiaryV2('', fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'trywithAddBeneficiary', type: 'trywithAcceptAmountRes', fields: trywithaddBeneficiaryV2 })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'trywithAddBeneficiary', type: 'trywithAcceptAmountRes', fields: trywithaddBeneficiaryV2 })

      return trywithaddBeneficiaryV2
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'trywithAddBeneficiary', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'trywithAddBeneficiary', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[TRY-CONFIRM-AMT]' }
    } finally {
      // if (tmpConn) connection.release()
    }
  }

  static async confirmVerifiedBeneficiaryV2 (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'confirmVerifiedBeneficiaryV2', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'confirmVerifiedBeneficiaryV2', type: 'request', fields: fields })

    try {
      fields.bank_verify = true
      const customerLoginV2 = await this.verifyBeneficiaryOtpV2(_, fields)
      return customerLoginV2
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'confirmVerifiedBeneficiaryV2', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'confirmVerifiedBeneficiaryV2', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001] + ' [CONFRM_BEN]', respcode: 1001, action_code: 1001 }
    }
  }

  static async sendOtpVerifiedBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpVerifiedBeneficiary', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'sendOtpVerifiedBeneficiary', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      fields.bank_verify = false
      // validation for ma_bene_verification of id
      const isBeneficaryVerifiedExistQuery = `SELECT ma_bene_verification_id FROM ma_bene_verification WHERE ma_bene_verification_id='${fields.ma_bene_verification_id}'`
      const isBeneficaryVerifiedExist = await this.rawQuery(isBeneficaryVerifiedExistQuery, connection)
      if (isBeneficaryVerifiedExist.length == 0) {
        return { status: 400, message: errorMsg.responseCode[1019], respcode: 1019 }
      }
      const customerLoginV2 = await this.addBeneficiaryV2(_, fields)
      return customerLoginV2
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpVerifiedBeneficiary', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'sendOtpVerifiedBeneficiary', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001] + ' [CONFRM_BEN]', respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async checkBeneIdExistData (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneIdExistData', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneIdExistData', type: 'request', fields: fields })
    const delIdDataSQL = `select ma_beneficiaries_id 
      FROM ma_beneficiaries 
      WHERE beneficiary_status= "Y" 
      AND  ma_beneficiaries_id = ${fields.ma_beneficiaries_id} limit 1 `
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneIdExistData', type: 'delIdDataSQL', fields: { delIdDataSQL } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneIdExistData', type: 'delIdDataSQL', fields: { delIdDataSQL } })
    const delIdData = await this.rawQuery(delIdDataSQL, connection)

    if (delIdData.length == 0) {
      return { status: 400, respcode: 1091, message: errorMsg.responseCode[1091] + '~NoRecordFound~[DEL_BE]' }
    } else {
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    }
  }

  /**
   *
   * @param {Object} fields
   * @param {Promise<mySQLWrapper.getConnectionFromPool()>} connection
   * @returns
   */
  static async massInsertBeneficiaries (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const bankIdsQuery = `SELECT ma_bank_master_id,ifsc FROM ma_bank_branch WHERE ifsc IN ('${fields.IfscCodes.join('\',\'')}')`

      const bankIds = await this.rawQuery(bankIdsQuery, conn)

      const isfcBankIdMapping = {}

      bankIds.forEach(bankId => {
        isfcBankIdMapping[bankId.ifsc] = bankId.ma_bank_master_id
      })

      await mySQLWrapper.beginTransaction(conn)
      const beneficiaries = []

      /* fetch existing beneficiaries */

      const existingBeneficiariesResult = await this.fetchExistingBeneficiary({ uic: fields.uic, connection: conn })

      if (existingBeneficiariesResult.status == 400) {
        return existingBeneficiariesResult
      }

      const existingBeneficiariesMapping = {}
      existingBeneficiariesResult.existingBeneficiaries.forEach((beneficiary) => {
        if (!existingBeneficiariesMapping[`${beneficiary.account_number}-${beneficiary.ifsc_code}`]) {
          existingBeneficiariesMapping[`${beneficiary.account_number}-${beneficiary.ifsc_code}`] = beneficiary
        }
      })

      /* insert only new Beneficiaries */

      const insertUniqueBene = []

      fields.beneList.forEach(beneficiary => {
        if (!existingBeneficiariesMapping[`${beneficiary.accountNumber}-${beneficiary.ifscCode}`]) {
          insertUniqueBene.push(beneficiary)
        }
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'insertUniqueBene', fields: insertUniqueBene })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'insertUniqueBene', fields: insertUniqueBene })

      const beneListLen = insertUniqueBene.length
      for (let index = 0; index < beneListLen; index++) {
        const beneficiary = insertUniqueBene[index]
        beneficiaries.push(`('${beneficiary.accountNumber}','${beneficiary.beneficiaryName}','${isfcBankIdMapping[beneficiary.ifscCode]}','${beneficiary.ifscCode}','IN','${beneficiary.beneficiaryMobileNumber || 0}','${fields.mobile_number}','${fields.uic}','${fields.ma_user_id}','${fields.userid}','Y')`)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'beneficiaries', fields: beneficiaries })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'beneficiaries', fields: beneficiaries })

      if (insertUniqueBene.length > 0 && beneficiaries.length > 0) {
        const insertbBeneficiariesResult = await this.insertBulk(conn, { fields: '(`account_number`,`beneficiary_name`,`ma_bank_master_id`,`ifsc_code`,`country_code`,`ben_mobile_number`,`mobile_number`,`uic`,`ma_user_id`,`userid`,`beneficiary_status`)', data: beneficiaries.join(',') })

        if (insertbBeneficiariesResult.status === 400 || insertbBeneficiariesResult.status == 401) {
          await mySQLWrapper.rollback(conn)
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }

        if (insertbBeneficiariesResult.affectedRows == 0) {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
      }

      // insert new beneficiaries mapping
      const beneficiariesMapping = []
      // select inserted beneficiaries and get the ids
      for (let index = 0; index < fields.beneList.length; index++) {
        const beneficiary = fields.beneList[index]
        const maBeneficiariesIdQuery = `SELECT ma_beneficiaries_id FROM ma_beneficiaries WHERE account_number='${beneficiary.account_number}' AND ifsc_code='${beneficiary.ifsc_code}' AND uic='${fields.uic}' AND ma_user_id=${fields.ma_user_id} AND beneficiary_status='Y'`
        const maBeneficiariesIdResult = await this.rawQuery(maBeneficiariesIdQuery, conn)
        if (maBeneficiariesIdResult.length == 0) {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
        beneficiariesMapping.push(`('${maBeneficiariesIdResult[0].ma_beneficiaries_id}','${fields.ma_bank_on_boarding_id}','${fields.uic}','${fields.ma_user_id}','${fields.userid}','S','${beneficiary.beneficiaryId}')`)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'maBeneficiariesIdResult', type: 'request', fields: beneficiariesMapping.join(',') })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'maBeneficiariesIdResult', type: 'request', fields: beneficiariesMapping.join(',') })

      // this.TABLE_NAME = 'ma_beneficiary_bank_mapping'
      // const insertbBeneficiariesMappingResult = await this.insertBulk(conn, { fields: '(`ma_beneficiaries_id`,`ma_bank_on_boarding_id`,`uic`,`ma_user_id`,`userid`,`bank_status`,`receiver_id`)', data: beneficiariesMapping })

      const Insertfields = '(`ma_beneficiaries_id`,`ma_bank_on_boarding_id`,`uic`,`ma_user_id`,`userid`,`bank_status`,`receiver_id`)'

      const insertbBeneficiariesMappingResultQuery = `INSERT IGNORE ma_beneficiary_bank_mapping${Insertfields} VALUES ${beneficiariesMapping.join(',')}`

      log.logger({ pagename: require('path').basename(__filename), action: 'maBeneficiariesIdResult', type: 'insertbBeneficiariesMappingResultQuery', fields: insertbBeneficiariesMappingResultQuery })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'maBeneficiariesIdResult', type: 'insertbBeneficiariesMappingResultQuery', fields: insertbBeneficiariesMappingResultQuery })

      const insertbBeneficiariesMappingResult = await this.rawQuery(insertbBeneficiariesMappingResultQuery, conn)

      log.logger({ pagename: require('path').basename(__filename), action: 'maBeneficiariesIdResult', type: 'insertbBeneficiariesMappingResult', fields: insertbBeneficiariesMappingResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'maBeneficiariesIdResult', type: 'insertbBeneficiariesMappingResult', fields: insertbBeneficiariesMappingResult })

      if (insertbBeneficiariesMappingResult.status === 400 || insertbBeneficiariesMappingResult.status == 401) {
        await mySQLWrapper.rollback(conn)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }

      if (insertbBeneficiariesMappingResult.affectedRows == 0) {
        await mySQLWrapper.commit(conn)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }

      await mySQLWrapper.commit(conn)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'catchError', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {Object} fields
   * @param {mySQLWrapper} param.connection
   * @returns
   */
  static async fetchBeneficiariesFromBank (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const handler = fields.bankHandler
      const apiAPIPayload = {
        sendermobilenumber: fields.mobile_number,
        ma_user_id: fields.ma_user_id,
        ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID
      }

      const payment = new Payment(handler.BANK_NAME, null, connection, handler.BANK_ON_BOARDING_ID)
      const resRemit = await payment.requestToBank('SYNC_BENEFICIARY', apiAPIPayload, fields.sessionRQ, conn)

      return resRemit
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'catchError', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {Object} param
   * @param {Object} param.bankHandler
   * @param {mySQLWrapper} param.connection
   * @returns
   */
  static async isSynBeneficiaryAllowed ({ bankHandler, connection }) {
    return await this.getMethodFlag({
      ma_bank_on_boarding_id: bankHandler.BANK_ON_BOARDING_ID,
      method: 'SYNC_BENEFICIARY',
      connection
    })
  }

  /**
   *
   * @param {Object} fields
   * @param {mySQLWrapper} param.connection
   * @returns
   */
  static async syncBeneficiariesFromBank (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      /* get the mobile number */

      const customerDataQuery = `SELECT mobile_number FROM ma_customer_details WHERE uic='${fields.uic}' AND customer_status='Y' LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'syncBeneficiaries_query', fields: customerDataQuery })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'syncBeneficiaries_query', fields: customerDataQuery })

      const customerDataResult = await this.rawQuery(customerDataQuery, conn)
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'customerDataResult', fields: customerDataResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'customerDataResult', fields: customerDataResult })
      if (customerDataResult.length == 0) {
        // 1033
        return { status: 400, message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']', respcode: 1115 }
      }

      fields.mobile_number = customerDataResult[0].mobile_number // data set for next request
      console.log('Mobile number: ', fields.mobile_number)

      const isSynBeneficiaryAllowedResult = await this.isSynBeneficiaryAllowed({
        bankHandler: fields.bankHandler,
        conn
      })

      if (isSynBeneficiaryAllowedResult.status == 400) {
        return isSynBeneficiaryAllowedResult
      }

      if (!isSynBeneficiaryAllowedResult || (isSynBeneficiaryAllowedResult.status == 200 && isSynBeneficiaryAllowedResult.respcode == 1002)) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }

      console.time('TIMER_fetchBeneficiariesFromBank')
      const requestStartBank = process.hrtime()
      const beneListResult = await this.fetchBeneficiariesFromBank(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'beneListResult', fields: beneListResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'beneListResult', fields: beneListResult })

      console.timeEnd('TIMER_fetchBeneficiariesFromBank')
      const requestEndBank = process.hrtime(requestStartBank)
      const timeInMsBank = (requestEndBank[0] * ********** + requestEndBank[1]) / 1000000
      log.logger({ pagename: require('path').basename(__filename), action: 'TIMER_fetchBeneficiariesFromBank', type: 'TIMER', fields: timeInMsBank })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'TIMER_fetchBeneficiariesFromBank', type: 'TIMER', fields: timeInMsBank })

      if (beneListResult.status != 200) {
        return beneListResult
      }
      console.log('BENE from Bank: ', beneListResult)

      console.time('TIMER_fetchBeneficiariesFromDB')
      const requestStartDB = process.hrtime()
      const beneficiariesResult = await this.fetchBeneficiariesFromDB({
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.bankHandler.BANK_ON_BOARDING_ID,
        connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'beneficiariesResult', fields: beneficiariesResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'beneficiariesResult', fields: beneficiariesResult })
      console.timeEnd('TIMER_fetchBeneficiariesFromDB')
      const requestEndDB = process.hrtime(requestStartDB)
      const timeInMsDB = (requestEndDB[0] * ********** + requestEndDB[1]) / 1000000
      log.logger({ pagename: require('path').basename(__filename), action: 'TIMER_fetchBeneficiariesFromDB', type: 'TIMER', fields: timeInMsDB })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'TIMER_fetchBeneficiariesFromDB', type: 'TIMER', fields: timeInMsDB })

      if (beneficiariesResult.status != 200) {
        return beneficiariesResult
      }

      const beneficiariesMap = {}
      const IfscCodes = []
      // dictionary of beneficiaries for checking existing beneficiaries
      beneficiariesResult.beneficiaries.forEach(beneficiary => {
        /* case sensitive issue handled */
        beneficiary.ifsc_code = beneficiary.ifsc_code.toLowerCase()

        // check if account number and isfc code combination exists or not
        if (!beneficiariesMap[`${beneficiary.account_number}-${beneficiary.ifsc_code}`]) {
          beneficiariesMap[`${beneficiary.account_number}-${beneficiary.ifsc_code}`] = beneficiary
        }
      })
      // compare current beneficiaries with bank side beneficiaries
      // if not exist at airpay then insert the beneficiaries
      let newBeneList = []
      beneListResult.beneList.forEach(beneficiary => {
        // account number&ifscCode doesn't exits then insert the new bene
        /* case sensitive issue handled */
        if (!beneficiariesMap[`${beneficiary.accountNumber}-${beneficiary.ifscCode.toLowerCase()}`]) {
          newBeneList.push(beneficiary)
        }

        // list of IFSC Code For Bank IDS
        IfscCodes.push(beneficiary.ifscCode)
      })

      if (beneficiariesResult.beneficiaries.length == 0 && beneListResult.beneList.length > 0) {
        newBeneList = beneListResult.beneList
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'newBeneList', fields: newBeneList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'newBeneList', fields: newBeneList })
      if (newBeneList.length == 0) {
        return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
      }

      fields.beneList = newBeneList // data set for next request
      fields.IfscCodes = IfscCodes // data set for next request
      fields.ma_bank_on_boarding_id = fields.bankHandler.BANK_ON_BOARDING_ID // data set for next request

      /*
      await mySQLWrapper.beginTransaction(conn)
      // Check whether beneficiary/beneficiary mapping already exist
      const checkBeneOrBeneMappingExists = await this.checkBeneOrBeneMappingExists(fields, conn)
      log.logger({ pagename: path.basename(__filename), action: 'isBeneExist', type: 'isBeneExist', fields: checkBeneOrBeneMappingExists })
      if (checkBeneOrBeneMappingExists.insert_bene.length > 0) {
        fields.IfscCodes = checkBeneOrBeneMappingExists.insert_bene.map(i => i.ifscCode)
        fields.beneList = checkBeneOrBeneMappingExists.insert_bene
        // const massInsertResult = await this.massInsertBeneficiaries(fields, conn)
        // log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'massInsertResult', fields: massInsertResult })
        const massInsertResult = await this.insertBene(fields, conn)
        log.logger({ pagename: path.basename(__filename), action: 'insertBene', type: 'insertBene-response', fields: massInsertResult })
        if (massInsertResult.status != 200) await mySQLWrapper.rollback(conn)

        const insertBeneMapping = await this.insertBeneMapping(fields, conn)
        log.logger({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'insertBeneMapping-response', fields: insertBeneMapping })
        if (insertBeneMapping.status != 200) await mySQLWrapper.rollback(conn)
      }
      if (checkBeneOrBeneMappingExists.insert_bene_mapping.length > 0) {
        fields.beneList = checkBeneOrBeneMappingExists.insert_bene_mapping
        const insertBeneMapping = await this.insertBeneMapping(fields, conn)
        log.logger({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'insertBeneMapping-response', fields: insertBeneMapping })
        if (insertBeneMapping.status != 200) await mySQLWrapper.rollback(conn)
      }
      if (checkBeneOrBeneMappingExists.update_bene.length > 0) {
        fields.beneList = checkBeneOrBeneMappingExists.update_bene.map(i => i.ma_beneficiaries_id)
        const updateBene = await this.updateBene(fields, conn, 'Y')
        log.logger({ pagename: path.basename(__filename), action: 'updateBene', type: 'updateBene-respone', fields: updateBene })
        if (updateBene.status != 200) await mySQLWrapper.rollback(conn)
      }
      if (checkBeneOrBeneMappingExists.update_bene_mapping.length > 0) {
        fields.beneList = checkBeneOrBeneMappingExists.update_bene_mapping.map(i => i.ma_beneficiary_bank_mapping_id)
        const updateMapping = await this.updateMapping(fields, conn, 'S')
        log.logger({ pagename: path.basename(__filename), action: 'partialUpdate', type: 'updateMapping-respone', fields: updateMapping })
        if (updateMapping.status != 200) await mySQLWrapper.rollback(conn)
      }
      await mySQLWrapper.commit(conn)
      */

      // const massInsertResult = await this.massInsertBeneficiaries(fields, connection)
      // log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'massInsertResult', fields: massInsertResult })
      /* if (massInsertResult.status != 200) {
        return massInsertResult
      } */

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, beneficiary_list: newBeneList, ma_bank_on_boarding_id: fields.bankHandler.BANK_ON_BOARDING_ID, mobile_number: customerDataResult[0].mobile_number }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'catchError', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {Object} param
   * @param {String} param.uic
   * @param {Number} param.ma_bank_on_boarding_id
   * @param {mySQLWrapper} param.connection
   * @returns
   */
  static async fetchBeneficiariesFromDB ({ uic, ma_bank_on_boarding_id, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: { uic, ma_bank_on_boarding_id } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: { uic, ma_bank_on_boarding_id } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const beneficiariesQuery = `SELECT mb.account_number,mb.ifsc_code,mbbm.ma_beneficiary_bank_mapping_id,mb.ma_beneficiaries_id FROM ma_beneficiaries mb INNER JOIN ma_beneficiary_bank_mapping mbbm ON mbbm.ma_beneficiaries_id = mb.ma_beneficiaries_id WHERE mb.uic='${uic}' AND mb.beneficiary_status = 'Y' AND mbbm.bank_status = 'S' AND  mbbm.ma_bank_on_boarding_id='${ma_bank_on_boarding_id}'`

      const beneficiaries = await this.rawQuery(beneficiariesQuery, conn)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, beneficiaries }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'catchError', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {Object} param
   * @param {Number} param.ma_bank_on_boarding_id
   * @param {String} param.method
   * @param {mySQLWrapper} param.connection
   * @returns
   */
  static async getMethodFlag ({ ma_bank_on_boarding_id, method, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const bankPreValidateRequire = await bankOnBoardDetails.getBanksDetails(
        null,
        {
          ma_bank_on_boarding_id: ma_bank_on_boarding_id,
          entity_type: method
        },
        conn
      )

      if (bankPreValidateRequire.status == 400) {
        return { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, customFields: [] }
      }

      if (bankPreValidateRequire.status == 200 && bankPreValidateRequire.data[0].otp_required == 'YES') {
        return true
      }

      if (bankPreValidateRequire.status == 200 && bankPreValidateRequire.data[0].otp_required == 'NO') {
        return false
      }
    } catch (error) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {Object} param
   * @param {String} param.uic
   */
  static async fetchExistingBeneficiary ({ uic, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: { uic } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficiaries', type: 'request', fields: { uic } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const beneficiariesQuery = `SELECT account_number,ifsc_code FROM ma_beneficiaries WHERE ma_beneficiaries.uic='${uic}' AND beneficiary_status = 'Y'`

      const existingBeneficiaries = await this.rawQuery(beneficiariesQuery, conn)

      log.logger({ pagename: require('path').basename(__filename), action: 'fetchExistingBeneficiary', type: 'beneficiaries', fields: existingBeneficiaries })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'fetchExistingBeneficiary', type: 'beneficiaries', fields: existingBeneficiaries })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, existingBeneficiaries }
    } catch (error) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   * MA PENNY DROP SERVICES
   * @param {*} _
   * @param {*} fields
   * @returns
   */
  static async beneficiaryVerification (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'request', fields: { _, fields } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'request', fields: { _, fields } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (_ && _.ip_address) {
        fields.ip_address = _.ip_address
      }
      /* ma_user_id & userid */
      const { ma_user_id, userid } = util[process.env.NODE_ENV || 'development'].beneficiaryVertification
      if (fields.ma_user_id != ma_user_id) {
        return { status: 400, respcode: 1111, message: 'Fail: Invalid ma_user_id provided :ma_user_id' }
      }

      if (fields.userid != userid) {
        return { status: 400, respcode: 1111, message: 'Fail: Invalid userid provided :userid' }
      }
      // mobile number
      if (!fields.mobile_number.match(/^[0-9]{10,15}$/)) {
        return { status: 400, respcode: 1111, message: 'Fail: Invalid mobile number provided :mobile_number' }
      }

      // ma_bank_verification_id validation
      if (!fields.ma_bank_verification_id.toString().match(/^[0-9]{1,2}$/)) {
        return { status: 400, respcode: 3001, message: errorMsg.responseCode[3002] + ' :ma_bank_verification_id' }
      }
      if (!validator.definedVal(fields.ma_bank_verification_id)) {
        return { status: 400, respcode: 3001, message: errorMsg.responseCode[3002] + ' :ma_bank_verification_id' }
      }
      // IFSC code verification
      if (!validator.definedVal(fields.ifsc_code)) {
        return { status: 400, respcode: 3001, message: errorMsg.responseCode[3001] + ':ifsc_code' }
      }

      if (!fields.ifsc_code.match(/^[A-Z]{4}0[A-Z0-9]{6}$/)) {
        return { status: 400, respcode: 3001, message: errorMsg.responseCode[3001] + ':ifsc_code' }
      }

      // validate mobile no
      const validate = validator.validateMobile(fields.mobile_number, fields.countrycode || 'IN')
      log.logger({ pagename: require('path').basename(__filename), action: 'customerLoginV2', type: 'response', fields: validate })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'customerLoginV2', type: 'response', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1033, message: 'Fail: Invalid mobile number provided :mobile_number' }
      }
      /* fetch uic & check remitter  */
      const checkRemitterStatusQuery = `SELECT remitter_id, mobile_number,uic FROM ma_customer_details WHERE mobile_number = '${validate.number}' AND customer_status = 'Y'`
      const checkRemitterStatus = await this.rawQuery(checkRemitterStatusQuery, connection)
      if (checkRemitterStatus.length == 0) {
        // return error - customer not found
        return {
          status: 400,
          respcode: 1115,
          message: errorMsg.responseCode[1115]
        }
      }

      /* create session */
      const routingObj = new Routing(null, connection)

      const bankHandler = await routingObj.getHandler(null, { transferId: null, orderId: null, transferMode: 'ADDCUSTOMER', ma_user_id: fields.ma_user_id, mobile_number: validate.number })
      // console.log('Handler values', bankHandler)
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'bankHandler', fields: bankHandler })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'bankHandler', fields: bankHandler })

      if (bankHandler.status != 200) {
        return bankHandler
      }

      /** Create SessionData */
      const sessionData = {
        ma_bank_on_boarding_id: bankHandler.handler.BANK_ON_BOARDING_ID,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        uic: checkRemitterStatus[0].uic,
        mobile_number: validate.number,
        bank_priority_json: JSON.stringify(bankHandler.priorityData),
        bene_bank_priority_json: JSON.stringify(bankHandler.beneVerify)
      }
      /* set sessionRQ next request */
      fields.sessionRQ = await dmtSession.insertData(connection, sessionData)
      /* fetch bank master id */
      /* set ifsc code  for next request */
      fields.ifsc = fields.ifsc_code
      const fetchBankIDResult = await bankBranch.getBranchByIfsc(_, fields)
      if (fetchBankIDResult.status != 200) {
        return fetchBankIDResult
      }
      /* set ma_bank_master_id next request */
      fields.ma_bank_master_id = fetchBankIDResult.ma_bank_master_id
      /* generate random order ID */
      const random = await otp.generateOTP()
      const timestamp = await otp.getExpiry('')
      /* set aggregator_order_id next request */
      fields.aggregator_order_id = `MATM${random}${timestamp}`
      /* set uic next request */
      fields.uic = checkRemitterStatus[0].uic
      fields.bank_verify = true
      /* NEW CHANGES : CHECK BENEFICIARY EXISTS */
      // Check beneficiary exist in master list
      const beneficiaryDetails = await this.checkBeneficiary(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'beneficiaryDetails', fields: beneficiaryDetails })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'beneficiaryDetails', fields: beneficiaryDetails })

      if (beneficiaryDetails.status == 400) {
        return beneficiaryDetails
      }

      /* AIRTEL CASE MOBILE MANDATORY : CHECK IF BENE MOBILE IS NULL  */
      if (beneficiaryDetails.data.length > 0 && beneficiaryDetails.data[0].beneficiary_status == 'N') {
        if (!beneficiaryDetails.data[0].ben_mobile_number || beneficiaryDetails.data[0].ben_mobile_number == '0' || beneficiaryDetails.data[0].ben_mobile_number == 'null') {
          fields.ben_mobile_number = fields.mobile_number
        }
      }
      /* BENE BLACKLIST CHANGES */
      fields.skipBeneficiaryBlacklistCheck = true
      /* BENE VALIDATION */
      fields.isBeneValidationBankDifferent = true
      if (beneficiaryDetails.data.length > 0 && beneficiaryDetails.data[0].beneficiary_status !== 'N') {
        /* set ma_beneficiaries_id next request */
        fields.ma_beneficiaries_id = beneficiaryDetails.data[0].ma_beneficiaries_id
        const response = await this.verifyExistingBeneficiary(_, fields)
        log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'verifyExistingBeneficiary', fields: response })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'verifyExistingBeneficiary', fields: response })
        return response
      }
      /* NEW CHANGES : CHECK BENEFICIARY EXISTS END */
      const beneVerificationResult = await this.addBeneficiaryV2(_, fields)
      /* ovveride the message */
      beneVerificationResult.message = beneVerificationResult.verification_message || beneVerificationResult.message
      return beneVerificationResult
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'response', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'beneficiaryVerification', type: 'response', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async isAutoDeleteBenficiariesAllowed ({ bankHandler, connection }) {
    return await this.getMethodFlag({
      ma_bank_on_boarding_id: bankHandler.BANK_ON_BOARDING_ID,
      method: 'AUTO_SYNC_DELETE_BENEFICIARIES',
      connection
    })
  }

  static async syncBeneDelete (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneDelete', type: 'request', fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneDelete', type: 'request', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const customerDataQuery = `SELECT mobile_number FROM ma_customer_details WHERE uic='${fields.uic}' AND customer_status='Y' LIMIT 1`
      const customerData = await this.rawQuery(customerDataQuery, conn)

      if (customerData.length == 0) {
        // 1033
        return { status: 400, message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']', respcode: 1115 }
      }

      fields.mobile_number = customerData[0].mobile_number
      const isAutoDeleteBeneAllowedResult = await this.isAutoDeleteBenficiariesAllowed({
        bankHandler: fields.bankHandler,
        conn
      })

      log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'isAutoDeleteBeneAllowedResult', fields: isAutoDeleteBeneAllowedResult })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'isAutoDeleteBeneAllowedResult', fields: isAutoDeleteBeneAllowedResult })

      if (isAutoDeleteBeneAllowedResult.status == 400) {
        return isAutoDeleteBeneAllowedResult
      }

      if (!isAutoDeleteBeneAllowedResult || (isAutoDeleteBeneAllowedResult.status == 200 && isAutoDeleteBeneAllowedResult.respcode == 1002)) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }

      const beneListResult = await this.fetchBeneficiariesFromBank(fields, connection)
      log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'beneListResult', fields: beneListResult })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'beneListResult', fields: beneListResult })
      if (beneListResult.status != 200) {
        return beneListResult
      }

      const beneficiariesResult = await this.fetchBeneficiariesFromDB({
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.bankHandler.BANK_ON_BOARDING_ID,
        connection
      })

      log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'beneficiariesResult', fields: beneficiariesResult })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'beneficiariesResult', fields: beneficiariesResult })

      if (beneficiariesResult.status != 200) {
        return beneficiariesResult
      }

      const beneficiariesMap = {}

      // dictionary of beneficiaries for checking existing beneficiaries at Bank side
      beneListResult.beneList.forEach(bene => {
        bene.ifsc_code = bene.ifscCode.toLowerCase()

        // check if account number and ifsc combination already exist or not in the dict
        if (!beneficiariesMap[`${bene.accountNumber}-${bene.ifsc_code}`]) {
          beneficiariesMap[`${bene.accountNumber}-${bene.ifsc_code}`] = bene
        }
      })

      // compare current beneficiaries with the beneficiaries in DB
      // if not exist bank side but exist at Airpay, then insert
      const newBene = []
      beneficiariesResult.beneficiaries.forEach(bene => {
        bene.ifsc_code = bene.ifsc_code.toLowerCase()
        // account number&ifscCode doesn't exits then insert the new bene
        if (!beneficiariesMap[`${bene.account_number}-${bene.ifsc_code}`]) {
          newBene.push(bene.ma_beneficiaries_id)
        }
      })

      log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'Beneficiaries to delete', fields: newBene })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'Beneficiaries to delete', fields: newBene })

      if (newBene.length == 0) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }

      fields.beneList = newBene
      fields.ma_bank_on_boarding_id = fields.bankHandler.BANK_ON_BOARDING_ID
      const massDelete = await this.massDeleteBene(fields, conn)
      log.logger({ pagename: path.basename(__filename), type: 'massDeleteBene', action: 'syncBeneDelete', fields: massDelete })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'massDeleteBene', action: 'syncBeneDelete', fields: massDelete })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'error', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'syncBeneDelete', type: 'error', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   *
   * @param {Object} fields
   * @param {Promise<mySQLWrapper.getConnectionFromPool()>} connection
   * @returns
   */
  static async massDeleteBene (fields, connection) {
    log.logger({ pagename: path.basename(__filename), type: 'request', action: 'massDeleteBene', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'massDeleteBene', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const deleteBeneRequest = []
      for (let index = 0; index < fields.beneList.length; index++) {
        const ma_beneficiaries_id = fields.beneList[index]
        deleteBeneRequest.push(this.deleteBeneFromDB({
          ma_beneficiaries_id,
          ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
        },
        connection
        ))
      }

      const deleteBeneResponse = await Promise.all(deleteBeneRequest)
      log.logger({ pagename: require('path').basename(__filename), action: 'massDeleteBene', type: 'deleteBeneResponse', fields: deleteBeneResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'massDeleteBene', type: 'deleteBeneResponse', fields: deleteBeneResponse })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'massDeleteBene', type: 'catchError', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'massDeleteBene', type: 'catchError', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async verifyExistingBeneficiary (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'request', fields: { _, fields } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'request', fields: { _, fields } })

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    if (!validator.definedVal(fields.aggregator_order_id)) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid order id' }
    }

    if (!validator.definedVal(fields.ma_beneficiaries_id)) {
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Invalid beneficiary id' }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()

    /* BENE BLACKLIST CHANGES */
    if (!fields.skipBeneficiaryBlacklistCheck) {
      const isBeneficaryBlackListed = await this.isBeneficaryBlackListed({
        ma_beneficiaries_id: fields.ma_beneficiaries_id,
        connection
      })

      if (isBeneficaryBlackListed.status != 200) return isBeneficaryBlackListed
    }

    try {
      /* RISK MANAGEMENT Changes : NEW CHANGES */
      if (_ && _.ip_address) {
        fields.ip_address = _.ip_address
      }

      // Call routing logic and add beneficiary at bank
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'verifyExistingBeneficiary', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'verifyExistingBeneficiary', fields: sessionData })

      if (sessionData.status == 400) {
        return sessionData
      }
      /* NEW CHANGE : USE PRIORITY BANK FOR BENE VERIFICATION */
      let handler = sessionData.handler
      fields.ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID

      /* MA BENE VALIDATION/PENNY DROP CASE HANDLED  */
      if (fields.isBeneValidationBankDifferent) {
        if (sessionData.data.bene_bank_priority_json) {
          JSON.parse(sessionData.data.bene_bank_priority_json).forEach(bank => {
            if (bank.ma_bank_on_boarding_id == fields.ma_bank_verification_id) {
              handler = {
                BANK_NAME: bank.bank_name,
                BANK_ON_BOARDING_ID: bank.ma_bank_on_boarding_id
              }

              fields.ma_bank_on_boarding_id = bank.ma_bank_on_boarding_id
            }
          })
        }

        if (!handler.BANK_ON_BOARDING_ID) {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Beneficiary Verification failed!' }
        }
      }

      const beneficiaryDetailData = await bankOnBoardDetails.getBeneficiaryDetails(null, { beneficiaryId: fields.ma_beneficiaries_id, ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id }, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'beneficiaryDetailData', fields: beneficiaryDetailData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'beneficiaryDetailData', fields: beneficiaryDetailData })

      if (beneficiaryDetailData.status != 200) {
        return beneficiaryDetailData
      }

      fields.beneficiaryDetailData = beneficiaryDetailData.data[0]
      console.log('fields.beneficiaryDetailData>>', fields.beneficiaryDetailData)

      if (fields.beneficiaryDetailData.bene_verify_status === 'P') {
        const verificationMessage = 'Verification Pending from bank side!'
        const bank_bene_name = null
        const bank_account_number = 0
        const bene_verify_status = 'P'

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] + ': Beneficiary Already verified', ma_bene_verification_id: fields.beneficiaryDetailData.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status }
      }

      if (fields.beneficiaryDetailData.bene_verify_status === 'S') {
        let verificationMessage = ''
        let bank_bene_name = null
        let bank_account_number = 0
        let bene_verify_status = 'U'

        if (fields.beneficiaryDetailData.bank_benename != null && fields.beneficiaryDetailData != '') {
          verificationMessage = 'Bank Verified'
          bank_bene_name = fields.beneficiaryDetailData.bank_benename
          bank_account_number = fields.beneficiaryDetailData.accountnumber
          bene_verify_status = 'S'
        } else {
          verificationMessage = 'Bank Verified'
          bank_bene_name = fields.beneficiaryDetailData.beneficiary_name
          bank_account_number = fields.beneficiaryDetailData.accountnumber
          bene_verify_status = 'S'
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] + ': Beneficiary Already verified', ma_bene_verification_id: fields.beneficiaryDetailData.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status }
      }

      const bankOnboardSql = `SELECT bene_verification as bank_bene_verification,bene_charges FROM ma_bank_on_boarding where onboarding_status='Y' and ma_bank_on_boarding_id=${fields.ma_bank_on_boarding_id} limit 1 `
      const bankOnboardData = await this.rawQuery(bankOnboardSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'bankOnboardData', fields: bankOnboardData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'bankOnboardData', fields: bankOnboardData })

      if (bankOnboardData.length === 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1192] }
      }

      fields.bank_bene_verification = bankOnboardData[0].bank_bene_verification
      fields.bene_charges = bankOnboardData[0].bene_charges

      const beneVerifySql = `SELECT boardbank.ma_bank_on_boarding_id,boardbank.bank_name as BANK_NAME FROM ma_user_on_boarding_bank_mapping as mapp 
      JOIN ma_bank_on_boarding as boardbank on boardbank.ma_bank_on_boarding_id = mapp.ma_bank_on_boarding_id
      where mapp.ma_user_id='${fields.ma_user_id}' and mapp.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}  and mapp.onBoarding_status='Y' AND boardbank.onboarding_status='Y' AND mapp.bene_verification = 'Y' AND boardbank.bene_verification = 'Y' limit 1 `
      const beneficiaryVerifyData = await this.rawQuery(beneVerifySql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'beneficiaryVerifyData', fields: beneficiaryVerifyData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'beneficiaryVerifyData', fields: beneficiaryVerifyData })

      console.log('beneVerifySql', beneVerifySql)
      if (beneficiaryVerifyData.length > 0) {
        fields.isBeneVerifyRequird = 'Y'
        fields.verifyBankOnBoardingId = beneficiaryVerifyData[0].ma_bank_on_boarding_id
        fields.bankName = beneficiaryVerifyData[0].BANK_NAME
      } else {
        fields.isBeneVerifyRequird = 'N'
        fields.verifyBankOnBoardingId = 0
      }
      /* Check if the customer is register with partner bank */
      const isRegisterData = await customBankMap.isCustomerRegister(fields.uic, fields.ma_bank_on_boarding_id, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'isCustomerRegister', type: 'isRegisterData', fields: isRegisterData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isCustomerRegister', type: 'isRegisterData', fields: isRegisterData })
      if (isRegisterData.status != 200) {
        return isRegisterData
      }

      if (isRegisterData.respcode === 1002) {
        return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077], uic: fields.uic }
      }

      if (fields.bank_bene_verification === 'Y' && fields.isBeneVerifyRequird === 'Y') {
        if (fields.ma_bank_on_boarding_id == 10) {
          const customerController = require('../customer/customerController')
          // session token api
          const sessionTokenResult = await customerController.airtelGenerateSessionTokenApi(fields)
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'sessionTokenResult', fields: sessionTokenResult })
          if (sessionTokenResult.status != 200) return sessionTokenResult
          const statusResultApi = await customerController.airtelGetCustomerApi(fields, sessionTokenResult.data.token)
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'statusResultApi', fields: statusResultApi })
          if (statusResultApi.status != 200) return statusResultApi
          if (statusResultApi.data.meta.status == 0 && statusResultApi.data.meta.code == '000') {
            // transaction verify otp
            const request_number = await this.generateClientId()
            const obj = {
              otp_type: 5,
              amount: '1',
              mobile_number: fields.mobile_number,
              otp_pin: fields.otp_pin,
              otp_id: fields.otp_request_id,
              requestNumber: request_number
            }
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'verifyOTPReq', fields: obj })
            const Payment = require('./../bankHandler/payment')
            const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
            console.time('Timer_BANK_INIT_TRANSACTION')
            const resRemit = await payment.requestToBank('VERIFY_OTP', obj)
            console.timeEnd('Timer_BANK_INIT_TRANSACTION')
            log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'verifyOTPRes', fields: resRemit })
            if (resRemit.status != 200) {
              return resRemit
            }
            fields.token = resRemit.token
          } else {
            return { status: 400, message: statusResultApi.data.meta.description || 'Fail: API Error', respcode: 1001, action_code: 1001 }
          }
        }
        /**
         * Check balance for Retailer
         */
        const balanceController = require('../balance/balanceController')
        const availableBalance = await balanceController.getWalletBalancesDirect(_, {
          ma_user_id: fields.ma_user_id,
          ma_status: 'ACTUAL',
          balance_flag: 'SUMMARY',
          connection: connection
        })

        const beneVerifyIncentiveCtrl = require('../incentive/beneVerifyIncentiveController')

        let customer_charges = 0
        fields.ma_bank_on_boarding_id = handler.BANK_ON_BOARDING_ID
        const globalValues = await beneVerifyIncentiveCtrl.globalValues(fields, connection)
        log.logger({ pagename: 'transfersDistributionController.js', action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })
        // lokiLogger.info({ pagename: 'transfersDistributionController.js', action: 'incentiveDistribution', type: 'response - globalValues', fields: globalValues })

        if (typeof (globalValues) === 'boolean') {
          return { status: 400, respcode: 1112, message: errorMsg.responseCode[1112] }
        }

        const globalSettings = Object.keys(globalValues).length > 0
        console.log('globalSetting ==============', globalValues, 'here', globalValues.customer_charges)
        if (globalSettings) {
          customer_charges = globalValues.customer_charges
        }
        fields.customer_charges = customer_charges
        console.log('cust charges ==============', customer_charges)

        // Points rate
        let pointsFactor = 1
        const pointsRateController = require('../pointsRate/pointsRateController')
        const pointsFactorData = await pointsRateController.getGlobalPointsRate(_, { connection: connection })
        if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
          pointsFactor = pointsFactorData.points_value
        }

        const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid} limit 1`
        console.log(userSQL)
        const integratedMer = await this.rawQuery(userSQL, connection)

        if (customer_charges > 0 && availableBalance.amount < customer_charges && !(integratedMer.length > 0)) {
          // ----- SEND TRANSFER FAILURE MESSAGE
          return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
        }

        const commissionVal = 0

        // Create transaction with I status
        const transactionController = require('../transaction/transactionController')
        if (customer_charges > 0) {
          const transferFields = {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            amount: customer_charges, // To do  check amount from
            bank_charges: fields.bene_charges, // To do ch This fiedl from ma_bank_on_boarding bene_charges
            aggregator_order_id: fields.aggregator_order_id, // TO do Need Ask to generate From Frontend
            transaction_id: fields.aggregator_order_id,
            commission_amount: commissionVal,
            points_factor: pointsFactor,
            transaction_status: 'P',
            transaction_type: '21', // BENEFICICARY VALIDATION
            mobile_number: fields.mobile_number,
            remarks: 'Bank Beneficiary verification Charges ' + customer_charges, // Added bank charges for reference
            ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
          }

          const transaction = await transactionController.createTransaction(_, transferFields, connection)
          if (transaction.status === 400 || transaction.status == 401) {
            const remarksUpdate = transferFields.remarks + '::' + transaction.message
            // const UpdateRes = await this.rawQuery(`UPDATE ma_transaction_master set transaction_status='F',remarks='${remarksUpdate}' where aggregator_order_id = '${fields.aggregator_order_id}' AND transaction_type = 21 `, connection)

            /* RISK MANAGEMENT Changes */
            const data = {
              remarks: remarksUpdate,
              transaction_status: 'F'
            }

            const updateTransactionResult = await transactionController.updateWhereData(connection, {
              data,
              id: fields.aggregator_order_id,
              where: 'aggregator_order_id'
            })

            log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

            return transaction
          }

          fields.ma_transaction_master_id = transaction.transaction_id

          console.time('Timer_CreateTransactionDetails')
          transferFields.customer_name = fields.beneficiaryDetailData.remitter_name || 'NA'
          transferFields.bank_name = fields.beneficiaryDetailData.bank ? fields.beneficiaryDetailData.bank : 'NA'
          transferFields.ma_transaction_master_id = transaction.transaction_id
          transferFields.utility_name = 'DMT'
          transferFields.customer_mobile = fields.mobile_number || 'NA'
          transferFields.provider_name = handler.BANK_NAME || 'NA'
          transferFields.receiver_account_number = fields.beneficiaryDetailData.accountnumber || 'NA'
          transferFields.receiver_name = fields.beneficiary_name || 'NA'

          const transactionDetails = await transactionController.createTransactionDetails(_, transferFields, connection)
          console.log('transactionDetailsResponse >>', transactionDetails)
          if (transactionDetails.status === 400) {
            return transactionDetails
          }
          fields.transaction_details_id = transactionDetails.transaction_details_id
          console.timeEnd('Timer_CreateTransactionDetails')
        } else {
          fields.ma_transaction_master_id = 0
        }

        if (fields.ma_transaction_master_id > 0) {
          /* RISK MANAGEMENT Changes : NEW CHANGES */
          const requestParams = {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            aggregator_order_id: fields.aggregator_order_id,
            amount: customer_charges,
            transaction_status: 'P',
            transaction_type: '21', // BENEFICICARY VALIDATION
            customer_name: fields.beneficiaryDetailData.remitter_name || '',
            customer_phone: fields.mobile_number,
            account_number: fields.beneficiaryDetailData.accountnumber || '',
            ben_mobile_number: fields.beneficiaryDetailData.bene_mobile_number || '',
            beneficiary_name: fields.beneficiary_name || '',
            bank_name: fields.beneficiaryDetailData.bank ? fields.beneficiaryDetailData.bank : '',
            ip_address: fields.ip_address || ''
          }
          const checkRiskAnalysisResp = await RiskManagementController.checkTransactionRiskAnalysis({ requestParams, connection })
          if (checkRiskAnalysisResp.status != 200) return checkRiskAnalysisResp
        }

        // LEDGER ENTRIES
        if (fields.ma_transaction_master_id > 0) {
          await mySQLWrapper.beginTransaction(connection)
          const debitLedgerEntries = await this.debitLedgerEntries(_, fields, connection)
          if (debitLedgerEntries.status != 200) {
            await mySQLWrapper.rollback(connection)
            return debitLedgerEntries
          }
          console.log('Debit doneeeeeeeeeeeeeeeeeeeee', debitLedgerEntries)
          await mySQLWrapper.commit(connection)
        }

        // Verifcation charges applicable in this block success or failure
        const beneVerificationCtrl = require('./beneVerificationController')

        const checkBeneVerifyData = await beneVerificationCtrl.getOrCreateBeneVerify(_, fields, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'checkBeneVerifyData', fields: checkBeneVerifyData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'checkBeneVerifyData', fields: checkBeneVerifyData })

        if (checkBeneVerifyData.status != 200) {
          return checkBeneVerifyData
        }

        const isallreadyverified = checkBeneVerifyData.details.allreadyverified
        fields.ma_bene_verification_id = checkBeneVerifyData.details.ma_bene_verification_id

        let walletDeduction = true
        let bankValidatedSuccess = false
        let verificationMessage = ''
        let bank_bene_name = null
        let bank_account_number = 0
        let bene_verify_status = 'U'
        let bankFailReason = errorMsg.responseCode[1028] + ': Beneficiary validation failed'
        if (isallreadyverified === false) {
          const bankBeneVerifyRes = await beneVerificationCtrl.beneVerificationWithBank(_, fields, handler, connection, fields.sessionRQ)
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'beneVerificationWithBank', fields: bankBeneVerifyRes })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyExistingBeneficiary', type: 'beneVerificationWithBank', fields: bankBeneVerifyRes })

          bankFailReason = bankBeneVerifyRes.message
          if (bankBeneVerifyRes.status === 200) {
            if (bankBeneVerifyRes.apiFailed == true) {
              walletDeduction = false
            }
            if (bankBeneVerifyRes.bene_verify_status == 'S' || bankBeneVerifyRes.bene_verify_status == 'P') {
              bankValidatedSuccess = true
              if (bankBeneVerifyRes.bene_verify_status == 'S') {
                if (bankBeneVerifyRes.bank_benename != null && bankBeneVerifyRes.bank_benename != '') {
                  verificationMessage = 'Bank Verified'
                  bank_bene_name = bankBeneVerifyRes.bank_benename
                  bank_account_number = checkBeneVerifyData.details.account_number
                  bene_verify_status = 'S'
                } else {
                  verificationMessage = 'Bank Verified & Beneficiary name empty'
                  bank_bene_name = fields.beneficiary_name
                  bank_account_number = checkBeneVerifyData.details.account_number
                  bene_verify_status = 'S'
                }
              } else if (bankBeneVerifyRes.bene_verify_status == 'P') {
                verificationMessage = 'Verification Pending from bank side!'
                bank_bene_name = null
                bank_account_number = 0
                bene_verify_status = 'P'
              }
            } else if (bankBeneVerifyRes.bene_verify_status == 'F') {
              walletDeduction = false
            }
          } else {
            walletDeduction = false
          }
        } else {
          if (checkBeneVerifyData.details.bene_verify_status === 'P') {
            verificationMessage = 'Verification Pending from bank side!'
            bank_bene_name = null
            bank_account_number = 0
            bene_verify_status = 'P'
          } else if (checkBeneVerifyData.details.bene_verify_status === 'S') {
            if (checkBeneVerifyData.details.bank_benename != null && checkBeneVerifyData.details.bank_benename != '') {
              verificationMessage = 'Bank Verified'
              bank_bene_name = checkBeneVerifyData.details.bank_benename
              bank_account_number = checkBeneVerifyData.details.account_number
              bene_verify_status = 'S'
            } else {
              verificationMessage = 'Bank Verified & Beneficiary name empty'
              bank_bene_name = fields.beneficiaryDetailData.beneficiary_name
              bank_account_number = fields.beneficiaryDetailData.accountnumber
              bene_verify_status = 'S'
            }
          }
          bankValidatedSuccess = true
        }

        console.log('walletDeduction>>', walletDeduction)
        console.log('bankValidatedSuccess>>', bankValidatedSuccess)

        if (walletDeduction === false && fields.ma_transaction_master_id > 0) {
          // revert deudction entries of ledgers
          // LEDGER ENTRIES Revert
          const data = { transaction_status: 'F' }
          // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
          /* RISK MANAGEMENT Changes */
          const updateTransactionResult = await transactionController.updateWhereData(connection, {
            data,
            id: fields.ma_transaction_master_id,
            where: 'ma_transaction_master_id'
          })

          log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
          console.log('WalletREVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
          await mySQLWrapper.beginTransaction(connection)
          const reverseDebitLedger = await this.reverseDebitLedger(_, fields, connection)
          console.log('reverseDebitLedger>>>', reverseDebitLedger)
          await mySQLWrapper.commit(connection)
          if (reverseDebitLedger.status != 200) {
            return reverseDebitLedger
          }
        }

        if (bankValidatedSuccess === true) {
          // incentive distribution function call
          if (fields.ma_transaction_master_id > 0) {
            await mySQLWrapper.beginTransaction(connection)
            console.log('INCENTIVEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV')
            const distribution = await beneVerifyIncentiveCtrl.incentiveDistribution(fields, connection)
            await mySQLWrapper.commit(connection)

            // Check if integrated merchant
            // const userSQL =  `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${fields.userid}`
            // const integratedMer = await this.rawQuery(userSQL, connection)
            if (integratedMer.length > 0) {
              // Post receipt
              var receiptData = {}
              receiptData.action = 'POSTRECEIPT'
              receiptData.aggregator_user_id = integratedMer[0].aggregator_user_id
              receiptData.aggregator_order_id = fields.aggregator_order_id
              receiptData.ma_user_id = fields.ma_user_id

              console.time('TIMER_BENE_POSTRECEIPT')
              const responsePostReceipt = await integrated.index(receiptData, connection)
              console.log('integrated returned this', responsePostReceipt)
              console.timeEnd('TIMER_BENE_POSTRECEIPT')
            }

            if (distribution.status === 400) {
              return distribution
            }
          }

          const data = { ma_bene_verification_id: fields.ma_bene_verification_id }
          console.log('DataUpdate>>', data, '>>ma_beneficiaries_id', fields.ma_beneficiaries_id)
          this.TABLE_NAME = 'ma_beneficiaries'
          const updateRes = await this.updateWhere(connection, { data, id: fields.ma_beneficiaries_id, where: 'ma_beneficiaries_id' })

          // updateRes.affectedRows = 0 // for test reverse entries
          if (updateRes.affectedRows > 0) {
            if (fields.ma_transaction_master_id > 0) {
              const data = { transaction_status: 'S' }
              // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
              /* RISK MANAGEMENT Changes */
              const updateTransactionResult = await transactionController.updateWhereData(connection, {
                data,
                id: fields.ma_transaction_master_id,
                where: 'ma_transaction_master_id'
              })

              log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
            }

            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_bene_verification_id: fields.ma_bene_verification_id, verification_message: verificationMessage, bank_bene_name: bank_bene_name, bank_account_number: bank_account_number, bene_verify_status: bene_verify_status, ma_transaction_master_id: fields.ma_transaction_master_id }
          } else {
            if (fields.ma_transaction_master_id > 0) {
              const data = { transaction_status: 'F' }
              // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })
              /* RISK MANAGEMENT Changes */
              const updateTransactionResult = await transactionController.updateWhereData(connection, {
                data,
                id: fields.ma_transaction_master_id,
                where: 'ma_transaction_master_id'
              })

              log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })
              // Revert debit entries
              console.log('REVERSEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD')
              await mySQLWrapper.beginTransaction(connection)
              const reverseDebitLedger = await this.reverseDebitLedger('_', fields, connection)
              await mySQLWrapper.commit(connection)
              if (reverseDebitLedger.status != 200) {
                return reverseDebitLedger
              }
            }

            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Beneficiary Verification failed!' }
          }
        } else {
          return { status: 400, respcode: 1028, message: bankFailReason }
        }
      } else {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Bank verification is not available !' }
      }
    } catch (error) {
      console.log('verifyExistingBeneficiary', error)
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Verify beneficiary:' + error.message }
    } finally {
      connection.release()
    }
  }

  static async isInterbankAllowed ({ bankHandler, connection, receiverBank }) {
    try {
      const regex = new RegExp(bankHandler.BANK_NAME, 'gi')
      if (!regex.test(receiverBank)) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, isAllowed: true }

      const flagResp = await this.getMethodFlag({
        ma_bank_on_boarding_id: bankHandler.BANK_ON_BOARDING_ID,
        method: 'INTERBANK_TRANSACTION',
        connection
      })

      if (flagResp.status == 400) return flagResp

      if ((flagResp.status == 200 && flagResp.respcode == 1002) || flagResp) return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, isAllowed: true }

      return { status: 400, message: `${errorMsg.responseCode[1028]}: Transfer of Funds to the Beneficiary Bank is currently unavailable !`, respcode: 1028 }
    } catch (error) {
      log.logger({ pagename: path.basename(__filename), action: 'isInterbankAllowed', type: 'error', fields: error })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'isInterbankAllowed', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async updateMapping (fields, connection, bank_status) {
    log.logger({ pagename: path.basename(__filename), type: 'request', action: 'updateMapping', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'updateMapping', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const bank_mapping_ids = `'${fields.beneList.join('\',\'')}'`
      const updateQuery = `UPDATE ma_beneficiary_bank_mapping SET bank_status = '${bank_status}' WHERE ma_beneficiary_bank_mapping_id IN (${bank_mapping_ids}) AND ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}`
      log.logger({ pagename: path.basename(__filename), type: 'updateQuery', action: 'updateMapping', fields: updateQuery })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'updateQuery', action: 'updateMapping', fields: updateQuery })

      const updateBene = await this.rawQuery(updateQuery, conn)
      log.logger({ pagename: path.basename(__filename), action: 'updateMapping', type: 'sql_response', fields: updateBene })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'updateMapping', type: 'sql_response', fields: updateBene })
      if (updateBene.affectedRows == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateMapping', type: 'catchError', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'updateMapping', type: 'catchError', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async checkBeneOrBeneMappingExists (fields, connection) {
    log.logger({ pagename: path.basename(__filename), type: 'request', action: 'checkBeneOrBeneMappingExists', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'checkBeneOrBeneMappingExists', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const { mobile_number, ma_bank_on_boarding_id } = fields
      const partialInsert = []
      const insert_bene = []
      const insert_bene_mapping = []
      const update_bene = []
      const update_bene_mapping = []

      const beneList = JSON.parse(Buffer.from(fields.beneList, 'base64').toString())
      log.logger({ pagename: path.basename(__filename), type: 'beneList', action: 'checkBeneOrBeneMappingExists', fields: beneList })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'beneList', action: 'checkBeneOrBeneMappingExists', fields: beneList })

      for (var i = 0; i < beneList.length; i++) {
        // const beneLists = `SELECT mb.beneficiary_status, mb.ma_beneficiaries_id,mbbm.ma_beneficiary_bank_mapping_id  ,mbbm.bank_status, mb.ifsc_code, mb.account_number FROM ma_beneficiaries mb INNER JOIN ma_beneficiary_bank_mapping mbbm ON mb.ma_beneficiaries_id=mbbm.ma_beneficiaries_id WHERE mb.account_number = '${beneList[i].accountNumber}' AND mb.ifsc_code = '${beneList[i].ifscCode}' and mb.mobile_number = '${mobile_number}'`
        const beneficiaryQuery = `SELECT mb.beneficiary_status, mb.ma_beneficiaries_id,mbbm.ma_beneficiary_bank_mapping_id ,mbbm.bank_status,mbbm.ma_bank_on_boarding_id , mb.ifsc_code as ifscCode, mb.account_number as accountNumber, ${beneList[i].beneficiaryId} as beneficiaryId FROM ma_beneficiaries mb LEFT JOIN ma_beneficiary_bank_mapping mbbm ON mb.ma_beneficiaries_id=mbbm.ma_beneficiaries_id AND mbbm.ma_bank_on_boarding_id = ${ma_bank_on_boarding_id} WHERE mb.account_number = '${beneList[i].accountNumber}' AND mb.ifsc_code = '${beneList[i].ifscCode}' AND mb.mobile_number = '${mobile_number}'`
        log.logger({ pagename: path.basename(__filename), type: 'request', action: 'checkBeneOrBeneMappingExists-Query', fields: beneficiaryQuery })
        // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'checkBeneOrBeneMappingExists-Query', fields: beneficiaryQuery })

        const response = await this.rawQuery(beneficiaryQuery, conn)
        log.logger({ pagename: path.basename(__filename), type: 'request', action: 'checkBeneOrBeneMappingExists-Response', fields: response })
        // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'checkBeneOrBeneMappingExists-Response', fields: response })
        if (response.length <= 0) insert_bene.push(beneList[i])
        partialInsert.push(...response)
        console.log(partialInsert)
      }

      if (partialInsert.length > 0) {
        partialInsert.forEach(i => {
          if ((i.bank_status == 'undefined' || i.bank_status == null) || (i.ma_beneficiary_bank_mapping_id == 'undefined' || i.ma_beneficiary_bank_mapping_id == null)) {
            insert_bene_mapping.push(i)
            return
          }
          if (i.bank_status == 'D' && i.beneficiary_status == 'D') {
            update_bene.push(i)
            update_bene_mapping.push(i)
            return
          }
          if (i.bank_status == 'D') {
            update_bene_mapping.push(i)
            return
          }
          if (i.beneficiary_status == 'D') {
            update_bene.push(i)
          }
          if (i.bank_status != 'Y' && i.beneficiary_status == 'N') {
            update_bene.push(i)
            update_bene_mapping.push(i)
          }
          if (i.beneficiary_status == 'N' && i.bank_status == 'undefined') {
            update_bene.push(i)
          }
        })
      }

      log.logger({ pagename: path.basename(__filename), type: 'response', action: 'checkBeneOrBeneMappingExists', fields: { update_bene, update_bene_mapping, insert_bene, insert_bene_mapping } })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'response', action: 'checkBeneOrBeneMappingExists', fields: { update_bene, update_bene_mapping, insert_bene, insert_bene_mapping } })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], update_bene, update_bene_mapping, insert_bene, insert_bene_mapping }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'err', method: 'checkBeneOrBeneMappingExists', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'err', method: 'checkBeneOrBeneMappingExists', fields: err })
    } finally {
      if (isSet) conn.release()
    }
  }

  static async updateBene (fields, connection, status) {
    log.logger({ pagename: path.basename(__filename), type: 'request', action: 'updateBene', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'updateBene', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const bene_id = `'${fields.beneList.join('\',\'')}'`
      const updateQuery = `UPDATE ma_beneficiaries set beneficiary_status = '${status}' WHERE ma_beneficiaries_id IN (${bene_id}) and ma_user_id = '${fields.ma_user_id}' and userid = '${fields.userid}'`
      const updateQueryRes = await this.rawQuery(updateQuery, conn)
      log.logger({ pagename: path.basename(__filename), type: 'response', action: 'updateBene', fields: updateQueryRes })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'response', action: 'updateBene', fields: updateQueryRes })
      if (updateQueryRes.affectedRows == 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), type: 'err', action: 'updateBene', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), type: 'err', action: 'updateBene', fields: err })
    } finally {
      if (isSet) conn.release()
    }
  }

  static async insertBene (fields, connection) {
    log.logger({ pagename: path.basename(__filename), type: 'request', action: 'insertBene', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'insertBene', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const bankIdsQuery = `SELECT ma_bank_master_id,ifsc FROM ma_bank_branch WHERE ifsc IN ('${fields.IfscCodes.join('\',\'')}')`

      log.logger({ pagename: path.basename(__filename), type: 'request', action: 'insertBene', fields: bankIdsQuery })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'request', action: 'insertBene', fields: bankIdsQuery })
      const bankIds = await this.rawQuery(bankIdsQuery, conn)
      log.logger({ pagename: path.basename(__filename), type: 'bankIds', action: 'bankIdsQuery', fields: bankIds })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'bankIds', action: 'bankIdsQuery', fields: bankIds })

      const isfcBankIdMapping = {}

      bankIds.forEach(bankId => {
        isfcBankIdMapping[bankId.ifsc] = bankId.ma_bank_master_id
      })

      const beneficiaries = []

      /* fetch existing beneficiaries */

      const existingBeneficiariesResult = await this.fetchExistingBeneficiary({ uic: fields.uic, connection: conn })
      log.logger({ pagename: path.basename(__filename), type: 'existingBeneficiariesResult', action: 'bankIdsQuery', fields: existingBeneficiariesResult })
      // lokiLogger.info({ pagename: path.basename(__filename), type: 'existingBeneficiariesResult', action: 'bankIdsQuery', fields: existingBeneficiariesResult })

      if (existingBeneficiariesResult.status == 400) {
        return existingBeneficiariesResult
      }

      const existingBeneficiariesMapping = {}
      existingBeneficiariesResult.existingBeneficiaries.forEach((beneficiary) => {
        if (!existingBeneficiariesMapping[`${beneficiary.account_number}-${beneficiary.ifsc_code}`]) {
          existingBeneficiariesMapping[`${beneficiary.account_number}-${beneficiary.ifsc_code}`] = beneficiary
        }
      })

      /* insert only new Beneficiaries */

      const insertUniqueBene = []

      fields.beneList.forEach(beneficiary => {
        if (!existingBeneficiariesMapping[`${beneficiary.accountNumber}-${beneficiary.ifscCode}`]) {
          insertUniqueBene.push(beneficiary)
        }
      })

      const beneListLen = insertUniqueBene.length
      for (let index = 0; index < beneListLen; index++) {
        const beneficiary = insertUniqueBene[index]
        beneficiaries.push(`('${beneficiary.accountNumber}','${beneficiary.beneficiaryName}','${isfcBankIdMapping[beneficiary.ifscCode]}','${beneficiary.ifscCode}','IN','${beneficiary.beneficiaryMobileNumber || 0}','${fields.mobile_number}','${fields.uic}','${fields.ma_user_id}','${fields.userid}','Y')`)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'insertBene', type: 'insertBeneficiaries', fields: beneficiaries })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'insertBene', type: 'insertBeneficiaries', fields: beneficiaries })

      if (insertUniqueBene.length > 0 && beneficiaries.length > 0) {
        const insertbBeneficiariesResult = await this.insertBulk(conn, { fields: '(`account_number`,`beneficiary_name`,`ma_bank_master_id`,`ifsc_code`,`country_code`,`ben_mobile_number`,`mobile_number`,`uic`,`ma_user_id`,`userid`,`beneficiary_status`)', data: beneficiaries.join(',') })
        console.log('inser result:', insertbBeneficiariesResult)
        if (insertbBeneficiariesResult.status === 400 || insertbBeneficiariesResult.status == 401) {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }

        if (insertbBeneficiariesResult.affectedRows == 0) {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'insertBene', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'insertBene', type: 'err', fields: err })
    } finally {
      if (isSet) conn.release()
    }
  }

  static async insertBeneMapping (fields, connection) {
    log.logger({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'request', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'request', fields })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      // insert new beneficiaries mapping
      const beneficiariesMapping = []
      // select inserted beneficiaries and get the ids
      for (let index = 0; index < fields.beneList.length; index++) {
        const beneficiary = fields.beneList[index]
        const maBeneficiariesIdQuery = `SELECT ma_beneficiaries_id FROM ma_beneficiaries WHERE account_number='${beneficiary.accountNumber}' AND ifsc_code='${beneficiary.ifscCode}' AND uic='${fields.uic}' AND ma_user_id=${fields.ma_user_id} AND beneficiary_status='Y'`
        log.logger({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'maBeneficiariesIdQuery', fields: maBeneficiariesIdQuery })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'maBeneficiariesIdQuery', fields: maBeneficiariesIdQuery })

        const maBeneficiariesIdResult = await this.rawQuery(maBeneficiariesIdQuery, conn)
        log.logger({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'maBeneficiariesIdResult', fields: maBeneficiariesIdResult })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'maBeneficiariesIdResult', fields: maBeneficiariesIdResult })

        if (maBeneficiariesIdResult.length == 0) {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
        beneficiariesMapping.push(`('${maBeneficiariesIdResult[0].ma_beneficiaries_id}','${fields.ma_bank_on_boarding_id}','${fields.uic}','${fields.ma_user_id}','${fields.userid}','S','${beneficiary.beneficiaryId}')`)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'insertBeneMapping', type: 'beneficiariesMapping', fields: beneficiariesMapping.join(',') })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'insertBeneMapping', type: 'beneficiariesMapping', fields: beneficiariesMapping.join(',') })

      // this.TABLE_NAME = 'ma_beneficiary_bank_mapping'
      // const insertbBeneficiariesMappingResult = await this.insertBulk(conn, { fields: '(`ma_beneficiaries_id`,`ma_bank_on_boarding_id`,`uic`,`ma_user_id`,`userid`,`bank_status`,`receiver_id`)', data: beneficiariesMapping })

      const Insertfields = '(`ma_beneficiaries_id`,`ma_bank_on_boarding_id`,`uic`,`ma_user_id`,`userid`,`bank_status`,`receiver_id`)'

      const insertbBeneficiariesMappingResultQuery = `INSERT IGNORE ma_beneficiary_bank_mapping${Insertfields} VALUES ${beneficiariesMapping.join(',')}`

      log.logger({ pagename: require('path').basename(__filename), action: 'insertBeneMapping', type: 'insertbBeneficiariesMappingResultQuery', fields: insertbBeneficiariesMappingResultQuery })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'insertBeneMapping', type: 'insertbBeneficiariesMappingResultQuery', fields: insertbBeneficiariesMappingResultQuery })

      const insertbBeneficiariesMappingResult = await this.rawQuery(insertbBeneficiariesMappingResultQuery, conn)

      log.logger({ pagename: require('path').basename(__filename), action: 'insertBeneMapping', type: 'insertbBeneficiariesMappingResult', fields: insertbBeneficiariesMappingResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'insertBeneMapping', type: 'insertbBeneficiariesMappingResult', fields: insertbBeneficiariesMappingResult })

      if (insertbBeneficiariesMappingResult.status === 400 || insertbBeneficiariesMappingResult.status == 401) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }

      if (insertbBeneficiariesMappingResult.affectedRows == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'insertBeneMapping', type: 'err', fields: err })
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{ma_beneficiaries_id:Number, account_number:String, ifsc_code:String, connection:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async isBeneficaryBlackListed ({ ma_beneficiaries_id, account_number, ifsc_code, connection }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isBeneficaryBlackListed', type: 'request', fields: { ma_beneficiaries_id, account_number, ifsc_code } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneficaryBlackListed', type: 'request', fields: { ma_beneficiaries_id, account_number, ifsc_code } })
    const isSet = (connection === null || connection === undefined)
    const connRead = isSet ? await mySQLWrapper.getConnectionFromReadReplica() : connection
    try {
      let accountNumber = account_number
      let ifscCode = ifsc_code
      if (ma_beneficiaries_id) {
        const beneResp = await this.getBeneficiaryDetails(ma_beneficiaries_id, connRead)
        if (beneResp.status != 200) return beneResp
        const { account_number, ifsc_code } = beneResp.details
        accountNumber = account_number
        ifscCode = ifsc_code
      }

      const fetchBlackListBeneQuery = `SELECT blacklist_status FROM ma_blacklisted_beneficiaries WHERE beneficiary_account_no = '${accountNumber}' AND ifsc_code = '${ifscCode}' LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'isBeneficaryBlackListed', type: 'fetchBlackListBeneQuery', fields: fetchBlackListBeneQuery })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneficaryBlackListed', type: 'fetchBlackListBeneQuery', fields: fetchBlackListBeneQuery })
      const fetchBlackListBeneResult = await this.rawQuery(fetchBlackListBeneQuery, connRead)
      log.logger({ pagename: require('path').basename(__filename), action: 'isBeneficaryBlackListed', type: 'fetchBlackListBeneResult', fields: fetchBlackListBeneResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneficaryBlackListed', type: 'fetchBlackListBeneResult', fields: fetchBlackListBeneResult })
      if (fetchBlackListBeneResult.length > 0 && fetchBlackListBeneResult[0].blacklist_status == 'A') return { status: 400, message: `${errorMsg.responseCode[1028]}: This beneficiary is not eligible for fund transfer services,inconvenience is regrated`, respcode: 1001, action_code: 1001 }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'massDeleteBene', type: 'catchError', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'massDeleteBene', type: 'catchError', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connRead.release()
    }
  }

  static async beneImportList (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'request', fields: fields })

    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()

    let handler = {}
    let priorityBankList = []

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    if (!validator.definedVal(fields.handler)) {
      if (!validator.definedVal(fields.sessionRQ)) {
        return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
      }

      const sessionDataTmp = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id || null
      })

      if (sessionDataTmp.status == 400) {
        return sessionDataTmp
      }

      const { bank_name, ma_bank_on_boarding_id, uicSession, bank_priority_json } = sessionDataTmp.data
      priorityBankList = JSON.parse(bank_priority_json)
      console.log('HANDLER: ', sessionDataTmp.handler)
      handler = sessionDataTmp.handler
    } else {
      handler = fields.handler
      priorityBankList = JSON.parse(fields.bank_priority_json)
    }
    try {
      fields.bankHandler = handler
      const syncBeneficiaryFromBank = await this.syncBeneficiariesFromBank(fields, connection)

      log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'response', fields: syncBeneficiaryFromBank })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'response', fields: syncBeneficiaryFromBank })
      if (syncBeneficiaryFromBank.status != 200) return syncBeneficiaryFromBank

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], newBeneList: syncBeneficiaryFromBank.beneficiary_list, ma_bank_on_boarding_id: syncBeneficiaryFromBank.ma_bank_on_boarding_id, mobile_number: syncBeneficiaryFromBank.mobile_number }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'error', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'beneImport', type: 'error', fields: err })
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  static async beneImport (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'request', fields: fields })

    this.TABLE_NAME = 'ma_beneficiaries'
    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()

    try {
      await mySQLWrapper.beginTransaction(connection)
      // Check whether beneficiary/beneficiary mapping already exist
      const checkBeneOrBeneMappingExists = await this.checkBeneOrBeneMappingExists(fields, connection)
      log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'isBeneExist', fields: checkBeneOrBeneMappingExists })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'isBeneExist', fields: checkBeneOrBeneMappingExists })
      if (checkBeneOrBeneMappingExists.insert_bene.length > 0) {
        fields.IfscCodes = checkBeneOrBeneMappingExists.insert_bene.map(i => i.ifscCode)
        fields.beneList = checkBeneOrBeneMappingExists.insert_bene
        // const massInsertResult = await this.massInsertBeneficiaries(fields, conn)
        // log.logger({ pagename: require('path').basename(__filename), action: 'massInsertBeneficiaries', type: 'massInsertResult', fields: massInsertResult })
        const massInsertResult = await this.insertBene(fields, connection)
        log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'insertBene-response', fields: massInsertResult })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'insertBene-response', fields: massInsertResult })
        if (massInsertResult.status != 200) await mySQLWrapper.rollback(connection)

        const insertBeneMapping = await this.insertBeneMapping(fields, connection)
        log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'insertBeneMapping-response', fields: insertBeneMapping })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'insertBeneMapping-response', fields: insertBeneMapping })
        if (insertBeneMapping.status != 200) await mySQLWrapper.rollback(connection)
      }
      if (checkBeneOrBeneMappingExists.update_bene.length > 0) {
        fields.beneList = checkBeneOrBeneMappingExists.update_bene.map(i => i.ma_beneficiaries_id)
        const updateBene = await this.updateBene(fields, connection, 'Y')
        log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'updateBene-respone', fields: updateBene })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'updateBene-respone', fields: updateBene })
        if (updateBene.status != 200) await mySQLWrapper.rollback(connection)
      }
      if (checkBeneOrBeneMappingExists.update_bene_mapping.length > 0) {
        fields.beneList = checkBeneOrBeneMappingExists.update_bene_mapping.map(i => i.ma_beneficiary_bank_mapping_id)
        const updateMapping = await this.updateMapping(fields, connection, 'S')
        log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'updateMapping-respone', fields: updateMapping })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'updateMapping-respone', fields: updateMapping })
        if (updateMapping.status != 200) await mySQLWrapper.rollback(connection)
      }
      if (checkBeneOrBeneMappingExists.insert_bene_mapping.length > 0) {
        fields.beneList = checkBeneOrBeneMappingExists.insert_bene_mapping
        const insertBeneMapping = await this.insertBeneMapping(fields, connection)
        log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'insertBeneMapping-response', fields: insertBeneMapping })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'beneImport', type: 'insertBeneMapping-response', fields: insertBeneMapping })
        if (insertBeneMapping.status != 200) await mySQLWrapper.rollback(connection)
      }
      await mySQLWrapper.commit(connection)

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'beneImport', type: 'error', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'beneImport', type: 'error', fields: err })
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  static async generateClientId (length = 50) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const prefix = 'MACL_'
    const suffix = Date.now().toString()
    const finalLength = length - prefix.length - suffix.length
    let result = prefix

    for (let i = 0; i < finalLength; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    result += suffix
    return result
  }
}

module.exports = Beneficiary
