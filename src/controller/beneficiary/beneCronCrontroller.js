const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const beneficiaryController = require('./beneficiaryController')
// const { lokiLogger } = require('../../util/lokiLogger')

class BeneCronController extends DAO {
  static async missingBeneCron () {
    // get connection
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get ma_transaction_master data that is not available in bene
      const missingBeneSql = `SELECT
      tm.ma_user_id,
      tm.ma_transaction_master_id,
      tm.aggregator_order_id,
      IF (
        ISNULL(plm.parent_id),
        'FAILED',
        'REVERSE'
      ) AS cron_process
    FROM
      ma_transaction_master tm
    LEFT JOIN
      ma_points_ledger_master plm
    ON
      tm.aggregator_order_id = plm.parent_id
    LEFT JOIN
      ma_bene_verification b
    ON
      b.ma_transaction_master_id = tm.ma_transaction_master_id
    WHERE
      tm.transaction_type = '21' AND
      tm.transaction_status = 'I' AND
      b.ma_transaction_master_id IS NULL AND
      TIMESTAMPDIFF(MINUTE,tm.updatedon,CURRENT_TIMESTAMP) between 30 AND 1470
    GROUP BY
      tm.aggregator_order_id`

      const missingBeneDatas = await this.rawQuery(missingBeneSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'missingBeneDatas', fields: missingBeneDatas })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'missingBeneDatas', fields: missingBeneDatas })

      const updateQuery = (transaction_id, remark) => `UPDATE
        ma_transaction_master
      SET
        remarks = '${remark}',
        transaction_status = 'F'
      WHERE
        transaction_type = 21 AND
        ma_transaction_master_id IN (${transaction_id})`

      const refund = []
      for (const missingBeneData of missingBeneDatas) {
        try {
          if (missingBeneData.cron_process == 'REVERSE') {
            await mySQLWrapper.beginTransaction(connection)
            const reverseLedgerAll = await beneficiaryController.reverseDebitLedger(null, {
              connection: connection,
              aggregator_order_id: missingBeneData.aggregator_order_id,
              ma_user_id: missingBeneData.ma_user_id
            }, connection)

            log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'reverseLedgerAll', fields: { reverseLedgerAll } })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'reverseLedgerAll', fields: { reverseLedgerAll } })
            if (reverseLedgerAll.status != 200) {
              await mySQLWrapper.rollback(connection)
              continue
            }

            const reverseUpdateQuery = updateQuery(missingBeneData.ma_transaction_master_id, 'CRON :: FAILED DUE TO DB ERROR PL ENTRY')

            log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'reverse update query', fields: { reverseUpdateQuery } })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'reverse update query', fields: { reverseUpdateQuery } })
            const reverseUpdate = await this.rawQuery(reverseUpdateQuery, connection)

            await mySQLWrapper.commit(connection)
          } else {
            refund.push(missingBeneData.ma_transaction_master_id)
          }
        } catch (err) {
          await mySQLWrapper.rollback(connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'loop catcherror', fields: { err } })
          // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'loop catcherror', fields: { err } })
        }
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'fail', fields: { refund } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'fail', fields: { refund } })

      if (refund.length > 0) {
        const refundUpdateQuery = updateQuery(refund.join(', '), 'CRON :: FAILED DUE TO DB ERROR')

        log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'refund update query', fields: { refundUpdateQuery } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'refund update query', fields: { refundUpdateQuery } })
        const refundUpdate = await this.rawQuery(refundUpdateQuery, connection)
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'missingBeneCron', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }
}

module.exports = BeneCronController
