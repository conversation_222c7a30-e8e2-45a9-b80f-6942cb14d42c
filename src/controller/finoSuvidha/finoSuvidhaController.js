const DAO = require('../../lib/dao')

class finoSuvidhaController extends DAO {

    static async addBeneficiary (_, fields) {
         try {
            return { status: 200, message: "Beneficiary added successfully" };
        } catch (error) {
            return { status: 500, message: `Internal Server Error: ${error.message}` };
        } 
    }

    
    static async getBeneficiaryList (_, fields) {
        try {
            return { status: 200, message: "Beneficiary List" };
        } catch (error) {
            return { status: 500, message: `Internal Server Error: ${error.message}` };
        } 
    }
      
}

module.exports = finoSuvidhaController;