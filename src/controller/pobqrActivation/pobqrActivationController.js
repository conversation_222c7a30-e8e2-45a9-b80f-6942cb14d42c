const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')

class pobqrActivationController extends DAO {
  static get TABLE_NAME () {
    return 'ma_pob_form_data'
  }


static async test (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'test', type: 'request', fields: { fields } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes
    
    try {
      const sql = `SELECT * FROM ${this.TABLE_NAME} WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'test', type: 'sql - query', fields: sql })
      const data = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'test', type: 'sql - response', fields: data })
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        data: data
      }
    }
    catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'test', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = pobqrActivationController  
