const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const logs = require('../../util/log')
const path = require('path')
const errorMsg = require('../../util/error')
const errorEmail = require('../../util/errorHandler')
const axios = require('axios')
// const constants = require('./constants')
const validator = require('../../util/validator')
const md5 = require('md5')
// const checksum = require('../../util/checksum')
const otpController = require('../otp/otpController')
const sms = require('../../util/sms')
const _ = require('lodash')
const moment = require('moment')
const crypto = require('crypto')
const configValue = require('./config')

class OndcSellerController extends DAO {
  static async sellerEncryption (input, param = true) {
    try {
      const key = Buffer.from(configValue.customKey)
      const buffer = Buffer.from(input)
      const iv = param ? Buffer.from('0123456789abcdef') : crypto.randomBytes(16)
      /* AES Ciphering */
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
      let encrypted = cipher.update(buffer, 'utf-8', 'base64')
      encrypted += cipher.final('base64')
      return iv.toString() + encrypted
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'sellerEncryption ', type: 'err', fields: err })
      return { status: 400, respcode: 1001, action_code: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async sellerDecryption (encrypted) {
    try {
      const key = configValue.customKey
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, encrypted.slice(0, 16))
      let decryptedData = decipher.update(encrypted.slice(16), 'base64')
      console.log('decryptedData>>>>>', decryptedData)
      decryptedData += decipher.final('utf-8')
      return decryptedData
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'sellerDecryption ', type: 'err', fields: err })
      return { status: 400, respcode: 1001, action_code: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async checkSellerExistApi (data) {
    try {
      const config = {
        method: 'POST',
        url: configValue.createSellerUrl,
        headers: {
          'Content-Type': 'application/json'
          // 'x-api-key': configData.apiKey
        },
        data: JSON.stringify(data)
      }
      let res
      try {
        const resResult = await axios(config)
        res = resResult.data
      } catch (axioserr) {
        logs.logger({ pagename: path.basename(__filename), action: 'checkSellerExistApi', type: 'axioserr', fields: axioserr })
        res = axioserr.response
      }
      console.log('res>>>>>', res)

      return res
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'checkSellerExistApi', type: 'err', fields: err })
      return { status: 400, respcode: 1001, action_code: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  // validate form data
  static async validateFormData (data, users) {
    try {
      let isValid = 'valid'
      for (const property in data) {
        console.log('property', property)
        console.log('isValid =====', isValid)

        const findByName = _.filter(users, { postKey: property })
        if (findByName.length > 0) {
          console.log(findByName[0])

          if (findByName[0].type.toLowerCase() == 'text' || findByName[0].type.toLowerCase() == 'number') {
            if (_.has(findByName[0], 'validation')) {
              console.log('apply validation rules')

              if (_.has(findByName[0].validation, 'min')) {
                console.log('min = ', findByName[0].validation.min)

                console.log('data ', property, ' = ', data[property])
                console.log('data ', property, ' = ', data)
                console.log('data.length ', property, ' = ', data[property].length)
                console.log('validation.min ', property, ' = ', findByName[0].validation.min)
                if (data[property].length < findByName[0].validation.min) {
                  isValid = `Fail[${property}]: ` + 'Min length is not valid'
                  break
                }
              }

              if (_.has(findByName[0].validation, 'max')) {
                console.log('max = ', findByName[0].validation.max)

                console.log('data ', property, ' = ', data[property])
                if (data[property].length > findByName[0].validation.max) {
                  isValid = `Fail[${property}]: ` + 'Max length is not valid'
                  break
                }
              }
            }
          }
          if (_.has(findByName[0].validation, 'regex')) {
            console.log('regex = ', findByName[0].validation.regex)
            const regexValue = findByName[0].validation.regex
            const regexPattern = new RegExp(regexValue)
            if (!regexPattern.test(data[property])) {
              isValid = `Fail[${property}]: ` + 'Invalid regex'
              break
            }
          }
        }
      }
      return isValid
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'validateFormData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async sellerAuthenticationApi (data) {
    logs.logger({ pagename: path.basename(__filename), action: 'sellerAuthenticationApi', type: 'data', fields: data })
    try {
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: configValue.sellerAuthenticateUrl,
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(data)
      }
      const res = await axios.request(config)
        .then((response) => {
          console.log('res>>>>>', response)
          return response
        })
        .catch((error) => {
          console.log('err>>>>>', error)
          return error
        })
      console.log('res>>>', res)
      if (res.data) {
        return res.data
      } else {
        return { status: 500, message: 'Fail: API Error' }
      }
      // const parsedResponse = JSON.parse(res)
      // console.log('parsedResponse>>>', parsedResponse)
      // return parsedResponse
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'sellerAuthenticationApi', type: 'err', fields: err })
      return { status: 400, respcode: 1001, action_code: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async sellerRegistrationApi (data) {
    try {
      const data = {
        first_name: 'Vipin',
        last_name: 'Yadav',
        email: '<EMAIL>',
        password: 'cc1234',
        password_confirmation: 'cc1234',
        url: 'vipin.yadav',
        ma_user_id: '28641'
      }

      const config = {
        method: 'POST',
        url: configValue.sellerRegistrationUrl,
        headers: {
          'Content-Type': 'application/json'
          // 'x-api-key': configData.apiKey
        },
        data: JSON.stringify(data)
      }
      let res
      try {
        res = await axios(config)
      } catch (axioserr) {
        logs.logger({ pagename: path.basename(__filename), action: 'checkSellerExistApi ', type: 'request', fields: axioserr })
        res = axioserr.response
      }
      console.log('res>>>>>', res)

      return res
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'sellerDecryption ', type: 'request', fields: err })
      return { status: 400, respcode: 1001, action_code: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async checkUserChannel (ma_user_id, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    // var channelDetails = {}
    try {
      // get state & user type
      // get state details
      const sqlA = `SELECT state, user_type FROM ma_user_master WHERE profileid = ${ma_user_id} limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'checkUserChannel', type: 'sqlA', fields: sqlA })
      const userADetails = await this.rawQuery(sqlA, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'checkUserChannel', type: 'userADetails', fields: userADetails })
      let state_id = 0
      let user_type = ''
      if (userADetails.length > 0) {
        state_id = userADetails[0].state
        user_type = userADetails[0].user_type
      }

      // get channel list with respect to user id
      const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = 0 and ma_user_id = ${ma_user_id} and record_status = 'Y' and user_type = '${user_type}' `
      logs.logger({ pagename: path.basename(__filename), action: 'checkUserChannel', type: 'sqlCh', fields: sqlCh })
      const channelDetails = await this.rawQuery(sqlCh, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'checkUserChannel', type: 'channelDetails', fields: channelDetails })
      var final_channel_arr = {}
      var channel_arr = []
      var check_channel_arr = []
      var checkInsurance = ''
      if (channelDetails.length > 0) {
        for (var j = 0; j < channelDetails.length; j++) {
          channel_arr[j] = channelDetails[j].channel_name
        }
        console.log('check channel arr ', channel_arr)
        // console.log('check channel arr new ', check_channel_arr)
        if (channel_arr.includes('ONDCSELLER')) {
          checkInsurance = 'yes'
        }

        console.log('inside user seller channel condition >>>>>>>>>>', checkInsurance)
        return checkInsurance
      } else {
        // get channel list with respect to state
        const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=1 and state_master_id = ${state_id} and ma_user_id = 0 and record_status = 'Y' and user_type = '${user_type}' `
        const channelStateDetails = await this.rawQuery(sqlCh, connection)
        // var final_channel_arr = {}
        if (channelStateDetails.length > 0) {
          for (var k = 0; k < channelStateDetails.length; k++) {
            channel_arr[k] = channelStateDetails[k].channel_name
          }

          if (channel_arr.includes('ONDCSELLER')) {
            checkInsurance = 'yes'
          }
          console.log('inside state channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
          return checkInsurance
        } else {
          const sqlCh = `SELECT channel_name FROM ma_channels_master WHERE settings_flag=2 and state_master_id =0 and record_status = 'Y' and ma_user_id = 0 and user_type = '${user_type}' `
          const channelGlobalDetails = await this.rawQuery(sqlCh, connection)
          // var final_channel_arr = {}
          if (channelGlobalDetails.length > 0) {
            for (var n = 0; n < channelGlobalDetails.length; n++) {
              channel_arr[n] = channelGlobalDetails[n].channel_name
            }
            console.log('channel_list ', channel_arr)
            if (channel_arr.includes('ONDCSELLER')) {
              checkInsurance = 'yes'
            }
            console.log('inside Global channel condition >>>>>>>>>>>>>>>>>', checkInsurance)
            return checkInsurance
          } else {
            checkInsurance = ''
            return checkInsurance
          }
        }
      }
    } catch (err) {
      logs.logger({ pagename: require('path').basename(__filename), action: 'checkUserChannel', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getSellerDynamicForm (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'request', fields })
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const { ma_user_id, userid, seller_type } = fields
      const userChannelData = await this.checkUserChannel(ma_user_id, connection)

      if (userChannelData == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
      }
      const getDynamicFormSql = 'Select api_params from ma_dynamic_forms where form_type = \'ondc_seller_form\' limit 1'
      const getDynamicForm = await this.rawQuery(getDynamicFormSql, connection)
      const formData = JSON.parse(getDynamicForm[0].api_params)
      console.log('formData>>>>', formData)
      console.log('sellerType>>', seller_type)
      const sellerType = seller_type.toLowerCase()
      if (sellerType == 'self') {
        const getRemitterDataSql = `Select firstname,lastname,email_id from ma_user_master where profileid =${ma_user_id} and userid=${userid}`
        const getRemitterData = await this.rawQuery(getRemitterDataSql, connection)
        if (getRemitterData.length < 1) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        const data = getRemitterData[0]
        const prefilledData = {
          first_name: data.firstname,
          last_name: data.lastname,
          email: data.email_id
        }
        formData.forEach(item => {
          console.log('item>>>', item)
          console.log('data>>', prefilledData[item.postKey])

          if (prefilledData[item.postKey] != null && prefilledData[
            item.postKey] != '') {
            // Update the value property in form with the value from prefilledData
            item.value = prefilledData[item.postKey]
            item.isEditable = false
          }
        })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, form_data: formData }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, form_data: formData }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getSellerDynamicForm', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
      connection.release()
    }
  }

  static async ondcsellerRegistration (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'request', fields })
    const validateFieldsResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'form_data'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const { ma_user_id, userid } = fields
      const userChannelData = await this.checkUserChannel(ma_user_id, connection)

      if (userChannelData == '') {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Transaction not allowed as channel is not assigned' }
      }
      const formData = JSON.parse(Buffer.from(fields.form_data, 'base64').toString('ascii'))
      logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'formData', fields: formData })
      const { first_name, last_name, email, password, confirm_password, add_shop_slug } = formData
      if (password != confirm_password) {
        return { status: 400, message: 'Password and confirm password are not matching', respcode: 1001, action_code: 1001 }
      }
      // Dynamic form validation check

      const getFormDataSql = "Select api_params from ma_dynamic_forms where form_type = 'ondc_seller_form' limit 1 "
      logs.logger({ pagename: require('path').basename(__filename), action: 'ondcsellerRegistration', type: 'getFormDataSql', fields: getFormDataSql })
      const getFormData = await this.rawQuery(getFormDataSql, connection)
      const formElement = JSON.parse(getFormData[0].api_params)
      console.log('formElement===', formElement)
      logs.logger({ pagename: require('path').basename(__filename), action: 'ondcsellerRegistration', type: 'getFormData', fields: formElement })
      const isValid = await this.validateFormData(formData, formElement)
      if (isValid != 'valid') {
        return { status: 400, respcode: 1001, action_code: 1001, message: isValid }
      }

      // check seller exist in db
      const isSellerExistInDbSql = `Select * from ma_ondc_seller_registraion_details where email = '${email}' and status ='Active' limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'isSellerExistInDbSql', fields: isSellerExistInDbSql })
      const isSellerExistInDb = await this.rawQuery(isSellerExistInDbSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'isSellerExistInDb', fields: isSellerExistInDb })
      if (isSellerExistInDb.length > 0) {
        return { status: 400, message: 'Seller is already Registered', respcode: 1001, action_code: 1001 }
      }

      const data = {
        first_name,
        last_name,
        email,
        password,
        password_confirmation: confirm_password,
        url: add_shop_slug,
        ma_user_id
      }
      console.log('data>>>>', data)
      const checkSellerExistResult = await this.checkSellerExistApi(data)
      logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'checkSellerExistResult', fields: checkSellerExistResult })
      if ((checkSellerExistResult.status != 200) && checkSellerExistResult.data.errors.includes('The url has already been taken.')) {
        return { status: 400, message: 'The add shop has already been taken.', respcode: 1001, action_code: 1001 }
      }
      if (checkSellerExistResult.status != 200) {
        return { status: 400, message: `Fail API Err : ${checkSellerExistResult.data.errors}`, respcode: 1001, action_code: 1001 }
      }
      // protected password logic
      const newPass = password.split('')

      const protectedPassArray = newPass.map((item) => {
        return 'X'
      })

      const protectedPass = protectedPassArray.join('')
      console.log('protectedPass>>>', protectedPass)
      // insert into ma_ondc_seller_registraion_details

      const insertQuerySql = `Insert into ma_ondc_seller_registraion_details  (ma_user_id,userid,first_name,last_name,email,password) Values(${ma_user_id},${userid},'${first_name}','${last_name}','${email}','${protectedPass}') `
      logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'insertQuerySql', fields: insertQuerySql })
      const insertQuery = await this.rawQuery(insertQuerySql, connection)

      return { status: 200, message: 'Seller registration is successful, please check your email for further details.', respcode: 1000, action_code: 1000 }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'ondcsellerRegistration', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      connection.release()
    }
  }

  static async showOptions (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'request', fields })
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const { ma_user_id, userid } = fields

      const getUserIdsql = `Select userid from ma_user_master where profileid =${ma_user_id} and userid =${userid} and mer_user ='mer' limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'getUserIdsql', fields: getUserIdsql })
      const getUserId = await this.rawQuery(getUserIdsql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'getUserId', fields: getUserId })
      if (getUserId.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      const userId = getUserId[0].userid

      const getEmailSql = `Select email from users where  id=${userId} limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'getEmailSql', fields: getEmailSql })
      const getEmail = await this.rawQuery(getEmailSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'getEmail', fields: getEmail })
      if (getEmail.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      const email = getEmail[0].email
      // check seller exist in db
      const isSellerExistInDbSql = `Select * from ma_ondc_seller_registraion_details where email = '${email}' and status ='Active' limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'isSellerExistInDbSql', fields: isSellerExistInDbSql })
      const isSellerExistInDb = await this.rawQuery(isSellerExistInDbSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'isSellerExistInDb', fields: isSellerExistInDb })
      if (isSellerExistInDb.length == 0) {
        console.log('length is 0>>>>>>')
        const options = [{
          key: 'self',
          value: 'Self'
        },
        {
          key: 'others',
          value: 'Others'
        }]
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, options }
      }
      const options = [{
        key: 'others',
        value: 'Others'
      }]
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, options }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'showOptions', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
      connection.release()
    }
  }

  static async getSellerList (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getSellerList', type: 'request', fields })
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const { ma_user_id, userid, limit, offset } = fields
      let nextFlag = false
      const isSellerExistInDbSql = `SELECT SQL_CALC_FOUND_ROWS first_name,last_name,email from ma_ondc_seller_registraion_details where ma_user_id = ${ma_user_id} and userid = ${userid}  order by addedon Limit ${limit} Offset ${offset}`
      logs.logger({ pagename: path.basename(__filename), action: 'getSellerList', type: 'isSellerExistInDbSql', fields: isSellerExistInDbSql })
      const isSellerExistInDb = await this.rawQuery(isSellerExistInDbSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getSellerList', type: 'isSellerExistInDb', fields: isSellerExistInDb })
      if (isSellerExistInDb.length == 0) {
        return { status: 400, message: 'No records found', respcode: 1001, action_code: 1001 }
      }
      const countSql = 'SELECT FOUND_ROWS() AS total'
      const countResult = await this.rawQuery(countSql, connection)
      if (countResult.length > 0) {
        const total = countResult[0].total
        if (fields.limit && (fields.offset !== undefined && fields.offset !== null)) {
          if ((fields.limit + fields.offset) < total) {
            nextFlag = true
          }
        }
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, nextFlag, data: isSellerExistInDb }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getSellerList', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
      connection.release()
    }
  }

  static async ondcSellerAuthentication (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'request', fields })
    const validateFieldsResponse = await validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateFieldsResponse.status == 400) {
      return validateFieldsResponse
    }
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const { ma_user_id, userid } = fields
      // const getUserIdsql = `SELECT userid from ma_user_master where profileid =${ma_user_id} and userid =${userid} and mer_user ='mer' limit 1`
      // logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'getUserIdsql', fields: getUserIdsql })
      // const getUserId = await this.rawQuery(getUserIdsql, connection)
      // logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'getUserId', fields: getUserId })
      // if (getUserId.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      // const userId = getUserId[0].userid

      // const getEmailSql = `SELECT email from users where  id=${userId} limit 1`
      // logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'getEmailSql', fields: getEmailSql })
      // const getEmail = await this.rawQuery(getEmailSql, connection)
      // logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'getEmail', fields: getEmail })
      // if (getEmail.length == 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      // const email = getEmail[0].email

      // const encryptedData = await this.sellerEncryption(JSON.stringify(ma_user_id))
      // logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'encryptedData', fields: encryptedData })

      // if (encryptedData.status == 400) {
      //   return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      // }

      // const decryptedEmail = await this.sellerDecryption(encryptedEmail)
      // console.log('decryptedEmail>>>', decryptedEmail)
      const data = {
        mid: ma_user_id
      }
      const sellerAuthenticationApiResult = await this.sellerAuthenticationApi(data)
      logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'sellerAuthenticationApiResult', fields: sellerAuthenticationApiResult })

      // Insert into activity log table
      const requestData = JSON.stringify(data)
      const responseData = JSON.stringify(sellerAuthenticationApiResult)

      const insertQuerySql = `Insert into ma_ondc_seller_activity_log(ma_user_id,userid,request_json,response_json)
      Values(${ma_user_id},${userid},'${requestData}','${responseData}')`
      logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'insertQuerySql', fields: insertQuerySql })
      const insertQuery = await this.rawQuery(insertQuerySql, connection)

      if (sellerAuthenticationApiResult.status != 200) {
        return { status: 400, message: sellerAuthenticationApiResult.message, respcode: 1001, action_code: 1001 }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, url: `${configValue.urlLink}${sellerAuthenticationApiResult.token}` }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'ondcSellerAuthentication', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Release Connection
      connection.release()
    }
  }
}

module.exports = OndcSellerController
