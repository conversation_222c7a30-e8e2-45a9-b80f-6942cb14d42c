const path = require('path')
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const logs = require('../../util/log')
const errorMsg = require('../../util/error')
const util = require('../../util/util')
const validator = require('../../util/validator')
const CommissionController = require('../../controller/commission/commissionController')
const MonthlyIncentiveController = require('../../controller/incentive/monthlyIncentiveDistributionController')
const PointsLedgerController = require('../../controller/creditDebit/pointsLedgerController')
const PointsDetailsController = require('../../controller/creditDebit/pointsDetailsController')
const BalanceController = require('../../controller/balance/balanceController')
const commonFunction = require('../../controller/common/commonFunctionController')
const ApplicationError = require('../../errors/ApplicationError')
const PointsAccountController = require('../../controller/creditDebit/pointsAccountController')
const CashAccountController = require('../../controller/creditDebit/cashAccountController')
const CashLedgerController = require('../../controller/creditDebit/cashLedgerController')
const { BASE_URL, ADD_CUSTOMER_API_URL, ASSIGN_CARD_API_URL, KYC_UPLOAD_API_URL, STAGE, HEADER_CONTENT, FROM_EMAIL, EMAIL_SIGNATURE, ACTIVATION_SUCCESS_EMAIL_SUBJECT, ACTIVATION_FAIL_EMAIL_SUBJECT, AFFILIATE, AIRPAY_KEY, P_CODE, CHECKSUM_SECRETKEY, DOC_TYPE, resident_type } = require('./constants').PREPAID_CONSTANTS
const common = require('../../util/common')
const prepaidCardController = require('../prepaidCard/prepaidCardController')
const PointsRateController = require('../pointsRate/pointsRateController')

class PrepaidCardDistributionController extends DAO {
  static get TABLE_NAME () {
    return 'ma_slabwise_distribution_card_issuance'
  }

  static get PRIMARY_KEY () {
    return 'ma_slabwise_distribution_card_issuance_id'
  }

  static async userDetails ({ ma_user_id, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'userDetails', type: 'request', fields: { ma_user_id } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      const decryptionKey = util[env].secondaryEncrptionKey

      const userQuery = `SELECT profileid,user_type,distributer_user_master_id,state,gst_number,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan,sales_id FROM ma_user_master where profileid = ${ma_user_id}`
      const userResult = await this.rawQuery(userQuery, connRead)
      logs.logger({ pagename: path.basename(__filename), action: 'userDetails', type: 'result', fields: userResult })

      if (userResult.length == 0) return false

      return userResult[0]
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'userDetails', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async getDistribution ({ ma_user_id, amount, state_master_id = 0, customer_type, program_id, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'request', fields: { ma_user_id, amount, state_master_id } })

    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      let rt_share = 0
      let dt_share = 0
      let sd_share = 0
      let customer_charges = 0

      const user = await this.userDetails({ ma_user_id, connectionRead: connRead })
      if (!user) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user ', action_code: 1001 }
      const state_master_id = user.state
      const globalShares = await this.getSlabs({
        ma_user_id,
        amount,
        state_master_id,
        customer_type,
        program_id,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'globalShares', fields: globalShares })

      if ((globalShares.status) && globalShares.status == 400) {
        return globalShares
      }
      const { globalShare } = globalShares
      console.log('global share : ', globalShare.customer_charges)
      customer_charges = this.calculateCustomerCharge({ ...globalShare.customer_charges, txnAmount: amount })
      logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'customer_charges', fields: customer_charges })
      rt_share = await this.findValue({ customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, ...globalShare.rt_share })
      dt_share = await this.findValue({ customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, ...globalShare.dt_share })
      sd_share = await this.findValue({ customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, ...globalShare.sd_share })

      logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'Calculated Charges', fields: { customer_charges, rt_share, dt_share, sd_share } })

      /*
      const gstRate = await common.getSystemCodes(this, util.prepaid_card_gst_rate, connRead)
      logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'gstRate', fields: gstRate })
      const gst = parseFloat((parseFloat(customer_charges.total_customer_charges) * parseFloat(gstRate)) / 100)
      console.log('GST: ', gst)
      */

      let gst = 0
      let gstRate = 0
      const gst_share = globalShare.gst
      const isGSTApplicable = gst_share.gst_applied_type == '2'

      console.log('isGSTAPPLICABLE: ', isGSTApplicable)
      if (isGSTApplicable) {
        gstRate = gst_share.gst
        gst = parseFloat((parseFloat(customer_charges.total_customer_charges) * parseFloat(gstRate)) / 100)
        logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'GST', fields: gst })
      }

      console.log('ALL: ', { rt_share, dt_share, sd_share, card_issuance_fee: customer_charges.issuance_fee, gst, total_customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges })
      return { rt_share, dt_share, sd_share, card_issuance_fee: customer_charges.issuance_fee, gst, total_customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, gst_rate: gstRate }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getDistribution', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static buildShareObject (arr) {
    if (arr.length == 0) return false
    const globalShare = {}
    globalShare.customer_charges = {
      card_issuance_fee: arr[0].card_issuance_fee,
      card_issuance_fee_applied_type: arr[0].card_issuance_fee_applied_type,
      airpay_charges: arr[0].airpay_charges,
      airpay_charges_applied_type: arr[0].airpay_charges_applied_type
    }
    globalShare.rt_share = {
      share: arr[0].rt_share,
      applied_type: arr[0].rt_share_applied_type,
      operative_amount: arr[0].rt_operative_amount
    }

    globalShare.dt_share = {
      share: arr[0].dt_share,
      applied_type: arr[0].dt_applied_type,
      operative_amount: arr[0].dt_operative_amount
    }

    globalShare.sd_share = {
      share: arr[0].sd_share,
      applied_type: arr[0].sd_applied_type,
      operative_amount: arr[0].sd_operative_amount
    }

    globalShare.gst = {
      gst: arr[0].gst,
      gst_applied_type: arr[0].gst_applied_type
    }

    return { globalShare }
  }

  static async getSlabs ({ ma_user_id, amount, state_master_id = 0, program_id, customer_type, connectionRead }) {
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      const baseSql = `SELECT state_master_id, ma_user_id , card_issuance_fee,card_issuance_fee_applied_type,rt_share,rt_share_applied_type,rt_operative_amount,dt_share,dt_applied_type,dt_operative_amount,sd_share,sd_applied_type,sd_operative_amount,customer_type,program_id,settings_flag,airpay_charges,airpay_charges_applied_type,record_status, gst, gst_applied_type FROM ma_slabwise_distribution_card_issuance msdci WHERE record_status = 'Y' and ${amount} between min_amount and max_amount and program_id = ${program_id} and customer_type = '${customer_type}'`
      /* retailer specific configuration */
      const retailerResult = await this.retailerValues({ baseSql, ma_user_id, connRead })
      console.log('RET: ', retailerResult)
      if (retailerResult) {
        return retailerResult
      }

      /* check other configurations */
      const dTResult = await this.DTValues({ baseSql, ma_user_id, connRead })
      console.log('DT: ', dTResult)
      if (dTResult) {
        return dTResult
      }

      /* state specific configuration  */
      const stateResult = await this.stateValues({ baseSql, ma_user_id, state_master_id, connRead })
      console.log('STATE: ', stateResult)
      if (stateResult) {
        return stateResult
      }

      /* check default configuration */
      const globalQuery = `${baseSql} and state_master_id = 0 and ma_user_id = 0 and ma_dt_sdt_id = 0`
      logs.logger({ pagename: path.basename(__filename), action: 'getSlabs', type: 'globalQuery', fields: globalQuery })
      const globalResult = await this.rawQuery(globalQuery, connRead)
      console.log('GLOBAL: ', globalResult)

      const result = this.buildShareObject(globalResult)
      console.log('RESULT: ', result)
      if (result) return result
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[120004] }
      // throw new ApplicationError(120004, errorMsg.responseCode[120004])
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getSlabs', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async retailerValues ({ baseSql, ma_user_id, connRead }) {
    if (ma_user_id == null && ma_user_id == undefined && ma_user_id == 0) return false
    const retailerSql = `${baseSql} and ma_user_id=${ma_user_id}`
    logs.logger({ pagename: path.basename(__filename), action: 'retailerSql', type: 'retailer specific configuration Query', fields: retailerSql })
    const retailerResult = await this.rawQuery(retailerSql, connRead)
    return this.buildShareObject(retailerResult)
  }

  static async DTValues ({ baseSql, ma_user_id, connRead }) {
    const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({
      sql: baseSql,
      fields: {
        ma_user_id
      },
      connection: connRead
    })
    logs.logger({ pagename: path.basename(__filename), action: 'globalValues', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
    if (getDistributionAdditionalCondition.status != 200) return false
    return this.buildShareObject(getDistributionAdditionalCondition.configurationData)
  }

  static async stateValues ({ baseSql, ma_user_id, state_master_id, connRead }) {
    let stateMasterId = parseInt(state_master_id)
    if (state_master_id == 0 || state_master_id == undefined || state_master_id == null) {
      const user = await this.userDetails({ ma_user_id, connectionRead: connRead })
      if (!user) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user ', action_code: 1001 }
      stateMasterId = user.state
    }
    if (stateMasterId == null && stateMasterId == undefined && stateMasterId == 0) return false
    const stateSql = `${baseSql} and state_master_id=${stateMasterId}`
    logs.logger({ pagename: path.basename(__filename), action: 'stateSql', type: 'state specific configuration Query', fields: stateSql })
    const stateResult = await this.rawQuery(stateSql, connRead)
    return this.buildShareObject(stateResult)
  }

  static calculateCustomerCharge ({ card_issuance_fee, card_issuance_fee_applied_type, airpay_charges, airpay_charges_applied_type, txnAmount }) {
    console.log('REQ: ', card_issuance_fee, card_issuance_fee_applied_type, airpay_charges, airpay_charges_applied_type, txnAmount)
    let issuance_fee = card_issuance_fee
    if (card_issuance_fee_applied_type != 1) {
      issuance_fee = card_issuance_fee * txnAmount / 100
    }

    if (airpay_charges_applied_type != 1) {
      airpay_charges = airpay_charges * txnAmount / 100
    }

    console.log(parseFloat(issuance_fee) + parseFloat(airpay_charges))

    return {
      total_customer_charges: parseFloat(issuance_fee) + parseFloat(airpay_charges),
      issuance_fee,
      airpay_charges
    }
  }

  static findValue ({ airpay_charges, customer_charges, share, applied_type, operative_amount }) {
    console.log('FINVALUE :::::', airpay_charges, customer_charges, share, applied_type, operative_amount)
    if (applied_type == 1) {
      return share
    }
    if (operative_amount == null) {
      return share * airpay_charges / 100
    }
    if (operative_amount == 1) {
      return share * customer_charges / 100
    }
    return share * airpay_charges / 100
  }

  static async ledgerEntries ({ ma_user_id, userid, amount, aggregator_order_id, customer_charges, commissionAmount, issuance_fee, gst_rate, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'request', fields: { ma_user_id, userid, amount, aggregator_order_id, customer_charges, commissionAmount, issuance_fee } })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      let descObj
      let descType
      /* LEDGER ENTRIES */
      await mySQLWrapper.beginTransaction(conn)

      /* Point To Cash */
      const pointsConvertResp = await PointsLedgerController.prepaidCardPointToCash({
        ma_user_id,
        userid,
        amount,
        orderid: aggregator_order_id,
        type: 'PREPAID CARD',
        connection: conn
      })

      if (pointsConvertResp.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointsConvertResp
      }

      /* POINTS ACCOUNT */
      const pointAccountEntry = await PointsAccountController.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: amount,
        transaction_type: 'CW',
        ma_status: 'S',
        orderid: aggregator_order_id,
        corresponding_id: ma_user_id,
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'pointAccountEntry', fields: pointAccountEntry })

      if (pointAccountEntry.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointAccountEntry
      }

      /* CashAccount */
      const cashAccountResp = await CashAccountController.createEntry('_', {
        ma_user_id: ma_user_id,
        amount: amount,
        transaction_type: 'CW',
        ma_status: 'S',
        orderid: aggregator_order_id,
        corresponding_id: util.airpayUserId,
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'cashAccount', fields: cashAccountResp })

      if (cashAccountResp.status === 400) {
        await mySQLWrapper.rollback(conn)
        return cashAccountResp
      }

      // Points Details Entries for transaction charges debit

      const pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
        ma_user_id: ma_user_id,
        amount: customer_charges,
        transactionType: '1',
        connection: conn
      })
      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
      if (pointsDetailsEntries.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointsDetailsEntries
      }

      // Debit customer charges

      const retailerDr = await PointsLedgerController.createEntry('_', {
        ma_user_id: ma_user_id,
        amount: customer_charges,
        mode: 'dr',
        transaction_type: 63,
        description: 'Cards Issuance - Transaction Charges',
        ma_status: 'S',
        orderid: aggregator_order_id,
        userid: userid,
        corresponding_id: util.airpayCommissionId,
        connection: conn
      })
      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'Debit Customer Charge', fields: retailerDr })
      if (retailerDr.status === 400) {
        await mySQLWrapper.rollback(conn)
        return retailerDr
      }

      for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await PointsDetailsController.createEntry('_', {
          ma_user_id: ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: retailerDr.id,
          orderid: aggregator_order_id,
          ma_status: 'S',
          connection: conn
        })
        logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'PointsDetailsEntries-Customer Charges', fields: entry })
        if (entry.status === 400) {
          await mySQLWrapper.rollback(conn)
          return entry
        }
      }

      console.log('THIS IS THE COM: ', commissionAmount > 0)
      // COMMISSION ENTRY
      if (commissionAmount > 0) {
        const commissionPointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
          ma_user_id: ma_user_id,
          amount: commissionAmount,
          transactionType: '1',
          connection: conn
        })
        logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'commissionPointsDetailsEntries', fields: commissionPointsDetailsEntries })
        if (commissionPointsDetailsEntries.status === 400) {
          await mySQLWrapper.rollback(conn)
          return commissionPointsDetailsEntries
        }

        descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '62' }, conn)
        descType = descObj.code_desc ? descObj.code_desc : ''
        const commissionDr = await PointsLedgerController.createEntry('_', {
          ma_user_id: util.airpayCommissionId,
          amount: commissionAmount,
          mode: 'cr',
          transaction_type: 62,
          description: `${descType} - ${gst_rate}%`,
          ma_status: 'S',
          orderid: aggregator_order_id,
          userid: userid,
          corresponding_id: util.airpayUserId,
          connection: conn
        })
        logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'Debit Commission Charge', fields: commissionDr })
        if (commissionDr.status === 400) {
          await mySQLWrapper.rollback(conn)
          return commissionDr
        }

        for (let i = 0; i < commissionPointsDetailsEntries.details.length; i++) {
          const entry = await PointsDetailsController.createEntry('_', {
            ma_user_id: ma_user_id,
            amount: commissionPointsDetailsEntries.details[i].deductionAmount,
            wallet_type: commissionPointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: commissionDr.id,
            orderid: aggregator_order_id,
            ma_status: 'S',
            connection: conn
          })
          logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'PointsDetailsEntries-Commission', fields: entry })
          if (entry.status === 400) {
            await mySQLWrapper.rollback(conn)
            return entry
          }
        }
      }

      // PREPAID CARD ISSUANCE LEDGER ENTRY
      /* const issuancePointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
        ma_user_id: ma_user_id,
        amount: issuance_fee,
        transactionType: '1',
        connection: conn
      })
      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'issuancePointsDetailsEntries', fields: issuancePointsDetailsEntries })
      if (issuancePointsDetailsEntries.status === 400) {
        await mySQLWrapper.rollback(conn)
        throw new ApplicationError(issuancePointsDetailsEntries.respcode, issuancePointsDetailsEntries.message)
      } */
      descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '61' }, conn)
      descType = descObj.code_desc ? descObj.code_desc : ''
      const issuanceDr = await PointsLedgerController.createEntry('_', {
        ma_user_id: util.airpayCommissionId,
        amount: issuance_fee,
        mode: 'cr',
        transaction_type: 61,
        description: descType,
        ma_status: 'S',
        orderid: aggregator_order_id,
        userid: userid,
        corresponding_id: util.airpayUserId,
        connection: conn
      })
      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'Debit Issuance Fee', fields: issuanceDr })
      if (issuanceDr.status === 400) {
        await mySQLWrapper.rollback(conn)
        return issuanceDr
      }

      /* for (let i = 0; i < issuancePointsDetailsEntries.details.length; i++) {
        const entry = await PointsDetailsController.createEntry('_', {
          ma_user_id: ma_user_id,
          amount: issuancePointsDetailsEntries.details[i].deductionAmount,
          wallet_type: issuancePointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: issuanceDr.id,
          orderid: aggregator_order_id,
          ma_status: 'S',
          connection: conn
        })
        logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'PointsDetailsEntries-Issuance', fields: entry })
        if (entry.status === 400) {
          await mySQLWrapper.rollback(conn)
          throw new ApplicationError(entry.respcode, entry.message)
        }
      } */

      await mySQLWrapper.commit(connection)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  static async incentiveEntries ({ ma_user_id, userid, amount, aggregator_order_id, customer_type, program_id, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'request', fields: { ma_user_id, userid, amount, aggregator_order_id } })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      const distributionObj = []
      const provider_share = 0
      let rt_share = 0
      let dt_share = 0
      let sd_share = 0
      let customer_charges = 0

      const balSql = `SELECT balance,actual_balance,wallet_type FROM ma_allow_withdrawal WHERE ma_user_id = '${ma_user_id}'  and  FIND_IN_SET('61', transaction_type)`
      const balDetails = await this.rawQuery(balSql, conn)
      logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'response - walletdetails', fields: balDetails })
      if (balDetails.length <= 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Wallet [61] Type Missing', action_code: 1001 }
      }
      const user = await this.userDetails({ ma_user_id, connectionRead: connRead })
      console.log('USER: ', user)
      if (!user) return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Invalid user ', action_code: 1001 }

      const state_master_id = user.state

      const globalShares = await this.getSlabs({
        ma_user_id,
        amount,
        state_master_id,
        customer_type,
        program_id,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'globalShares', fields: globalShares })

      if ((globalShares.status) && globalShares.status == 400) {
        return globalShares
      }
      const { globalShare } = globalShares
      customer_charges = this.calculateCustomerCharge({ ...globalShare.customer_charges, txnAmount: amount })
      logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'customer_charges', fields: customer_charges })
      rt_share = await this.findValue({ customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, ...globalShare.rt_share })
      dt_share = await this.findValue({ customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, ...globalShare.dt_share })
      sd_share = await this.findValue({ customer_charges: customer_charges.total_customer_charges, airpay_charges: customer_charges.airpay_charges, ...globalShare.sd_share })

      logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'Calculated Charges', fields: { customer_charges, rt_share, dt_share, sd_share } })

      let afterDeductionAmt = customer_charges.airpay_charges
      logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'afterDeductionAmt', fields: afterDeductionAmt })

      await mySQLWrapper.beginTransaction(conn)
      // Push RT details
      if (rt_share > 0 && afterDeductionAmt - rt_share > 0) {
        var TDStypeval = '1'
        const isValidPAN = validator.validatePAN(user.pan)
        if (isValidPAN) {
          TDStypeval = '3'
        }
        afterDeductionAmt = afterDeductionAmt - rt_share
        logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'afterDeductionAmt after RT charges', fields: afterDeductionAmt })
        distributionObj.push({
          ma_user_id: ma_user_id,
          amount: rt_share,
          gst_number: user.gst_number,
          user_type: 'RT',
          TDStype: TDStypeval // With PAN or without
        })
      }

      // Find DT
      if (user.distributer_user_master_id > 0) {
        const distributor = await this.userDetails({ ma_user_id: user.distributer_user_master_id, connectionRead: connRead })
        logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'response - DT', fields: distributor })
        if (distributor) {
          distributor.share = dt_share
          // IF Distributer
          logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'Distributor details', fields: { distributor_type_of: typeof distributor.user_type, distributor } })
          if (distributor.user_type == 'DT') {
            // Push DT details
            if (dt_share > 0 && afterDeductionAmt - dt_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distributor.pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - dt_share
              logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'afterDeductionAmt after DT charges', fields: afterDeductionAmt })
              distributionObj.push({
                ma_user_id: distributor.profileid,
                amount: dt_share,
                gst_number: distributor.gst_number,
                user_type: 'DT',
                TDStype: TDStypeval // With PAN or without
              })
            }
            // Find Super DT
            if (distributor.distributer_user_master_id > 0) {
              const superDistributor = await this.userDetails({ ma_user_id: distributor.distributer_user_master_id, connectionRead: connRead })
              logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'response - SDT', fields: superDistributor })
              if (superDistributor && superDistributor.user_type == 'SDT') {
                // Push SDT details
                if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
                  TDStypeval = '1'
                  const isValidPAN = validator.validatePAN(superDistributor.pan)
                  if (isValidPAN) {
                    TDStypeval = '3'
                  }
                  afterDeductionAmt = afterDeductionAmt - sd_share
                  logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'afterDeductionAmt after SDT charges', fields: afterDeductionAmt })
                  distributionObj.push({
                    ma_user_id: superDistributor.profileid,
                    amount: sd_share,
                    gst_number: superDistributor.gst_number,
                    user_type: 'SDT',
                    TDStype: TDStypeval // With PAN or without
                  })
                }
              }
            }
          } else if (distributor.user_type === 'SDT') {
            if (sd_share > 0 && afterDeductionAmt - sd_share > 0) {
              TDStypeval = '1'
              const isValidPAN = validator.validatePAN(distributor.pan)
              if (isValidPAN) {
                TDStypeval = '3'
              }
              afterDeductionAmt = afterDeductionAmt - sd_share
              logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'afterDeductionAmt after SDT charges', fields: afterDeductionAmt })
              distributionObj.push({
                ma_user_id: distributor.profileid,
                amount: sd_share,
                user_type: 'SDT',
                gst_number: distributor.gst_number,
                TDStype: TDStypeval // With PAN or without
              })
            }
          }
        }
      }

      // Airpay Share array
      if (afterDeductionAmt > 0) {
        distributionObj.push({
          ma_user_id: util.airpayCommissionId,
          user_type: 'Airpay',
          amount: afterDeductionAmt
        })
      }

      // Debit Entries for all shares
      try {
        await this.distributionEntries({
          distributionObj,
          aggregator_order_id,
          userid,
          connection: conn
        })

        await mySQLWrapper.commit(conn)

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } catch (err) {
        await mySQLWrapper.rollback(conn)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'incentiveEntries', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  static async distributionEntries ({ userid, distributionObj, aggregator_order_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    logs.logger({ pagename: path.basename(__filename), action: 'incentiveDistribution', type: 'request', fields: { distributionObj, aggregator_order_id, userid } })
    try {
      const TransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await TransactionController.getTransactionDetailsByParentId(aggregator_order_id, conn)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      const distribution = distributionObj
      for (const value of distribution) {
        var subAmount = value.amount
        var descPart = ''
        var tds = 0
        if (subAmount <= 0) continue
        if (value.ma_user_id !== util.airpayUserId && value.ma_user_id !== util.airpayCommissionId) {
          // TDS will be be applied
          const commissionDetails = await CommissionController.getCommission('_', {
            ma_user_id: value.ma_user_id,
            ma_commission_type: 59,
            amount: value.amount,
            ma_deduction_type: value.TDStype,
            connection: conn
          })

          tds = commissionDetails.status === 400 ? 0 : commissionDetails.commissionVal
          console.log('TDS', tds)
          var appliedCommission = ''
          // TDS entry
          if (tds > 0) {
            subAmount = subAmount - tds
            appliedCommission = commissionDetails.appliedCommission
            const tdsInsert = await PointsLedgerController.createEntry('_', {
              ma_user_id: util.airpayCommissionId,
              amount: tds,
              mode: 'cr',
              transaction_type: '11',
              description: 'Cards Issuance - TDS -' + value.user_type,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + aggregator_order_id,
              userid: userid,
              corresponding_id: util.airpayUserId,
              connection: conn
            })

            if (tdsInsert.status === 400) return tdsInsert
            // [START] Make entry in ma_orderwise_taxes table
            let gst = 0
            const gstSql = `SELECT percentage from ma_gst_master where ma_user_id=${value.ma_user_id}`
            const gstSqlRes = await this.rawQuery(gstSql, conn)
            if (gstSqlRes.length > 0) {
              gst = parseFloat((value.amount - (value.amount * (100 / (100 + gstSqlRes[0].percentage)))).toFixed(4))
            }
            const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
            const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
              transaction_type: 60,
              orderid: aggregator_order_id,
              ma_user_id: value.ma_user_id,
              amount: value.amount,
              gst_amount: gst,
              tds_amount: tds,
              ma_status: 'S',
              connection: conn
            })

            if (orderwiseEntry.status === 400) return orderwiseEntry
            // [END] Make entry in ma_orderwise_taxes table
          }
        }

        if (subAmount > 0) {
          descPart = tds !== 0 ? ' (' + appliedCommission + ' deducted TDS is ' + parseFloat(tds).toFixed(4) + ')' : ''
          const globalIncentiveType = await MonthlyIncentiveController.getGlobalIncentiveSetting({ ma_user_id: value.ma_user_id, connection: conn })
          console.log('>>> LOGGING HERE: ', globalIncentiveType, value)
          if (globalIncentiveType.incentiveType == 'D') {
            const PointsLedgerControllerCredits = await PointsLedgerController.createEntry('_', {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 60,
              description: `Cards Issuance - ${value.user_type} Commission ${descPart}`,
              ma_status: 'S',
              orderid: aggregator_order_id,
              userid: userid,
              corresponding_id: util.airpayUserId,
              connection: conn
            })

            if (PointsLedgerControllerCredits.status === 400) return PointsLedgerControllerCredits
          } else {
            const PointsLedgerControllerCredits = await PointsLedgerController.createEntry('_', {
              ma_user_id: util.airpayHoldingId,
              amount: subAmount,
              mode: 'cr',
              transaction_type: 60,
              description: `Cards Issuance - ${value.user_type} Commission` + descPart,
              ma_status: 'S',
              orderid: value.ma_user_id + '-' + aggregator_order_id,
              userid: userid,
              corresponding_id: util.airpayUserId,
              connection: conn
            })

            if (PointsLedgerControllerCredits.status === 400) return PointsLedgerControllerCredits

            const data = {
              ma_user_id: value.ma_user_id,
              amount: subAmount,
              transaction_type: 60,
              monthyear: thisMonth + ' ' + now.getFullYear(),
              summary_id: 0,
              transaction_master_id: parent_transaction_master_id
            }
            const monthlyIncCreate = await MonthlyIncentiveController.createEntry('_', data, conn)
            if (monthlyIncCreate.status === 400) return monthlyIncCreate
          }
        }
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async reverseIncentiveEntries ({ ma_user_id, userid, aggregator_order_id, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentiveEntries', type: 'request', fields: { ma_user_id, userid, aggregator_order_id } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const isIncentivePresentQuery = `SELECT * FROM ma_points_ledger_master WHERE parent_id = '${aggregator_order_id}' AND transaction_type IN ('60','61','62','63',11) AND ma_status='S' AND mode='cr'`
      const isIncentiveEntriesPresent = await this.rawQuery(isIncentivePresentQuery, conn)

      if (isIncentiveEntriesPresent.length < 1) {
        return false
      }

      await mySQLWrapper.beginTransaction(conn)
      const sqlOrderWise = `SELECT * FROM ma_orderwise_taxes WHERE parent_id = '${aggregator_order_id}' AND ma_status='S' AND transaction_type = '60'`
      const sqlOrderWiseRes = await this.rawQuery(sqlOrderWise, conn)

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentiveEntries', type: 'chargesDistribution', fields: sqlOrderWiseRes })

      if (sqlOrderWiseRes.length > 0) {
        const insertOrderwiseTaxes = require('../incentive/orderwiseTaxesController')
        for (const valueOrderWise of sqlOrderWiseRes) {
          const orderWiseTaxAmount = parseFloat((valueOrderWise.amount).toFixed(4))
          const orderWiseGstAmount = parseFloat((valueOrderWise.gst_amount).toFixed(4))
          const orderWiseTdsAmount = parseFloat((valueOrderWise.tds_amount).toFixed(4))
          const orderwiseEntry = await insertOrderwiseTaxes.createEntry('_', {
            transaction_type: '4',
            orderid: 'R' + '-' + valueOrderWise.orderid,
            ma_user_id: valueOrderWise.ma_user_id,
            amount: (-1 * parseFloat(orderWiseTaxAmount)),
            gst_amount: (-1 * parseFloat(orderWiseGstAmount)),
            tds_amount: (-1 * parseFloat(orderWiseTdsAmount)),
            ma_status: 'R', // Refund/Reverse
            connection: conn
          })

          logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'insertOrderwiseTaxes', fields: sqlOrderWiseRes })

          if (orderwiseEntry.status === 400) {
            await mySQLWrapper.rollback(conn)
            return orderwiseEntry
          }
        }
      }
      // [END] Make refund/reverse entry in ma_orderwise_taxes table

      // check for entry in monthly incentives
      const TransactionController = require('../transaction/transactionController')
      const parent_transaction_master_id = await TransactionController.getTransactionDetailsByParentId(aggregator_order_id, conn)
      const monthlyIncentiveController = require('../incentive/monthlyIncentiveDistributionController')
      const sqlincmonth = `SELECT * FROM ma_monthly_incentives_process WHERE transaction_master_id = '${parent_transaction_master_id}' AND isRefund = 'FALSE'`
      const sqlincmonthRes = await this.rawQuery(sqlincmonth, conn)
      var now = new Date()
      var thisMonth = util.monthsconfig[now.getMonth()]
      for (var i = 0; i < sqlincmonthRes.length; i++) {
        const monthlyIncCreateRev = await monthlyIncentiveController.createEntry('_', {
          ma_user_id: sqlincmonthRes[i].ma_user_id,
          amount: (parseFloat((-1 * sqlincmonthRes[i].amount).toFixed(4))),
          transaction_type: sqlincmonthRes[i].transaction_type,
          monthyear: thisMonth + ' ' + now.getFullYear(),
          summary_id: 0,
          transaction_master_id: parent_transaction_master_id,
          isRefund: 'TRUE'
        }, conn)
        if (monthlyIncCreateRev.status === 400) {
          await mySQLWrapper.rollback(conn)
          logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentiveEntries', type: 'monthlyIncCreateRev', fields: monthlyIncCreateRev })
          return monthlyIncCreateRev
        }
      }

      let counting = 1
      for (const value of isIncentiveEntriesPresent) {
        counting++
        const subId = value.ma_user_id
        const subAmount = parseFloat((value.amount).toFixed(4))
        var pointsDetailsEntries = {}
        if (util.airpaymerchantconfig.includes(subId) == false) {
          pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
            ma_user_id: subId,
            amount: subAmount,
            transactionType: '1',
            connection: conn
          })
          if (pointsDetailsEntries.status === 400) {
            await mySQLWrapper.rollback(conn)
            logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
            return pointsDetailsEntries
          }
        }

        const subInsert = await PointsLedgerController.createEntry('_', {
          ma_user_id: subId,
          amount: subAmount,
          mode: 'dr',
          transaction_type: value.transaction_type,
          description: 'Debit - Card Issuance - ' + value.description + ' - Reversed',
          ma_status: 'R',
          orderid: 'R' + '-' + value.orderid,
          userid: userid,
          corresponding_id: value.corresponding_id,
          connection: conn
        })
        if (subInsert.status === 400) {
          await mySQLWrapper.rollback(conn)
          logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'subInsert', fields: subInsert })
          return subInsert
        }
        if (util.airpaymerchantconfig.includes(subId) == false) {
          for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
            const entry = await PointsDetailsController.createEntry('_', {
              ma_user_id: subId,
              amount: pointsDetailsEntries.details[i].deductionAmount,
              wallet_type: pointsDetailsEntries.details[i].wallet_type,
              ma_points_ledger_master_id: subInsert.id,
              orderid: 'R' + '-' + value.orderid,
              ma_status: 'R',
              transaction_status: 'R',
              connection: conn
            })
            if (entry.status === 400) {
              await mySQLWrapper.rollback(conn)
              logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'entry', fields: entry })
              return entry
            }
          }
        }
      }
      await mySQLWrapper.commit(conn)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'distributionEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async reverseLedgerEntries ({ ma_user_id, userid, aggregator_order_id, amount, transaction_charge, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'reverseLedgerEntries', type: 'request', fields: { ma_user_id, userid, aggregator_order_id, amount, transaction_charge } })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      const isLedgerEntriesPresentQuery = `SELECT * FROM ma_points_ledger_master WHERE mode = 'dr' AND orderid = '${aggregator_order_id}' AND transaction_type = '59'`
      const isLedgerEntriesPresentResult = await this.rawQuery(isLedgerEntriesPresentQuery, connRead)
      if (isLedgerEntriesPresentResult.length == 0) return false

      await mySQLWrapper.beginTransaction(conn)

      // Cash account -  load
      const cashAccount = await CashAccountController.createEntry('_', {
        ma_user_id: ma_user_id,
        amount: amount,
        transaction_type: 'CL',
        ma_status: 'REV',
        orderid: 'REV-' + aggregator_order_id,
        corresponding_id: util.airpayUserId,
        userid: userid,
        connection: conn
      })
      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'cashAccount', fields: cashAccount })

      if (cashAccount.status === 400) {
        await mySQLWrapper.rollback(conn)
        return cashAccount
      }

      // Points account - load
      const pointsAccount = await PointsAccountController.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount,
        transaction_type: 'CL',
        ma_status: 'REV',
        orderid: 'REV-' + aggregator_order_id,
        corresponding_id: ma_user_id,
        userid: userid,
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'pointsAccount', fields: pointsAccount })

      if (pointsAccount.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointsAccount
      }

      // Debit amount from airpay
      const pointsLedgerAP = await PointsLedgerController.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'dr',
        transaction_type: 59,
        description: 'Card Issuance  Transaction',
        ma_status: 'REV',
        orderid: 'REV-' + aggregator_order_id,
        userid: userid,
        corresponding_id: ma_user_id,
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'pointsLedgerAP', fields: pointsLedgerAP })

      if (pointsLedgerAP.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointsLedgerAP
      }

      // Cash Ledger Merchant
      const merchantCash = await CashLedgerController.createEntry('_', {
        ma_user_id: ma_user_id,
        amount,
        mode: 'dr',
        transaction_type: 59,
        description: util.topupCashCredit,
        orderid: 'REV' + '-' + aggregator_order_id,
        userid: userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'REV',
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'merchantCash', fields: merchantCash })

      if (merchantCash.status === 400) {
        await mySQLWrapper.rollback(conn)
        return merchantCash
      }

      // Cash Ledger Airpay/DT
      const airpayCash = await CashLedgerController.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount,
        mode: 'cr',
        transaction_type: 59,
        description: util.topupCashDebit,
        orderid: 'REV' + '-' + aggregator_order_id,
        userid: userid,
        corresponding_id: ma_user_id,
        ma_status: 'REV',
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'airpayCash', fields: airpayCash })

      if (airpayCash.status === 400) {
        await mySQLWrapper.rollback(conn)
        return airpayCash
      }

      // Credit DMT amount to merchant
      const pointsLedgerMerchant = await PointsLedgerController.createEntry('_', {
        ma_user_id: ma_user_id,
        amount,
        mode: 'cr',
        transaction_type: 59,
        description: 'Card Issuance Transaction Reversed',
        ma_status: 'REV',
        orderid: 'REV-' + aggregator_order_id,
        userid: userid,
        corresponding_id: util.airpayUserId,
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'pointsLedgerMerchant', fields: pointsLedgerMerchant })

      if (pointsLedgerMerchant.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointsLedgerMerchant
      }

      // Credit amount to merchant
      const pointsLedgerChargesMerchant = await PointsLedgerController.createEntry('_', {
        ma_user_id: ma_user_id,
        amount: transaction_charge,
        mode: 'cr',
        transaction_type: 63,
        description: 'Card Issuance - Transaction Charges Reversed',
        ma_status: 'REV',
        orderid: 'REV-' + aggregator_order_id,
        userid: userid,
        corresponding_id: util.airpayUserId,
        connection: conn
      })

      logs.logger({ pagename: path.basename(__filename), action: 'reverseIncentive', type: 'pointsLedgerChargesMerchant', fields: pointsLedgerChargesMerchant })

      if (pointsLedgerChargesMerchant.status === 400) {
        await mySQLWrapper.rollback(conn)
        return pointsLedgerChargesMerchant
      }

      await mySQLWrapper.commit(conn)
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'reverseLedgerEntries', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }
}

module.exports = PrepaidCardDistributionController
