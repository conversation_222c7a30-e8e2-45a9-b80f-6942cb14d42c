const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const path = require('path')
const validator = require('../../util/validator')
const util = require('../../util/util')
const axios = require('axios')
const otpClass = require('../otp/otpController')
const errorEmail = require('../../util/errorHandler')
const crypto = require('crypto')
const qs = require('qs')
const { fileLoader } = require('ejs')
const moment = require('moment-timezone')
const mailer = require('../../util/sendEmails')
const { BASE_URL, ADD_CUSTOMER_API_URL, ASSIGN_CARD_API_URL, KYC_UPLOAD_API_URL, STAGE, HEADER_CONTENT, FROM_EMAIL, EMAIL_SIGNATURE, ACTIVATION_SUCCESS_EMAIL_SUBJECT, ACTIVATION_FAIL_EMAIL_SUBJECT, AFFILIATE, AIRPAY_KEY, P_CODE_GOA_GOV, P_CODE_SEVA_MONEY, CHECKSUM_SECRETKEY, DOC_TYPE, resident_type, GENERATE_CAF_PDF, TOPUP_URL, TOKEN_URL, ACTIVATE_CARD_URL, WALLET_BALANCE, UPDATE_CUSTOMER, DIGI_LOCKER_API_URL, BAAS_DIGI_LOCKER_APIKEY, BAAS_AFFILIATE, DIGI_LOCKER_CHECKSUM, BAAS_AIRPAYKEY, PAN_VALIDATION_API_URL, BASS_PAN_CARD_APIKEY, BASS_AADHAAR_EKYC_APIKEY, AADHAAR_EKYC_VALIDATION_API_URL } = require('./constants').PREPAID_CONSTANTS
const prepaidCardDistributionController = require('./prepaidCardDistributionController')
const securePinCtrl = require('../securityPin/securityPinController')
const BalanceController = require('../balance/balanceController')
const TransactionController = require('../transaction/transactionController')
const PointsRateController = require('../pointsRate/pointsRateController')
const ApplicationError = require('../../errors/ApplicationError')
const sms = require('../../util/sms')
const { forEach } = require('lodash')
const { title } = require('process')

class prepaidCardController extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_prepaid_card'
  }

  static get PRIMARY_KEY () {
    return 'ma_prepaid_card_id'
  }

  // Fetch form data from database
  static async getPrepaidCardForm (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'request', fields: 'request' })
    // const connection = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('type>>>', fields.resident_type)
      var getParamsQuery = ''
      if (fields.resident_type == resident_type.RESIDENT) {
        getParamsQuery = "SELECT api_params from ma_dynamic_forms where form_type = 'resident_card_form' and isActive = 'Y' limit 1"
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'getParamsQuery', fields: getParamsQuery })
      } else if (fields.resident_type == resident_type.TOURIST) {
        getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'Prepaid Card' and form_type = 'prepaid_card_form' and  isActive = 'Y' limit 1"
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'getParamsQuery', fields: getParamsQuery })
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      /* const getResidentParams = await this.rawQuery(getParamsQuery, connection)
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'getResidentParams', fields: getResidentParams })
        if (getResidentParams.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        // const form = JSON.parse(params[0].api_params)
        const customField = JSON.parse(getResidentParams[0].api_params)
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'params-customField', fields: customField })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicPrepaidForm: customField } */

      const params = await this.rawQuery(getParamsQuery, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'params-response', fields: params })
      if (params.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      // const form = JSON.parse(params[0].api_params)

      const customField = JSON.parse(params[0].api_params)
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'params-customField', fields: customField })
      // CHECK FOR INPUT TYPE & IF DROP DOWN GET THE STATIC VALUES FOR THE DROP DOWN FROM THE TABLE
      for (const field of customField) {
        // console.log('fields : ', field)
        // console.log('field type : ', field.type)
        if ((field.type == 'DD' || field.type == 'DD_mobile') && field.postKey != 'gender' && field.postKey != 'title') {
          console.log('field type 1234 ====', field.type)
          const ddvalue = await this.getDynamicDataByKey({ static_data_type: field.postKey, connectionRead: connection })
          field.ddvalue = ddvalue
        }
      }
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'params-post-customField', fields: customField })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicPrepaidForm: customField }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  // Fetch form data from database
  static async getPrepaidCardSplitForm (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardSplitForm', type: 'request', fields: 'request' })
    // const connection = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log('type>>>', fields.resident_type)
      var getParamsQuery = ''
      if (fields.resident_type == resident_type.RESIDENT) {
        getParamsQuery = "SELECT api_params from ma_dynamic_forms where form_type = 'prepaid_resident_form' and isActive = 'Y' limit 1"
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'getParamsQuery', fields: getParamsQuery })
      } else if (fields.resident_type == resident_type.TOURIST) {
        getParamsQuery = "SELECT api_params from ma_dynamic_forms where form_type = 'prepaid_card_tourist_form' and  isActive = 'Y' limit 1"
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'getParamsQuery', fields: getParamsQuery })
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      /* const getResidentParams = await this.rawQuery(getParamsQuery, connection)
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'getResidentParams', fields: getResidentParams })
        if (getResidentParams.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        // const form = JSON.parse(params[0].api_params)
        const customField = JSON.parse(getResidentParams[0].api_params)
        log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardForm', type: 'params-customField', fields: customField })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicPrepaidForm: customField } */

      const params = await this.rawQuery(getParamsQuery, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardSplitForm', type: 'params-response', fields: params })
      if (params.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      // const form = JSON.parse(params[0].api_params)

      const customField = JSON.parse(params[0].api_params)
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardSplitForm', type: 'params-customField', fields: customField })

      // Get program name
      const fetchProgramTypeQuery = `SELECT ma_prepaid_card_program_master_id as program_id, program_name from ma_prepaid_card_program_master where program_status = 'Y' and ma_prepaid_card_program_master_id = ${fields.program_id}`
      const fetchProgramResult = await this.rawQuery(fetchProgramTypeQuery, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardSplitForm', type: 'fetchProgramResult', fields: fetchProgramResult })
      let program_id_value = ''
      if (fetchProgramResult.length > 0) {
        program_id_value = fetchProgramResult[0].program_name
      }

      // CHECK FOR INPUT TYPE & IF DROP DOWN GET THE STATIC VALUES FOR THE DROP DOWN FROM THE TABLE
      for (const field of customField) {
        // console.log('fields : ', field)
        // console.log('field type : ', field.type)
        if ((field.type == 'DD' || field.type == 'DD_mobile') && field.postKey != 'gender' && field.postKey != 'title') {
          console.log('field type 1234 ====', field.type)
          const ddvalue = await this.getDynamicDataByKey({ static_data_type: field.postKey, connectionRead: connection })
          field.ddvalue = ddvalue
        }
        if (field.postKey == 'program_name') {
          field.value = program_id_value
        }
        if (field.postKey == 'program_type') {
          field.value = fields.program_id
        }
        /* if (field.postKey == 'mobile_code') {
          field.value = fields.mobile_code
        } */
        if (field.postKey == 'mobile_no') {
          field.value = fields.mobile_no
          field.code = fields.mobile_code
        }
      }
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardSplitForm', type: 'params-post-customField', fields: customField })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, dynamicPrepaidForm: customField }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getPrepaidCardSplitForm', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async digilockerResidentApi (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'digilockerResidentApi', type: 'request', fields })
    // const connection = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // get mobile no. from prepaid card table
      const customerResult = await this.getCustomerInfo(_, fields)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerResidentApi', type: 'customerResult-response', fields: customerResult })
      const mobile_number = customerResult.customerDetails.customer_mobile_no
      const qs = require('qs')
      const data = qs.stringify({
        mobile_no: mobile_number,
        stage: 'authorization'
      })

      const param_string = mobile_number + 'authorization' + CHECKSUM_SECRETKEY

      // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerResidentApi', type: 'requestData_param_string', fields: param_string })
      const CHECKSUM_KEY = crypto.createHash('sha256').update(param_string).digest('hex')

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: DIGI_LOCKER_API_URL,
        headers: {
          AIRPAYKEY: BAAS_AIRPAYKEY,
          AFFILIATE: BAAS_AFFILIATE,
          CHECKSUM: CHECKSUM_KEY,
          APIKEY: BAAS_DIGI_LOCKER_APIKEY,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: data
      }
      log.logger({ pagename: path.basename(__filename), action: 'digilockerResidentApi', type: 'customerResult-config', fields: config })
      const response = await axios.request(config)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerResidentApi', type: ' API) - response', fields: response })

      if (response != '' && response.status === 200) {
        const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
        console.log('responseData>>>>>>>', responseData)
        if (responseData.status == 200) {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: responseData.data, action_code: 1000 }
        } else {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'digilockerResidentApi', type: 'digilockerResidentApi-response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving DigiLocker details', action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'digilockerResidentApi', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getDynamicDataByKey ({ static_data_type, connectionRead }) {
    log.logger({ pagename: path.basename(__filename), action: 'getDynamicDataByKey', type: 'request', fields: static_data_type })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    // const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromPool() : connectionRead
    try {
      if (static_data_type == 'mobile_no') {
        const fetchMobileCodeQuery = "SELECT CONCAT('+', phonecode) as phonecode_val,phonecode FROM ma_country WHERE country_status = 'Y'"
        const fetchMobileCodeResult = await this.rawQuery(fetchMobileCodeQuery, connectionRead)
        log.logger({ pagename: path.basename(__filename), action: 'getDynamicDataByKey', type: 'fetchMobileCodeResult', fields: fetchMobileCodeResult })
        if (fetchMobileCodeResult.length == 0) return []
        const mobileArray = fetchMobileCodeResult.map(data => ({ value: data.phonecode_val, key: data.phonecode }))
        console.log('Mobile array : ', mobileArray)
        return mobileArray
      } else if (static_data_type == 'country') {
        const fetchCountryQuery = "SELECT id, nicename as country_name FROM ma_country WHERE country_status = 'Y'"
        const fetchCountryResult = await this.rawQuery(fetchCountryQuery, connectionRead)
        log.logger({ pagename: path.basename(__filename), action: 'getDynamicDataByKey', type: 'fetchCountryResult', fields: fetchCountryResult })
        if (fetchCountryResult.length == 0) return []
        const countryArray = fetchCountryResult.map(data => ({ value: data.country_name, key: data.id + '~' + data.country_name }))
        console.log('Country array : ', countryArray)
        return countryArray
      } else if (static_data_type == 'program_type') {
        const fetchProgramTypeQuery = "SELECT ma_prepaid_card_program_master_id as id, program_name from ma_prepaid_card_program_master where program_status = 'Y'"
        const fetchProgramResult = await this.rawQuery(fetchProgramTypeQuery, connectionRead)
        log.logger({ pagename: path.basename(__filename), action: 'getDynamicDataByKey', type: 'fetchProgramResult', fields: fetchProgramResult })
        if (fetchProgramResult.length == 0) return []
        const programArray = fetchProgramResult.map(data => ({ value: data.program_name, key: data.id }))
        console.log('Program array : ', programArray)
        return programArray
      } else if (static_data_type == 'state') {
        const fetchStateQuery = "SELECT id, name as state_name from ma_states_master where record_status = 'active' order by id"
        const fetchStateResult = await this.rawQuery(fetchStateQuery, connectionRead)
        log.logger({ pagename: path.basename(__filename), action: 'getDynamicDataByKey', type: 'fetchProgramResult', fields: fetchStateResult })
        if (fetchStateResult.length == 0) return []
        const programArray = fetchStateResult.map(data => ({ value: data.state_name, key: data.id + '~' + data.state_name }))
        console.log('State array : ', programArray)
        return programArray
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }

      /* const fetchStaticResult = await this.rawQuery(fetchStaticQuery, connectionRead)
      log.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'fetchStaticResult', fields: fetchStaticResult })
      if (fetchStaticResult.length == 0) return []
      */
      /* const JSONParseData = JSON.parse(fetchStaticResult[0].static_data_response)
      if (!('a:DataList' in JSONParseData) || !('a:Data' in JSONParseData['a:DataList'])) //throw new ApplicationError(1001, errorMsg.responseCode[1001])
      let DataList = []
      const { 'a:Data': Data } = JSONParseData['a:DataList']
      DataList = Data
      if (DataList.constructor.name == 'Object') DataList = [Data]
      const data = DataList.map(data => ({ value: data['a:Label'].trim(), key: data['a:Value'].trim() }))
      log.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'fetchStaticResult', fields: data }) */
      // return true
    } catch (error) {
      log.logger({ pagename: path.basename(__filename), action: 'getBank', type: 'error', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async getMerchantDetail (ma_user_id, userid, conn) {
    const tempConnection = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      var userDetails = ''
      let merchant_name = ''
      let address = ''
      let mobile_number
      const sql = `SELECT CONCAT(firstname, ' ', lastname) AS merchant_name, address, mobile_id as mobile_number FROM ma_user_master WHERE profileid = ${ma_user_id} and userid = ${userid} and mer_user = 'mer' limit 1 `
      userDetails = await this.rawQuery(sql, connection)
      if (userDetails.length > 0) {
        merchant_name = userDetails[0].merchant_name
        address = userDetails[0].address
        mobile_number = userDetails[0].mobile_number
        return { status: 200, merchant_name: merchant_name, address: address, mobile_number }
      } else {
        return { status: 400, message: errorMsg.responseCode[1003], respcode: 1003 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetail', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
     * Generate random number
     */
  static async generateOTP (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  static async getExpiry (type, otp_type = null) {
    console.log(type)
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    if (type === 'expiry') {
      console.log('inside type')
      if (timezone !== -330) {
        today.setTime(today.getTime() + (util.expiry[otp_type] * 60 * 1000) + (30 * 60 * 1000) + (5 * 60 * 60 * 1000)) // time converted to ist from utc for testing purpose on kubeless. On live remove the 5:30 from date calculation.
      } else {
        today.setTime(today.getTime() + (util.expiry[otp_type] * 60 * 1000))
      }
      const min = today.getMinutes()
      const sec = today.getSeconds()
      const hh = today.getHours()
      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`
    } else {
      const timeStamp = Math.floor(Date.now() / 1000)
      return `${timeStamp}`
    }
  }

  // Save lead form details in database
  static async savePrepaidCardForm (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'savePrepaidCardForm', type: 'request', fields: 'request' })
    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'form_data'])
    console.log('Response:', validatorResponse)
    if (validatorResponse.status != 200) return validatorResponse
    try {
      const { ma_user_id, userid, form_data, resident_type } = fields
      const formData = JSON.parse(Buffer.from(form_data, 'base64').toString('ascii'))
      console.log('FORM DATA', formData)

      // check mobile no. and program type already exists then send customer details

      const sql = `SELECT CONCAT(pc.title,'. ', pc.first_name, ' ', pc.last_name) AS full_name, CONCAT(pc.mobile_code,'',pc.mobile_no) as mobile_no,mc.nicename  as country, pc.email, pc.passport_no, pc.visa_no, pc.customer_type, pc.reference_id, pc.ma_prepaid_card_id, pc.passport_kyc_verify, pc.visa_kyc_verify, pc.card_kit_assign, pc.customer_id, pc.live_photo_link, pc.caf_link,'PASSPORT' as doc_type_name_one, 'VISA' as doc_type_name_two, pc.customer_email_otp_verify, pc.resident_kyc_verify,pc.customer_pan_no, pcpm.program_name, pc.resident_kyc_type FROM ma_prepaid_card pc Join ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = pc.program_type_id LEFT JOIN ma_country mc on pc.country_id = mc.id WHERE pc.mobile_no = '${formData.mobile_no}' and pc.program_type_id = '${formData.program_type}' and pc.customer_type = '${fields.resident_type}' and pc.card_status = 'A' limit 1 `
      const customerInfoDetails = await this.rawQuery(sql, conn)
      log.logger({ pagename: path.basename(__filename), action: 'savePrepaidCardForm', type: 'customerInfoDetails', customerInfoDetails })
      if (customerInfoDetails.length > 0) {
        const customerDetails = {
          application_no: customerInfoDetails[0].ma_prepaid_card_id,
          reference_id: customerInfoDetails[0].reference_id,
          program_name: customerInfoDetails[0].program_name,
          customer_type: customerInfoDetails[0].customer_type,
          customer_id: customerInfoDetails[0].customer_id,
          customer_name: customerInfoDetails[0].full_name,
          customer_mobile_no: customerInfoDetails[0].mobile_no,
          country_name: customerInfoDetails[0].country,
          customer_email: customerInfoDetails[0].email,
          passport_no: customerInfoDetails[0].passport_no,
          visa_no: customerInfoDetails[0].visa_no,
          passport_kyc_verify: customerInfoDetails[0].passport_kyc_verify,
          visa_kyc_verify: customerInfoDetails[0].visa_kyc_verify,
          card_kit_assign: customerInfoDetails[0].card_kit_assign
        }
        let authAction = ''
        const customerType = customerInfoDetails[0].customer_type
        if (customerType == 'TOURIST') {
          if (customerInfoDetails[0].passport_kyc_verify == 'N' || customerInfoDetails[0].visa_kyc_verify == 'N') {
            authAction = 'COMPLETE_KYC'
          } else if (customerInfoDetails[0].live_photo_link == null) {
            authAction = 'LIVE_PHOTO'
          } else if (customerInfoDetails[0].caf_link == null) {
            authAction = 'CAF'
          } else if (customerInfoDetails[0].card_kit_assign == 'F') {
            authAction = 'ISSUE_CARD'
          } else {
            authAction = 'TOP_UP'
          }
        } else if (customerType == 'RESIDENT') {
          if (customerInfoDetails[0].customer_email_otp_verify == 'F') {
            authAction = 'CUSTOMER_EMAIL_OTP'
          } else if (customerInfoDetails[0].resident_kyc_verify == 'F') {
            authAction = 'COMPLETE_KYC'
          } else if (customerInfoDetails[0].customer_pan_no == null) {
            authAction = 'PAN_CARD'
          } else if (customerInfoDetails[0].live_photo_link == null) {
            authAction = 'LIVE_PHOTO'
          } else if (customerInfoDetails[0].caf_link == null && customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION') {
            authAction = 'CAF'
          } else if (customerInfoDetails[0].card_kit_assign == 'F') {
            authAction = 'ISSUE_CARD'
          } else {
            authAction = 'TOP_UP'
          }
        }

        let orderid = ''
        if (authAction == 'CUSTOMER_EMAIL_OTP' && customerType == 'RESIDENT') {
          // send email otp to verify customer
          const merchantOtpResponse = await this.sendEmailOtpToCustomer(_, { ma_user_id, userid, uniqueCustId: customerInfoDetails[0].customer_id })
          orderid = merchantOtpResponse.orderid
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uniqueCustId: customerInfoDetails[0].customer_id, auth_action: authAction, orderid: orderid }
      }

      /* const sql = `SELECT ma_prepaid_card_id FROM ma_prepaid_card WHERE email = '${formData.email}' and card_status = 'A'  limit 1 `
      const cardDetails = await this.rawQuery(sql, conn)
      if (cardDetails.length > 0) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Customer email already registered. ', action_code: 1001 }
      } */
      // const valid = data.find(i => i.postKey == 'loan_type')
      const regex = /^[a-zA-Z]+$/
      const pinCodeRegex = /^[1-9][0-9]{5}$/
      const emailRegex = /^[a-zA-Z0-9]+(?:[_%+.-][a-zA-Z0-9]+)?@[a-zA-Z0-9.-]+.[A-Za-z]{2,10}$/
      const mobileNoRegex = /^[6-9]{1}[0-9]{9}$/
      if (!(regex.test(formData.full_name))) {
        // return { status: 400, message: `${errorMsg.responseCode[1028]}: Invalid input Full name`, respcode: 1028 }
      }
      console.log('Regex value:', regex.test(formData.full_name))

      if (!(emailRegex.test(formData.email))) {
        return { status: 400, message: `${errorMsg.responseCode[1028]}: Invalid Email Address`, respcode: 1028 }
      }
      /* if (!(mobileNoRegex.test(formData.mobile_no))) {
        return { status: 400, message: `${errorMsg.responseCode[1028]}: Invalid Mobile Number`, respcode: 1028 }
      } */

      // split the data for mobile code, country
      formData.country = formData.country || ''
      let country_id = 99 // for resident type user
      let country_name = 'India'
      if (formData.country) {
        const country_arr = formData.country.split('~') // 1~India
        country_id = country_arr[0] //
        country_name = country_arr[1]
        // validate country should not be India in Tourist Type

        if (fields.resident_type == 'TOURIST' && country_name == 'India') {
          return { status: 400, message: 'Fail: Please select country other than India for Tourist Type', respcode: 1001 }
        }
      }

      // split the data for state
      formData.state = formData.state || ''
      let state_id = 0
      let state_name = ''
      if (formData.state) {
        const state_arr = formData.state.split('~') // 1~Punjab
        state_id = state_arr[0] //
        state_name = state_arr[1]
      }

      // get user details
      const responseMData = await this.getMerchantDetail(fields.ma_user_id, fields.userid, conn)
      if (responseMData.status == 400) {
        return responseMData
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'savePrepaidCardForm', type: 'result_user_detail', fields: { responseMData } })
      const merchant_name = responseMData.merchant_name
      const merchant_address = responseMData.address

      const customerType = fields.resident_type == 'TOURIST' ? 'foreign' : 'resident'
      // call MS Add customer API
      let gender_val = 'T'
      if (formData.gender == 'male') {
        gender_val = 'M'
      } else if (formData.gender == 'female') {
        gender_val = 'F'
      }

      if (gender_val != 'T') {
        if (formData.title == 'Mr' && gender_val != 'M') {
          return { status: 400, message: 'Title & Gender mismatch!', respcode: 1001 }
        }
        if ((formData.title == 'Mrs' || formData.title == 'Ms') && gender_val != 'F') {
          return { status: 400, message: 'Title & Gender mismatch!', respcode: 1001 }
        }
      }
      // const gender_val = formData.gender == 'male' ? 'M' : 'F'
      const countryName = fields.resident_type == 'TOURIST' ? country_name : 'India' // send default country India for resident type
      const kycType = 'MIN' // can be MIN, FULL, FLOW
      const requestFormData = {
        country_code: formData.mobile_code,
        mobile_no: formData.mobile_no,
        title: formData.title,
        first_name: formData.first_name,
        last_name: formData.last_name,
        gender: gender_val,
        dob: formData.dob,
        email: formData.email,
        address: formData.address || null,
        address2: formData.address2 || null,
        country: countryName,
        state: state_name || null,
        city: formData.city || null,
        pincode: formData.pincode || null,
        // doc_type: '',
        // doc_number: '',
        // doc_expiry: '',
        // doc_country_issue: '',
        password: '',
        card_design_type: '',
        vyapari_name: merchant_name,
        vyapari_address: merchant_address,
        requestor_name: '',
        customer_type: customerType,
        kyc_type: kycType
      }
      console.log('requestFormData >>>', requestFormData)
      const requestDataParam = qs.stringify(requestFormData)
      let param_string
      let stateId = ''

      if (resident_type == 'RESIDENT') {
        console.log('Insident resident type>>>>>>>')
        // const sqlQuery = `Select id from ma_states_master where name = '${formData.state}'`
        // const sqlResult = await this.rawQuery(sqlQuery, conn)
        // console.log('sqlResult>>>', sqlResult)
        // stateId = sqlResult[0].id
        param_string = formData.mobile_code + '' + formData.mobile_no + '' + formData.title + '' + formData.first_name + '' + formData.last_name + '' + gender_val + '' + formData.dob + '' + formData.email + '' + formData.address + '' + country_name + '' + state_name + '' + formData.city + '' + formData.pincode + '' + merchant_name + '' + merchant_address + '' + customerType + '' + kycType + CHECKSUM_SECRETKEY
      } else {
        param_string = formData.mobile_code + '' + formData.mobile_no + '' + formData.title + '' + formData.first_name + '' + formData.last_name + '' + gender_val + '' + formData.dob + '' + formData.email + '' + country_name + '' + '' + merchant_name + '' + merchant_address + '' + customerType + '' + kycType + CHECKSUM_SECRETKEY
      }

      // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
      log.logger({ pagename: path.basename(__filename), action: 'savePrepaidCardForm', type: 'requestData_param_string', fields: param_string })
      const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')

      log.logger({ pagename: path.basename(__filename), action: 'savePrepaidCardForm', type: 'checksum_key', fields: checksum_key })

      // Call API *****
      // check program type
      let PCODE = ''
      if (formData.program_type == 1) {
        PCODE = P_CODE_GOA_GOV
      } else if (formData.program_type == 2) {
        PCODE = P_CODE_SEVA_MONEY
      }
      let customer_id = ''
      const addCustomerApiResponse = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: 'register', endpoint: ADD_CUSTOMER_API_URL, pcode: PCODE })
      log.logger({ pagename: path.basename(__filename), action: 'savePrepaidCardForm', type: 'addCustomerApiResponse', fields: addCustomerApiResponse })
      // let customer_id = ''
      if (addCustomerApiResponse.status == 200) {
        customer_id = addCustomerApiResponse.axiosResponse.data.entityId
      } else {
        console.log('addCustomerApiResponse : ', addCustomerApiResponse)
        return { status: 400, message: addCustomerApiResponse.message, respcode: 1001, action_code: 1001 }
      }
      stateId = state_id || 0
      // console.log('state_id>>', state_id)

      const random = await this.generateOTP(4)
      const timestamp = await this.getExpiry('')
      const reference_id = `PC${random}${timestamp}`

      // save data in ma_prepaid_card table
      const dataInsert = `INSERT INTO ma_prepaid_card(ma_user_id, userid, program_type_id, title, first_name, last_name, gender, dob, mobile_code, mobile_no,address, city,state_id, pincode,country_id, email, customer_type, additional_info, card_status, customer_id, reference_id, kyc_type)
      VALUES (${fields.ma_user_id}, ${fields.userid},${formData.program_type}, '${formData.title}', '${formData.first_name}', '${formData.last_name}', '${gender_val}', '${formData.dob}','${formData.mobile_code}','${formData.mobile_no}','${requestFormData.address}','${requestFormData.city}',${stateId},'${requestFormData.pincode}',${country_id},'${formData.email}','${fields.resident_type}','${JSON.stringify(formData)}', 'A','${customer_id}','${reference_id}','${kycType}')`
      log.logger({ pagename: require('path').basename(__filename), action: 'savePrepaidCardForm', type: 'dataInsert', fields: dataInsert })
      const insertSqlResponse = await this.rawQuery(dataInsert, conn)
      let uniqueCustId = ''
      if (insertSqlResponse.insertId) {
        uniqueCustId = insertSqlResponse.insertId
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'savePrepaidCardForm', type: 'insertSqlResponse', fields: insertSqlResponse })
      let orderid = ''
      if (fields.resident_type == 'RESIDENT') {
        // send email otp to verify customer
        const merchantOtpResponse = await this.sendEmailOtpToCustomer(_, { ma_user_id, userid, uniqueCustId: customer_id })
        orderid = merchantOtpResponse.orderid
      }

      return { status: 200, respcode: 1000, message: 'Your request has been submitted successfully!', uniqueCustId: customer_id, orderid: orderid }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getDynamicForm', type: 'request', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  static async resendOtpToMerchant (_, { ma_user_id, userid, aggregator_order_id }) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // const gcm_id = 'hjdgfhgdfhg1hjhhhhg'
      var deviceos = ''
      const response = {}
      var mobile_number = ''
      let userid = ''
      const sql = `SELECT mobile, userid FROM ma_otp_master WHERE aggregator_order_id = '${aggregator_order_id}' limit 1`
      const userOtpDetails = await this.rawQuery(sql, connection)
      if (userOtpDetails.length > 0) {
        mobile_number = userOtpDetails[0].mobile
        userid = userOtpDetails[0].userid
      }
      var resp = await otpClass.resentOtp(mobile_number, aggregator_order_id, 'PCMERREGOTP')
      log.logger({ pagename: path.basename(__filename), action: 'resendOtpToMerchant', type: 'resp', fields: resp })
      console.log('data', resp)
      if (resp.status === true) {
        response.status = 200
        response.respcode = 1000
        response.message = errorMsg.responseCode[1000]
        response.action_code = 1000
        // const mailTemplateData = resp.data.template_text
      } else {
        response.status = 400
        response.respcode = resp.respcode
        response.message = resp.message
        response.action_code = 1001
      }
      return response
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOtpToMerchant', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async sendMerchantOtpMail (user_emailid, templatedata) {
    const responseUrl = util[process.env.NODE_ENV].ENV_DOMAIN
    var removeUselessWords = function (txt) {
      var uselessWordsArray =
        [
          'Dear Customer, ', 'Team airpay vyaapaar', 'Regards, Team airpay vyaapaar. '
        ]

      var expStr = uselessWordsArray.join('|')
      return txt.replace(new RegExp('\\b(' + expStr + ')\\b', 'gi'), ' ')
        .replace(/\s{2,}/g, ' ')
    }
    const template_content = removeUselessWords(templatedata)
    const mailerData = {
      template: 'sendPcMerchantOtp.ejs',
      content: {
        url: responseUrl,
        body: template_content
      },
      from: '"Airpay" <<EMAIL>>',
      to: [user_emailid],
      subject: 'One Time Otp - Airpay Prepaid Card'
    }
    const mailResponse = await mailer(mailerData)
    log.logger({ pagename: require('path').basename(__filename), action: 'sendCustomerOtpMail', type: 'Mail response', fields: mailResponse })
  }

  /*
  *check user login with sendOtp
  * <AUTHOR> Nayak
  */
  static async sendOtpToMerchant (_, { ma_user_id, userid, mobile_number, email }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpToMerchant', type: 'request', fields: { ma_user_id, userid } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // sent otp
      const sqlUserMasterD = `SELECT profileid, mobile_id FROM ma_user_master WHERE userid = ${userid} limit 1`
      const userMasterDetailsL = await this.rawQuery(sqlUserMasterD, connection)
      let ma_user_id = ''
      let mobile_number = ''

      if (userMasterDetailsL.length > 0) {
        ma_user_id = userMasterDetailsL[0].profileid
        mobile_number = userMasterDetailsL[0].mobile_id
      }
      const otpResponse = await otpClass.sentOtp(ma_user_id, userid, mobile_number, 'PCMERREGOTP')
      log.logger({ pagename: path.basename(__filename), action: 'sendOtpToMerchant', type: 'otpResponse', fields: otpResponse })
      if (otpResponse.status === true) {
        // const mailTemplateData = otpResponse.data.template_text

        // const mailResponse = this.sendCustomerOtpMail(email, mailTemplateData)
        return { status: 200, respcode: 2002, message: 'Success: OTP Sent to Merchant', orderid: otpResponse.data.aggregator_order_id }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpToMerchant', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async sendCustomerRegSuccessMail (user_emailid, templatedata) {
    const responseUrl = util[process.env.NODE_ENV].ENV_DOMAIN
    var removeUselessWords = function (txt) {
      var uselessWordsArray =
        [
          'Dear Customer, ', 'Team airpay vyaapaar', 'Regards, Team airpay vyaapaar. '
        ]

      var expStr = uselessWordsArray.join('|')
      return txt.replace(new RegExp('\\b(' + expStr + ')\\b', 'gi'), ' ')
        .replace(/\s{2,}/g, ' ')
    }
    const template_content = removeUselessWords(templatedata)
    const mailerData = {
      template: 'sendCustomerRegSuccessMail.ejs',
      content: {
        url: responseUrl,
        body: template_content
      },
      from: '"Airpay" <<EMAIL>>',
      to: [user_emailid],
      subject: 'Customer Registration Successful - Airpay Prepaid Card'
    }
    const mailResponse = await mailer(mailerData)
    log.logger({ pagename: require('path').basename(__filename), action: 'sendCustomerOtpMail', type: 'Mail response', fields: mailResponse })
  }

  /**
   * do verifyOtp
   * @param {*} _
   * @param {*} fields
  */
  static async verifyOtpFromMerchant (_, { ma_user_id, userid, aggregator_order_id, otp, uniqueCustId }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpFromMerchant', type: 'request', fields: { otp, aggregator_order_id, uniqueCustId } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const verifyResponse = await otpClass.verifyOtp(aggregator_order_id, 'PCMERREGOTP', otp)
      log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'verifyResponse', fields: verifyResponse })
      if (verifyResponse.status === false) {
        return { status: 400, respcode: verifyResponse.respcode, message: verifyResponse.message }
      } else {
        // update customer otp flag to true
        const sql = `UPDATE ma_prepaid_card set agent_otp_verify = 'P' WHERE customer_id = '${uniqueCustId}' `
        log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'updateSql', fields: sql })
        const verifyMerchantOtpResult = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'verifyMerchantOtpResult', fields: verifyMerchantOtpResult })
        const _userData = {}
        _userData.respcode = 1000
        _userData.status = 200
        _userData.message = errorMsg.responseCode[1000]
        // get  mobile & email from table
        const sqlCust = `SELECT  ma_prepaid_card_id, mobile_no, email, customer_id, reference_id FROM ma_prepaid_card WHERE customer_id = "${uniqueCustId}" limit 1  `
        log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'sqlCust', fields: sql })
        const customerInfoDetails = await this.rawQuery(sqlCust, connection)
        log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'customerInfoDetails', fields: customerInfoDetails })
        if (customerInfoDetails.length > 0) {
          const customer_email = customerInfoDetails[0].email
          const mobile_number = customerInfoDetails[0].mobile_no
          const reference_id = customerInfoDetails[0].reference_id

          // Send sms to customer after transaction is successful
          const common = require('../../util/common')
          let commMessage = ''
          let template = ''
          const TEMPASS = await common.getSystemCodes(this, util.prepaid_card_temp_pass, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpFromMerchant', type: 'TEMPASS', fields: { TEMPASS: TEMPASS } })

          commMessage = util.communication.PCSUCCESSFULREGISTRATION
          template = util.templateid.PCSUCCESSFULREGISTRATION
          const sms = require('../../util/sms')
          // var dt = new Date(date_time)
          // const datetimeformat = dt.toLocaleString('en-IN')
          commMessage = commMessage.replace('<CUSTID>', uniqueCustId)
          commMessage = commMessage.replace('<REFID>', reference_id)
          commMessage = commMessage.replace('<TEMPASS>', TEMPASS)
          console.log('common message ', commMessage)
          await sms.sentSmsAsync(commMessage, mobile_number, template)
          const mailResponse = this.sendCustomerRegSuccessMail(customer_email, commMessage)

          const sqlCurrentTime = 'SELECT DATE_FORMAT(CURRENT_TIMESTAMP,\'%d-%m-%Y %H:%i %p\') as currentTime'
          const resultTime = await this.rawQuery(sqlCurrentTime, connection)
          log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'result_date_time', fields: { resultTime } })
          const date_time = resultTime[0].currentTime

          // return _userData
          return { status: 200, message: 'Customer ID is generated Successfully.', uniqueCustId: uniqueCustId, registerMsg: 'Customer is successfully registerd with airpay.', registerTime: date_time, respcode: 1000 }
        } else {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
        }
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpFromMerchant', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async resendOtpToCustomer (_, { ma_user_id, userid, aggregator_order_id, uniqueCustId }) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // const gcm_id = 'hjdgfhgdfhg1hjhhhhg'
      var deviceos = ''
      const response = {}
      var mobile_number = ''
      let userid = ''
      const sql = `SELECT mobile, userid FROM ma_otp_master WHERE aggregator_order_id = '${aggregator_order_id}' limit 1`
      const userOtpDetails = await this.rawQuery(sql, connection)
      if (userOtpDetails.length > 0) {
        mobile_number = userOtpDetails[0].mobile
        userid = userOtpDetails[0].userid
      }
      var resp = await otpClass.resentOtp(mobile_number, aggregator_order_id, 'PCCUSTREGOTP')
      log.logger({ pagename: path.basename(__filename), action: 'resendOtpToCustomer', type: 'resp', fields: resp })
      if (resp.status === true) {
        response.status = 200
        response.respcode = 1000
        response.message = errorMsg.responseCode[1000]
        response.action_code = 1000

        const mailTemplateData = resp.template_text
        // send mail to customer
        const sql = `SELECT  ma_prepaid_card_id, email FROM ma_prepaid_card WHERE customer_id = "${uniqueCustId}" limit 1  `
        log.logger({ pagename: path.basename(__filename), action: 'resendOtpToCustomer', type: 'sql', fields: sql })
        const customerInfoDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'resendOtpToCustomer', type: 'customerInfoDetails', fields: customerInfoDetails })
        if (customerInfoDetails.length > 0) {
          const customer_email = customerInfoDetails[0].email
          const mailResponse = this.sendCustomerOtpMail(customer_email, mailTemplateData)
        } else {
          response.status = 400
          response.respcode = resp.respcode
          response.message = resp.message
          response.action_code = 1001
        }
      } else {
        response.status = 400
        response.respcode = resp.respcode
        response.message = resp.message
        response.action_code = 1001
      }
      return response
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOtpToCustomer', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async sendCustomerOtpMail (user_emailid, templatedata) {
    const responseUrl = util[process.env.NODE_ENV].ENV_DOMAIN
    var removeUselessWords = function (txt) {
      var uselessWordsArray =
        [
          'Dear Customer, ', 'Team airpay vyaapaar', 'Regards, Team airpay vyaapaar. '
        ]

      var expStr = uselessWordsArray.join('|')
      return txt.replace(new RegExp('\\b(' + expStr + ')\\b', 'gi'), ' ')
        .replace(/\s{2,}/g, ' ')
    }
    const template_content = removeUselessWords(templatedata)
    const mailerData = {
      template: 'sendPcCustomerOtp.ejs',
      content: {
        url: responseUrl,
        body: template_content
      },
      from: '"Airpay" <<EMAIL>>',
      to: [user_emailid],
      subject: 'One Time Otp - Airpay Prepaid Card'
    }
    const mailResponse = await mailer(mailerData)
    log.logger({ pagename: require('path').basename(__filename), action: 'sendCustomerOtpMail', type: 'Mail response', fields: mailResponse })
  }

  /*
  *check user login with sendOtp
  * <AUTHOR> Nayak
  */
  static async sendOtpToCustomer (_, { ma_user_id, userid, mobile_number, email, uniqueCustId }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOtpToCustomer', type: 'request', fields: { ma_user_id, userid } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // get  mobile & email from table
      const sql = `SELECT  ma_prepaid_card_id, mobile_no, email FROM ma_prepaid_card WHERE customer_id = "${uniqueCustId}" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'sendOtpToCustomer', type: 'send otp customer sql', fields: sql })
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'sendOtpToCustomer', type: 'customerInfoDetails', fields: customerInfoDetails })
      if (customerInfoDetails.length > 0) {
        const customer_email = customerInfoDetails[0].email
        const mobile_number = customerInfoDetails[0].mobile_no
        // sent otp
        const otpResponse = await otpClass.sentOtp(ma_user_id, userid, mobile_number, 'PCCUSTREGOTP')
        log.logger({ pagename: path.basename(__filename), action: 'sendOtpToCustomer', type: 'otpResponse', fields: otpResponse })
        if (otpResponse.status === true) {
          const mailTemplateData = otpResponse.data.template_text
          const mailResponse = this.sendCustomerOtpMail(customer_email, mailTemplateData)
          return { status: 200, respcode: 2002, message: errorMsg.responseCode[2002], orderid: otpResponse.data.aggregator_order_id }
        } else {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * do verifyOtp
   * @param {*} _
   * @param {*} fields
  */
  static async verifyOtpFromCustomer (_, { ma_user_id, userid, aggregator_order_id, otp, uniqueCustId }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpFromCustomer', type: 'request', fields: { otp, aggregator_order_id, uniqueCustId } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const verifyResponse = await otpClass.verifyOtp(aggregator_order_id, 'PCCUSTREGOTP', otp)
      log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromCustomer', type: 'verifyResponse', fields: verifyResponse })
      if (verifyResponse.status === false) {
        return { status: 400, respcode: verifyResponse.respcode, message: verifyResponse.message }
      } else {
        // update customer otp flag to true
        const sql = `UPDATE ma_prepaid_card set customer_otp_verify = 'P' WHERE customer_id = '${uniqueCustId}' `
        log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromCustomer', type: 'updateSql', fields: sql })
        const verifyCustomerOtpResult = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromCustomer', type: 'verifyCustomerOtpResult', fields: verifyCustomerOtpResult })
        const _userData = {}
        _userData.respcode = 1000
        _userData.status = 200
        // _userData.message = errorMsg.responseCode[1000]
        _userData.message = 'OTP has been sent to you on your Mobile Number, Please enter it below.'
        // call sendOtp to merchant
        const merchantOtpResponse = await this.sendOtpToMerchant(_, { ma_user_id, userid })
        _userData.orderid = merchantOtpResponse.orderid
        return _userData
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpFromCustomer', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async getCustomerInfo (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getCustomerInfo', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const sql = `SELECT pc.title, CONCAT(pc.first_name, ' ', pc.last_name) AS full_name, pc.mobile_code, pc.mobile_no, pc.email,mc.nicename  as country, pc.passport_no, pc.visa_no, pc.customer_type, pc.reference_id, pc.ma_prepaid_card_id, DATE_FORMAT(pc.dob,'%d/%m/%Y') as dob,'PASSPORT' as doc_type_name_one, 'VISA' as doc_type_name_two, pcpm.program_name, pc.resident_email_otp_verify,pc.resident_kyc_type,pc.resident_kyc_sub_type,pc.resident_kyc_expiry_date,pc.resident_kyc_doc_no,pc.resident_offline_file_front,pc.resident_offline_file_back,pc.digi_aadhaar_response,pc.resident_kyc_verify,pc.customer_email_otp_verify,pc.customer_pan_no,pc.customer_pan_validated,pc.customer_pan_no_details,pc.customer_id, pc.live_photo_link FROM ma_prepaid_card pc Join ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = pc.program_type_id LEFT JOIN ma_country mc on pc.country_id = mc.id WHERE pc.customer_id = '${fields.uniqueCustId}' and pc.card_status = 'A' limit 1 `
      const customerInfoDetails = await this.rawQuery(sql, connection)
      if (customerInfoDetails.length > 0) {
        const customerDetails = {
          application_no: customerInfoDetails[0].ma_prepaid_card_id,
          dob: customerInfoDetails[0].dob,
          reference_id: customerInfoDetails[0].reference_id,
          program_name: customerInfoDetails[0].program_name,
          customer_type: customerInfoDetails[0].customer_type,
          customer_id: customerInfoDetails[0].customer_id,
          title: customerInfoDetails[0].title,
          customer_name: customerInfoDetails[0].full_name,
          country_code: '+' + customerInfoDetails[0].mobile_code,
          customer_mobile_no: customerInfoDetails[0].mobile_no,
          country_name: customerInfoDetails[0].country,
          customer_email: customerInfoDetails[0].email,
          passport_no: customerInfoDetails[0].passport_no,
          visa_no: customerInfoDetails[0].visa_no,
          resident_email_otp_verify: customerInfoDetails[0].resident_email_otp_verify,
          resident_kyc_type: customerInfoDetails[0].resident_kyc_type,
          resident_kyc_sub_type: customerInfoDetails[0].resident_kyc_sub_type,
          resident_kyc_expiry_date: customerInfoDetails[0].resident_kyc_expiry_date,
          resident_kyc_doc_no: customerInfoDetails[0].resident_kyc_doc_no,
          resident_offline_file_front: customerInfoDetails[0].resident_offline_file_front,
          resident_offline_file_back: customerInfoDetails[0].resident_offline_file_back,
          digi_aadhaar_response: customerInfoDetails[0].digi_aadhaar_response,
          resident_kyc_verify: customerInfoDetails[0].resident_kyc_verify,
          customer_email_otp_verify: customerInfoDetails[0].customer_email_otp_verify,
          customer_pan_no: customerInfoDetails[0].customer_pan_no,
          customer_pan_validated: customerInfoDetails[0].customer_pan_validated,
          customer_pan_no_details: customerInfoDetails[0].customer_pan_no_details,
          live_photo_link: customerInfoDetails[0].live_photo_link
          /* document_proof_one_name: customerInfoDetails[0].doc_type_name_one,
          document_proof_one_no: customerInfoDetails[0].passport_no,
          document_proof_two_name: customerInfoDetails[0].doc_type_name_two,
          document_proof_two_no: customerInfoDetails[0].visa_no,
          document_identity_proof: '',
          document_identity_proof_no: '',
          customer_pan_no: '' */
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], customerDetails: customerDetails }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getCustomerInfo', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getCustomerInfo',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async uploadCustomerDoc (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'uniqueCustId'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      let updateString = ''
      fields.document_no = fields.document_no || ''
      fields.doc_front_link = fields.doc_front_link || ''
      fields.doc_back_link = fields.doc_back_link || ''
      fields.document_expiry_date = fields.document_expiry_date || ''

      if (fields.uploadFileType == 'PASSPORT' && (fields.document_no == null || fields.document_no == '' || fields.document_no == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document No. required for Passport KYC validation.' }
      }

      /* if (fields.uploadFileType == 'PASSPORT' && (fields.document_expiry_date == null || fields.document_expiry_date == '' || fields.document_expiry_date == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Expiry Date required for Passport KYC validation.' }
      } */

      if (fields.uploadFileType == 'PASSPORT' && (fields.doc_back_link == null || fields.doc_back_link == '' || fields.doc_back_link == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Back Photo required for Passport KYC validation.' }
      }

      if (fields.uploadFileType == 'VISA' && (fields.document_no == null || fields.document_no == '' || fields.document_no == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document No. required for Visa KYC validation.' }
      }

      /* if (fields.uploadFileType == 'VISA' && (fields.document_expiry_date == null || fields.document_expiry_date == '' || fields.document_expiry_date == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Expiry Date required for Visa KYC validation.' }
      } */

      /* if (fields.uploadFileType == 'VISA' && (fields.doc_back_link == null || fields.doc_back_link == '' || fields.doc_back_link == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Back Photo required for Passport KYC validation.' }
      } */

      if (fields.uploadFileType == 'PASSPORT' && (fields.doc_back_link == null || fields.doc_back_link == '' || fields.doc_back_link == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Expiry Date required for Passport KYC validation.' }
      }

      if (fields.uploadFileType == 'DL' && (fields.document_no == null || fields.document_no == '' || fields.document_no == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document No. required for Driving License KYC validation.' }
      }

      /* if (fields.uploadFileType == 'DL' && (fields.document_expiry_date == null || fields.document_expiry_date == '' || fields.document_expiry_date == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Expiry Date required for Driving License KYC validation.' }
      } */

      if (fields.uploadFileType == 'DL' && (fields.doc_back_link == null || fields.doc_back_link == '' || fields.doc_back_link == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Back Photo required for Driving License KYC validation.' }
      }

      if (fields.uploadFileType == 'VOTER_ID' && (fields.doc_back_link == null || fields.doc_back_link == '' || fields.doc_back_link == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Back Photo required for Voter Id KYC validation.' }
      }

      if (fields.uploadFileType == 'NREGA' && (fields.doc_back_link == null || fields.doc_back_link == '' || fields.doc_back_link == undefined)) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Document Back Photo required for NREGA KYC validation.' }
      }

      fields.uploadFileType = fields.uploadFileType || ''
      fields.document_no = fields.document_no || ''
      fields.doc_front_link = fields.doc_front_link || ''
      fields.doc_back_link = fields.doc_back_link || ''
      fields.document_expiry_date = fields.document_expiry_date || ''
      fields.device_id = fields.device_id || ''
      fields.ip_address = fields.ip_address || ''

      let uploadFileType = ''
      if (fields.uploadFileType == 'VOTER_ID') {
        uploadFileType = 'VOTERID'
      } else if (fields.uploadFileType == 'NREGA') {
        uploadFileType = 'NREGNA'
      } else {
        uploadFileType = fields.uploadFileType
      }

      const requestFormData = {
        entity_id: fields.uniqueCustId,
        document_type: uploadFileType,
        document_number: fields.document_no,
        front_photo: fields.doc_front_link,
        back_photo: fields.doc_back_link,
        doc_expiry: fields.document_expiry_date,
        device_id: fields.device_id,
        ip: fields.ip_address
      }

      // get customer details

      const sql = `SELECT CONCAT(pc.title,'. ', pc.first_name, ' ', pc.last_name) AS full_name, CONCAT(pc.mobile_code,'',pc.mobile_no) as mobile_no, pc.email, pc.passport_no, pc.visa_no, pc.customer_type, pc.reference_id, pc.ma_prepaid_card_id,'PASSPORT' as doc_type_name_one, 'VISA' as doc_type_name_two, pcpm.program_name, pc.program_type_id FROM ma_prepaid_card pc Join ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = pc.program_type_id WHERE pc.customer_id = '${fields.uniqueCustId}' and pc.card_status = 'A' limit 1 `
      const customerInfoDetails = await this.rawQuery(sql, connection)
      let customer_type = ''
      if (customerInfoDetails.length < 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid Customer Id' }
      } else {
        /* application_no: customerInfoDetails[0].ma_prepaid_card_id,
          reference_id: customerInfoDetails[0].reference_id,
          program_name: customerInfoDetails[0].program_name,
          customer_type: customerInfoDetails[0].customer_type,
          customer_name: customerInfoDetails[0].full_name,
          customer_mobile_no: customerInfoDetails[0].mobile_no,
          country_name: customerInfoDetails[0].country,
          customer_email: customerInfoDetails[0].email,
          passport_no: customerInfoDetails[0].passport_no,
          visa_no: customerInfoDetails[0].visa_no
          document_proof_one_name: customerInfoDetails[0].doc_type_name_one,
          document_proof_one_no: customerInfoDetails[0].passport_no,
          document_proof_two_name: customerInfoDetails[0].doc_type_name_two,
          document_proof_two_no: customerInfoDetails[0].visa_no,
          document_identity_proof: '',
          document_identity_proof_no: '',
          customer_pan_no: '' */
        customer_type = customerInfoDetails[0].customer_type
      }

      const requestDataParam = qs.stringify(requestFormData)

      const param_string = fields.uniqueCustId + '' + fields.uploadFileType + '' + fields.document_no + '' + fields.doc_front_link + '' + fields.doc_back_link + '' + fields.document_expiry_date + '' + fields.device_id + '' + fields.ip_address + CHECKSUM_SECRETKEY

      // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
      log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'requestData_param_string', fields: param_string })
      const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')

      log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'checksum_key', fields: checksum_key })

      // Call API *****
      const customer_id = ''
      let PCODE = ''
      if (customerInfoDetails[0].program_type_id == 1) {
        PCODE = P_CODE_GOA_GOV
      } else if (customerInfoDetails[0].program_type_id == 2) {
        PCODE = P_CODE_SEVA_MONEY
      }
      const uploadKycCustomerApiResponse = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: 'register', endpoint: KYC_UPLOAD_API_URL, pcode: PCODE })
      log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'uploadKycCustomerApiResponse', fields: uploadKycCustomerApiResponse })
      // let customer_id = ''
      if (uploadKycCustomerApiResponse.status == 200 && uploadKycCustomerApiResponse.axiosResponse.status == 200) {
        const customer_id = uploadKycCustomerApiResponse.axiosResponse.data.entityId
        // change date form from dd-mm-yyyy to yyyy
        console.log('doc Expiry Date ++++++>>', fields.document_expiry_date)
        // const expiry_date = fields.document_expiry_date
        let docExpiryDate = fields.document_expiry_date || ''
        if (docExpiryDate != '') {
          docExpiryDate = moment.tz(fields.document_expiry_date, 'Asia/Kolkata').format('YYYY-MM-DD ')
        }
        // const docExpiryDate = resultTime[0].expiry_dt
        console.log('docExpiryDate>>', docExpiryDate)
        if (customer_type == 'TOURIST') {
          switch (fields.uploadFileType) {
            case 'PASSPORT':
              updateString = " passport_no = '" + fields.document_no + "',  passport_front_link='" + fields.doc_front_link + "', passport_back_link='" + fields.doc_back_link + "', passport_kyc_verify = 'Y', passport_expiry_date = '" + docExpiryDate + "'  "
              break
            case 'VISA': // RESEND
              updateString = " visa_no = '" + fields.document_no + "',  visa_front_link='" + fields.doc_front_link + "', visa_back_link='" + fields.doc_back_link + "', visa_kyc_verify = 'Y', visa_expiry_date = '" + docExpiryDate + "' "
              break
            case 'LIVE_PHOTO': // VERIFY
              updateString = " live_photo_link = '" + fields.doc_front_link + "' "
              break
            case 'CAF' :
              updateString = " caf_link = '" + fields.doc_front_link + "' "
              break
            default:
              return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid request, Please try again.' }
          }
        } else if (customer_type == 'RESIDENT') {
          switch (fields.uploadFileType) {
            case 'VOTER_ID':
              updateString = " resident_kyc_type = 'OFFLINE_VERIFICATION', resident_kyc_sub_type = 'VOTER_ID', resident_kyc_doc_no = '" + fields.document_no + "',  resident_offline_file_front='" + fields.doc_front_link + "', resident_offline_file_back='" + fields.doc_back_link + "', resident_kyc_verify = 'P' "
              break
            case 'DL':
              updateString = " resident_kyc_type = 'OFFLINE_VERIFICATION', resident_kyc_sub_type = 'DL', resident_kyc_doc_no = '" + fields.document_no + "', resident_kyc_expiry_date = '" + docExpiryDate + "', resident_offline_file_front='" + fields.doc_front_link + "', resident_offline_file_back='" + fields.doc_back_link + "', resident_kyc_verify = 'P' "
              break
            case 'PASSPORT':
              updateString = " resident_kyc_type = 'OFFLINE_VERIFICATION', resident_kyc_sub_type = 'PASSPORT', resident_kyc_doc_no = '" + fields.document_no + "', resident_kyc_expiry_date = '" + docExpiryDate + "',  resident_offline_file_front='" + fields.doc_front_link + "', resident_offline_file_back='" + fields.doc_back_link + "', resident_kyc_verify = 'P' "
              break
            case 'NREGA':
              updateString = " resident_kyc_type = 'OFFLINE_VERIFICATION', resident_kyc_sub_type = 'NREGA', resident_kyc_doc_no = '" + fields.document_no + "',  resident_offline_file_front='" + fields.doc_front_link + "', resident_offline_file_back='" + fields.doc_back_link + "', resident_kyc_verify = 'P' "
              break
            case 'CAF' :
              updateString = " caf_link = '" + fields.doc_front_link + "' "
              break
            case 'LIVE_PHOTO': // VERIFY
              updateString = " live_photo_link = '" + fields.doc_front_link + "' "
              break
            default:
              return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid request, Please try again.' }
          }
        }

        const sql = `UPDATE ma_prepaid_card set ${updateString} WHERE customer_id = '${fields.uniqueCustId}' `
        log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'updateSql', fields: sql })
        const updateCustomerFileUploadResult = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'updateCustomerFileUploadResult', fields: updateCustomerFileUploadResult })
        return { status: 200, respcode: 1000, message: 'Success: ' + fields.uploadFileType + ' - KYC details updated successfully.' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + uploadKycCustomerApiResponse.message }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'uploadCustomerDoc', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'uploadCustomerDoc',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async fetchUploadedDocs (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'doc_type'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const sql1 = `SELECT  ma_prepaid_card_id, customer_type FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" and card_status = "A" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'sql1', fields: sql1 })
      const customerInfoDetails1 = await this.rawQuery(sql1, connection)
      log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'customerInfoDetails1', fields: customerInfoDetails1 })
      let customer_type = ''
      if (customerInfoDetails1.length == 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid Customer Id. ' }
      } else {
        customer_type = customerInfoDetails1[0].customer_type
      }

      const doc_type = fields.doc_type
      let select_str = ''
      if (customer_type == 'TOURIST') {
        switch (doc_type) {
          case 'PASSPORT':
            select_str = ' passport_no as doc_no,  passport_front_link as front_link, passport_back_link as back_link, passport_kyc_verify as kyc_verify, passport_expiry_date, "Passport" as  "doc_label" ,  "Y" as "required_back_pic_flag" '
            break
          case 'VISA':
            select_str = ' visa_no as doc_no, visa_front_link as front_link, visa_back_link as back_link, visa_kyc_verify as kyc_verify, visa_expiry_date, "Visa" as "doc_label",  "N" as "required_back_pic_flag" '
            break
          case 'LIVE_PHOTO':
            select_str = '"" as doc_no, live_photo_link as front_link, "LIVE PHOTO" as "doc_label", "N" as "required_back_pic_flag" '
            break
          case 'CAF':
            select_str = ' "" as doc_no,caf_link as front_link, "" as back_link, visa_kyc_verify as kyc_verify, visa_expiry_date, "CAF" as "doc_label",  "N" as "required_back_pic_flag" '
            break
          default:
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid request, Please try again.' }
        }

        const sql = 'SELECT  ma_prepaid_card_id, ' + select_str + ` FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" and card_status = "A" limit 1  `
        log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'sql', fields: sql })
        const customerInfoDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'customerInfoDetails', fields: customerInfoDetails })
        if (customerInfoDetails.length > 0) {
          const customerDetails = {
            ma_prepaid_card_id: customerInfoDetails[0].ma_prepaid_card_id,
            kyc_verify: customerInfoDetails[0].kyc_verify,
            doc_no: customerInfoDetails[0].doc_no,
            front_link: customerInfoDetails[0].front_link,
            back_link: customerInfoDetails[0].back_link,
            doc_label: customerInfoDetails[0].doc_label,
            required_back_pic_flag: customerInfoDetails[0].required_back_pic_flag
          }

          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uploadedCustomerDocs: customerDetails }
        } else {
          return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
        }
      } else {
        switch (doc_type) {
          case 'PASSPORT':
            select_str = ' resident_kyc_doc_no as doc_no, resident_offline_file_front as front_link, resident_offline_file_back as back_link, resident_kyc_verify as kyc_verify, resident_kyc_expiry_date, "Passport" as  "doc_label" ,  "Y" as "required_back_pic_flag" '
            break
          case 'VOTER_ID':
            select_str = ' resident_kyc_doc_no as doc_no, resident_offline_file_front as front_link, resident_offline_file_back as back_link, resident_kyc_verify as kyc_verify, resident_kyc_expiry_date, "Voter ID" as "doc_label",  "Y" as "required_back_pic_flag" '
            break
          case 'NREGA':
            select_str = ' resident_kyc_doc_no as doc_no, resident_offline_file_front as front_link, resident_offline_file_back as back_link, resident_kyc_verify as kyc_verify, resident_kyc_expiry_date, "NREGA" as "doc_label",  "Y" as "required_back_pic_flag" '
            break
          case 'DL':
            select_str = ' resident_kyc_doc_no as doc_no, resident_offline_file_front as front_link, resident_offline_file_back as back_link, resident_kyc_verify as kyc_verify, resident_kyc_expiry_date, "Driving License" as "doc_label",  "Y" as "required_back_pic_flag" '
            break
          case 'LIVE_PHOTO':
            select_str = '"" as doc_no, live_photo_link as front_link, "LIVE PHOTO" as "doc_label", "N" as "required_back_pic_flag" '
            break
          case 'CAF':
            select_str = ' "" as doc_no,caf_link as front_link, "" as back_link, visa_kyc_verify as kyc_verify, visa_expiry_date, "CAF" as "doc_label",  "N" as "required_back_pic_flag" '
            break
          default:
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid request, Please try again.' }
        }

        const whereStr = ''
        /* if (customer_type != 'TOURIST') {
          if (doc_type == 'PASSPORT') {
            whereStr = " AND resident_kyc_sub_type = 'PASSPORT'"
          } else if (doc_type == 'NREGA') {
            whereStr = " AND resident_kyc_sub_type = 'NREGA'"
          } else if (doc_type == 'VOTER_ID') {
            whereStr = " AND resident_kyc_sub_type = 'VOTER_ID'"
          } else if (doc_type == 'DL') {
            whereStr = " AND resident_kyc_sub_type = 'DL'"
          }
        } */

        const sql = 'SELECT  ma_prepaid_card_id, resident_kyc_sub_type, ' + select_str + ` FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" and card_status = "A" ${whereStr} limit 1  `
        log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'sql', fields: sql })
        const customerInfoDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'customerInfoDetails', fields: customerInfoDetails })
        if (customerInfoDetails.length > 0) {
          let customerDetails = ''
          if (['LIVE_PHOTO', 'CAF'].includes(fields.doc_type)) {
            customerDetails = {
              ma_prepaid_card_id: customerInfoDetails[0].ma_prepaid_card_id,
              kyc_verify: customerInfoDetails[0].kyc_verify,
              doc_no: customerInfoDetails[0].doc_no,
              front_link: customerInfoDetails[0].front_link,
              back_link: customerInfoDetails[0].back_link,
              doc_label: customerInfoDetails[0].doc_label,
              required_back_pic_flag: customerInfoDetails[0].required_back_pic_flag
            }
          } else if (['NREGA', 'VOTER_ID', 'DL', 'PASSPORT'].includes(fields.doc_type) && customerInfoDetails[0].resident_kyc_sub_type == fields.doc_type) {
            customerDetails = {
              ma_prepaid_card_id: customerInfoDetails[0].ma_prepaid_card_id,
              kyc_verify: customerInfoDetails[0].kyc_verify,
              doc_no: customerInfoDetails[0].doc_no,
              front_link: customerInfoDetails[0].front_link,
              back_link: customerInfoDetails[0].back_link,
              doc_label: customerInfoDetails[0].doc_label,
              required_back_pic_flag: customerInfoDetails[0].required_back_pic_flag
            }
          } else if (customerInfoDetails[0].resident_kyc_sub_type == null || customerInfoDetails[0].resident_kyc_sub_type == '') {
            customerDetails = { ma_prepaid_card_id: customerInfoDetails[0].ma_prepaid_card_id, kyc_verify: customerInfoDetails[0].kyc_verify, doc_no: null, front_link: null, back_link: null, doc_label: fields.doc_type, required_back_pic_flag: 'Y' }
          } else {
            customerDetails = { ma_prepaid_card_id: customerInfoDetails[0].ma_prepaid_card_id, kyc_verify: customerInfoDetails[0].kyc_verify, doc_no: null, front_link: null, back_link: null, doc_label: fields.doc_type, required_back_pic_flag: 'Y' }
          }

          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uploadedCustomerDocs: customerDetails }
        } else {
          return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
        }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'fetchUploadedDocs', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getCustomerInfo',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async getKycFileUploadType (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getKycFileUploadType', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const sql = `SELECT  ma_prepaid_card_id, passport_kyc_verify, visa_kyc_verify,  visa_front_link, live_photo_link,passport_front_link, caf_link, resident_kyc_type, resident_kyc_sub_type, digi_aadhaar_response,customer_type FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" and card_status = "A" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'getKycFileUploadType', type: 'sql', fields: sql })
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getKycFileUploadType', type: 'customerInfoDetails', fields: customerInfoDetails })
      let visa_kyc_verify = ''
      let passport_kyc_verify = ''
      let caf_link = ''
      let live_photo_link = ''
      let visa_front_link = ''
      let passport_front_link = ''
      let resident_kyc_type = ''
      if (customerInfoDetails.length == 0) {
        return { status: 400, message: errorMsg.responseCode[1028] + ': Invalid Customer Id.', respcode: 1028 }
      } else {
        visa_kyc_verify = customerInfoDetails[0].visa_kyc_verify
        visa_front_link = customerInfoDetails[0].visa_front_link
        passport_front_link = customerInfoDetails[0].passport_front_link
        passport_kyc_verify = customerInfoDetails[0].passport_kyc_verify
        caf_link = customerInfoDetails[0].caf_link
        live_photo_link = customerInfoDetails[0].live_photo_link
        resident_kyc_type = customerInfoDetails[0].resident_kyc_type

        let fileTypes = ''
        switch (fields.resident_type) {
          case 'RESIDENT':
            fileTypes = [
              {
                doc_id: 'NREGA',
                doc_label: 'NREGA',
                has_document: (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION' && customerInfoDetails[0].resident_kyc_sub_type == 'NREGA') ? 'true' : 'false',
                doc_icon: 'url',
                required_back_pic_flag: 'Y'
              },
              {
                doc_id: 'DL',
                doc_label: 'Driving License',
                has_document: (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION' && customerInfoDetails[0].resident_kyc_sub_type == 'DL') ? 'true' : 'false',
                doc_icon: 'url',
                required_back_pic_flag: 'Y'
              },
              {
                doc_id: 'VOTER_ID',
                doc_label: 'Voter Id',
                has_document: (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION' && customerInfoDetails[0].resident_kyc_sub_type == 'VOTER_ID') ? 'true' : 'false',
                doc_icon: 'url',
                required_back_pic_flag: 'Y'
              },
              {
                doc_id: 'PASSPORT',
                doc_label: 'Passport',
                has_document: (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION' && customerInfoDetails[0].resident_kyc_sub_type == 'PASSPORT') ? 'true' : 'false',
                doc_icon: 'url',
                required_back_pic_flag: 'Y'
              }
            ]
            break
          case 'TOURIST' :
            fileTypes = [
              {
                doc_id: 'PASSPORT',
                doc_label: 'Passport',
                has_document: passport_kyc_verify == 'Y' ? 'true' : 'false',
                doc_icon: 'url',
                required_back_pic_flag: 'Y'
              },
              {
                doc_id: 'VISA',
                doc_label: 'Visa',
                has_document: visa_kyc_verify == 'Y' ? 'true' : 'false',
                doc_icon: 'url',
                required_back_pic_flag: 'N'
              }
            ]
            break
          default:
            return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid request, Please try again.' }
        }

        log.logger({ pagename: path.basename(__filename), action: 'getKycFileUploadType', type: 'uploadFileTypes', fields: fileTypes })
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uploadFileTypes: fileTypes }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getKycFileUploadType', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getKycFileUploadType',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async maSideApiAxiosCall ({ requestData, checksum, stage = '', endpoint, token, timeout = null, pcode = '' }) {
    log.logger({ pagename: path.basename(__filename), action: 'maSideApiAxiosCall', type: 'request', fields: { requestData, endpoint, timeout } })
    let response = {}
    try {
      // if (timeout) th/is.CONFIG.timeout = timeout
      console.log('get timeśout val ', timeout)
      let configTimeout = ''
      if (timeout) {
        configTimeout = timeout
      } else {
        configTimeout = 120000
      }
      console.log('get stage value ', stage)
      let stageHeader = ''
      if (stage) {
        stageHeader = 'STAGE:' + stage
      }
      let program_code = ''
      if (pcode) {
        program_code = pcode
      }

      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        AFFILIATE: AFFILIATE,
        AIRPAYKEY: AIRPAY_KEY,
        PCODE: program_code,
        CHECKSUM: checksum,
        STAGE: stage
      }

      if (token) {
        headers.token = token
      }

      response = await axios({
        method: 'POST',
        data: requestData,
        url: BASE_URL + '' + endpoint,
        // ...this.CONFIG
        headers,
        timeout: configTimeout
      })

      log.logger({ pagename: path.basename(__filename), action: 'maSideApiAxiosCall', type: `${endpoint}(${endpoint} API) - response`, fields: response })

      if (response != '' && response.status === 200) {
        const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
        console.log('responseData>>>>>>>', responseData)
        if (responseData.status == 200) {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], axiosResponse: responseData, action_code: 1000 }
        } else {
          let errorMessage = ''
          if (typeof responseData.message === 'object' && responseData.message !== null) {
            errorMessage = Object.values(responseData.message)
            console.log('response : ', Object.values(responseData.message))
          } else {
            errorMessage = responseData.message
          }
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + errorMessage, action_code: 1001 }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'maSideApiAxiosCall', type: 'response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving customer details', action_code: 1001 }
      }
    } catch (axiosErr) {
      log.logger({ pagename: path.basename(__filename), action: 'maSideApiAxiosCall', type: `${endpoint}(${endpoint} API) - axios catcherror`, fields: axiosErr })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    }
  }

  static async assignCardKitSuccessMail (user_emailid, templatedata) {
    const responseUrl = util[process.env.NODE_ENV].ENV_DOMAIN
    var removeUselessWords = function (txt) {
      var uselessWordsArray =
        [
          'Dear Customer, ', 'Team airpay vyaapaar', 'Regards, Team airpay vyaapaar. '
        ]

      var expStr = uselessWordsArray.join('|')
      return txt.replace(new RegExp('\\b(' + expStr + ')\\b', 'gi'), ' ')
        .replace(/\s{2,}/g, ' ')
    }

    const template_content = removeUselessWords(templatedata)
    const mailerData = {
      template: 'assignCardKitSuccessMail.ejs',
      content: {
        url: responseUrl,
        body: template_content
      },
      from: '"Airpay" <<EMAIL>>',
      to: [user_emailid],
      subject: 'Assign Card Kit Success - Airpay Prepaid Card'
    }
    const mailResponse = await mailer(mailerData)
    log.logger({ pagename: require('path').basename(__filename), action: 'assignCardKitSuccessMail', type: 'Mail response', fields: mailResponse })
  }

  static async activatePrepaidCard ({ uniqueCustId, mobile_number, customer_email, lastFour = '', PCODE = '' }) {
    // call Generate Token API
    const token = await this.generateToken({ mobile_number, PCODE })
    console.log('TOKEN issue Card: ', token)

    const requestFormData = {
      entity_id: uniqueCustId,
      card_e4: lastFour
    }

    const requestDataParam = qs.stringify(requestFormData)

    const param_string = uniqueCustId + '' + lastFour + CHECKSUM_SECRETKEY

    log.logger({ pagename: path.basename(__filename), action: 'activatePrepaidCard', type: 'requestData_param_string', fields: param_string })
    const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')

    const activateCardAPI = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: '', endpoint: ACTIVATE_CARD_URL, token })

    if (activateCardAPI.status == 200 && activateCardAPI.axiosResponse.status == 200) {
      const rc_number = activateCardAPI.axiosResponse.data[0].rc
      if (rc_number == '201') {
        const common = require('../../util/common')
        let commMessage = ''
        let template = ''

        commMessage = util.communication.PREPAIDCARDACTIVATION
        template = util.templateid.PREPAIDCARDACTIVATION
        const sms = require('../../util/sms')
        // var dt = new Date(date_time)
        // const datetimeformat = dt.toLocaleString('en-IN')
        // commMessage = commMessage.replace('<CUSTID>', uniqueCustId)
        console.log('common message ', commMessage)
        await sms.sentSmsAsync(commMessage, mobile_number, template)
        const mailResponse = this.assignCardKitSuccessMail(customer_email, commMessage)
        return { status: 200, respcode: 1000, message: 'Success: Card Assigned & Activated Successfully.' }
      } else {
        return { status: 400, respcode: 1001, message: 'Invalid activate card response, Please try again !' }
      }
    } else {
      return { status: 400, respcode: 1001, message: activateCardAPI.message }
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async issueCardKit (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get customer info
      // get  mobile & email from table
      const sqlCust1 = `SELECT  ma_prepaid_card_id, mobile_no, email, card_kit_no, customer_card_no, program_type_id FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'sqlCust1', fields: sqlCust1 })
      const customerInfoDetails1 = await this.rawQuery(sqlCust1, connection)
      log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'customerInfoDetails1', fields: customerInfoDetails1 })
      if (customerInfoDetails1.length > 0) {
        const customer_email = customerInfoDetails1[0].email
        const mobile_number = customerInfoDetails1[0].mobile_no
        const card_kit_no = customerInfoDetails1[0].card_kit_no == null ? '' : customerInfoDetails1[0].card_kit_no
        const customer_card_no = customerInfoDetails1[0].customer_card_no == null ? '' : (customerInfoDetails1[0].customer_card_no).toString()
        // const card_number = issueCardKitApiResponse.axiosResponse.data.card_number

        // PCODE
        let PCODE = ''
        if (customerInfoDetails1[0].program_type_id == 1) {
          PCODE = P_CODE_GOA_GOV
        } else if (customerInfoDetails1[0].program_type_id == 2) {
          PCODE = P_CODE_SEVA_MONEY
        }

        if (card_kit_no == fields.kitNo && (customer_card_no != null || customer_card_no != '')) {
          console.log('Kit no & card no. exists')
          var lastFour = customer_card_no.slice(-4)
          console.log('last four digits ', lastFour)
          const activateCardAPI = await this.activatePrepaidCard({ uniqueCustId: fields.uniqueCustId, mobile_number, customer_email, lastFour, PCODE })
          if (activateCardAPI.status == 200) {
            const updateString = "  card_kit_assign ='P' "

            const sql = `UPDATE ma_prepaid_card set ${updateString} WHERE customer_id = '${fields.uniqueCustId}' `
            log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateSql', fields: sql })
            const updateCustomerFileUploadResult = await this.rawQuery(sql, connection)
            log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateCustomerFileUploadResult', fields: updateCustomerFileUploadResult })
          } else {
            /* const sql = `UPDATE ma_prepaid_card set card_kit_fail_reason = '${issueCardKitApiResponse.message}' WHERE customer_id = '${fields.uniqueCustId}' `
            log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateSql', fields: sql })
            const updateCustomerFileUploadResult = await this.rawQuery(sql, connection)
            log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateCustomerFileUploadResult', fields: updateCustomerFileUploadResult }) */
          }
          return activateCardAPI
        } else {
          let updateString = ''
          const requestFormData = {
            entity_id: fields.uniqueCustId,
            kit_no: fields.kitNo
          }

          const requestDataParam = qs.stringify(requestFormData)

          const param_string = fields.uniqueCustId + '' + fields.kitNo + CHECKSUM_SECRETKEY

          // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
          log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'requestData_param_string', fields: param_string })
          const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')

          log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'checksum_key', fields: checksum_key })

          // Call API *****
          const customer_id = ''
          let PCODE = ''
          if (customerInfoDetails1[0].program_type_id == 1) {
            PCODE = P_CODE_GOA_GOV
          } else if (customerInfoDetails1[0].program_type_id == 2) {
            PCODE = P_CODE_SEVA_MONEY
          }
          const issueCardKitApiResponse = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: '', endpoint: ASSIGN_CARD_API_URL, pcode: PCODE })
          log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'issueCardKitApiResponse', fields: issueCardKitApiResponse })
          // let customer_id = ''
          if (issueCardKitApiResponse.status == 200 && issueCardKitApiResponse.axiosResponse.status == 200) {
            const customer_id = issueCardKitApiResponse.axiosResponse.data.entityId
            // change date form from dd-mm-yyyy to yyyy
            console.log('doc Expiry Date ++++++>>', fields.document_expiry_date)

            // Call Activate Card (MS) API
            const card_number = (issueCardKitApiResponse.axiosResponse.data.card_number).toString()
            const lastFour = card_number.slice(-4)
            console.log('last four digits ', lastFour)

            // call activateCardAPI
            const activateCardAPI = await this.activatePrepaidCard({ uniqueCustId: fields.uniqueCustId, mobile_number, customer_email, lastFour, PCODE })
            if (activateCardAPI.status == 200) {
              updateString = " card_kit_no = '" + fields.kitNo + "',  ms_customer_id='" + issueCardKitApiResponse.axiosResponse.data.customer_id + "', customer_card_no='" + issueCardKitApiResponse.axiosResponse.data.card_number + "', card_expiry_date = '" + issueCardKitApiResponse.axiosResponse.data.expiry_date + "', card_kit_assign ='P', card_kit_fail_reason = 'N/A' "

              const sql = `UPDATE ma_prepaid_card set ${updateString} WHERE customer_id = '${fields.uniqueCustId}' `
              log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateSql', fields: sql })
              const updateCustomerFileUploadResult = await this.rawQuery(sql, connection)
              log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateCustomerFileUploadResult', fields: updateCustomerFileUploadResult })
            } else {
              const sql = `UPDATE ma_prepaid_card set card_kit_fail_reason = '${issueCardKitApiResponse.message}' WHERE customer_id = '${fields.uniqueCustId}' `
              log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateSql', fields: sql })
              const updateCustomerFileUploadResult = await this.rawQuery(sql, connection)
              log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateCustomerFileUploadResult', fields: updateCustomerFileUploadResult })
            }

            return activateCardAPI
          } else {
            const sql = `UPDATE ma_prepaid_card set card_kit_fail_reason = '${issueCardKitApiResponse.message}' WHERE customer_id = '${fields.uniqueCustId}' `
            log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateSql', fields: sql })
            const updateCustomerFileUploadResult = await this.rawQuery(sql, connection)
            log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'updateCustomerFileUploadResult', fields: updateCustomerFileUploadResult })
            return { status: 400, respcode: 1001, message: issueCardKitApiResponse.message }
          }
        }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'issueCardKit', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'issueCardKit',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async generateCafPdf (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'generateCafPdf', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const sql = `SELECT CONCAT(pc.title,'. ', pc.first_name, ' ', pc.last_name) AS full_name, pc.mobile_code,pc.mobile_no, pc.email, pc.dob, mc.nicename  as country, pc.passport_no, pc.visa_no, pc.customer_type, pc.reference_id, pc.ma_prepaid_card_id, pc.resident_kyc_type, pc.resident_kyc_sub_type, pc.resident_kyc_doc_no, pc.customer_pan_no, pc.address,pc.city,pc.state_id,pc.pincode, 'PASSPORT' as doc_type_name_one, 'VISA' as doc_type_name_two, pcpm.program_name, sm.name as state_name FROM ma_prepaid_card pc Join ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = pc.program_type_id LEFT JOIN ma_country mc on pc.country_id = mc.id LEFT JOIN ma_states_master sm on pc.state_id = sm.id WHERE pc.customer_id = '${fields.uniqueCustId}' and pc.card_status = 'A' limit 1 `
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'generateCafPdf', type: 'customerInfoDetails', customerInfoDetails })
      if (customerInfoDetails.length > 0) {
        let customerDetails = ''
        const moment = require('moment')
        const dob = customerInfoDetails[0].dob
        var dateOfBirth = moment(dob).format('DD/MM/YYYY')

        if (customerInfoDetails[0].customer_type == 'TOURIST') {
          customerDetails = {
            application_no: customerInfoDetails[0].ma_prepaid_card_id,
            reference_id: customerInfoDetails[0].reference_id,
            program_name: customerInfoDetails[0].program_name,
            customer_type: customerInfoDetails[0].customer_type,
            customer_name: customerInfoDetails[0].full_name,
            customer_dob: dateOfBirth,
            country_code: '+' + customerInfoDetails[0].mobile_code,
            customer_mobile_no: customerInfoDetails[0].mobile_no,
            country_name: customerInfoDetails[0].country,
            customer_email: customerInfoDetails[0].email,
            passport_no: customerInfoDetails[0].passport_no,
            visa_no: customerInfoDetails[0].visa_no,
            document_proof_one_name: customerInfoDetails[0].doc_type_name_one,
            document_proof_one_no: customerInfoDetails[0].passport_no,
            document_proof_two_name: customerInfoDetails[0].doc_type_name_two,
            document_proof_two_no: customerInfoDetails[0].visa_no,
            document_identity_proof: '',
            document_identity_proof_no: '',
            customer_pan_no: ''
          }
        } else {
          let document_proof_one_name = ''
          let document_proof_one_no = ''
          let document_proof_two_name = ''
          let document_proof_two_no = ''

          if (customerInfoDetails[0].resident_kyc_type == 'DIGI_LOCKER' || customerInfoDetails[0].resident_kyc_type == 'AADHAAR_EKYC') {
            document_proof_one_name = 'AADHAAR No.'
            document_proof_one_no = customerInfoDetails[0].resident_kyc_doc_no
            document_proof_two_name = 'PAN Card No.'
            document_proof_two_no = customerInfoDetails[0].customer_pan_no
          }

          if (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION') {
            let doc_name_one = ''
            if (customerInfoDetails[0].resident_kyc_sub_type == 'DL') {
              doc_name_one = 'DRIVING LICENSE'
            } else if (customerInfoDetails[0].resident_kyc_sub_type == 'VOTER_ID') {
              doc_name_one = 'VOTER ID'
            } else {
              doc_name_one = customerInfoDetails[0].resident_kyc_sub_type
            }
            document_proof_one_name = doc_name_one
            document_proof_one_no = customerInfoDetails[0].resident_kyc_doc_no
            document_proof_two_name = 'PAN Card No.'
            document_proof_two_no = customerInfoDetails[0].customer_pan_no
          }

          customerDetails = {
            application_no: customerInfoDetails[0].ma_prepaid_card_id,
            reference_id: customerInfoDetails[0].reference_id,
            program_name: customerInfoDetails[0].program_name,
            customer_type: customerInfoDetails[0].customer_type,
            customer_name: customerInfoDetails[0].full_name,
            customer_dob: dateOfBirth,
            country_code: '+' + customerInfoDetails[0].mobile_code,
            customer_mobile_no: customerInfoDetails[0].mobile_no,
            // country_name: customerInfoDetails[0].country,
            customer_email: customerInfoDetails[0].email,
            customer_address: customerInfoDetails[0].address,
            customer_city: customerInfoDetails[0].city,
            customer_state: customerInfoDetails[0].state_name,
            customer_pincode: customerInfoDetails[0].pincode,
            passport_no: '',
            visa_no: '',
            document_proof_one_name: '',
            document_proof_one_no: '',
            document_proof_two_name: '',
            document_proof_two_no: '',
            document_identity_proof: document_proof_one_name,
            document_identity_proof_no: document_proof_one_no,
            customer_pan_no: customerInfoDetails[0].customer_pan_no
          }
        }

        // call admin API - to create CAF pdf, send the path in response
        let response = {}

        // const requestDataParam = qs.stringify(customerDetails)

        response = await axios({
          method: 'POST',
          data: customerDetails,
          url: GENERATE_CAF_PDF,
          // ...this.CONFIG
          headers: {
            'Content-Type': 'application//json'
          },
          timeout: 120000
        })

        log.logger({ pagename: path.basename(__filename), action: 'generateCafPdf', type: `${GENERATE_CAF_PDF} API) - response`, fields: response })

        if (response != '' && response.status === 200) {
          const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
          if (responseData.status == 200) {
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], customerDetails: responseData, action_code: 1000 }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
          }
        } else {
          log.logger({ pagename: require('path').basename(__filename), action: 'generateCafPdf', type: 'response', fields: {} })
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving customer details', action_code: 1001 }
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'generateCafPdf', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'generateCafPdf',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async calculateCharges (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'calculateCharges', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromPool()

    try {
      // Validate All Required Fields - Add parameter name in array for fields validation
      const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'customer_id', 'amount'])
      if (validatorResponse.status != 200) return validatorResponse
      if (parseInt(fields.amount) < 500) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid Amount, Amount should be greater than 500. ' }
      }
      const customer = await this.getCustomerDetails({ customer_id: fields.customer_id, connectionRead })

      // save remarks in prepaid card table

      const sql = `UPDATE ma_prepaid_card set topup_remarks = '${fields.remarks}' WHERE customer_id = '${fields.customer_id}' `
      log.logger({ pagename: path.basename(__filename), action: 'calculateCharges', type: 'updateSql', fields: sql })
      const saveCustomerOtpResult = await this.rawQuery(sql, connectionRead)
      log.logger({ pagename: path.basename(__filename), action: 'calculateCharges', type: 'saveCustomerOtpResult', fields: saveCustomerOtpResult })

      const distribution = await prepaidCardDistributionController.getDistribution({ ...fields, customer_type: customer.customer_type, program_id: customer.program_id })

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        ...distribution,
        topup_amount: fields.amount,
        total_amount: parseFloat(distribution.total_customer_charges) + parseFloat(distribution.gst) + parseFloat(fields.amount),
        airpay_charges: parseFloat(distribution.card_issuance_fee) + parseFloat(distribution.airpay_charges)
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'calculateCharges', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'calculateCharges',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connectionRead.release()
    }
  }

  static async verifyPin (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'request', fields: fields })

    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'amount', 'pin', 'aggregator_order_id', 'customer_id'])
    console.log('Response:', validatorResponse)
    if (validatorResponse.status != 200) return validatorResponse
    try {
      const { ma_user_id, userid, amount, pin, aggregator_order_id, customer_id } = fields

      // CHECK MERCHANT DETAILS
      const merchant = await this.getMerchantDetail(ma_user_id, userid, connectionRead)
      if (merchant.status != 200) return merchant

      fields.merchant_mobile_number = merchant.mobile_number
      // CHECK CUSTOMER DETAILS
      const customer = await this.getCustomerDetails({ customer_id, connectionRead })
      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'customer_detail', fields: customer })

      // Verify Pin
      const securePin = await securePinCtrl.verifySecurePin(null, {
        ma_user_id,
        userid,
        security_pin: pin,
        connection
      })

      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'securePin', fields: securePin })
      if (securePin.status != 200) return securePin

      // Check Available
      const availableBalance = await BalanceController.getWalletBalancesDirect('_', {
        ma_user_id: ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection: connectionRead
      })
      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'availableBalance', fields: availableBalance })
      if (availableBalance.amount < fields.amount) return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], action_code: 1001 }

      // GET CUSTOMER CHARGES FOR POINTS LEDGER & TRANSACTION MASTER
      const customer_charges = await prepaidCardDistributionController.getDistribution({ ma_user_id, amount, program_id: customer.program_id, customer_type: customer.customer_type, connectionRead })
      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'customer_charges', fields: customer_charges })

      if (customer_charges.status == 400) {
        return { status: 400, respcode: 1001, message: customer_charges.message }
      }

      let point_factor = 1
      const pointsFactorData = await PointsRateController.getGlobalPointsRate('_', { connection: connectionRead })
      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'pointsFactorData', fields: pointsFactorData })

      if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
        point_factor = pointsFactorData.points_value
      }

      fields.points_factor = point_factor
      fields.mobile_number = customer.mobile_no
      // pass customer name, mobile_no, email for transaction master details table
      fields.customer_name = customer.full_name
      fields.customer_mobile = customer.mobile_no
      fields.customer_email = customer.email

      // Initiate Transaction
      const initTransaction = await this.initiateTransaction({ fields: { ...fields, transaction_charges: (customer_charges.total_customer_charges + customer_charges.gst), transaction_type: '59' }, connection, connectionRead })
      if (initTransaction.status != 200) {
        return initTransaction
      }
      // Points Ledger
      const ledgerEntries = await this.ledgerEntries({ ma_user_id, userid, amount, aggregator_order_id, customer_charges: (customer_charges.total_customer_charges + customer_charges.gst), commissionAmount: customer_charges.gst, issuance_fee: customer_charges.card_issuance_fee, customer_type: customer.customer_type, program_id: customer.program_id, gst_rate: customer_charges.gst_rate, connection, connectionRead })
      if (ledgerEntries.status == 400) {
        return ledgerEntries
      }

      // TOPUP API
      const topupAPI = await this.topupAPI({ ma_user_id, userid, amount, aggregator_order_id, customer_id, mobile_number: customer.mobile_no, program_id: customer.program_id })
      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'topupAPI-response', fields: topupAPI })
      if (topupAPI.status != 200) {
        // REVERSE LEDGER API & UPDATE TRANSACTION
        await prepaidCardDistributionController.reverseLedgerEntries({ ma_user_id, userid, aggregator_order_id, amount, transaction_charge: (customer_charges.gst + customer_charges.total_customer_charges), connection, connectionRead })

        await prepaidCardDistributionController.reverseIncentiveEntries({ ma_user_id, userid, aggregator_order_id, connection, connectionRead })

        const updateTxn = `UPDATE ma_transaction_master set transaction_status = 'F' where aggregator_order_id = '${aggregator_order_id}'`
        await this.rawQuery(updateTxn, connection)

        // Entry in table
        await this.insertTopupTxn({ fields: { ma_user_id, userid, amount, aggregator_order_id, customer_id, mpin_verify: 'P', transaction_status: 'F', topup_remarks: customer.topup_remarks }, connection })

        return { status: 400, respcode: 1001, message: topupAPI.message }
      }

      const updateTxn = `UPDATE ma_transaction_master set transaction_status = 'S' where aggregator_order_id = '${aggregator_order_id}'`
      await this.rawQuery(updateTxn, connection)

      // Entry in table
      await this.insertTopupTxn({ fields: { ma_user_id, userid, amount, aggregator_order_id, customer_id, mpin_verify: 'P', transaction_status: 'S', topup_remarks: customer.topup_remarks }, connection })

      const merchantBalance = await this.getMerchantWalletBalance({ ma_user_id, userid, connectionRead })
      const customerBalance = await this.getCustomerWalletBalance({ entity_id: customer_id, mobile_number: customer.mobile_no, program_id: customer.program_id })

      console.log('WALLET_BALANCE: ', merchantBalance, customerBalance)

      // Send sms to customer after transaction is successful
      const customer_templateid = util.templateid.PREPAIDCARDTOPUPCUSTOMER
      let topupMsg = util.communication.PREPAIDCARDTOPUPCUSTOMER
      topupMsg = topupMsg.replace('<CARDNO>', customer.card_kit_no)
      topupMsg = topupMsg.replace('<TRANSACTIONAMOUNT>', amount)
      topupMsg = topupMsg.replace('<AVAILABLEBALANCE>', customerBalance)
      await sms.sentSmsAsync(topupMsg, customer.mobile_no, customer_templateid)

      // Send SMS to merchant after transaction is successful
      const merchant_templateid = util.templateid.PREPAIDCARDTOPUPMERCHANT
      let merchantMsg = util.communication.PREPAIDCARDTOPUPMERCHANT
      merchantMsg = merchantMsg.replace('<TRANSACTIONAMOUNT>', amount)
      merchantMsg = merchantMsg.replace('<CARDNO>', customer.card_kit_no)
      merchantMsg = merchantMsg.replace('<BALANCE>', merchantBalance)
      await sms.sentSmsAsync(merchantMsg, merchant.mobile_number, merchant_templateid)

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], aggregator_order_id }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'verifyPin', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'verifyPin',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  static async initiateTransaction ({ fields, connection, connectionRead }) {
    log.logger({ pagename: path.basename(__filename), action: 'initiateTransaction', type: 'request', fields: fields })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      /* create transaction & transaction details  */
      await mySQLWrapper.beginTransaction(conn)
      const transactionFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        aggregator_order_id: fields.aggregator_order_id,
        points_factor: fields.points_factor,
        transaction_id: fields.aggregator_order_id,
        amount: fields.amount,
        commission_amount: fields.transaction_charges,
        transaction_type: fields.transaction_type,
        transaction_status: 'I',
        remarks: fields.remarks || '',
        customer_name: fields.customer_name,
        customer_mobile: fields.customer_mobile,
        customer_email: fields.customer_email
      }
      const transactionResult = await TransactionController.initiateTransaction('_', transactionFields)
      log.logger({ pagename: path.basename(__filename), action: 'initiateTransaction', type: 'transactionResult', fields: transactionResult })
      if (transactionResult.status != 200) {
        await mySQLWrapper.rollback(conn)
        return transactionResult
      }

      await mySQLWrapper.commit(conn)
      return transactionResult
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'initiateTransaction', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'initiateTransaction',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  static async getCustomerDetails ({ customer_id, connectionRead }) {
    log.logger({ pagename: path.basename(__filename), action: 'getCustomerDetails', type: 'request', fields: customer_id })

    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const customerDetailQuery = `SELECT CONCAT(title,'. ', first_name, ' ', last_name) AS full_name,program_type_id as program_id, state_id, customer_type, customer_id, ma_prepaid_card_id, mobile_no, email, card_kit_no, topup_remarks from ma_prepaid_card where customer_id = '${customer_id}' and card_kit_assign = 'P' limit 1`
      const customerDetail = await this.rawQuery(customerDetailQuery, connectionRead)

      if (customerDetail.length < 1) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }

      return customerDetail[0]
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getCustomerDetails', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getCustomerDetails',
        data: { customer_id },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async ledgerEntries ({ ma_user_id, userid, amount, aggregator_order_id, issuance_fee, commissionAmount, customer_charges, program_id, customer_type, gst_rate, connection, connectionRead }) {
    log.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'request', fields: { ma_user_id, userid, amount, aggregator_order_id, issuance_fee, commissionAmount, customer_charges } })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      const ledgerEntries = await prepaidCardDistributionController.ledgerEntries({ ma_user_id, userid, amount, aggregator_order_id, customer_charges, commissionAmount, issuance_fee, gst_rate, connection })

      if (ledgerEntries.status == 400) {
        return ledgerEntries
      }

      // Incentive Entries
      const incentiveEntries = await prepaidCardDistributionController.incentiveEntries({ ma_user_id, userid, amount, aggregator_order_id, program_id, customer_type, connection: conn, connectionRead: connRead })

      if (incentiveEntries.status != 400) {
        return incentiveEntries
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'ledgerEntries', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'ledgerEntries',
        data: { ma_user_id, userid, amount, aggregator_order_id, issuance_fee, commissionAmount, customer_charges },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  static async insertTopupTxn ({ fields, connection }) {
    log.logger({ pagename: path.basename(__filename), action: 'insertTopupTxn', type: 'request', fields: fields })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const { ma_user_id, userid, customer_id, amount, mpin_verify, aggregator_order_id, transaction_status, topup_remarks } = fields
      const insertQuery = `INSERT INTO ma_prepaid_card_topup (ma_user_id, userid, customer_id, card_top_up_amount, mpin_verify, aggregator_order_id, transaction_status, topup_remarks) values (${ma_user_id}, ${userid}, '${customer_id}', '${amount}', '${mpin_verify}', '${aggregator_order_id}', '${transaction_status}', '${topup_remarks}')`
      log.logger({ pagename: path.basename(__filename), action: 'insertTopupTxn', type: 'insertQuery', fields: insertQuery })
      const insertTopupTxn = await this.rawQuery(insertQuery, conn)

      if (insertTopupTxn.affectedRows == 0) return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022], action_code: 1001 }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'insertTopupTxn', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'insertTopupTxn',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async topupAPI ({ ma_user_id, userid, amount, aggregator_order_id, customer_id, mobile_number, program_id, connection }) {
    log.logger({ pagename: path.basename(__filename), action: 'topupAPI', type: 'request', fields: { ma_user_id, userid, amount, aggregator_order_id, customer_id, program_id } })

    // Validate All Required Fields - Add parameter name in array for fields validation
    // const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'amount', 'customer_id', 'aggregator_order_id'])
    // if (validatorResponse.status != 200) return validatorResponse

    try {
      const requestFormData = {
        entity_id: customer_id,
        amount: amount,
        txn_id: aggregator_order_id
      }

      const requestDataParam = qs.stringify(requestFormData)

      const param_string = customer_id + '' + amount + '' + aggregator_order_id + CHECKSUM_SECRETKEY

      log.logger({ pagename: path.basename(__filename), action: 'topupAPI', type: 'requestData_param_string', fields: param_string })
      const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')

      // program code
      let PCODE = ''
      if (program_id) {
        if (program_id == 1) {
          PCODE = P_CODE_GOA_GOV
        } else if (program_id == 2) {
          PCODE = P_CODE_SEVA_MONEY
        }
      }

      const token = await this.generateToken({ mobile_number, PCODE })
      console.log('TOKEN: ', token)

      const topupAPI = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: '', endpoint: TOPUP_URL, token })

      return topupAPI
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'topupAPI', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'topupAPI',
        data: { ma_user_id, userid, amount, aggregator_order_id, customer_id },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async generateToken ({ mobile_number, PCODE = '' }) {
    try {
      const requestFormData = {
        mobile_no: mobile_number
      }

      const requestDataParam = qs.stringify(requestFormData)
      const param_string = mobile_number + CHECKSUM_SECRETKEY

      log.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'requestData_param_string', fields: param_string })
      const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')
      const tokenResp = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: '', endpoint: TOKEN_URL, pcode: PCODE })

      log.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'response', fields: tokenResp })

      if (tokenResp.axiosResponse.status != 200) return tokenResp.axiosResponse
      const token = tokenResp.axiosResponse.data.token
      console.log('RESPONSEL ', token)
      return token
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'generateToken',
        data: { mobile_number },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async getCustomerWalletBalance ({ entity_id, mobile_number, program_id = '' }) {
    try {
      const requestFormData = {
        entity_id
      }

      const requestDataParam = qs.stringify(requestFormData)

      const param_string = entity_id + CHECKSUM_SECRETKEY

      log.logger({ pagename: path.basename(__filename), action: 'getCustomerWalletBalance', type: 'requestData_param_string', fields: param_string })
      const checksum_key = crypto.createHash('sha256').update(param_string).digest('hex')

      // program code
      let PCODE = ''
      if (program_id) {
        if (program_id == 1) {
          PCODE = P_CODE_GOA_GOV
        } else if (program_id == 2) {
          PCODE = P_CODE_SEVA_MONEY
        }
      }
      const token = await this.generateToken({ mobile_number, PCODE })
      console.log('TOKEN: ', token)

      const walletBalanceResp = await this.maSideApiAxiosCall({ requestData: requestDataParam, checksum: checksum_key, stage: '', endpoint: WALLET_BALANCE, token, pcode: PCODE })

      if (walletBalanceResp.axiosResponse.status != 200) return walletBalanceResp.axiosResponse
      const walletBalance = walletBalanceResp.axiosResponse.data.balance
      console.log('RESPONSE: ', walletBalance)
      return walletBalance
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getCustomerWalletBalance', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'topupAPI',
        data: { entity_id },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async getMerchantWalletBalance ({ ma_user_id, userid, connectionRead }) {
    log.logger({ pagename: path.basename(__filename), action: 'getMerchantWalletBalance', type: 'request', fields: { ma_user_id, userid } })

    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      const availableBalance = await BalanceController.getWalletBalancesDirect('_', {
        ma_user_id: ma_user_id,
        ma_status: 'ACTUAL',
        balance_flag: 'SUMMARY',
        connection: connRead
      })
      log.logger({ pagename: path.basename(__filename), action: 'getMerchantWalletBalance', type: 'availableBalance', fields: availableBalance })

      if (availableBalance.status != 200) return availableBalance

      return availableBalance.amount
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getMerchantWalletBalance', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'topupAPI',
        data: { ma_user_id, userid },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  static async getPrepaidCardTransactionDetails (_, args) {
    log.logger({ pagename: 'prepaidCardController.js', action: 'getPrepaidCardTransactionDetails', type: 'request', fields: args })

    // create a new connection for API
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let sqlCondition = ''
      if (args.ma_transaction_master_id) {
        sqlCondition = ` AND a.ma_transaction_master_id = '${args.ma_transaction_master_id}'`
      }

      if (args.aggregator_order_id) {
        sqlCondition = ` AND a.aggregator_order_id = '${args.aggregator_order_id}'`
      }
      const sql = `SELECT a.ma_transaction_master_id,
                            IF(c.customer_name IS NULL,'N/A',c.customer_name) as customer_name,
                            IF(c.customer_mobile IS NULL,'N/A',c.customer_mobile) as customer_mobile,
                            (a.amount + a.commission_amount) as amount,
                            a.amount as topup_amount,
                            a.aggregator_order_id,
                            CONCAT(b.firstname,' ',b.lastname) AS merchant_name,
                            b.mobile_id AS merchant_mobile,
                            b.address,
                            FROM_UNIXTIME(UNIX_TIMESTAMP(a.addedon),'%d-%m-%Y %r') AS transaction_time,
                            IF (a.transaction_status = 'P', 'PND', a.transaction_status) AS transaction_status,
                            a.transaction_reason,
                            c.bank_name,
                            c.terminalid,
                            a.aggregator_txn_id,
                            a.userid,
                            a.bank_rrn,
                            a.ma_user_id,
                            mpc.customer_type as user_type,                            
                            mpc.customer_id,
                            pcpm.program_name
                    FROM ma_transaction_master AS a
                    INNER JOIN ma_user_master AS b ON a.userid = b.userid AND a.ma_user_id = b.profileid
                    LEFT JOIN ma_transaction_master_details AS c ON a.ma_transaction_master_id = c.ma_transaction_master_id
                    LEFT JOIN ma_prepaid_card_topup as mpct ON mpct.aggregator_order_id = a.aggregator_order_id
                    LEFT JOIN ma_prepaid_card AS mpc ON mpc.customer_id = mpct.customer_id
                    LEFT JOIN ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = mpc.program_type_id
                    WHERE a.transaction_type = '59' 
                    ${sqlCondition}`
      const sqlResponse = await this.rawQuery(sql, connection)
      console.log('+++++++++++++++++sql', sql)
      console.log('response+++++++++++++++++++++', sqlResponse)
      if (sqlResponse.length > 0) {
        log.logger({ pagename: 'prepaidCardController.js', action: 'getPrepaidCardTransactionDetails', type: 'response', fields: sqlResponse[0] })
        const res = sqlResponse[0]
        var txnStatus = ''
        var txnBankRRN = ''
        var txnBankBalance = ''
        const card_num = ''

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          customer_name: res.customer_name,
          customer_mobile: res.customer_mobile,
          address: res.address,
          amount: res.amount,
          aggregator_order_id: res.aggregator_order_id,
          merchant_name: res.merchant_name,
          merchant_mobile: res.merchant_mobile,
          transaction_time: res.transaction_time,
          transaction_status: res.transaction_status,
          transaction_reason: res.transaction_reason,
          bank_name: res.bank_name,
          aggregator_txn_id: res.aggregator_txn_id,
          userid: res.userid,
          rrn: res.bank_rrn,
          terminal_id: res.terminalid,
          user_type: res.user_type,
          card_name: res.customer_name,
          merchant_id: res.ma_user_id,
          customer_id: res.customer_id,
          program_name: res.program_name,
          topup_amount: res.topup_amount
        }
      }
      log.logger({ pagename: 'prepaidCardController.js', action: 'getPrepaidCardTransactionDetails', type: 'response', fields: {} })
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (err) {
      log.logger({ pagename: 'prepaidCardController.js', action: 'getPrepaidCardTransactionDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async getResidentKycType (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getResidentKycType', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse
    try {
      const connection = await mySQLWrapper.getConnectionFromPool()
      const sql = `SELECT  ma_prepaid_card_id, passport_kyc_verify, visa_kyc_verify,  visa_front_link, live_photo_link,passport_front_link, caf_link, resident_kyc_type, resident_kyc_sub_type, digi_aadhaar_response, resident_kyc_type, resident_offline_file_front FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" and card_status = "A" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'getResidentKycType', type: 'sql', fields: sql })
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getResidentKycType', type: 'customerInfoDetails', fields: customerInfoDetails })
      let visa_kyc_verify = ''
      let passport_kyc_verify = ''
      let caf_link = ''
      let live_photo_link = ''
      let visa_front_link = ''
      let passport_front_link = ''
      let resident_kyc_type = ''
      if (customerInfoDetails.length > 0) {
        visa_kyc_verify = customerInfoDetails[0].visa_kyc_verify
        visa_front_link = customerInfoDetails[0].visa_front_link
        passport_front_link = customerInfoDetails[0].passport_front_link
        passport_kyc_verify = customerInfoDetails[0].passport_kyc_verify
        caf_link = customerInfoDetails[0].caf_link
        live_photo_link = customerInfoDetails[0].live_photo_link
        resident_kyc_type = customerInfoDetails[0].resident_kyc_type

        let offline_enabled_flag = ''
        let digi_enabled_flag = ''
        let aadhar_enabled_flag = ''
        if (customerInfoDetails[0].resident_kyc_type == null || customerInfoDetails[0].resident_kyc_type == '') {
          offline_enabled_flag = 'true'
        } else if (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION' && customerInfoDetails[0].resident_offline_file_front != null) {
          offline_enabled_flag = 'true'
        } else if (customerInfoDetails[0].resident_kyc_type != null && customerInfoDetails[0].resident_kyc_type != 'OFFLINE_VERIFICATION') {
          offline_enabled_flag = 'false'
        }

        if (customerInfoDetails[0].resident_kyc_type == null || customerInfoDetails[0].resident_kyc_type == '') {
          digi_enabled_flag = 'true'
        } else if (customerInfoDetails[0].resident_kyc_type == 'DIGI_LOCKER' && customerInfoDetails[0].digi_aadhaar_response != null) {
          digi_enabled_flag = 'true'
        } else if (customerInfoDetails[0].resident_kyc_type != null && customerInfoDetails[0].resident_kyc_type != 'DIGI_LOCKER') {
          digi_enabled_flag = 'false'
        }

        if (customerInfoDetails[0].resident_kyc_type == null || customerInfoDetails[0].resident_kyc_type == '') {
          aadhar_enabled_flag = 'true'
        } else if (customerInfoDetails[0].resident_kyc_type == 'AADHAAR_EKYC' && customerInfoDetails[0].digi_aadhaar_response != null) {
          aadhar_enabled_flag = 'true'
        } else if (customerInfoDetails[0].resident_kyc_type != null && customerInfoDetails[0].resident_kyc_type != 'AADHAAR_EKYC') {
          aadhar_enabled_flag = 'false'
        }

        let fileTypes = ''
        fileTypes = [
          {
            doc_id: 'OFFLINE_VERIFICATION',
            doc_label: 'Offline Verfication',
            enabled: offline_enabled_flag,
            verified: (customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION' && customerInfoDetails[0].resident_offline_file_front != null) ? 'true' : 'false',
            doc_icon: 'url'
          },
          {
            doc_id: 'DIGI_LOCKER',
            doc_label: 'DigiLocker',
            enabled: digi_enabled_flag,
            verified: (customerInfoDetails[0].resident_kyc_type == 'DIGI_LOCKER' && customerInfoDetails[0].digi_aadhaar_response != null) ? 'true' : 'false',
            doc_icon: 'url'
          },
          {
            doc_id: 'AADHAAR_EKYC',
            doc_label: 'Aadhaar Ekyc',
            enabled: aadhar_enabled_flag,
            verified: (customerInfoDetails[0].resident_kyc_type == 'AADHAAR_EKYC' && customerInfoDetails[0].digi_aadhaar_response != null) ? 'true' : 'false',
            doc_icon: 'url'
          }
        ]

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uploadFileTypes: fileTypes }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + ': Invalid Customer Id. ' }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getResidentKycType', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getResidentKycType',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // connection.release()
    }
  }

  static async sendCustomerEmailOtpMail (user_emailid, templatedata) {
    const responseUrl = util[process.env.NODE_ENV].ENV_DOMAIN
    var removeUselessWords = function (txt) {
      var uselessWordsArray =
        [
          'Dear Customer, ', 'Team airpay vyaapaar', 'Regards, Team airpay vyaapaar. '
        ]

      var expStr = uselessWordsArray.join('|')
      return txt.replace(new RegExp('\\b(' + expStr + ')\\b', 'gi'), ' ')
        .replace(/\s{2,}/g, ' ')
    }

    const template_content = removeUselessWords(templatedata)
    const mailerData = {
      template: 'sendCustomerEmailOtp.ejs',
      content: {
        url: responseUrl,
        body: template_content
      },
      from: '"Airpay" <<EMAIL>>',
      to: [user_emailid],
      subject: 'One Time Otp - Airpay Prepaid Card'
    }
    const mailResponse = await mailer(mailerData)
    log.logger({ pagename: require('path').basename(__filename), action: 'sendCustomerEmailOtpMail', type: 'Mail response', fields: mailResponse })
  }

  static async sendEmailOtpToCustomer (_, { ma_user_id, userid, uniqueCustId }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendEmailOtpToCustomer', type: 'request', fields: { ma_user_id, userid } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // get  mobile & email from table PCEMAILOTP
      const sql = `SELECT  ma_prepaid_card_id, mobile_no, email FROM ma_prepaid_card WHERE customer_id = "${uniqueCustId}" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'sendEmailOtpToCustomer', type: 'sql', fields: sql })
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'sendEmailOtpToCustomer', type: 'customerInfoDetails', fields: customerInfoDetails })
      if (customerInfoDetails.length > 0) {
        const customer_email = customerInfoDetails[0].email
        const mobile_number = customerInfoDetails[0].mobile_no
        // sent otp
        const otpResponse = await otpClass.sentOtp(ma_user_id, userid, mobile_number, 'PCEMAILOTP')
        log.logger({ pagename: path.basename(__filename), action: 'sendEmailOtpToCustomer', type: 'otpResponse', fields: otpResponse })
        if (otpResponse.status === true) {
          const mailTemplateData = otpResponse.data.template_text
          const mailResponse = this.sendCustomerEmailOtpMail(customer_email, mailTemplateData)
          return { status: 200, respcode: 2002, message: "OTP has been sent to the Customer's Mobile No. / Email ID , Please enter it below.", orderid: otpResponse.data.aggregator_order_id }
        } else {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (err) {
      // await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendEmailOtpToCustomer', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async verifyEmailOtpFromCustomer (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'verifyEmailOtpFromCustomer', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const verifyResponse = await otpClass.verifyOtp(fields.aggregator_order_id, 'PCEMAILOTP', fields.otp)
      log.logger({ pagename: path.basename(__filename), action: 'verifyEmailOtpFromCustomer', type: 'verifyResponse', fields: verifyResponse })
      if (verifyResponse.status === false) {
        return { status: 400, respcode: verifyResponse.respcode, message: verifyResponse.message }
      } else {
        // update customer otp flag to true
        const sql = `UPDATE ma_prepaid_card set customer_email_otp_verify = 'P' WHERE customer_id = '${fields.uniqueCustId}' `
        log.logger({ pagename: path.basename(__filename), action: 'verifyEmailOtpFromCustomer', type: 'updateSql', fields: sql })
        const verifyCustomerOtpResult = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'verifyEmailOtpFromCustomer', type: 'verifyCustomerOtpResult', fields: verifyCustomerOtpResult })
        const _userData = {}
        _userData.respcode = 1000
        _userData.status = 200
        _userData.message = errorMsg.responseCode[1000]

        // call sendOtp to merchant
        // const merchantOtpResponse = await this.sendOtpToMerchant(_, { ma_user_id, userid })
        // _userData.orderid = merchantOtpResponse.orderid
        return _userData
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'verifyEmailOtpFromCustomer', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'verifyEmailOtpFromCustomer',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async resendEmailOtpToCustomer (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'resendEmailOtpToCustomer', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      var deviceos = ''
      const response = {}
      var mobile_number = ''
      let userid = ''
      const sql = `SELECT mobile, userid FROM ma_otp_master WHERE aggregator_order_id = '${fields.aggregator_order_id}' limit 1`
      const userOtpDetails = await this.rawQuery(sql, connection)
      if (userOtpDetails.length > 0) {
        mobile_number = userOtpDetails[0].mobile
        userid = userOtpDetails[0].userid
      }
      var resp = await otpClass.resentOtp(mobile_number, fields.aggregator_order_id, 'PCEMAILOTP')
      log.logger({ pagename: path.basename(__filename), action: 'resendEmailOtpToCustomer', type: 'resp', fields: resp })
      if (resp.status === true) {
        response.status = 200
        response.respcode = 1000
        response.message = errorMsg.responseCode[1000]
        response.action_code = 1000

        const mailTemplateData = resp.template_text
        // send mail to customer
        const sql = `SELECT  ma_prepaid_card_id, email FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" limit 1  `
        log.logger({ pagename: path.basename(__filename), action: 'resendEmailOtpToCustomer', type: 'sql', fields: sql })
        const customerInfoDetails = await this.rawQuery(sql, connection)
        log.logger({ pagename: path.basename(__filename), action: 'resendEmailOtpToCustomer', type: 'customerInfoDetails', fields: customerInfoDetails })
        if (customerInfoDetails.length > 0) {
          const customer_email = customerInfoDetails[0].email
          const mailResponse = this.sendCustomerEmailOtpMail(customer_email, mailTemplateData)
        } else {
          response.status = 400
          response.respcode = resp.respcode
          response.message = resp.message
          response.action_code = 1001
        }
      } else {
        response.status = 400
        response.respcode = resp.respcode
        response.message = resp.message
        response.action_code = 1001
      }
      return response
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'resendEmailOtpToCustomer', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'resendEmailOtpToCustomer',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async validatePanCardNo (_, fields) {
    const common_fns = require('../../util/common_fns')
    log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'request', fields: common_fns.maskValue(fields, 'pan_card_no') })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'pan_card_no'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get mobile no. from prepaid card table
      const customerResult = await this.getCustomerInfo(_, fields)
      // const moment = require('moment')
      const dateOfBirth = customerResult.customerDetails.dob
      // var dateOfBirth = moment(dob).format('DD/MM/YYYY')
      log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'customerResult-response', fields: customerResult })
      // const mobile_number = customerResult.customerDetails.customer_mobile_no
      // const qs = require('qs')
      const data = JSON.stringify({ pan: fields.pan_card_no, name: fields.customer_name, dob: dateOfBirth, fathername: 'test' })

      const param_string = fields.pan_card_no + fields.customer_name + dateOfBirth + 'test' + CHECKSUM_SECRETKEY

      // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
      log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'requestData_param_string', fields: param_string })
      const CHECKSUM_KEY = crypto.createHash('sha256').update(param_string).digest('hex')

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: PAN_VALIDATION_API_URL,
        headers: {
          AIRPAYKEY: BAAS_AIRPAYKEY,
          AFFILIATE: BAAS_AFFILIATE,
          CHECKSUM: CHECKSUM_KEY,
          APIKEY: BASS_PAN_CARD_APIKEY,
          'Content-Type': 'application/json'
        },
        data: data
      }
      log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'customerResult-config', fields: config })
      const response = await axios.request(config)

      log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: ' API) - response', fields: response })

      if (response != '' && response.status === 200) {
        const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
        if (responseData.status == 200) {
          const string = responseData.data['Pan Status'] // integrate new api and check where status contains below string
          const substring = 'Existing and Valid'
          string.includes(substring)
          if (string.includes(substring)) {
          // save customer pan card details & set flag to 'P'
          // const customer_pan_no = common_fns.maskValue(fields, 'pan_card_no')
            const string = String(fields.pan_card_no)
            const sliced = string.slice(-4)
            const customer_pan_no = String(sliced).padStart(string.length, '*')
            // console.log(customer_pan_no)
            const customer_pan_validated = ''

            const sql = `UPDATE ma_prepaid_card set customer_pan_no = '${customer_pan_no}',customer_pan_validated = 'P' WHERE customer_id = '${fields.uniqueCustId}' `
            log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'updateSql', fields: sql })
            const verifyMerchantOtpResult = await this.rawQuery(sql, connection)
            log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'verifyMerchantOtpResult', fields: verifyMerchantOtpResult })
            return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: responseData.data }
          } else {
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
          }
        } else {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'validatePanCardNo', type: 'validatePanCardNo-response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving Pan Card details', action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'validatePanCardNo', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'validatePanCardNo',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async validateAadhaarEkyc (_, fields) {
    const common_fns = require('../../util/common_fns')
    log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'request', fields: common_fns.maskValue(fields, 'pan_card_no') })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get mobile no. from prepaid card table
      const customerResult = await this.getCustomerInfo(_, fields)
      log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'customerResult-response', fields: customerResult })

      if (customerResult.status == 200) {
        if (customerResult.customerDetails.resident_kyc_type == 'AADHAAR_EKYC' && customerResult.customerDetails.resident_kyc_doc_no && customerResult.customerDetails.digi_aadhaar_response) {
          return { status: 200, message: 'Aadhaar EKYC already verified!', respcode: 1000 }
        } else {
          // const mobile_number = customerResult.customerDetails.customer_mobile_no
          const qs = require('qs')
          let CHECKSUM_KEY = ''
          let data = ''
          if (fields.kycOption == 'otp') {
            data = qs.stringify({
              aadhaarNo: fields.aadhaar_card_no,
              type: fields.kycOption
            })

            const param_string = fields.aadhaar_card_no + fields.kycOption + CHECKSUM_SECRETKEY

            // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
            log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'requestData_param_string', fields: param_string })
            CHECKSUM_KEY = crypto.createHash('sha256').update(param_string).digest('hex')
          } else {
            data = qs.stringify({
              otp: fields.otp,
              requestId: fields.requestId,
              type: fields.kycOption,
              aadhaarNo: fields.aadhaar_card_no,
              airpay_txn_id: fields.airpayTransactionId
            })

            const param_string = fields.otp + fields.requestId + fields.kycOption + fields.aadhaar_card_no + fields.airpayTransactionId + CHECKSUM_SECRETKEY

            // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
            log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'requestData_param_string', fields: param_string })
            CHECKSUM_KEY = crypto.createHash('sha256').update(param_string).digest('hex')
          }
          const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: AADHAAR_EKYC_VALIDATION_API_URL,
            headers: {
              AIRPAYKEY: BAAS_AIRPAYKEY,
              AFFILIATE: BAAS_AFFILIATE,
              CHECKSUM: CHECKSUM_KEY,
              APIKEY: BASS_AADHAAR_EKYC_APIKEY,
              live: 'Y',
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: data
          }
          log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'customerResult-config', fields: config })
          const response = await axios.request(config)

          log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: ' API) - response', fields: response })

          if (response != '' && response.status === 200) {
            const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
            console.log('responseData>>>>>>>', responseData)
            if (responseData.status == 200) {
              // save customer pan card details & set flag to 'P'
              // const customer_pan_no = common_fns.maskValue(fields, 'pan_card_no')
              const string = String(fields.aadhaar_card_no)
              const sliced = string.slice(-4)
              const customer_aadhaar_no = String(sliced).padStart(string.length, '*')
              console.log(customer_aadhaar_no)
              const customer_pan_validated = ''
              responseData.data = responseData.data || ''
              if (fields.kycOption == 'otp') {
                const sql = `UPDATE ma_prepaid_card set resident_kyc_doc_no = '${customer_aadhaar_no}', digi_aadhaar_response = '${JSON.stringify(responseData.data)}' WHERE customer_id = '${fields.uniqueCustId}' `
                log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'updateSql', fields: sql })
                const verifyMerchantOtpResult = await this.rawQuery(sql, connection)
                log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'verifyMerchantOtpResult', fields: verifyMerchantOtpResult })
              } else {
                const sql = `UPDATE ma_prepaid_card set digi_aadhaar_response = '${JSON.stringify(responseData.data)}', resident_kyc_type = 'AADHAAR_EKYC', resident_kyc_verify = 'P', caf_link = 'NA' WHERE customer_id = '${fields.uniqueCustId}' `
                log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'updateSql', fields: sql })
                const verifyMerchantOtpResult = await this.rawQuery(sql, connection)
                log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'verifyMerchantOtpResult', fields: verifyMerchantOtpResult })
              }

              return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, requestId: responseData.requstId, airpayTransactionId: responseData.airpay_txn_id, data: responseData.data }
            } else {
              return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
            }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'validateAadhaarEkyc', type: 'validateAadhaarEkyc-response', fields: {} })
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving Aadhaar Card details', action_code: 1001 }
          }
        }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'validateAadhaarEkyc', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'validateAadhaarEkyc',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async digilockerXmlApi (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'request', fields })
    // const connection = await mySQLWrapper.getConnectionFromReadReplica()
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // get mobile no. from prepaid card table
      const customerResult = await this.getCustomerInfo(_, fields)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'customerResult-response', fields: customerResult })
      const mobile_number = customerResult.customerDetails.customer_mobile_no
      const qs = require('qs')
      const data = qs.stringify({
        mobile_no: mobile_number,
        stage: 'status'
      })

      const param_string = mobile_number + 'status' + CHECKSUM_SECRETKEY

      // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'requestData_param_string', fields: param_string })
      const CHECKSUM_KEY = crypto.createHash('sha256').update(param_string).digest('hex')

      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: DIGI_LOCKER_API_URL,
        headers: {
          AIRPAYKEY: BAAS_AIRPAYKEY,
          AFFILIATE: BAAS_AFFILIATE,
          CHECKSUM: CHECKSUM_KEY,
          APIKEY: BAAS_DIGI_LOCKER_APIKEY,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: data
      }
      log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'customerResult-config', fields: config })
      const response = await axios.request(config)

      log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: ' API) - response', fields: response })

      if (response != '' && response.status === 200) {
        const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
        console.log('responseData>>>>>>>', responseData)
        if (responseData.status == 200) {
          // Access is granted for digi locker
          // call digilocker xml API
          const qs = require('qs')
          const data = qs.stringify({
            mobile_no: mobile_number,
            stage: 'xml'
          })

          const param_string = mobile_number + 'xml' + CHECKSUM_SECRETKEY

          // const checksum = sha256(param_string + CHECKSUM_SECRETKEY)
          log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'requestData_param_string', fields: param_string })
          const CHECKSUM_KEY = crypto.createHash('sha256').update(param_string).digest('hex')

          const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: DIGI_LOCKER_API_URL,
            headers: {
              AIRPAYKEY: BAAS_AIRPAYKEY,
              AFFILIATE: BAAS_AFFILIATE,
              CHECKSUM: CHECKSUM_KEY,
              APIKEY: BAAS_DIGI_LOCKER_APIKEY,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: data
          }
          log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'customerResult-config', fields: config })
          const response = await axios.request(config)
          log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: ' API) - response', fields: response })

          if (response != '' && response.status === 200) {
            const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
            console.log('responseData>>>>>>>', responseData)
            if (responseData.status == 200) {
              // check pan card details, if exists then save pan no and set pan card flag to 'P'
              const digi_aadhaar_response_data = JSON.stringify(responseData.data)
              console.log('DIGI response : ', digi_aadhaar_response_data)
              let aadhaar_card_no = ''
              let pan_card_no = ''
              let customer_pan_no = ''
              let aadhaarStr = ''
              let panStr = ''
              if ((responseData.data.dataFromAadhaar) && (responseData.data.dataFromAadhaar.uid)) {
                console.log('here check --------------- 1')
                aadhaar_card_no = responseData.data.dataFromAadhaar.uid
                aadhaarStr = ` ,resident_kyc_doc_no = '${aadhaar_card_no}'`
              }
              if ((responseData.data.dataFromPan) && (responseData.data.dataFromPan.number)) {
                console.log('here check --------------- 2')
                pan_card_no = responseData.data.dataFromPan.number
                const string = String(pan_card_no)
                const sliced = string.slice(-4)
                customer_pan_no = String(sliced).padStart(string.length, '*')
                panStr = `, customer_pan_no = '${customer_pan_no}',customer_pan_validated = 'P'`
              }

              const sql = `UPDATE ma_prepaid_card set digi_aadhaar_response = '${digi_aadhaar_response_data}', resident_kyc_type = 'DIGI_LOCKER', resident_kyc_verify = 'P', caf_link = 'NA' ${aadhaarStr} ${panStr} WHERE customer_id = '${fields.uniqueCustId}' `
              log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'updateSql', fields: sql })
              const updateDigiReponseResult = await this.rawQuery(sql, connection)
              log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'updateDigiReponseResult', fields: updateDigiReponseResult })
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: responseData.data, action_code: 1000 }
            } else {
              return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
            }
          } else {
            log.logger({ pagename: require('path').basename(__filename), action: 'digilockerXmlApi', type: 'digilockerXmlApi-response', fields: {} })
            return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving DigiLocker details', action_code: 1001 }
          }

          // return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: responseData.data, action_code: 1000 }
        } else {
          return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': ' + responseData.message, action_code: 1001 }
        }
      } else {
        log.logger({ pagename: require('path').basename(__filename), action: 'digilockerXmlApi', type: 'digilockerXmlApi-response', fields: {} })
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ' : Error in retreiving DigiLocker details', action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'digilockerXmlApi', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async personalDetailsApi (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'personalDetailsApi', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const sql = `SELECT pc.resident_kyc_type,pc.resident_kyc_sub_type,pc.resident_kyc_expiry_date,pc.resident_kyc_doc_no,pc.digi_aadhaar_response,pc.resident_kyc_verify,pc.customer_email_otp_verify,pc.customer_pan_no,pc.customer_pan_validated,pc.customer_pan_no_details FROM ma_prepaid_card pc Join ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = pc.program_type_id WHERE pc.customer_id = '${fields.uniqueCustId}' and pc.card_status = 'A' limit 1 `
      console.log('query : ', sql)
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'personalDetailsApi', type: 'customerInfoDetails-config', fields: customerInfoDetails })
      if (customerInfoDetails.length > 0) {
        // console.log('digi data 3: ', customerInfoDetails[0].digi_aadhaar_response)
        const digi_aadhaar_response = JSON.parse(customerInfoDetails[0].digi_aadhaar_response)
        // console.log('digi data 1: ', digi_aadhaar_response)
        // console.log('digi data 2: ', customerInfoDetails[0].digi_aadhaar_response.toString())
        // console.log('data ', JSON.stringify(whichIsVisible(customerInfoDetails[0].digi_aadhaar_response)))
        let customerDetails = ''
        if (fields.kycType == 'AADHAAR_EKYC') {
          customerDetails = {
          // digi_aadhaar_response: customerInfoDetails[0].digi_aadhaar_response,
            name: digi_aadhaar_response.dataFromAadhaar.name,
            dob: digi_aadhaar_response.dataFromAadhaar.dob,
            gender: digi_aadhaar_response.dataFromAadhaar.gender,
            address: digi_aadhaar_response.dataFromAadhaar.address.combinedAddress,
            state: digi_aadhaar_response.dataFromAadhaar.address.splitAddress.state,
            city: digi_aadhaar_response.dataFromAadhaar.address.splitAddress.vtcName,
            district: digi_aadhaar_response.dataFromAadhaar.address.splitAddress.district,
            pincode: digi_aadhaar_response.dataFromAadhaar.address.splitAddress.pincode,
            pan_card_no: ''
          }
        } else if (fields.kycType == 'DIGI_LOCKER') {
          customerDetails = {
            // digi_aadhaar_response: customerInfoDetails[0].digi_aadhaar_response,
            name: digi_aadhaar_response.dataFromAadhaar.name,
            dob: digi_aadhaar_response.dataFromAadhaar.dob,
            gender: digi_aadhaar_response.dataFromAadhaar.gender,
            address: digi_aadhaar_response.dataFromAadhaar.co + ', ' + digi_aadhaar_response.dataFromAadhaar.house + ', ' + digi_aadhaar_response.dataFromAadhaar.street + ', ' + digi_aadhaar_response.dataFromAadhaar.vtc,
            state: digi_aadhaar_response.dataFromAadhaar.state,
            city: digi_aadhaar_response.dataFromAadhaar.vtc,
            district: digi_aadhaar_response.dataFromAadhaar.dist,
            pincode: digi_aadhaar_response.dataFromAadhaar.pc,
            pan_card_no: digi_aadhaar_response.dataFromPan.number
          }
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], customerDetails: customerDetails }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'personalDetailsApi', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'personalDetailsApi',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async getProgramDropdown (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getProgramDropdown', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const fetchProgramTypeQuery = "SELECT ma_prepaid_card_program_master_id as program_id, program_name from ma_prepaid_card_program_master where program_status = 'Y'"
      const fetchProgramResult = await this.rawQuery(fetchProgramTypeQuery, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getProgramDropdown', type: 'fetchProgramResult', fields: fetchProgramResult })
      if (fetchProgramResult.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], programDetails: fetchProgramResult }
      } else {
        return { status: 400, message: errorMsg.responseCode[1028] + ': No records!', respcode: 1028 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getProgramDropdown', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getProgramDropdown',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async checkCustomerInfo (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // check mobile no. and program type already exists then send customer details
      let mobile_code = ''
      let qryStr = ''
      let qryStr2 = ''
      if (fields.mobile_code != null && fields.mobile_code != undefined && fields.mobile_code != '') {
        mobile_code = fields.mobile_code.slice(1)
        qryStr = ' and mobile_code = ' + mobile_code
      }
      if (fields.resident_type != null && fields.resident_type != undefined && fields.resident_type != '') {
        qryStr2 = " and customer_type = '" + fields.resident_type + "' "
      }

      const sql = `SELECT pc.title, CONCAT(pc.first_name, ' ', pc.last_name) AS full_name, pc.mobile_code, pc.mobile_no,mc.nicename  as country, pc.email, pc.passport_no, pc.visa_no, pc.customer_type, pc.reference_id, pc.ma_prepaid_card_id, pc.passport_kyc_verify, pc.visa_kyc_verify, pc.card_kit_assign, pc.customer_id, pc.live_photo_link, pc.caf_link,'PASSPORT' as doc_type_name_one, 'VISA' as doc_type_name_two, pc.customer_email_otp_verify, pc.resident_kyc_verify,pc.customer_pan_no, pcpm.program_name, pc.resident_kyc_type FROM ma_prepaid_card pc Join ma_prepaid_card_program_master pcpm on pcpm.ma_prepaid_card_program_master_id = pc.program_type_id LEFT JOIN ma_country mc on pc.country_id = mc.id WHERE pc.mobile_no = '${fields.mobile_no}' and pc.program_type_id = '${fields.program_id}' and pc.card_status = 'A' ${qryStr} ${qryStr2} limit 1 `
      const customerInfoDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'customerInfoDetails', customerInfoDetails })
      let authAction = ''
      if (customerInfoDetails.length > 0) {
        const customerDetails = {
          application_no: customerInfoDetails[0].ma_prepaid_card_id,
          reference_id: customerInfoDetails[0].reference_id,
          program_name: customerInfoDetails[0].program_name,
          customer_type: customerInfoDetails[0].customer_type,
          customer_id: customerInfoDetails[0].customer_id,
          customer_title: customerInfoDetails[0].title,
          customer_name: customerInfoDetails[0].full_name,
          customer_mobile_no: customerInfoDetails[0].mobile_no,
          mobile_code: '+' + customerInfoDetails[0].mobile_code,
          country_name: customerInfoDetails[0].country,
          customer_email: customerInfoDetails[0].email,
          passport_no: customerInfoDetails[0].passport_no,
          visa_no: customerInfoDetails[0].visa_no,
          passport_kyc_verify: customerInfoDetails[0].passport_kyc_verify,
          visa_kyc_verify: customerInfoDetails[0].visa_kyc_verify,
          card_kit_assign: customerInfoDetails[0].card_kit_assign
        }
        const customerType = customerInfoDetails[0].customer_type
        let kyc_source_value = ''
        if (customerType == 'TOURIST') {
          if (customerInfoDetails[0].passport_kyc_verify == 'N' || customerInfoDetails[0].visa_kyc_verify == 'N') {
            authAction = 'COMPLETE_KYC'
          } else if (customerInfoDetails[0].live_photo_link == null) {
            authAction = 'LIVE_PHOTO'
          } else if (customerInfoDetails[0].caf_link == null) {
            authAction = 'CAF'
          } else if (customerInfoDetails[0].card_kit_assign == 'F') {
            authAction = 'ISSUE_CARD'
          } else {
            authAction = 'TOP_UP'
          }
        } else if (customerType == 'RESIDENT') {
          if (customerInfoDetails[0].customer_email_otp_verify == 'F') {
            authAction = 'CUSTOMER_EMAIL_OTP'
          } else if (customerInfoDetails[0].resident_kyc_verify == 'F') {
            authAction = 'COMPLETE_KYC'
          } else if (customerInfoDetails[0].customer_pan_no == null) {
            authAction = 'PAN_CARD'
          } else if (customerInfoDetails[0].live_photo_link == null) {
            authAction = 'LIVE_PHOTO'
          } else if (customerInfoDetails[0].caf_link == null && customerInfoDetails[0].resident_kyc_type == 'OFFLINE_VERIFICATION') {
            authAction = 'CAF'
          } else if (customerInfoDetails[0].card_kit_assign == 'F') {
            authAction = 'ISSUE_CARD'
          } else {
            authAction = 'TOP_UP'
          }

          // pass kyc source value
          if (customerInfoDetails[0].resident_kyc_type == 'DIGI_LOCKER') {
            kyc_source_value = 'digilocker'
          }
          if (customerInfoDetails[0].resident_kyc_type == 'AADHAAR_EKYC') {
            kyc_source_value = 'aadhaarekyc'
          }
        }

        let orderid = ''
        if (authAction == 'CUSTOMER_EMAIL_OTP' && customerType == 'RESIDENT') {
          // send email otp to verify customer
          const merchantOtpResponse = await this.sendEmailOtpToCustomer(_, { ma_user_id: fields.ma_user_id, userid: fields.userid, uniqueCustId: customerInfoDetails[0].customer_id })
          orderid = merchantOtpResponse.orderid
        }

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uniqueCustId: customerInfoDetails[0].customer_id, auth_action: authAction, orderid: orderid, kyc_source: kyc_source_value }
      } else {
        // get form data
        let getParamsQuery = ''
        fields.resident_type = fields.resident_type || 'RESIDENT'
        if (fields.resident_type == resident_type.RESIDENT) {
          getParamsQuery = "SELECT api_params from ma_dynamic_forms where form_type = 'resident_card_form' and isActive = 'Y' limit 1"
          log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'getParamsQuery', fields: getParamsQuery })
        } else if (fields.resident_type == resident_type.TOURIST) {
          getParamsQuery = "SELECT api_params from ma_dynamic_forms where channel_name = 'Prepaid Card' and form_type = 'prepaid_card_form' and  isActive = 'Y' limit 1"
          log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'getParamsQuery', fields: getParamsQuery })
        } else {
          return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        }

        const params = await this.rawQuery(getParamsQuery, connection)
        log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'params-response', fields: params })
        if (params.length == 0) return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
        // const form = JSON.parse(params[0].api_params)

        const customField = JSON.parse(params[0].api_params)
        log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'params-customField', fields: customField })
        // CHECK FOR INPUT TYPE & IF DROP DOWN GET THE STATIC VALUES FOR THE DROP DOWN FROM THE TABLE
        for (const field of customField) {
          // console.log('fields : ', field)
          // console.log('field type : ', field.type)
          if ((field.type == 'DD' || field.type == 'DD_mobile') && field.postKey != 'gender' && field.postKey != 'title') {
            console.log('field type 1234 ====', field.type)
            const ddvalue = await this.getDynamicDataByKey({ static_data_type: field.postKey, connectionRead: connection })
            field.ddvalue = ddvalue
            if (field.type == 'DD_mobile') {
              field.value = fields.mobile_no
            }
            if (field.postKey == 'program_type') {
              field.value = fields.program_id
            }
          }
        }
        log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'params-post-customField-check-cust-info', fields: customField })

        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], uniqueCustId: '', auth_action: 'NEW_USER', dynamicPrepaidForm: customField }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'checkCustomerInfo', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'checkCustomerInfo',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * methodName description - What's the method about?
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async kycSuccessPage (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'kycSuccessPage', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // get  mobile & email from table
      const sqlCust = `SELECT  ma_prepaid_card_id, mobile_no, email, customer_id, reference_id FROM ma_prepaid_card WHERE customer_id = "${fields.uniqueCustId}" limit 1  `
      log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'sqlCust', fields: sqlCust })
      const customerInfoDetails = await this.rawQuery(sqlCust, connection)
      log.logger({ pagename: path.basename(__filename), action: 'verifyOtpFromMerchant', type: 'customerInfoDetails', fields: customerInfoDetails })
      if (customerInfoDetails.length > 0) {
        const customer_email = customerInfoDetails[0].email
        const mobile_number = customerInfoDetails[0].mobile_no
        const reference_id = customerInfoDetails[0].reference_id

        // Send sms to customer after transaction is successful
        const common = require('../../util/common')
        let commMessage = ''
        let template = ''
        const TEMPASS = await common.getSystemCodes(this, util.prepaid_card_temp_pass, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtpFromMerchant', type: 'TEMPASS', fields: { TEMPASS: TEMPASS } })

        commMessage = util.communication.PCSUCCESSFULREGISTRATION
        template = util.templateid.PCSUCCESSFULREGISTRATION
        const sms = require('../../util/sms')
        // var dt = new Date(date_time)
        // const datetimeformat = dt.toLocaleString('en-IN')
        commMessage = commMessage.replace('<CUSTID>', fields.uniqueCustId)
        commMessage = commMessage.replace('<REFID>', reference_id)
        commMessage = commMessage.replace('<TEMPASS>', TEMPASS)
        console.log('common message ', commMessage)
        await sms.sentSmsAsync(commMessage, mobile_number, template)
        const mailResponse = this.sendCustomerRegSuccessMail(customer_email, commMessage)

        const sqlCurrentTime = 'SELECT DATE_FORMAT(CURRENT_TIMESTAMP,\'%d-%m-%Y %H:%i %p\') as currentTime'
        const resultTime = await this.rawQuery(sqlCurrentTime, connection)
        log.logger({ pagename: 'posAcitvationController.js', action: 'posActivate', type: 'result_date_time', fields: { resultTime } })
        const date_time = resultTime[0].currentTime
        return { status: 200, message: 'Customer ID is generated Successfully.', uniqueCustId: fields.uniqueCustId, registerMsg: 'Customer is successfully registered with airpay.', registerTime: date_time, respcode: 1000 }
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'kycSuccessPage', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'kycSuccessPage',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}

module.exports = prepaidCardController
