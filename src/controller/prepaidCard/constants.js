const util = require('../../util/util')

const PREPAID_CONSTANTS = {
  // BASE_URL: !util.isProduction() ? 'https://cards.airpay.ninja/api/user' : 'https://cards.airpay.co.in/api/user',
  BASE_URL: 'https://cards.airpay.ninja/api/',
  ADD_CUSTOMER_API_URL: 'user/add-customer-offline',
  ASSIGN_CARD_API_URL: 'user/assigncard',
  KYC_UPLOAD_API_URL: 'user/kyc-details',
  TOPUP_URL: 'card/topup',
  TOKEN_URL: 'card/token',
  ACTIVATE_CARD_URL: 'card/activatecard',
  WALLET_BALANCE: 'card/getwalletbalance',
  UPDATE_CUSTOMER: 'card/update-customer',
  STAGE: {
    REGISTER: 'register'
  },
  GENERATE_CAF_PDF: !util.isProduction() ? 'https://retaila.airpay.ninja/api/generate-kyc-form' : 'https://retaila.airpay.co.in/api/generate-kyc-form',
  DIGI_LOCKER_API_URL: !util.isProduction() ? 'https://baas.airpay.ninja/node/api/DigiLocker' : 'https://baas.airpay.co.in/node/api/DigiLocker',
  PAN_VALIDATION_API_URL: !util.isProduction() ? 'https://baas.airpay.ninja/node/api/panVerificationv1' : 'https://baas.airpay.co.in/node/api/panVerification1',
  AADHAAR_EKYC_VALIDATION_API_URL: !util.isProduction() ? 'https://baas.airpay.ninja/node/api/aadhaarXml' : 'https://baas.airpay.co.in/node/api/aadhaarXml',
  BAAS_AIRPAYKEY: !util.isProduction() ? 'AL0006AL1625833854' : '',
  BAAS_AFFILIATE: !util.isProduction() ? 'AF0022AF1632473204' : '',
  BAAS_DIGI_LOCKER_APIKEY: !util.isProduction() ? 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw=21=' : '',
  BASS_PAN_CARD_APIKEY: !util.isProduction() ? 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==1' : '',
  BASS_AADHAAR_EKYC_APIKEY: !util.isProduction() ? 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsYvfg==' : '',
  HEADER_CONTENT: {
    'Content-Type': 'x-www-form-urlencoded'
  },
  resident_type: {
    RESIDENT: 'RESIDENT',
    TOURIST: 'TOURIST'

  },
  ACTIVATION_SUCCESS_EMAIL_SUBJECT: 'SEVA Money - Registration Notification',
  ACTIVATION_FAIL_EMAIL_SUBJECT: 'Failure - POS Device Activation ',
  EMAIL_SIGNATURE: 'Team Airpay Vyaapaar',
  FROM_EMAIL: '"Airpay" <<EMAIL>>',
  AFFILIATE: 'AF0067AF1695042642',
  AIRPAY_KEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
  P_CODE_GOA_GOV: 'PRGMC22D3E231E',
  P_CODE_SEVA_MONEY: 'PRGM691E85CDB2',
  CHECKSUM_SECRETKEY: 'b0B1c#dsaf45ADSF5645adf0215',
  DOC_TYPE: ['PASSPORT', 'VISA', 'DRIVINGLICENSE', 'VOTERID', 'CAF', 'LIVEPHOTO']
}

module.exports = {
  PREPAID_CONSTANTS: PREPAID_CONSTANTS
}
