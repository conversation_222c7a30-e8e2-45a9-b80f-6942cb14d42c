const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const logs = require('../../util/log')
const path = require('path')
const validator = require('../../util/validator')
const util = require('../../util/util')
const axios = require('axios')
const errorEmail = require('../../util/errorHandler')
const common_fns = require('../../util/common_fns')
const { generateOrderchecksum } = require('../../util/checksum')
const { AEPS_AUTH_API_URL, HEADER_CONTENT, FINO_BANK_ID,CREDOPAY_BANK_ID } = require('./config').AEPS_AUTH_CONSTANTS
const aepsTransactionController = require('../transaction/aepsTransactionController')
const transactionMaster = require('../transaction/transactionController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const balanceController = require('../balance/balanceController')
// const { lokiLogger } = require('../../util/lokiLogger')

class aepsMerchantAuthController extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_aeps_merchant_auth_token'
  }

  static get PRIMARY_KEY () {
    return 'ma_aeps_merchant_auth_token_id'
  }

  /**
   * This function is used to save token in ma_aeps_merchant_auth_token table
   * @param {*} _
   * @param {*} fields
  */
  static async createEntry (_, fields) {
    logs.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const _result = await this.insert(connection, {
        data: {
          ma_user_id: fields.ma_user_id,
          authtoken: fields.auth_token,
          expiry_time: fields.expiry_time,
          sent_bank_id: fields.sentBankId,
          received_bank_id: fields.receivedBankId
        }
      })
      logs.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'result', fields: _result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'result', fields: _result })
      return _result
    } catch (err) {
      logs.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      const data = Object.assign({}, fields)
      delete data.connection
      errorEmail.notifyCatchErrorEmail({
        function: 'createEntry',
        data: data,
        error: err
      })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /*
  This function is used to generate Otp
  */
  static async generateOTP (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  /*
  This function is used to genterate Timestamp
  */

  static async getTimestamp () {
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    const timeStamp = Math.floor(Date.now() / 1000)
    return `${timeStamp}`
  }

  /*
  This function is used to generate Order No.
  */
  static async generateOrderNo () {
    const random = await this.generateOTP(4)
    const timestamp = await this.getTimestamp('')
    const orderId = `MAWEB${random}${timestamp}`
    return orderId
  }

  /**
   * @private
   * callAuthApi description - call MA api to get merchant checksum and validate
   * @param {{ ma_user_id: number, userid: number }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async callAuthApi (fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'request', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      console.time('TIME_FOR_AEPS_AUTH_API')
      console.log('Capture Response Decoded : ', JSON.parse(Buffer.from(fields.captureResponse, 'base64').toString('utf-8')))
      console.log('Card number response decoded : ', common_fns.maskValue(JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8')), 'adhaarNumber'))

      // check whether, we are getting the bank code from RegisterMerchantAuth else take the bank code from ma_aeps_bank_master table
      let bankCodeVal = ''

      if (fields.transactionType == 'AUTH') {
        if ((!fields.bank_code) || fields.bank_code == null || fields.bank_code == ' ' || fields.bank_code == '0') {
          const bankCodeResult = await this.getBankCode()
          bankCodeVal = bankCodeResult.bankCode
        } else {
          bankCodeVal = fields.bank_code
        }
      } else if (fields.transactionType == 'TRANSACTION_AUTH') {
        // get bank code from aeps_merchant auth table for Cash withdrawal transaction
        const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
        logs.logger({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantQuery', fields: getMerchantQuery })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantQuery', fields: getMerchantQuery })
        const getMerchantResult = await this.rawQuery(getMerchantQuery, connectionRead)
        logs.logger({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantResult', fields: getMerchantResult })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantResult', fields: getMerchantResult })
        if (getMerchantResult.length > 0) { // if user is already registered then check expiry time
          // ${expiry_time} > DATE_SUB(NOW(), INTERVAL 12 HOUR) ,
          // Get expiry hours from system codes table
          bankCodeVal = getMerchantResult[0].received_bank_id
        } else {
          const bankCodeResult = await this.getBankCode()
          bankCodeVal = bankCodeResult.bankCode
        }
      }

      // get mobile no. from ma_user_master table
      // get bank code from aeps_merchant auth table for Cash withdrawal transaction
      const getMerchantMob = `select mobile_id, CONCAT(firstname, ' ', lastname) AS merchant_name from ma_user_master mamat where profileid = '${fields.ma_user_id}' and mer_user = 'mer' order by ma_user_master_id desc limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantMob', fields: getMerchantMob })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantMob', fields: getMerchantMob })
      const getMerchantMobResult = await this.rawQuery(getMerchantMob, connectionRead)
      logs.logger({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantMobResult', fields: getMerchantMobResult })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'callAuthApi', type: 'getMerchantMobResult', fields: getMerchantMobResult })
      if (getMerchantMobResult.length > 0) {
        fields.mobile_number = getMerchantMobResult[0].mobile_id
      }

      // Call Aeps Auth API
      const postParams = {
        merchantTransactionId: fields.order_id,
        merchantTranId: fields.order_id,
        captureResponse: JSON.parse(Buffer.from(fields.captureResponse, 'base64').toString('utf-8')),
        cardnumberORUID: JSON.parse(Buffer.from(fields.cardnumberORUID, 'base64').toString('utf-8')),
        languageCode: fields.languageCode,
        latitude: fields.latitude,
        longitude: fields.longitude,
        mobileNumber: fields.mobile_number,
        paymentType: fields.paymentType,
        requestRemarks: '',
        timestamp: fields.timestamp,
        transactionAmount: '',
        transactionType: fields.transactionType == 'AUTH' ? 'AUTH' : 'MAUTH', // pass mauth for aeps cashwwithdrawal transaction merchant check
        merchantAggName: getMerchantMobResult[0].merchant_name,
        merchantPin: '',
        subMerchantId: '',
        call_type: fields.call_type,
        email: '',
        mercid: fields.ma_user_id,
        userid: fields.userid,
        privatekey: fields.privatekey,
        checksum: '',
        bankCode: bankCodeVal,
        mer_dom: Buffer.from((Buffer.from(fields.mer_dom, 'base64').toString('utf-8')).replace(/(^"|"$)/g, '')).toString('base64')
      }
      console.log('---Auth API Params---', { ...postParams, cardnumberORUID: common_fns.maskValue(postParams.cardnumberORUID, 'adhaarNumber') })

      const postData = await generateOrderchecksum(this, postParams)
      if (postData.status != 200) {
        return { status: postData.status, respcode: postData.respcode, message: postData.message }
      }

      const response = await axios({
        method: 'post',
        url: AEPS_AUTH_API_URL,
        data: postData.data,
        headers: HEADER_CONTENT,
        timeout: 25000 // 25 secs
      })
      console.timeEnd('TIME_FOR_AEPS_AUTH_API')

      return { status: 200, message: 'Success', response: response, sentBankCode: bankCodeVal }
    } catch (err) {
      logs.logger({ pagename: require('path').basename(__filename), action: 'callAuthApi', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'callAuthApi', type: 'catcherror', fields: err })
      if (err.isAxiosError && err.code) {
        return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + ': Request aborted, Please try again!' }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } finally {
      connectionRead.release()
    }
  }

  /**
   * This function is used to save register merchant at MA side using mobile & aadhaar no.
   * @param {*} _
   * @param {*} fields
  */
  static async registerMerchantForAepsAuth (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'request', fields: common_fns.maskValue(fields, 'merchant_aadhaar_no') })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'request', fields: common_fns.maskValue(fields, 'merchant_aadhaar_no') })
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'captureResponse'])
    logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'validatorResponse', fields: validatorResponse })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'validatorResponse', fields: validatorResponse })
    if (validatorResponse.status != 200) return validatorResponse
    const connection = await mySQLWrapper.getConnectionFromPool()
    const response_aadhar_val = common_fns.maskValue(fields.merchant_aadhaar_no).substr(-4)

    try {
      const common = require('../../util/common')
      const aepsTwoFactorAuthCheckVal = await common.getSystemCodes(this, util.aepsTwoFactorAuthCheck, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'aepsTwoFactorAuthCheckVal-response', fields: aepsTwoFactorAuthCheckVal })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'aepsTwoFactorAuthCheckVal-response', fields: aepsTwoFactorAuthCheckVal })

      if (!fields.order_id || fields.order_id == '' || fields.order_id == null || fields.order_id == ' ' || fields.order_id == undefined) {
        fields.order_id = await this.generateOrderNo()
      }

      if (aepsTwoFactorAuthCheckVal == 'Y') {
        // check merchant location within assigned kilometers
        const aepsMerchantLocationValidationsVal = await common.getSystemCodes(this, util.aepsMerchantLocationValidation, connection)
        logs.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'aepsMerchantLocationValidationsVal', fields: aepsMerchantLocationValidationsVal })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'aepsMerchantLocationValidationsVal', fields: aepsMerchantLocationValidationsVal })
        if (aepsMerchantLocationValidationsVal == 'Y') {
          const skipSavingLocation = 'Y'
          //fields.login_ip = ''
          fields.aggregator_order_id = fields.order_id
          const merchantLocationResult = await aepsTransactionController.checkMerchantRadius(fields, skipSavingLocation)
          logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'merchantLocationResult', fields: merchantLocationResult })
          // lokiLogger.info({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'merchantLocationResult', fields: merchantLocationResult })
          if (merchantLocationResult.status == 400) {
            return merchantLocationResult
          }
        }

        // check if merchant is already registered then update the checksum
        const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, expiry_time,sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' ORDER BY ma_aeps_merchant_auth_token_id DESC limit 1`
        logs.logger({ pagename: path.basename(__filename), action: 'getLeadForm', type: 'getMerchantQuery', fields: getMerchantQuery })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'getLeadForm', type: 'getMerchantQuery', fields: getMerchantQuery })
        const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
        logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'getMerchantResult', fields: getMerchantResult })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'getMerchantResult', fields: getMerchantResult })

        let ma_aeps_merchant_auth_token_id = 0
        let sendBankId = 0
        if (getMerchantResult.length > 0) {
          ma_aeps_merchant_auth_token_id = getMerchantResult[0].ma_aeps_merchant_auth_token_id
        }
        // Call MA Api - pass Merchant mobile no & aadhar card no.

        // check whether transaction type is adpay
        //&&&&&
        // if (((fields.transaction_type) && fields.transaction_type == 'adpay') && (fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == 0 || fields.bank_code == 'null')) {
        //   fields.bank_code = FINO_BANK_ID
        // }
        //&&&&&
        if (((fields.transaction_type) && fields.transaction_type == 'adpay') && (fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == 0 || fields.bank_code == 'null')) {
          console.log('inside')
          const bankCodeResult = await this.getAdpayBankCode()
          if(bankCodeResult.status == 200){
            fields.bank_code = bankCodeResult.bankCode
          }
        }
        console.log('fields.bank_code',fields.bank_code)
        if(fields.bank_code == CREDOPAY_BANK_ID && fields.call_type == 'adpay'){
          //check for kyc and route
          const kycStatusQuery = `SELECT approval_status,rekyc_id FROM ma_credopay_merchant_onboarding WHERE ma_user_id = '${fields.ma_user_id}' AND user_id = '${fields.userid}' ORDER BY addedon DESC LIMIT 1`
          logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'check record exists or not', fields: kycStatusQuery })
          const kycStatusResult = await this.rawQuery(kycStatusQuery, connection)
          logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'check record exists or not', fields: kycStatusResult })
          if (kycStatusResult.length > 0 && kycStatusResult[0].approval_status == 'S') {
            fields.bank_code = CREDOPAY_BANK_ID
          }else{
            //route to next priority bank for adpay
            fields.bank_code = FINO_BANK_ID
          }
        }
        if ((!fields.bank_code) || fields.bank_code == null || fields.bank_code == ' ' || fields.bank_code == '0' && ((fields.transaction_type) && fields.transaction_type == 'aeps')) {
          const bankCodeResult = await this.getBankCode()
          fields.bank_code = bankCodeResult.bankCode
        } else {
          fields.bank_code = fields.bank_code
        }
        if( fields.bank_code == CREDOPAY_BANK_ID && fields.call_type == 'aeps'){
          const kycStatusQuery = `SELECT approval_status,rekyc_id FROM ma_credopay_merchant_onboarding WHERE ma_user_id = '${fields.ma_user_id}' AND user_id = '${fields.userid}' ORDER BY addedon DESC LIMIT 1`
          logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuthAEPS', type: 'check record exists or not', fields: kycStatusQuery })
          const kycStatusResult = await this.rawQuery(kycStatusQuery, connection)
          logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuthAEPS', type: 'check record exists or not', fields: kycStatusResult })
          if (kycStatusResult.length > 0 && kycStatusResult[0].approval_status == 'S') {
            fields.bank_code = CREDOPAY_BANK_ID
          }else{
             //route to next priority bank for aeps
             const sqlIntReq = `SELECT bank_code FROM ma_aeps_bank_master WHERE active_status = 'A' ORDER BY priority ASC LIMIT 1 OFFSET 1`
             logs.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'sqlIntReq', fields: sqlIntReq })
             const dataIntReq = await this.rawQuery(sqlIntReq, connection)
             fields.bank_code = dataIntReq[0].bank_code
             logs.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getBankCode-request', fields: fields })
          }
        }
        console.log("Final bank code", fields.bank_code)
        console.log('source>',fields.user_agent)
        const str = fields.user_agent.substring(0,6)
        if(str != 'okhttp'){  
          const data = {
             ma_user_id:fields.ma_user_id,
             userid :fields.userid,
             login_ip : fields.login_ip
         }      
         const getSuccessLatLongData =  await aepsTransactionController.getSuccessGeoLocationData(data)
         logs.logger({ pagename: require('path').basename(__filename), action: 'doAepsTransaction', type: 'getSuccessLatLongData', fields: getSuccessLatLongData })
         if(getSuccessLatLongData.status == 400){
           return getSuccessLatLongData
         }
        if(getSuccessLatLongData.status == 200 && getSuccessLatLongData.data){
           fields.latitude =  getSuccessLatLongData.data.latitude
           fields.longitude = getSuccessLatLongData.data.longitude
         }
        }
        console.log('fields.latitude',fields.latitude)
        console.log('fields.longitude',fields.longitude)
        const responseDataResult = await this.callAuthApi(fields)
        // console.log("responseDataResult",responseDataResult)
        // logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'callAuthApi-response', fields: responseDataResult })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'callAuthApi-response', fields: responseDataResult })
        const response = responseDataResult.response
        // console.log("response result",response.data.message);
        console.log('response', responseDataResult)
        const response_data = JSON.stringify(responseDataResult.response.data)
        console.log('response_data', response_data)
        console.log('response result', response.data.message)
        fields.bank_code = responseDataResult.response.data.bankcode
        console.log('recieved bankcode ', fields.bank_code)
        const order2FA_ID = await this.generateOrderNo()
        console.log('2FA charges orderid', order2FA_ID)
        if (fields.transactionType == 'AUTH') {
          let flag = false
          let chargesAmount = ''
          let gstType = ''
          let gst_per = ''
          let charge_amount_type = ''
          let GSTAmount = ''
          // Check condition for integrated users
          const userintegrated = `Select ma_user_id from ma_integration_user_master Where ma_user_id = '${fields.ma_user_id}' AND integration_code = 'EMITRA' AND record_status = 'Y'`
          const resultint = await this.rawQuery(userintegrated, connection)
          logs.logger({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - Integrated user check', fields: resultint })
          // lokiLogger.info({ pagename: 'aepsDistributionController.js', action: 'getDistribution', type: 'response - Integrated user check', fields: resultint })
          if (resultint.length > 0) {
            flag = true
          } else {
            const sqlCharge = `select charge_amount,gst_type,gst_amount,charge_amount_type,bank_id from ma_slabwise_distribution_2fa where charge_type ='Calendar Day' and record_status = 'Y' and bank_id = '${fields.bank_code}' limit 1`
            logs.logger({
              pagename: require('path').basename(__filename),
              action: 'registerMerchantForAepsAuth',
              type: 'sqlCharge',
              fields: sqlCharge
            })
            // lokiLogger.info({
            //   pagename: require('path').basename(__filename),
            //   action: 'registerMerchantForAepsAuth',
            //   type: 'sqlCharge',
            //   fields: sqlCharge
            // })
            const sqlChargeDetails = await this.rawQuery(sqlCharge, connection)
            logs.logger({
              pagename: require('path').basename(__filename),
              action: 'doAepsCashWithdrawal',
              type: 'sqlChargeResult',
              fields: sqlChargeDetails
            })
            // lokiLogger.info({
            //   pagename: require('path').basename(__filename),
            //   action: 'doAepsCashWithdrawal',
            //   type: 'sqlChargeResult',
            //   fields: sqlChargeDetails
            // })
            if (sqlChargeDetails.length == 0) {
              const sqlCharge = 'select charge_amount,gst_type,gst_amount,charge_amount_type,bank_id from ma_slabwise_distribution_2fa where charge_type =\'Calendar Day\' and record_status = \'Y\' and bank_id = \'-1\' limit 1'
              logs.logger({
                pagename: require('path').basename(__filename),
                action: 'registerMerchantForAepsAuth',
                type: 'sqlCharge',
                fields: sqlCharge
              })
              // lokiLogger.info({
              //   pagename: require('path').basename(__filename),
              //   action: 'registerMerchantForAepsAuth',
              //   type: 'sqlCharge',
              //   fields: sqlCharge
              // })
              const sqlChargeDetails = await this.rawQuery(sqlCharge, connection)
              logs.logger({
                pagename: require('path').basename(__filename),
                action: 'registerMerchantForAepsAuth',
                type: 'sqlChargeResult',
                fields: sqlChargeDetails
              })
              // lokiLogger.info({
              //   pagename: require('path').basename(__filename),
              //   action: 'registerMerchantForAepsAuth',
              //   type: 'sqlChargeResult',
              //   fields: sqlChargeDetails
              // })
              // All code
              if (sqlChargeDetails.length == 0) {
                logs.logger({
                  pagename: require('path').basename(__filename),
                  action: 'registerMerchantForAepsAuth',
                  type: 'sqlChargeResult',
                  fields: 'No bank slabs found'
                })
                // lokiLogger.info({
                //   pagename: require('path').basename(__filename),
                //   action: 'registerMerchantForAepsAuth',
                //   type: 'sqlChargeResult',
                //   fields: 'No bank slabs found'
                // })
                flag = true
              } else {
                chargesAmount = sqlChargeDetails[0].charge_amount
                if (chargesAmount == 0 || chargesAmount == null) {
                  flag = true
                }
                console.log('chargesAmount', chargesAmount)
                gstType = sqlChargeDetails[0].gst_type
                gst_per = sqlChargeDetails[0].gst_amount
                charge_amount_type = sqlChargeDetails[0].charge_amount_type
                console.log('charge_amount_type', charge_amount_type)
                console.log('gst_per', gst_per)

                GSTAmount = chargesAmount * (gst_per / 100)
                console.log('GSTAmount', GSTAmount)
                if (charge_amount_type == 2) {
                  return {
                    status: 400,
                    respcode: 1001,
                    message: 'percentage charge type not allowed'
                  }
                }
              }
            } else {
            // bank wise
              chargesAmount = sqlChargeDetails[0].charge_amount
              if (chargesAmount == 0 || chargesAmount == null) {
                flag = true
              }
              console.log('chargesAmount', chargesAmount)
              gstType = sqlChargeDetails[0].gst_type
              gst_per = sqlChargeDetails[0].gst_amount
              charge_amount_type = sqlChargeDetails[0].charge_amount_type
              console.log('charge_amount_type', charge_amount_type)
              console.log('gst_per', gst_per)

              GSTAmount = chargesAmount * (gst_per / 100)
              console.log('GSTAmount', GSTAmount)
              if (charge_amount_type == 2) {
                return {
                  status: 400,
                  respcode: 1001,
                  message: 'percentage charge type not allowed'
                }
              }
            }
          }
          if (flag == false) {
            let totalAmount = ''
            if (gstType == 'E') {
              totalAmount = (chargesAmount + GSTAmount).toFixed(2)
            } else if (gstType == 'I') {
              totalAmount = (chargesAmount - GSTAmount).toFixed(2)
            } else {
              totalAmount = chargesAmount.toFixed(2)
            }

            console.log('totalAmount', totalAmount)
            // Check available balance
            const availableBalance =
            await balanceController.getWalletBalancesDirect(_, {
              ma_user_id: fields.ma_user_id,
              ma_status: 'ACTUAL',
              balance_flag: 'SUMMARY',
              connection: connection
            })
            if (availableBalance.status != 200) return availableBalance
            logs.logger({
              pagename: path.basename(__filename),
              action: 'registerMerchantForAepsAuth',
              type: 'balanceCheck',
              fields: availableBalance
            })
            // lokiLogger.info({
            //   pagename: path.basename(__filename),
            //   action: 'registerMerchantForAepsAuth',
            //   type: 'balanceCheck',
            //   fields: availableBalance
            // })
            if (availableBalance.amount < totalAmount) {
              return {
                status: 400,
                respcode: 1001,
                message: 'Insufficient balance, kindly fund the account to enable AePS Service.'
              }
            }
            const newFields = {
              ma_user_id: fields.ma_user_id,
              userid: fields.userid,
              orderid: fields.order_id,
              amount: totalAmount,
              chargesAmount: chargesAmount,
              transaction_type: '64',
              remarks: 'AEPS 2FA calendar charge Transaction',
              gstType: gstType,
              GSTAmount: GSTAmount,
              newAmount: totalAmount,
              gst_amount: gst_per,
              description: 'AEPS 2FA calendar charge Transaction',
              bank_rrn: response?.data?.rrn 
            }
            await mySQLWrapper.beginTransaction(connection)

            const transactionResponse = await this.create2FATransaction({
              fields: newFields,
              connection
            })
            console.log('transactionResponse', transactionResponse)
            logs.logger({
              pagename: path.basename(__filename),
              action: 'registerMerchantForAepsAuth',
              type: 'transaction Response',
              fields: transactionResponse
            })
            // lokiLogger.info({
            //   pagename: path.basename(__filename),
            //   action: 'registerMerchantForAepsAuth',
            //   type: 'transaction Response',
            //   fields: transactionResponse
            // })
            if (transactionResponse.status != 200) {
              await mySQLWrapper.rollback(connection)
              return transactionResponse
            }
            // update ledger entries
            const ledgerEntries = await this.ledgerEntries({
              fields: newFields,
              connection
            })
            logs.logger({
              pagename: path.basename(__filename),
              action: 'registerMerchantForAepsAuth',
              type: 'ledgerEntries',
              fields: ledgerEntries
            })
            // lokiLogger.info({
            //   pagename: path.basename(__filename),
            //   action: 'registerMerchantForAepsAuth',
            //   type: 'ledgerEntries',
            //   fields: ledgerEntries
            // })
            if (ledgerEntries.status != 200) {
              await mySQLWrapper.rollback(connection)
              return ledgerEntries
            }
            const aepsTransactionLogsQuery = `insert into ma_aeps_transaction_logs (ma_user_id, order_id, amount, transaction_status, aeps_mode, aggregator_bank, bank_id,bank_response) values ( '${fields.ma_user_id}','${fields.order_id}','${totalAmount}', 'S', 'AEPS daily charges','${fields.bank_code || 0}','${fields.bank_id || 0}','${response_data}')`
            logs.logger({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogsQuery', type: 'LogQuery', fields: aepsTransactionLogsQuery })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogsQuery', type: 'LogQuery', fields: aepsTransactionLogsQuery })
            const aepsTransactionLogs = await this.rawQuery(aepsTransactionLogsQuery, connection)
            logs.logger({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogs', type: 'LogResult', fields: aepsTransactionLogs })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogs', type: 'LogResult', fields: aepsTransactionLogs })
          } else if (flag == true) {
            const newFields = {
              ma_user_id: fields.ma_user_id,
              userid: fields.userid,
              orderid: fields.order_id,
              amount: 0,
              chargesAmount: 0,
              transaction_type: '64',
              remarks: 'AEPS 2FA calendar charge Transaction',
              gstType: gstType,
              GSTAmount: GSTAmount,
              merchant_type: 'emitra', 
              bank_rrn: response?.data?.rrn 
            }
            logs.logger({
              pagename: path.basename(__filename),
              action: 'registerMerchantForAepsAuth',
              type: 'transaction Response for No slab',
              fields: newFields
            })
            // lokiLogger.info({
            //   pagename: path.basename(__filename),
            //   action: 'registerMerchantForAepsAuth',
            //   type: 'transaction Response for No slab',
            //   fields: newFields
            // })
            await mySQLWrapper.beginTransaction(connection)

            const transactionResponse = await this.create2FATransaction({
              fields: newFields,
              connection
            })
            console.log('transactionResponse for No slab', transactionResponse)
            logs.logger({
              pagename: path.basename(__filename),
              action: 'registerMerchantForAepsAuth',
              type: 'transaction Response for No slab',
              fields: transactionResponse
            })
            // lokiLogger.info({
            //   pagename: path.basename(__filename),
            //   action: 'registerMerchantForAepsAuth',
            //   type: 'transaction Response for No slab',
            //   fields: transactionResponse
            // })
            if (transactionResponse.status != 200) {
              await mySQLWrapper.rollback(connection)
              return transactionResponse
            }
          }
        }
        const insertUserQuery = `INSERT INTO ma_geo_location (ma_user_id,user_id,order_id,request_type,amount,latitude,longitude,login_ip) VALUES (${fields.ma_user_id},${fields.userid},'${fields.order_id}','aeps','0','${fields.latitude}','${fields.longitude}','${fields.login_ip}')`
        logs.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: insertUserQuery })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: insertUserQuery })
        const params = await this.rawQuery(insertUserQuery, connection)
        logs.logger({ pagename: require('path').basename(__filename), action: 'checkMerchantRadius', type: 'insertUserQuery', fields: params })

        if (responseDataResult.status == 200 && response != '' && response.status == 200) {
          const responseData = typeof (response.data) == 'string' ? JSON.parse(response.data) : response.data
          console.log('response data', responseData.message)
          logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'responseData status', fields: responseData })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'responseData status', fields: responseData })
          if (responseData.status == 200 && (responseData.responseCode == '0' || responseData.responseCode == '00')) {
            const sqlCurrentTimeNow = 'SELECT DATE_FORMAT(CURRENT_TIMESTAMP,\'%Y-%m-%d 23:59:59\') as sqlCurrentTimeNow'
            const resultTimeNow = await this.rawQuery(sqlCurrentTimeNow, connection)
            console.log('resultTime>>', resultTimeNow)
            const sqlCurrentTimeVal = resultTimeNow[0].sqlCurrentTimeNow
            sendBankId = responseDataResult.sentBankCode
            if (fields.transactionType == 'AUTH') {
              if (ma_aeps_merchant_auth_token_id > 0 && (fields.bank_code == '' || fields.bank_code == undefined || fields.bank_code == ' ' || fields.bank_code == '0') && getMerchantResult[0].sent_bank_id == responseDataResult.sentBankCode) {
              // update query
                const sqlUpdateMerchantAuth = `Update ma_aeps_merchant_auth_token set authtoken ='${responseData.checksum}', expiry_time= '${sqlCurrentTimeVal}', sent_bank_id = '${responseDataResult.sentBankCode}', received_bank_id = '${responseData.bankcode}' where ma_aeps_merchant_auth_token_id = ${ma_aeps_merchant_auth_token_id} `
                const sqlUpdateMerchantAuthResult1 = await this.rawQuery(sqlUpdateMerchantAuth, connection)
                logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'updateEntry status', fields: sqlUpdateMerchantAuthResult1 })
                // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'updateEntry status', fields: sqlUpdateMerchantAuthResult1 })

                // User is authorised, save details in aeps_auth_logs table
                const logData = {
                  ma_user_id: fields.ma_user_id,
                  order_id: fields.order_id,
                  api_response: JSON.stringify(responseData).replace(/'/g, '"'),
                  request_type: 'Authorisation',
                  bank_code: responseData.bankcode,
                  auth_status: 'Success',
                  bank_rrn: responseData.rrn
                }
                const resultTwoFaLogs = await this.saveTwoFactorLogs(logData, connection)
                logs.logger({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
                // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
                //&&&&&
                // if (fields.transaction_type == 'adpay' && responseData.bankcode != FINO_BANK_ID && FINO_BANK_ID == responseDataResult.sentBankCode) {
                //   return { status: 400, respcode: 1001, message: 'Bank is temporarily down, please try after sometime.', authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH' }
                //   // return { status: 400, respcode: 1001,message: responseData.message, authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode }
                // }
                //&&&&&
                 if (fields.transaction_type == 'adpay' && (responseData.bankcode != FINO_BANK_ID || responseData.bankcode != CREDOPAY_BANK_ID) && (FINO_BANK_ID == responseDataResult.sentBankCode || CREDOPAY_BANK_ID == responseDataResult.sentBankCode)) {
                  return { status: 400, respcode: 1001, message: 'Bank is temporarily down, please try after sometime.', authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH' }
                  // return { status: 400, respcode: 1001,message: responseData.message, authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode }
                }
                // return { status: 200, respcode: 1000, message: 'The authentication has been successfully completed for the Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH' }
                let cwAuthRequired = ''
                const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
                  bankCode: responseData.bankcode,
                  connection
                })
                if (iscw2FAAuthRequiredResult.status == 200) {
                  cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                  logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  console.log('cwAuthRequire', cwAuthRequired)
                  return { status: 200, respcode: 1000, message: 'The authentication has been successfully completed for the Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH', cw_auth_required: cwAuthRequired }
                } else {
                  console.log('iscw2FAAuthRequiredResult ', iscw2FAAuthRequiredResult.status)
                  const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                  return { status: 200, respcode: 1000, message: 'The authentication has been successfully completed for the Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH', cw_auth_required: cwAuthRequired }
                }
              } else {
              // else insert new record in ma_aeps_merchant_auth table
                const _data = await this.createEntry(_, { ma_user_id: fields.ma_user_id, auth_token: responseData.checksum, expiry_time: sqlCurrentTimeVal, sentBankId: responseDataResult.sentBankCode, receivedBankId: responseData.bankcode })
                logs.logger({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'createEntry status', fields: _data })
                // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'registerMerchantForAepsAuth', type: 'createEntry status', fields: _data })

                // User is authorised, save details in aeps_auth_logs table
                const logData = {
                  ma_user_id: fields.ma_user_id,
                  order_id: fields.order_id,
                  api_response: JSON.stringify(responseData).replace(/'/g, '"'),
                  request_type: 'Registration',
                  bank_code: responseData.bankcode,
                  auth_status: 'Success',
                  bank_rrn: responseData.rrn
                }
                const resultTwoFaLogs = await this.saveTwoFactorLogs(logData, connection)
                logs.logger({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
                // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
                //&&&&&
                // if (fields.transaction_type == 'adpay' && responseData.bankcode != FINO_BANK_ID && FINO_BANK_ID == responseDataResult.sentBankCode) {
                //   return { status: 400, respcode: 1001, message: 'Bank is temporarily down, please try after sometime.', authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH' }
                // // return { status: 400, respcode: 1001,message: responseData.message, authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode }
                // }
                //&&&&&
                if (fields.transaction_type == 'adpay' && (responseData.bankcode != FINO_BANK_ID || responseData.bankcode != CREDOPAY_BANK_ID) && (FINO_BANK_ID == responseDataResult.sentBankCode || CREDOPAY_BANK_ID == responseDataResult.sentBankCode)) {
                  return { status: 400, respcode: 1001, message: 'Bank is temporarily down, please try after sometime.', authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH' }
                // return { status: 400, respcode: 1001,message: responseData.message, authentication_required: 'Y', userRegistered: 'Y', bank_code: responseData.bankcode }
                }

                // return { status: 200, respcode: 1000, message: 'Your registration for AePS two factor authentication is successful for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH' }
                let cwAuthRequired = ''
                const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({ bankCode: responseData.bankcode, connection })
                if (iscw2FAAuthRequiredResult.status == 200) {
                  cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                  logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  console.log('cwAuthRequire', cwAuthRequired)
                  return { status: 200, respcode: 1000, message: 'Your registration for AePS two factor authentication is successful for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH', cw_auth_required: cwAuthRequired }
                } else {
                  console.log('iscw2FAAuthRequiredResult ', iscw2FAAuthRequiredResult.status)
                  const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                  return { status: 200, respcode: 1000, message: 'Your registration for AePS two factor authentication is successful for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'AUTH', cw_auth_required: cwAuthRequired }
                }
              }
            } else if (fields.transactionType == 'TRANSACTION_AUTH') {
              // save logs in aeps cash withdrawal table
              const insertLogQuery = `INSERT INTO ma_aeps_cw_transaction_logs (ma_user_id,order_id,merchant_txn_id,sent_bank_id,received_bank_id,request_type,two_factor_auth_status,api_response,two_factor_order_id) VALUES (${fields.ma_user_id},'${fields.order_id}','${responseData.MerAuthTxnId}','${responseDataResult.sentBankCode}','${responseData.bankcode}','CW','S','${response_data}','${order2FA_ID}')`
              logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
              const LogResult = await this.rawQuery(insertLogQuery, connection)
              logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
              return { status: 200, respcode: 1000, message: 'The authentication has been successfully completed for the Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'N', userRegistered: 'Y', bank_code: responseData.bankcode, auth_type: 'TRANSACTION_AUTH', two_factor_order_id: order2FA_ID }
            }
          } else if (responseData.status == 400 && (responseData.error != undefined && responseData.error != '' && responseData.error == true)) {
            console.log('responseData error', responseData.error)
            if (fields.transactionType == 'TRANSACTION_AUTH') {
              // save logs in aeps cash withdrawal table
              const insertLogQuery = `INSERT INTO ma_aeps_cw_transaction_logs (ma_user_id,order_id,merchant_txn_id,sent_bank_id,received_bank_id,request_type,two_factor_auth_status,api_response,two_factor_order_id) VALUES (${fields.ma_user_id},'${fields.order_id}','${responseData.MerAuthTxnId}','${responseDataResult.sentBankCode}','${responseData.bankcode}','CW','F','${response_data}','${order2FA_ID}')`
              logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
              const LogResult = await this.rawQuery(insertLogQuery, connection)
              logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
              const failedEntriesData = {
                ma_user_id: fields.ma_user_id,
                userid: fields.userid,
                orderid: order2FA_ID,
                bank_code: responseData.bankcode,
                parent_orderid: fields.order_id,
                response_data: response_data,
                description: 'AEPS 2FA cash withdrawal Transaction'
              }
              logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'failedEntriesData', fields: failedEntriesData })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'failedEntriesData', fields: failedEntriesData })

              const transactionUpdate = await this.FailedTransactionDetails(_, failedEntriesData)
              if (transactionUpdate.status != 200) return transactionUpdate

              // return { status: 400, respcode: 1001, message: 'Failed ! Your authentication failed for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'Y', userRegistered: 'N', bank_code: 0, auth_type: 'TRANSACTION_AUTH' }
              return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + response.data.message, authentication_required: 'Y', userRegistered: 'N', bank_code: responseData.bankcode, auth_type: 'TRANSACTION_AUTH', two_factor_order_id: order2FA_ID }
            }
            const logData = {
              ma_user_id: fields.ma_user_id,
              order_id: fields.order_id,
              api_response: JSON.stringify(responseData).replace(/'/g, '"'),
              request_type: 'Authorisation',
              bank_code: 0,
              auth_status: 'Fail',
              bank_rrn: responseData.rrn
            }
            const resultTwoFaLogs = await this.saveTwoFactorLogs(logData, connection)
            logs.logger({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
            return { status: 400, respcode: 1001, message: 'Your onboarding with the bank for AePS services is pending. Please contact Customer Support for assistance.', authentication_required: 'Y', userRegistered: 'N', bank_code: 0, auth_type: 'AUTH' }
            // return { status: 400, respcode: 1001, message: responseData.message, authentication_required: 'Y', userRegistered: 'N', bank_code: 0 }
          } else {
            if (fields.transactionType == 'TRANSACTION_AUTH') {
              // save logs in aeps cash withdrawal table
              const insertLogQuery = `INSERT INTO ma_aeps_cw_transaction_logs (ma_user_id,order_id,merchant_txn_id,sent_bank_id,received_bank_id,request_type,two_factor_auth_status,api_response,two_factor_order_id) VALUES (${fields.ma_user_id},'${fields.order_id}','${responseData.MerAuthTxnId}','${responseDataResult.sentBankCode}','${responseData.bankcode}','CW','F','${response_data}','${order2FA_ID}')`
              logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
              const LogResult = await this.rawQuery(insertLogQuery, connection)
              logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
              const failedEntriesData = {
                ma_user_id: fields.ma_user_id,
                userid: fields.userid,
                order_id: order2FA_ID,
                parent_orderid: fields.order_id,
                bank_code: responseData.bankcode,
                response_data: response_data,
                description: 'AEPS 2FA cash withdrawal Transaction'
              }
              logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'failedEntriesData', fields: failedEntriesData })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'failedEntriesData', fields: failedEntriesData })
              const transactionUpdate = await this.FailedTransactionDetails(_, failedEntriesData)
              if (transactionUpdate.status != 200) return transactionUpdate
              return { status: 400, respcode: 1001, message: errorMsg.responseCode[1028] + response.data.message, authentication_required: 'Y', userRegistered: 'N', bank_code: responseData.bankcode, auth_type: 'TRANSACTION_AUTH', two_factor_order_id: order2FA_ID }
            }
            // User is authorised, save details in aeps_auth_logs table
            if (ma_aeps_merchant_auth_token_id > 0) {
              // User is authorised, save details in aeps_auth_logs table
              const logData = {
                ma_user_id: fields.ma_user_id,
                order_id: fields.order_id,
                api_response: JSON.stringify(responseData).replace(/'/g, '"'),
                request_type: 'Authorisation',
                bank_code: 0,
                auth_status: 'Fail',
                bank_rrn: responseData.rrn
              }
              const resultTwoFaLogs = await this.saveTwoFactorLogs(logData, connection)
              logs.logger({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
              // return { status: 400, respcode: 1001, message: 'Failed ! Your registration for AePS two factor authentication failed for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'Y', userRegistered: 'N', bank_code: 0 }
              if (responseData.message == null || responseData.message == '') {
                return { status: 400, respcode: 1001, message: 'Failed ! Your authentication failed for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'Y', userRegistered: 'N', bank_code: 0, auth_type: 'AUTH' }
                // return { status: 400, respcode: 1001,message: responseData.message, authentication_required: 'Y', userRegistered: 'N', bank_code: 0 }
              } else {
                return { status: 400, respcode: 1001, message: responseData.message, authentication_required: 'Y', userRegistered: 'N', bank_code: 0, auth_type: 'AUTH' }
              }
            } else {
              // User is authorised, save details in aeps_auth_logs table
              const logData = {
                ma_user_id: fields.ma_user_id,
                order_id: fields.order_id,
                api_response: JSON.stringify(responseData).replace(/'/g, '"'),
                request_type: 'Authorisation',
                bank_code: 0,
                auth_status: 'Fail',
                bank_rrn: responseData.rrn
              }
              const resultTwoFaLogs = await this.saveTwoFactorLogs(logData, connection)
              logs.logger({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getMerchantResult', type: 'LogResult', fields: resultTwoFaLogs })
              // return { status: 400, respcode: 1001, message: 'Failed ! Your authentication failed for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'Y', userRegistered: 'N', bank_code: 0 }
              if (responseData.message == null || responseData.message == '') {
                return { status: 400, respcode: 1001, message: 'Failed ! Your authentication failed for Aadhaar number ending with ' + response_aadhar_val + '.', authentication_required: 'Y', userRegistered: 'N', bank_code: 0, auth_type: 'AUTH' }
                // return { status: 400, respcode: 1001,message: responseData.message, authentication_required: 'Y', userRegistered: 'N', bank_code: 0 }
              } else {
                return { status: 400, respcode: 1001, message: responseData.message, authentication_required: 'Y', userRegistered: 'N', bank_code: 0, auth_type: 'AUTH' }
              }
            }
          }
        } else if (response != '' && response.status == 400 && fields.bank_code != '' && fields.bank_code != ' ') {
          // In retry transaction, if the authentication is failed with new bank then ask Merchant to try with other bank
          return { status: 400, message: errorMsg.responseCode[1028] + ' : Authentication failed, Please try with other bank.', respcode: 1028, authentication_required: 'Y', userRegistered: 'Y', bank_code: 0, auth_type: 'AUTH' }
          // return { status: 400, message: errorMsg.responseCode[1028] + response.data.message, respcode: 1028, authentication_required: 'Y', userRegistered: 'Y', bank_code: 0 }
        } else {
          return { status: 400, message: errorMsg.responseCode[1028] + response.data.message, respcode: 1028, authentication_required: 'Y', userRegistered: 'Y', auth_type: 'AUTH' }
          // return { status: 400, message: errorMsg.responseCode[1028] + ' : Invalid Response', respcode: 1028, authentication_required: 'Y', userRegistered: 'Y' }
        }
      } else {
        return { status: 200, respcode: 1000, message: 'Success', authentication_required: 'N', userRegistered: 'Y', auth_type: 'AUTH' }
      }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'request', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'registerMerchantForAepsAuth', type: 'request', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /**
   * getAepsMerchantAuth description - This function is used to get Flag from ma_aeps_merchant_auth_token, whether merchant auth is active or not.
   * @param {null} _
   * @param {{ ma_user_id: number, userid: number }} fields
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async getAepsMerchantAuth (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'request', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const common = require('../../util/common')
      const aepsTwoFactorAuthCheckVal = await common.getSystemCodes(this, util.aepsTwoFactorAuthCheck, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'aepsTwoFactorAuthCheckVal', fields: aepsTwoFactorAuthCheckVal })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'aepsTwoFactorAuthCheckVal', fields: aepsTwoFactorAuthCheckVal })

      let consent_flag_required = 'Y'
      // Start -->check for consent in aeps txn
      const getConsentDetailsSql = `Select * from ma_merchant_consent where ma_user_id = ${fields.ma_user_id} and userid = ${fields.userid} and transaction_type = 'aeps' limit 1`
      logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getConsentDetailsSql', fields: getConsentDetailsSql })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getConsentDetailsSql', fields: getConsentDetailsSql })
      const getConsentDetailsSqlResp = await this.rawQuery(getConsentDetailsSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getConsentDetailsSqlResp', fields: getConsentDetailsSqlResp })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getConsentDetailsSqlResp', fields: getConsentDetailsSqlResp })
      if (getConsentDetailsSqlResp.length > 0) {
        if (getConsentDetailsSqlResp[0].consent_flag == 'Y' || getConsentDetailsSqlResp[0].consent_flag == 'y') {
          consent_flag_required = 'N'
        }
      }
      // End -->check for consent in aeps txn

      if (aepsTwoFactorAuthCheckVal == 'Y') {
        const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' order by ma_aeps_merchant_auth_token_id desc limit 1`
        logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery', fields: getMerchantQuery })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery', fields: getMerchantQuery })
        const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
        logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult', fields: getMerchantResult })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult', fields: getMerchantResult })
        if (getMerchantResult.length > 0) { // if user is already registered then check expiry time
          // ${expiry_time} > DATE_SUB(NOW(), INTERVAL 12 HOUR) ,
          // Get expiry hours from system codes table
          const expiry_time = getMerchantResult[0].expiry_time
          const recieved_bankcode = getMerchantResult[0].received_bank_id
          console.log('recieved_bankcode', recieved_bankcode)

          // check whether current timestamp is less then expiry timestamp saved in the table
          const sqlCurrentTime = `SELECT IF(CURRENT_TIMESTAMP() < '${expiry_time}','Y','N') as expiry_flag`
          console.log('sqlCurrentTime>>', sqlCurrentTime)
          const resultTime = await this.rawQuery(sqlCurrentTime, connection)
          console.log('resultTime>>', resultTime)
          console.log('transaction_type >>', fields.transaction_type)
          // get Fino bank value from constant file
          const fino_bank_value = FINO_BANK_ID
          console.log('Bank id value : ', fino_bank_value)
          // check merchant is not expired & sent and received bank id is same
          if (resultTime[0].expiry_flag == 'Y') {
            // check if the transaction_type is aeps or adpay
            if (fields.transaction_type == 'aeps') {
              if (fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == undefined || fields.bank_code == '0') {
                console.log('bank id null')
                // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: 0, cw_auth_required :"N/A" }// && getMerchantResult[0].sent_bank_id != getMerchantResult[0].received_bank_id, check below condition, if sent bank id is not null and bank code is not equal to received bank id
                const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
                  bankCode: recieved_bankcode,
                  connection
                })
                if (iscw2FAAuthRequiredResult.status == 200) {
                  const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                  logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  console.log('cwAuthRequire', cwAuthRequired)
                  return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: recieved_bankcode, cw_auth_required: cwAuthRequired, consent_flag_required }
                } else {
                  console.log('iscw2FAAuthRequiredResult ', iscw2FAAuthRequiredResult.status)
                  const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                  return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: recieved_bankcode, cw_auth_required: cwAuthRequired, consent_flag_required }
                }
              } else if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' || fields.bank_code != ' ' || fields.bank_code != '0')) { // check whether bank code is registered with merchant
                console.log('bank id not null')
                const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and received_bank_id = '${fields.bank_code}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
                logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
                logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank', fields: getMerchantResult })
                // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank', fields: getMerchantResult })
                if (getMerchantResult.length > 0) {
                  const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
                    bankCode: fields.bank_code,
                    connection
                  })
                  if (iscw2FAAuthRequiredResult.status == 200) {
                    const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                    logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                    console.log('cwAuthRequired', cwAuthRequired)
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: cwAuthRequired, consent_flag_required }
                  } else {
                    console.log('iscw2FAAuthRequiredResult ', iscw2FAAuthRequiredResult.status)
                    const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: cwAuthRequired, consent_flag_required }
                  }
                } else {
                  const iscw2FAAuthRequiredResult = await this.is2FAAuthRequired({
                    bankCode: fields.bank_code,
                    connection
                  })
                  if (iscw2FAAuthRequiredResult.status == 200) {
                    const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                    logs.logger({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                    console.log('cwAuthRequired', cwAuthRequired)
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: cwAuthRequired, consent_flag_required }
                  } else {
                    console.log('iscw2FAAuthRequiredResult ', iscw2FAAuthRequiredResult.status)
                    const cwAuthRequired = iscw2FAAuthRequiredResult.cw_auth_required
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: cwAuthRequired, consent_flag_required }
                  }
                }
              } /* else if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' && fields.bank_code != ' ') && fields.bank_code != getMerchantResult[0].sent_bank_id) {
              // return { status: 200, message: 'Caution! the partner bank may not support this transaction at this moment, please try after some time or try using different aadhaar number.', respcode: 1000, authentication_required: 'W', userRegistered: 'Y', bank_code: fields.bank_code }
                return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code }
              } else if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' && fields.bank_code != ' ') && fields.bank_code == getMerchantResult[0].sent_bank_id && fields.bank_code != getMerchantResult[0].received_bank_id) {
                return { status: 200, message: 'Caution! the partner bank may not support this transaction at this moment, please try after some time or try using different aadhaar number.', respcode: 1000, authentication_required: 'W', userRegistered: 'Y', bank_code: fields.bank_code }
              } */
            } else if (fields.transaction_type == 'adpay') {
              console.log('here---------------------')
              // if ((fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == undefined || fields.bank_code == '0') && (fino_bank_value == getMerchantResult[0].received_bank_id)) {
                if (fields.bank_code == '' || fields.bank_code == ' ' || fields.bank_code == undefined || fields.bank_code == '0') {
                  // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAepsMerchantAuth', type: 'cwAuthRequired response', fields: iscw2FAAuthRequiredResult })
                  if(recieved_bankcode == CREDOPAY_BANK_ID || fino_bank_value == recieved_bankcode){
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: recieved_bankcode}
                  }else{
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: FINO_BANK_ID}
                  }
                //&&&&&
                // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fino_bank_value }
                // &&&&&
                //  // && getMerchantResult[0].sent_bank_id != getMerchantResult[0].received_bank_id, check below condition, if sent bank id is not null and bank code is not equal to received bank id
              } else if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' || fields.bank_code != ' ' || fields.bank_code != '0')) {
                // check whether bank code is registered with merchant
                const getMerchantQuery = `select ma_aeps_merchant_auth_token_id, DATE_FORMAT(expiry_time,'%Y-%m-%d %H:%i:%s') as expiry_time, sent_bank_id, received_bank_id from ma_aeps_merchant_auth_token mamat where ma_user_id = '${fields.ma_user_id}' and received_bank_id = '${fields.bank_code}' and current_timestamp() < expiry_time order by ma_aeps_merchant_auth_token_id desc limit 1`
                logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantQuery-check received bank', fields: getMerchantQuery })
                const getMerchantResult = await this.rawQuery(getMerchantQuery, connection)
                logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank', fields: getMerchantResult })
                // lokiLogger.info({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'getMerchantResult-check received bank', fields: getMerchantResult })
                if (getMerchantResult.length > 0) {
                    return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: recieved_bankcode}           
                } else {
                  return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: recieved_bankcode}
                }
                // return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: fields.bank_code }
              } /* else if ((getMerchantResult[0].sent_bank_id != null && getMerchantResult[0].sent_bank_id != undefined && getMerchantResult[0].sent_bank_id != '') && (fields.bank_code != '' && fields.bank_code != ' ') && fields.bank_code == getMerchantResult[0].sent_bank_id && fields.bank_code != getMerchantResult[0].received_bank_id) {
                return { status: 200, message: 'Caution! the partner bank may not support this transaction at this moment, please try after some time or try using different aadhaar number.', respcode: 1000, authentication_required: 'W', userRegistered: 'Y', bank_code: fields.bank_code }
              } */
            }
          } else {
            if (fields.transaction_type == 'adpay') {
              fields.bank_code = fields.bank_code
            }
            return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'Y', userRegistered: 'Y', bank_code: fields.bank_code, cw_auth_required: 'N/A', consent_flag_required }
          }
        } else {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1028] + ': Merchant Not Registered, Please Register for 2FA', authentication_required: 'Y', userRegistered: 'N', bank_code: 0, cw_auth_required: 'N/A', consent_flag_required }
        }
      } else {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, authentication_required: 'N', userRegistered: 'Y', bank_code: 0, cw_auth_required: 'N/A', consent_flag_required }
      }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'getAepsMerchantAuth', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getAepsMerchantAuth',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async is2FAAuthRequired ({ bankCode, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const getAuthFlag = `select cw_auth_flag from ma_aeps_bank_master where bank_code = '${bankCode}' and active_status = 'A'`
      logs.logger({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'is2FAAuthRequired received bank', fields: getAuthFlag })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'is2FAAuthRequired received bank', fields: getAuthFlag })
      const getAuthFlagResult = await this.rawQuery(getAuthFlag, connection)
      logs.logger({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'is2FAAuthRequired-check received bank', fields: getAuthFlagResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'is2FAAuthRequired-check received bank', fields: getAuthFlagResult })
      if (getAuthFlagResult.length > 0) {
        const cw_auth_flag = getAuthFlagResult[0].cw_auth_flag
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, cw_auth_required: cw_auth_flag }
      } else {
        return { status: 400, message: errorMsg.responseCode[1000], respcode: 1000, cw_auth_required: 'N/A' }
      }
    } catch (error) {
      logs.logger({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'is2FAAuthRequired', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }

  /**
   * aepsDeleteMerchantResultsData description - CRON for deleting 2 days before results
   * @param {null}
   * @returns {Promise <{ status: number, message: string, respcode: number, action_code: 1000|1001 }>}
   */
  static async aepsDeleteMerchantAuthResultsData () {
    logs.logger({ pagename: path.basename(__filename), action: 'aepsDeleteMerchantAuthResultsData', type: 'request', fields: 'request' })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'aepsDeleteMerchantAuthResultsData', type: 'request', fields: 'request' })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Delete last 2 days data from ma_aeps_merchant_auth_token
      const deleteOldRecordsSql = ' DELETE FROM ma_aeps_merchant_auth_token WHERE addedon < DATE_ADD(DATE_FORMAT(CURRENT_TIMESTAMP,"%Y-%m-%d %H:%i:%s"),interval -2 day) '
      const deleteOldRecordsResult = await this.secureRawQuery(deleteOldRecordsSql, { connection, params: [] })

      logs.logger({ pagename: path.basename(__filename), action: 'aepsDeleteMerchantAuthResultsData', type: 'deleteOldRecordsResult', fields: deleteOldRecordsResult })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'aepsDeleteMerchantAuthResultsData', type: 'deleteOldRecordsResult', fields: deleteOldRecordsResult })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'aepsDeleteMerchantAuthResultsData', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'aepsDeleteMerchantAuthResultsData', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * @private
   * getBankCode description - What's the method about?
   * @param {{ ma_user_id: number, userid: number }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async getBankCode () {
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const priorityBankNameSql = 'SELECT bank_code, bank_name FROM ma_aeps_bank_master WHERE active_status = "A" ORDER BY priority ASC limit 1'
      logs.logger({ pagename: path.basename(__filename), action: 'getBankCode', type: 'priorityBankNameSql', fields: priorityBankNameSql })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankCode', type: 'priorityBankNameSql', fields: priorityBankNameSql })
      const priorityBankNameData = await this.rawQuery(priorityBankNameSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getBankCode', type: 'priorityBankNameData', fields: priorityBankNameData })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankCode', type: 'priorityBankNameData', fields: priorityBankNameData })
      console.log('Priority Bank:', priorityBankNameData)
      if (priorityBankNameData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      } else {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bankCode: priorityBankNameData[0].bank_code }
      }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBankCode', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'getBankCode', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getBankCode',
        data: {},
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
  static async getAdpayBankCode () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const priorityBankNameSql = 'SELECT bank_code, bank_name FROM ma_adpay_bank_master WHERE active_status = "A" ORDER BY priority ASC limit 1'
      logs.logger({ pagename: path.basename(__filename), action: 'getAdpayBankCode', type: 'priorityBankNameSql', fields: priorityBankNameSql })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankCode', type: 'priorityBankNameSql', fields: priorityBankNameSql })
      const priorityBankNameData = await this.rawQuery(priorityBankNameSql, connection)
      logs.logger({ pagename: path.basename(__filename), action: 'getAdpayBankCode', type: 'priorityBankNameData', fields: priorityBankNameData })
      // lokiLogger.info({ pagename: path.basename(__filename), action: 'getBankCode', type: 'priorityBankNameData', fields: priorityBankNameData })
      console.log('Priority Bank For Adpay:', priorityBankNameData)
      if (priorityBankNameData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      } else {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], bankCode: priorityBankNameData[0].bank_code }
      }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'getAdpayBankCode', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'getBankCode', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'getAdpayBankCode',
        data: {},
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  /**
   * @private
   * saveTwoFactorLogs description - save aeps 2 factor logs
   * @param {{ ma_user_id: number, userid: number }} fields
   * @param {any} con connection
   * @returns {Promise <{ status: number, message: string, respcode: number }>}
   */
  static async saveTwoFactorLogs (fields, con) {
    logs.logger({ pagename: path.basename(__filename), action: 'saveTwoFactorLogs', type: 'request', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'saveTwoFactorLogs', type: 'request', fields })

    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    try {
      // Save aeps two factor logs
      const insertLogQuery = `INSERT INTO ma_aeps_two_factor_logs (ma_user_id,orderid,api_response,request_type,bank_code,auth_status, bank_rrn) VALUES (${fields.ma_user_id},'${fields.order_id}','${fields.api_response}','${fields.request_type}','${fields.bank_code}','${fields.auth_status}', ${logData.bank_rrn ? `'${logData.bank_rrn}'`: `NULL`})`
      logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'insertLogQuery', fields: insertLogQuery })
      const LogResult = await this.rawQuery(insertLogQuery, connection)
      logs.logger({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'saveTwoFactorLogs', type: 'LogResult', fields: LogResult })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'saveTwoFactorLogs', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'saveTwoFactorLogs', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'saveTwoFactorLogs',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tempConnection) connection.release()
    }
  }

  static async create2FATransaction ({ fields, connection }) {
    logs.logger({
      pagename: path.basename(__filename),
      action: 'create2FATransaction',
      type: 'request',
      fields: fields
    })
    // lokiLogger.info({
    //   pagename: path.basename(__filename),
    //   action: 'create2FATransaction',
    //   type: 'request',
    //   fields: fields
    // })
    const isSet = connection === null || connection === undefined
    const conn = isSet
      ? await mySQLWrapper.getConnectionFromPool()
      : connection
    try {
      if (fields.gstType == 'I') {
        fields.amount = fields.chargesAmount
      }
      console.log('amountt', fields.amount)
      const transactionData = await transactionMaster.initiateTransaction('', {
        // connection: conn,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        aggregator_order_id: fields.orderid,
        transaction_id: fields.orderid,
        amount: fields.amount,
        commission_amount: fields.GSTAmount ? fields.GSTAmount : '0',
        transaction_type: fields.transaction_type, // aeps 2FA  charge
        remarks: fields.remarks,
        provider_id: '0', // to do
        utility_id: '0', // to do
        utility_name: 'AEPS', // to do
        action_type: 'instapay',
        transaction_status: 'S',
        bank_name: '',
        merchant_type: fields.merchant_type,
        bank_rrn: fields.bank_rrn
      })

      logs.logger({
        pagename: path.basename(__filename),
        action: 'create2FATransaction',
        type: 'transactionData',
        fields: transactionData
      })
      // lokiLogger.info({
      //   pagename: path.basename(__filename),
      //   action: 'create2FATransaction',
      //   type: 'transactionData',
      //   fields: transactionData
      // })

      if (transactionData.status != 200) return transactionData
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        action_code: 1000,
        orderid: fields.orderid
      }
    } catch (error) {
      logs.logger({
        pagename: path.basename(__filename),
        action: 'create2FATransaction',
        type: 'catcherror',
        fields: error
      })
      // lokiLogger.error({
      //   pagename: path.basename(__filename),
      //   action: 'create2FATransaction',
      //   type: 'catcherror',
      //   fields: error
      // })
      return {
        status: 400,
        message: errorMsg.responseCode[1001],
        respcode: 1001
      }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async ledgerEntries ({ fields, connection }) {
    logs.logger({
      pagename: path.basename(__filename),
      action: 'ledgerEntries',
      type: 'request',
      fields: fields
    })
    // lokiLogger.info({
    //   pagename: path.basename(__filename),
    //   action: 'ledgerEntries',
    //   type: 'request',
    //   fields: fields
    // })
    const isSet = connection === null || connection === undefined
    const conn = isSet
      ? await mySQLWrapper.getConnectionFromPool()
      : connection
    try {
      let final_amount = ''
      if (fields.gstType == 'E') {
        final_amount = fields.chargesAmount
      } else if (fields.gstType == 'I') {
        final_amount = fields.newAmount
      }
      console.log('finalAmount', final_amount)
      const updateTransactionResult = await transactionMaster.updateWhereData(
        conn,
        {
          data: {
            transaction_reason: `Balance Deducted ${fields.amount}`,
            transaction_status: 'S'
          },
          id: fields.orderid,
          where: 'aggregator_order_id'
        }
      )
      logs.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'updateTransactionResult',
        fields: updateTransactionResult
      })
      // lokiLogger.info({
      //   pagename: require('path').basename(__filename),
      //   action: 'ledgerEntries',
      //   type: 'updateTransactionResult',
      //   fields: updateTransactionResult
      // })
      if (updateTransactionResult.status != 200) return updateTransactionResult

      var pointsDetailsEntries = {}
      pointsDetailsEntries = await balanceController.getWalletBalanceDirect(
        '_',
        {
          ma_user_id: fields.ma_user_id,
          amount: final_amount,
          transactionType: '1',
          connection: conn
        }
      )
      logs.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'getWalletBalanceDirect',
        fields: pointsDetailsEntries
      })
      // lokiLogger.info({
      //   pagename: require('path').basename(__filename),
      //   action: 'ledgerEntries',
      //   type: 'getWalletBalanceDirect',
      //   fields: pointsDetailsEntries
      // })
      if (pointsDetailsEntries.status === 400) return pointsDetailsEntries

      // point leger entry
      const pointLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: final_amount,
        mode: 'dr',
        transaction_type: fields.transaction_type,
        description: fields.description,
        ma_status: 'S',
        orderid: fields.orderid,
        userid: fields.userid,
        conn
      })
      logs.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'pointsLedger',
        fields: pointLedgerId
      })
      // lokiLogger.info({
      // pagename: require('path').basename(__filename),
      //   action: 'ledgerEntries',
      //   type: 'pointsLedger',
      //   fields: pointLedgerId
      // })
      if (pointLedgerId.status === 400) return pointLedgerId

      // pointsDetails entry
      for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: pointLedgerId.id,
          orderid: fields.orderid,
          ma_status: 'S',
          connection: conn
        })
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'pointsDetailsController',
          fields: entry
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'ledgerEntries',
        //   type: 'pointsDetailsController',
        //   fields: entry
        // })
        if (entry.status === 400) return entry
      }

      // airpay id credit entry

      const retailerLedgerId = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: final_amount,
        mode: 'cr',
        transaction_type: fields.transaction_type,
        ma_status: 'S',
        description: fields.description,
        userid: fields.userid,
        orderid: fields.orderid,
        corresponding_id: fields.ma_user_id,
        conn
      })
      logs.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'createEntry',
        fields: retailerLedgerId
      })
      // lokiLogger.info({
      //   pagename: require('path').basename(__filename),
      //   action: 'ledgerEntries',
      //   type: 'createEntry',
      //   fields: retailerLedgerId
      // })
      if (retailerLedgerId.status === 400) return retailerLedgerId

      if (fields.gstType == 'E' || fields.gstType == 'I') {
        const gst_amount = fields.GSTAmount
        console.log('GSTAmount', gst_amount)

        pointsDetailsEntries = await balanceController.getWalletBalanceDirect(
          '_',
          {
            ma_user_id: fields.ma_user_id,
            amount: gst_amount.toFixed(2),
            transactionType: '1',
            connection: conn
          }
        )
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'getWalletBalanceDirect',
          fields: pointsDetailsEntries
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'ledgerEntries',
        //   type: 'getWalletBalanceDirect',
        //   fields: pointsDetailsEntries
        // })
        if (pointsDetailsEntries.status === 400) return pointsDetailsEntries

        // point leger entry
        const pointLedgerId = await pointsLedger.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: gst_amount.toFixed(2),
          mode: 'dr',
          transaction_type: 65,
          description: `AEPS 2FA GST charges at ${fields.gst_amount}%`,
          ma_status: 'S',
          orderid: fields.orderid,
          userid: fields.userid,
          conn
        })
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'pointsLedger',
          fields: pointLedgerId
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'ledgerEntries',
        //   type: 'pointsLedger',
        //   fields: pointLedgerId
        // })
        if (pointLedgerId.status === 400) return pointLedgerId

        // pointsDetails entry
        // eslint-disable-next-line no-redeclare
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry('_', {
            ma_user_id: fields.ma_user_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: pointLedgerId.id,
            orderid: fields.orderid,
            ma_status: 'S',
            connection: conn
          })
          logs.logger({
            pagename: require('path').basename(__filename),
            action: 'ledgerEntries',
            type: 'pointsDetailsController',
            fields: entry
          })
          // lokiLogger.info({
          //   pagename: require('path').basename(__filename),
          //   action: 'ledgerEntries',
          //   type: 'pointsDetailsController',
          //   fields: entry
          // })
          if (entry.status === 400) return entry
        }

        // airpay id credit entry

        const retailerLedgerId = await pointsLedger.createEntry('_', {
          ma_user_id: util.airpayUserId,
          amount: gst_amount.toFixed(2),
          mode: 'cr',
          transaction_type: 65,
          ma_status: 'S',
          description: `AEPS 2FA GST charges at ${fields.gst_amount}%`,
          userid: fields.userid,
          orderid: fields.orderid,
          corresponding_id: fields.ma_user_id,
          conn
        })
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'ledgerEntries',
          type: 'createEntry',
          fields: retailerLedgerId
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'ledgerEntries',
        //   type: 'createEntry',
        //   fields: retailerLedgerId
        // })
        if (retailerLedgerId.status === 400) return retailerLedgerId
      }

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        action_code: 1000
      }
    } catch (error) {
      logs.logger({
        pagename: require('path').basename(__filename),
        action: 'ledgerEntries',
        type: 'catcherror',
        fields: error
      })
      // lokiLogger.error({
      //   pagename: require('path').basename(__filename),
      //   action: 'ledgerEntries',
      //   type: 'catcherror',
      //   fields: error
      // })
      return {
        status: 400,
        message: errorMsg.responseCode[1001],
        respcode: 1001,
        action_code: 1001
      }
    } finally {
      if (isSet) conn.release()
    }
  }

  static async FailedTransactionDetails (_, fields) {
    logs.logger({
      pagename: path.basename(__filename),
      action: 'FailedTransactionDetails',
      type: 'request',
      fields: fields
    })
    // lokiLogger.info({
    //   pagename: path.basename(__filename),
    //   action: 'FailedTransactionDetails',
    //   type: 'request',
    //   fields: fields
    // })
    const isSet = (fields.connection === null || fields.connection === undefined)
    const connection = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection
    try {
      let flag = false
      let chargesAmount = ''
      let gstType = ''
      let gst_per = ''
      let charge_amount_type = ''
      let GSTAmount = ''
      // Check condition for integrated users
      const userintegrated = `Select ma_user_id from ma_integration_user_master Where ma_user_id = ${fields.ma_user_id} AND integration_code = 'EMITRA' AND record_status = 'Y'`
      logs.logger({ pagename: 'doAepsCashWithdrawal.js', action: 'get mid', type: 'request - Integrated user check', fields: userintegrated })
      // lokiLogger.info({ pagename: 'doAepsCashWithdrawal.js', action: 'get mid', type: 'request - Integrated user check', fields: userintegrated })
      const resultint = await this.rawQuery(userintegrated, connection)
      logs.logger({ pagename: 'doAepsCashWithdrawal.js', action: 'get mid', type: 'response - Integrated user check', fields: resultint })
      // lokiLogger.info({ pagename: 'doAepsCashWithdrawal.js', action: 'get mid', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        flag = true
      } else {
        const sqlCharge = `select charge_amount,gst_type,gst_amount,charge_amount_type,bank_id from ma_slabwise_distribution_2fa where charge_type ='Cash Withdrawal' and record_status = 'Y' and bank_id = ${fields.bank_code} limit 1`
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'doAepsCashWithdrawal',
          type: 'sqlCharge',
          fields: sqlCharge
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'doAepsCashWithdrawal',
        //   type: 'sqlCharge',
        //   fields: sqlCharge
        // })
        const sqlChargeDetails = await this.rawQuery(sqlCharge, connection)
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'doAepsCashWithdrawal',
          type: 'sqlChargeResult',
          fields: sqlChargeDetails
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'doAepsCashWithdrawal',
        //   type: 'sqlChargeResult',
        //   fields: sqlChargeDetails
        // })
        if (sqlChargeDetails.length == 0) {
        // all
          const sqlCharge = 'select charge_amount,gst_type,gst_amount,charge_amount_type,bank_id from ma_slabwise_distribution_2fa where charge_type =\'Cash Withdrawal\' and record_status = \'Y\' and bank_id = \'-1\' limit 1'
          logs.logger({
            pagename: require('path').basename(__filename),
            action: 'doAepsCashWithdrawal',
            type: 'sqlCharge',
            fields: sqlCharge
          })
          // lokiLogger.info({
          //   pagename: require('path').basename(__filename),
          //   action: 'doAepsCashWithdrawal',
          //   type: 'sqlCharge',
          //   fields: sqlCharge
          // })
          const sqlChargeDetails = await this.rawQuery(sqlCharge, connection)
          logs.logger({
            pagename: require('path').basename(__filename),
            action: 'doAepsCashWithdrawal',
            type: 'sqlChargeResult',
            fields: sqlChargeDetails
          })
          // lokiLogger.info({
          //   pagename: require('path').basename(__filename),
          //   action: 'doAepsCashWithdrawal',
          //   type: 'sqlChargeResult',
          //   fields: sqlChargeDetails
          // })
          if (sqlChargeDetails.length == 0) {
            logs.logger({
              pagename: require('path').basename(__filename),
              action: 'doAepsCashWithdrawal',
              type: 'sqlChargeResult',
              fields: 'No bank slabs found'
            })
            // lokiLogger.info({
            //   pagename: require('path').basename(__filename),
            //   action: 'doAepsCashWithdrawal',
            //   type: 'sqlChargeResult',
            //   fields: 'No bank slabs found'
            // })
            flag = true
          } else {
            chargesAmount = sqlChargeDetails[0].charge_amount
            if (chargesAmount == 0 || chargesAmount == null) {
              flag = true
            }
            console.log('chargesAmount', chargesAmount)
            gstType = sqlChargeDetails[0].gst_type
            gst_per = sqlChargeDetails[0].gst_amount
            charge_amount_type = sqlChargeDetails[0].charge_amount_type
            console.log('charge_amount_type', charge_amount_type)
            console.log('gst_per', gst_per)

            GSTAmount = chargesAmount * (gst_per / 100)
            console.log('GSTAmount', GSTAmount)
            if (charge_amount_type == 2) { // percentage type
              return {
                status: 400,
                respcode: 1001,
                message: 'percentage charge type not allowed'
              }
            }
          }
        } else {
        // bank wise
          chargesAmount = sqlChargeDetails[0].charge_amount
          if (chargesAmount == 0 || chargesAmount == null) {
            flag = true
          }
          console.log('chargesAmount', chargesAmount)
          gstType = sqlChargeDetails[0].gst_type
          gst_per = sqlChargeDetails[0].gst_amount
          charge_amount_type = sqlChargeDetails[0].charge_amount_type
          console.log('charge_amount_type', charge_amount_type)
          console.log('gst_per', gst_per)

          GSTAmount = chargesAmount * (gst_per / 100)
          console.log('GSTAmount', GSTAmount)
          if (charge_amount_type == 2) { // percentage type
            return {
              status: 400,
              respcode: 1001,
              message: 'percentage charge type not allowed'
            }
          }
        }
      }
      if (flag == false) {
        let totalAmount = ''
        if (gstType == 'I') {
          totalAmount = (chargesAmount - GSTAmount).toFixed(2)
        } else if (gstType == 'E') {
          totalAmount = (chargesAmount + GSTAmount).toFixed(2)
        } else {
          totalAmount = chargesAmount.toFixed(2)
        }
        const availableBalance =
          await balanceController.getWalletBalancesDirect(_, {
            ma_user_id: fields.ma_user_id,
            ma_status: 'ACTUAL',
            balance_flag: 'SUMMARY',
            connection: connection
          })
        if (availableBalance.status != 200) return availableBalance
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'doAepsCashWithdrawal',
          type: 'balanceCheck',
          fields: availableBalance
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'doAepsCashWithdrawal',
        //   type: 'balanceCheck',
        //   fields: availableBalance
        // })
        if (availableBalance.amount < totalAmount) {
          return {
            status: 400,
            respcode: 1001,
            message: 'Insufficient balance, kindly fund the account to enable AePS Service.'
          }
        }

        const newFields = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          orderid: fields.order_id,
          amount: totalAmount,
          chargesAmount: chargesAmount,
          mobile_number: fields.mobile_number,
          customer_mobile: fields.mobile_number ? fields.mobile_number : '0',
          transaction_type: '66',
          remarks: `AEPS 2FA cash withdrawal Transaction Ref ID : ${fields.parent_orderid}`,
          gstType: gstType,
          GSTAmount: GSTAmount,
          newAmount: totalAmount,
          gst_amount: gst_per,
          description: fields.description
        }
        await mySQLWrapper.beginTransaction(connection)

        const transactionResponse = await this.create2FATransaction({
          fields: newFields,
          connection
        })
        console.log('transactionResponse', transactionResponse)
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'doAepsCashWithdrawal',
          type: 'transaction Response',
          fields: transactionResponse
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'doAepsCashWithdrawal',
        //   type: 'transaction Response',
        //   fields: transactionResponse
        // })
        if (transactionResponse.status != 200) {
          await mySQLWrapper.rollback(connection)
          return transactionResponse
        }
        // update ledger entries
        const ledgerEntries = await this.ledgerEntries({
          fields: newFields,
          connection
        })
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'doAepsCashWithdrawal',
          type: 'ledgerEntries',
          fields: ledgerEntries
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'doAepsCashWithdrawal',
        //   type: 'ledgerEntries',
        //   fields: ledgerEntries
        // })
        if (ledgerEntries.status != 200) {
          await mySQLWrapper.rollback(connection)
          return ledgerEntries
        }
        console.log('Bank Code', fields.bank_code)
        const aepsTransactionLogsQuery = `insert into ma_aeps_transaction_logs (ma_user_id, order_id, amount, transaction_status, aeps_mode, aggregator_bank,bank_id,bank_response) values ( '${fields.ma_user_id}','${fields.order_id}','${totalAmount}', 'S', 'AEPS CW Charges','${fields.bank_code || 0}','${fields.bank_id || 0}','${fields.response_data}')`
        logs.logger({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogsQuery', type: 'LogQuery', fields: aepsTransactionLogsQuery })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogsQuery', type: 'LogQuery', fields: aepsTransactionLogsQuery })
        const aepsTransactionLogs = await this.rawQuery(aepsTransactionLogsQuery, connection)
        logs.logger({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogs', type: 'LogResult', fields: aepsTransactionLogs })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'aepsTransactionLogs', type: 'LogResult', fields: aepsTransactionLogs })
      } else if (flag == true) {
        const newFields = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          orderid: fields.order_id,
          amount: 0,
          chargesAmount: 0,
          transaction_type: '66',
          mobile_number: fields.mobile_number ? fields.mobile_number : '0',
          customer_mobile: fields.mobile_number ? fields.mobile_number : '0',
          remarks: `AEPS 2FA cash withdrawal Transaction Ref ID : ${fields.parent_orderid}`,
          gstType: gstType,
          GSTAmount: GSTAmount,
          merchant_type: 'emitra'
        }
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'registerMerchantForAepsAuth',
          type: 'transaction Response for No slab',
          fields: newFields
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'registerMerchantForAepsAuth',
        //   type: 'transaction Response for No slab',
        //   fields: newFields
        // })
        await mySQLWrapper.beginTransaction(connection)

        const transactionResponse = await this.create2FATransaction({
          fields: newFields,
          connection
        })
        console.log('transactionResponse for No slab', transactionResponse)
        logs.logger({
          pagename: require('path').basename(__filename),
          action: 'registerMerchantForAepsAuth',
          type: 'transaction Response for No slab',
          fields: transactionResponse
        })
        // lokiLogger.info({
        //   pagename: require('path').basename(__filename),
        //   action: 'registerMerchantForAepsAuth',
        //   type: 'transaction Response for No slab',
        //   fields: transactionResponse
        // })
        if (transactionResponse.status != 200) {
          await mySQLWrapper.rollback(connection)
          return transactionResponse
        }
      }
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        action_code: 1000
      }
    } catch (error) {
      logs.logger({
        pagename: require('path').basename(__filename),
        action: 'FailedTransactionDetails',
        type: 'catcherror',
        fields: error
      })
      // lokiLogger.error({
      //   pagename: require('path').basename(__filename),
      //   action: 'FailedTransactionDetails',
      //   type: 'catcherror',
      //   fields: error
      // })
      return {
        status: 400,
        message: errorMsg.responseCode[1001],
        respcode: 1001,
        action_code: 1001
      }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
   * This function is used to accept aeps consent
   * @param {*} _
   * @param {*} fields { ma_user_id, userid , consent_flag}
  */
  static async acceptAepsConsent (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'request', fields })
    // lokiLogger.info({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'request', fields })

    // Validate All Required Fields - Add parameter name in array for fields validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validatorResponse.status != 200) return validatorResponse

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.consent_flag != 'Y') {
        return { status: 400, respcode: 1001, message: 'Fail: Please Provide valid Consent to proceed' }
      }
      if (fields.transaction_type != 'aeps') {
        return { status: 400, respcode: 1001, message: "Fail: Please Provide Transaction Type 'aeps' to proceed" }
      }
      const getConsentDetailsSql = `Select * from ma_merchant_consent where ma_user_id = ${fields.ma_user_id} and userid = ${fields.userid} and transaction_type = 'aeps' limit 1`
      logs.logger({ pagename: require('path').basename(__filename), action: 'acceptAepsConsent', type: 'getConsentDetailsSql', fields: getConsentDetailsSql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'acceptAepsConsent', type: 'getConsentDetailsSql', fields: getConsentDetailsSql })
      const getConsentDetailsSqlResp = await this.rawQuery(getConsentDetailsSql, connection)
      logs.logger({ pagename: require('path').basename(__filename), action: 'acceptAepsConsent', type: 'getConsentDetailsSqlResp', fields: getConsentDetailsSqlResp })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'acceptAepsConsent', type: 'getConsentDetailsSqlResp', fields: getConsentDetailsSqlResp })
      if (getConsentDetailsSqlResp.length > 0) {
        const updateAepsConsentSql = `Update ma_merchant_consent set consent_flag = '${fields.consent_flag}' where ma_user_id = ${fields.ma_user_id} and userid=${fields.userid} and transaction_type = 'aeps'`
        logs.logger({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'updateAepsConsentSql', fields: updateAepsConsentSql })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'updateAepsConsentSql', fields: updateAepsConsentSql })
        const updateAepsConsentSqlResp = await this.rawQuery(updateAepsConsentSql, connection)
        logs.logger({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'updateAepsConsentSqlResp', fields: updateAepsConsentSqlResp })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'updateAepsConsentSqlResp', fields: updateAepsConsentSqlResp })
      } else {
        // insert the user record with consent_flag true
        const insertConsentDetailsSql = `INSERT INTO ma_merchant_consent (ma_user_id,userid,consent_flag,transaction_type) VALUES (${fields.ma_user_id},${fields.userid},'${fields.consent_flag}','aeps')`
        logs.logger({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'insertConsentDetailsSql', fields: insertConsentDetailsSql })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'insertConsentDetailsSql', fields: insertConsentDetailsSql })
        const insertConsentDetailsSqlResp = await this.rawQuery(insertConsentDetailsSql, connection)
        logs.logger({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'insertConsentDetailsSqlResp', fields: insertConsentDetailsSqlResp })
        // lokiLogger.info({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'insertConsentDetailsSqlResp', fields: insertConsentDetailsSqlResp })
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (err) {
      logs.logger({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'err', fields: err })
      // lokiLogger.error({ pagename: path.basename(__filename), action: 'acceptAepsConsent', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'acceptAepsConsent',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
}
module.exports = aepsMerchantAuthController