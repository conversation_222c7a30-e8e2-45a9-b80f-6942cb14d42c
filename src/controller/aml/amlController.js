const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const { getConnectionFromPool } = require('../../lib/mysqlWrapper')
// const { lokiLogger } = require('../../util/lokiLogger')

class Aml extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_customer_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_customer_details_id'
  }

  static async findMatching (_, fields) {
    // Returns early with all transactions if no criteria was passed
    log.logger({ pagename: 'amlController.js', action: 'findMatching', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'findMatching', type: 'request', fields: fields })
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching transactions
    const res = await this.findByFields({
      fields
    })
    log.logger({ pagename: 'amlController.js', action: 'findMatching', type: 'response', fields: res })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'findMatching', type: 'response', fields: res })
    return res
  }

  static async createEntry (_, fields, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'createEntry', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'createEntry', type: 'request', fields: fields })
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      //   await mySQLWrapper.beginTransaction(connection)
      const _result = await this.insert(connection, {
        data: fields
      })
      log.logger({ pagename: 'amlController.js', action: 'createEntry', type: 'response', fields: _result })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'createEntry', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        return { status: 200, message: 'success', data: _result.insertId }
      } else {
        return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async createEntryBulk (_, queryFields, queryData, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'createEntryBulk', type: 'request', fields: { queryFields: queryFields, queryData: queryData } })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'createEntryBulk', type: 'request', fields: { queryFields: queryFields, queryData: queryData } })
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      //   await mySQLWrapper.beginTransaction(connection)
      const _result = await this.insertBulk(connection, { fields: queryFields, data: queryData })
      log.logger({ pagename: 'amlController.js', action: 'createEntryBulk', type: 'response', fields: _result })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'createEntryBulk', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        return { status: 200, message: 'success' }
      } else {
        return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntryBulk', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntryBulk', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getAmlDetails (_, fields, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      var resp = {}
      var blacklisting = {}
      let spiketransfer = {}
      for (var type in fields.aml_type) {
        switch (fields.aml_type[type]) {
          case 'threshold':
            await this.thresholdDetails(fields, connection)
            break
          case 'kyc':
            await this.kycDetails(fields, connection)
            break
          case 'spiketransfer':
            spiketransfer = await this.spiketransferDetails(fields, connection)
            break
          case 'blacklisting':
            blacklisting = await this.blacklistingDetails(fields, connection)
            break
        }
        console.log(fields.aml_type[type])
      }
      console.log('blacklisting data', blacklisting)
      resp.status = 200
      resp.message = errorMsg.responseCode[1000]
      resp.respcode = 1000
      if (blacklisting.blacklisted == 'Y' || blacklisting.valuesdata == 'Y') {
        resp.blacklisted = 'Y'
        resp.message = errorMsg.responseCode[1066] + blacklisting.type
      } else {
        resp.blacklisted = 'N'
        if (spiketransfer.status === 400 && spiketransfer.respcode == 1124) {
          resp.respcode = 1124
          resp.message = errorMsg.responseCode[1124]
        }
      }
      return resp
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAmlDetails', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getAmlDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async blacklistingDetails (fields, connection) {
    log.logger({ pagename: 'amlController.js', action: 'blacklistingDetails', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'blacklistingDetails', type: 'request', fields: fields })
    try {
      this.TABLE_NAME = 'ma_customer_details'
      // const customerdata = await this.findMatching('', { uic: fields.uic })
      const customerdata = await this.rawQuery(`SELECT mobile_number 
      FROM ma_customer_details  
      WHERE uic ='${fields.uic}' limit 1`, connection)

      var blacklistedstatus = {}
      var beneficiaryid = ''
      var accountno = ''
      var pancard = ''
      var aadharcard = ''
      var mobileno = ''
      if (fields.beneficiaryid !== undefined && fields.beneficiaryid != null) {
        beneficiaryid = fields.beneficiaryid
      }
      if (fields.accountno !== undefined && fields.accountno != null) {
        accountno = fields.accountno
      }
      if (fields.mobileno !== undefined && fields.mobileno != null) {
        mobileno = fields.mobileno
      }
      if (fields.aadharcard !== undefined && fields.aadharcard != null) {
        aadharcard = fields.aadharcard
      }
      if (fields.pancard !== undefined && fields.pancard != null) {
        pancard = fields.pancard
      }
      const customer_mob = (customerdata.length == 0) ? '' : customerdata[0].mobile_number
      /* const blacklisteddata = await this.getBlacklistedData(customerid, fields.ma_user_id, beneficiaryid, connection)
      for (const value of blacklisteddata) {
        if (value.blacklisted_type == 'B' && value.blacklisted_id == beneficiaryid) {
          blacklistedstatus.blacklisted = 'Y'
          break
        }
        if (value.blacklisted_type == 'C' && value.blacklisted_id == customerdata[0].ma_customer_details_id) {
          blacklistedstatus.blacklisted = 'Y'
          break
        }
        if (value.blacklisted_type == 'DT' && value.blacklisted_id == fields.ma_user_id) {
          blacklistedstatus.blacklisted = 'Y'
          break
        }
        if (value.blacklisted_type == 'RT' && value.blacklisted_id == fields.ma_user_id) {
          blacklistedstatus.blacklisted = 'Y'
          break
        }
      }
      if (blacklistedstatus.blacklisted == 'Y') {
        await mySQLWrapper.beginTransaction(connection)
        const blacklistrequest = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          type: fields.calltype
        }
        this.TABLE_NAME = 'ma_blacklisted_request'
        const createBlacklistDetails = await this.createEntry('', blacklistrequest, connection)
        if (createBlacklistDetails.status === 400) {
          await mySQLWrapper.rollback(connection)
          return createBlacklistDetails
        }
        await mySQLWrapper.commit(connection)
        log.logger({ pagename: 'amlController.js', action: 'blacklistingDetails', type: 'response', fields: blacklistedstatus })
        return blacklistedstatus
      } */
      const blacklistedvaluesdata = await this.getBlacklistedValuesData(accountno, mobileno, aadharcard, pancard, customer_mob, connection)
      if (blacklistedvaluesdata.length > 0) {
        await mySQLWrapper.beginTransaction(connection)
        blacklistedstatus.valuesdata = 'Y'
        console.log('Blacklisted type : ' + blacklistedvaluesdata[0].value_type)
        switch (blacklistedvaluesdata[0].value_type) {
          case 'PAN':
            blacklistedstatus.type = 'PAN Number'
            break
          case 'AADHAR':
            blacklistedstatus.type = 'AADHAAR Number'
            break
          case 'ACCNO':
            blacklistedstatus.type = 'Account Number'
            break
          case 'MOBNO':
            blacklistedstatus.type = 'Mobile Number'
            break
          default:
            blacklistedstatus.type = ''
            break
        }
        const blacklistrequest = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          value_type: blacklistedvaluesdata[0].value_type,
          blacklisted_value: blacklistedvaluesdata[0].blacklisted_value,
          type: fields.calltype
        }
        this.TABLE_NAME = 'ma_blacklisted_request'
        const createBlacklistDetails = await this.createEntry('', blacklistrequest, connection)
        if (createBlacklistDetails.status === 400) {
          await mySQLWrapper.rollback(connection)
          return createBlacklistDetails
        }
        await mySQLWrapper.commit(connection)
        return blacklistedstatus
      }
      blacklistedstatus.blacklisted = 'N'
      blacklistedstatus.valuesdata = 'N'
      log.logger({ pagename: 'amlController.js', action: 'blacklistingDetails', type: 'response', fields: blacklistedstatus })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'blacklistingDetails', type: 'response', fields: blacklistedstatus })
      return { status: true, message: 'success', blacklistedstatus }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'blacklistingDetails', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'blacklistingDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {

    }
  }

  static async spiketransferDetails (fields, connection) {
    log.logger({ pagename: 'amlController.js', action: 'spiketransferDetails', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'spiketransferDetails', type: 'request', fields: fields })
    try {
      let transferAmount = 0
      const transfersdata = await this.getTransfersData(fields.uic, fields.beneficiaryid, 1, 'spiketransfer', fields.ma_user_id, connection)
      console.log('------------transfersdata--------------')
      console.log(transfersdata)
      if (transfersdata.length > 0 && transfersdata[0].amount != null && transfersdata[0].amount != undefined) {
        transferAmount = transfersdata[0].amount
      }
      transferAmount = transferAmount + fields.amount // Add current transfer amount

      let transfermaster = {}
      const spikeThresholdSql = `SELECT threshold_amount,block_transaction,ma_transfers_threshold_id
                                FROM ma_transfers_threshold 
                                WHERE threshold_period = '1' 
                                AND threshold_status = 'A' 
                                AND (ma_user_id = '${fields.ma_user_id}' OR ma_user_id = '0')
                                ORDER BY ma_user_id DESC`
      const spikeThresholdData = await this.rawQuery(spikeThresholdSql, connection)
      console.log('------------spikeThresholdData--------------')
      console.log(spikeThresholdData)
      if (spikeThresholdData.length > 0) {
        for (const data of spikeThresholdData) {
          if (data.ma_user_id === fields.ma_user_id) {
            transfermaster = data
            break
          }
        }
        // If no rule found for merchant id then assign global rule to merchant
        if (Object.keys(transfermaster).length === 0) {
          transfermaster = spikeThresholdData[0]
        }
      } else {
        return { status: 200, respcode: 1000, message: 'success' } // No spike threshold found so return success
      }

      if (transferAmount > transfermaster.threshold_amount) {
        if (transfermaster.block_transaction == 'Y') {
          return { status: 400, respcode: 1124, message: errorMsg.responseCode[1124] }
        } else {
          await mySQLWrapper.beginTransaction(connection)

          const amlhighlighted = {
            uic: fields.uic,
            suspect_transaction_id: fields.aggregator_order_id,
            suspect_amount: fields.amount,
            ma_beneficiaries_id: fields.beneficiaryid,
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            type: 'SPIKE',
            period: 1,
            remarks: 'Spike transfer for 1 day(s)',
            threshold_id: transfermaster.ma_transfers_threshold_id,
            exceeded_by: (transfersdata[0].amount - transfermaster.threshold_amount)
          }
          this.TABLE_NAME = 'ma_aml_highlighted'
          const createAml = await this.createEntry('', amlhighlighted, connection)
          if (createAml.status === 400) {
            await mySQLWrapper.rollback(connection)
            return createAml
          }
          if (fields.ma_transfers_id) {
            this.TABLE_NAME = 'ma_transfers'
            // const currenttransferdata = await this.findMatching('', { ma_transfers_id: fields.ma_transfers_id })
            const currenttransferdata = await this.rawQuery('SELECT uic,ma_transfers_id,transfer_amount,ma_beneficiaries_id,ma_user_id,userid FROM ma_transfers  WHERE ma_transfers_id =' + fields.ma_transfers_id, connection)
            const amlhighlighteddetails = {
              ma_aml_threshold_id: createAml.data,
              uic: currenttransferdata[0].uic,
              suspect_transaction_id: currenttransferdata[0].ma_transfers_id,
              suspect_amount: currenttransferdata[0].transfer_amount,
              ma_beneficiaries_id: currenttransferdata[0].ma_beneficiaries_id,
              ma_user_id: currenttransferdata[0].ma_user_id,
              userid: currenttransferdata[0].userid,
              type: 'SPIKE'
            }
            this.TABLE_NAME = 'ma_aml_highlighted_details'
            const createAmlDetails = await this.createEntry('', amlhighlighteddetails, connection)
            if (createAmlDetails.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAmlDetails
            }
          }
          await mySQLWrapper.commit(connection)
        }
      }
      return { status: 200, respcode: 1000, message: 'success' }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: 'amlController.js', action: 'spiketransferDetails', type: 'response', fields: err })
      // lokiLogger.error({ pagename: 'amlController.js', action: 'spiketransferDetails', type: 'response', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.sqlMessage }
    } finally {

    }
  }

  static async beneThreshold (fields, connection) {
    log.logger({ pagename: 'amlController.js', action: 'beneThreshold', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'beneThreshold', type: 'request', fields: fields })
    try {
      let transfermaster = {}
      let addedBeneCount = 0
      let deletedBeneCount = 0
      const beneThresholdSql = `SELECT beneficiary_threshold,block_beneficiary
                                FROM ma_transfers_threshold 
                                WHERE threshold_period = '1' 
                                AND threshold_status = 'A' 
                                AND (ma_user_id = '${fields.ma_user_id}' OR ma_user_id = '0')
                                ORDER BY ma_user_id DESC`
      const beneThresholdData = await this.rawQuery(beneThresholdSql, connection)
      console.log('------------beneThresholdData--------------')
      console.log(beneThresholdData)
      if (beneThresholdData.length > 0) {
        for (const data of beneThresholdData) {
          if (data.ma_user_id === fields.ma_user_id) {
            transfermaster = data
            break
          }
        }
        // If no rule found for merchant id then assign global rule to merchant
        if (Object.keys(transfermaster).length === 0) {
          transfermaster = beneThresholdData[0]
        }
      } else {
        return { status: 200, respcode: 1000, message: 'success' } // No Bene threshold found so return success
      }

      if (transfermaster.beneficiary_threshold === null || transfermaster.beneficiary_threshold === undefined || transfermaster.beneficiary_threshold === 0) {
        return { status: 200, respcode: 1000, message: 'success' } // No Bene threshold found so return success
      }

      // Get added bene count for merchant
      const beneAddedSql = `SELECT COUNT(1) AS added_bene 
                            FROM ma_beneficiaries 
                            WHERE ma_user_id = '${fields.ma_user_id}' 
                            AND beneficiary_status = 'Y'
                            AND addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59')`
      console.log('---beneAddedSql---')
      console.log(beneAddedSql)
      const beneAddedData = await this.rawQuery(beneAddedSql, connection)
      console.log('------------beneAddedData--------------')
      console.log(beneAddedData)
      if (beneAddedData.length > 0) {
        addedBeneCount = beneAddedData[0].added_bene
      }
      // Get delete bene count merchant
      const beneDeletedSql = `SELECT COUNT(DISTINCT bene.ma_beneficiaries_id) AS deleted_bene
                              FROM ma_beneficiaries AS bene
                              INNER JOIN ma_transfers AS trf ON bene.ma_beneficiaries_id = trf.ma_beneficiaries_id AND bene.ma_user_id = trf.ma_user_id
                              WHERE bene.ma_user_id = '${fields.ma_user_id}'
                              AND bene.beneficiary_status = 'D'
                              AND bene.addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59')
                              AND trf.addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59')
                              GROUP BY bene.ma_beneficiaries_id`
      console.log('---beneDeletedSql---')
      console.log(beneDeletedSql)
      const beneDeletedData = await this.rawQuery(beneDeletedSql, connection)
      console.log('------------beneDeletedData--------------')
      console.log(beneDeletedData)
      if (beneDeletedData.length > 0) {
        deletedBeneCount = beneDeletedData[0].deleted_bene
      }
      const bene_count = (addedBeneCount + deletedBeneCount) + 1 // Add 1 for current bene which is to be added
      log.logger({ pagename: 'amlController.js', action: 'beneThreshold', type: 'response', fields: { bene_count: bene_count, bene_threshold: transfermaster.beneficiary_threshold } })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'beneThreshold', type: 'response', fields: { bene_count: bene_count, bene_threshold: transfermaster.beneficiary_threshold } })

      // Check if added and deleted bene count exclude threshold
      if (bene_count > transfermaster.beneficiary_threshold) {
        if (transfermaster.block_beneficiary == 'Y') {
          console.log('------ADD BENEFICIARY BLOCKED AS THRESHOLD EXCEEEDED-------')
          return { status: 400, respcode: 1125, message: errorMsg.responseCode[1125] }
        } else {
          console.log('------ADD BENEFICIARY HIGHLIGHTED AS THRESHOLD EXCEEEDED-------')
          const amlhighlighted = {
            ma_user_id: fields.ma_user_id,
            uic: fields.uic,
            beneficiary_name: fields.beneficiary_name,
            account_number: fields.account_number,
            mobile_number: fields.mobile_number,
            ifsc_code: fields.ifsc_code,
            bank_master_id: fields.bank_master_id
          }
          this.TABLE_NAME = 'ma_aml_highlighted_bene'
          const createAml = await this.createEntry('', amlhighlighted, connection)
          if (createAml.status === 400) {
            await mySQLWrapper.rollback(connection)
            return createAml
          }
        }
      }
      return { status: 200, respcode: 1000, message: 'success' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'spiketransferDetails', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'spiketransferDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {

    }
  }

  static async kycDetails (fields, connection) {
    log.logger({ pagename: 'amlController.js', action: 'kycDetails', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'kycDetails', type: 'request', fields: fields })
    try {
      this.TABLE_NAME = 'ma_customer_details'
      // const customerdata = await this.findMatching('', { uic: fields.uic })
      const customerdata = await this.rawQuery('SELECT ma_customer_details_id,kyc_status FROM ma_customer_details  WHERE uic =' + fields.uic, connection)
      if (customerdata[0].kyc_status == 'V') {
        this.TABLE_NAME = 'ma_kyc_compliance'
        const kycdata = await this.findMatching('', { ma_customer_details_id: customerdata[0].ma_customer_details_id, type_of_document: '1' })
        if (kycdata[0].document_number != fields.pancard) {
          await mySQLWrapper.beginTransaction(connection)
          this.TABLE_NAME = 'ma_transfers'
          const currenttransferdata = await this.findMatching('', { ma_transfers_id: fields.ma_transfers_id })
          const amlhighlighted = {
            uic: currenttransferdata[0].uic,
            suspect_transaction_id: currenttransferdata[0].ma_transfers_id,
            suspect_amount: currenttransferdata[0].transfer_amount,
            ma_beneficiaries_id: currenttransferdata[0].ma_beneficiaries_id,
            ma_user_id: currenttransferdata[0].ma_user_id,
            userid: currenttransferdata[0].userid,
            type: 'KYC'
          }
          this.TABLE_NAME = 'ma_aml_highlighted'
          const createAml = await this.createEntry('', amlhighlighted, connection)
          if (createAml.status === 400) {
            await mySQLWrapper.rollback(connection)
            return createAml
          }
          const amlhighlighteddetails = {
            ma_aml_threshold_id: createAml.data,
            uic: currenttransferdata[0].uic,
            suspect_transaction_id: currenttransferdata[0].ma_transfers_id,
            suspect_amount: currenttransferdata[0].transfer_amount,
            ma_beneficiaries_id: currenttransferdata[0].ma_beneficiaries_id,
            ma_user_id: currenttransferdata[0].ma_user_id,
            userid: currenttransferdata[0].userid,
            type: 'KYC'
          }
          this.TABLE_NAME = 'ma_aml_highlighted_details'
          const createAmlDetails = await this.createEntry('', amlhighlighteddetails, connection)
          if (createAmlDetails.status === 400) {
            await mySQLWrapper.rollback(connection)
            return createAmlDetails
          }
        }
        await mySQLWrapper.commit(connection)
      }
      return { status: true, message: 'success' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'kycDetails', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'kycDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {

    }
  }

  static async thresholdDetails (fields, connection) {
    log.logger({ pagename: 'amlController.js', action: 'thresholdDetails', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'thresholdDetails', type: 'request', fields: fields })
    try {
      // this.TABLE_NAME = 'ma_aml_threshold'
      // const thresholddata = await this.findMatching('', { period: 1 })
      // const transfersdata = await this.getTransfersData(fields.uic, fields.beneficiaryid, 1, 'threshold', '', connection)
      var customer = []
      // var customersum = 0
      var beneficiary = []
      // var beneficiarysum = 0
      // var customertobeneficiary = []
      // var customertobeneficiarysum = 0
      /* transfersdata.forEach(element => {
        if (element.uic === fields.uic) {
          customer.push(element)
          customersum = customersum + element.transfer_amount
        }
        if (element.ma_beneficiaries_id === fields.beneficiaryid) {
          beneficiary.push(element)
          beneficiarysum = beneficiarysum + element.transfer_amount
        }
        if (element.uic === fields.uic && element.ma_beneficiaries_id === fields.beneficiaryid) {
          customertobeneficiary.push(element)
          customertobeneficiarysum = customertobeneficiarysum + element.transfer_amount
        }
      }) */

      /* for (var index in thresholddata) {
        if (thresholddata[index].operative_customer == 1 && thresholddata[index].operative_beneficiary == 1) {
          if (customertobeneficiarysum >= thresholddata[index].amount) {
            const amlhighlighted = {
              uic: customertobeneficiary[customertobeneficiary.length - 1].uic,
              suspect_transaction_id: customertobeneficiary[customertobeneficiary.length - 1].ma_transfers_id,
              suspect_amount: customertobeneficiary[customertobeneficiary.length - 1].transfer_amount,
              ma_beneficiaries_id: customertobeneficiary[customertobeneficiary.length - 1].ma_beneficiaries_id,
              ma_user_id: customertobeneficiary[customertobeneficiary.length - 1].ma_user_id,
              userid: customertobeneficiary[customertobeneficiary.length - 1].userid,
              type: 'ONE2ONE'
            }
            this.TABLE_NAME = 'ma_aml_highlighted'
            const createAml = await this.createEntry('', amlhighlighted, connection)
            if (createAml.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAml
            }
            var str = ''
            var data = ''
            var queryfields = ''
            customertobeneficiary.forEach(function (element) {
              data = ('(' + createAml.data + ',"' + element.uic + '","' + element.ma_transfers_id + '","' + element.transfer_amount + '","' + element.ma_beneficiaries_id + '","' + element.ma_user_id + '","' + element.userid + '","ONE2ONE"),')
              str += data
            })
            str = str.substring(0, str.length - 1)
            queryfields = '(`ma_aml_threshold_id`,`uic`,`suspect_transaction_id`,`suspect_amount`,`ma_beneficiaries_id`,`ma_user_id`,`userid`,`type`)'
            this.TABLE_NAME = 'ma_aml_highlighted_details'
            const createAmlDetails = await this.createEntryBulk('', queryfields, str, connection)
            if (createAmlDetails.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAmlDetails
            }
            break
          }
        }
        if (thresholddata[index].operative_customer == 1 && thresholddata[index].operative_beneficiary == 3) {
          if (customersum >= thresholddata[index].amount) {
            const amlhighlighted = {
              uic: customer[customer.length - 1].uic,
              suspect_transaction_id: customer[customer.length - 1].ma_transfers_id,
              suspect_amount: customer[customer.length - 1].transfer_amount,
              ma_beneficiaries_id: customer[customer.length - 1].ma_beneficiaries_id,
              ma_user_id: customer[customer.length - 1].ma_user_id,
              userid: customer[customer.length - 1].userid,
              type: 'ONE2MANY'
            }
            this.TABLE_NAME = 'ma_aml_highlighted'
            const createAml = await this.createEntry('', amlhighlighted, connection)
            if (createAml.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAml
            }
            var strCust = ''
            var dataCust = ''
            var queryfieldsCust = ''
            customer.forEach(function (element) {
              dataCust = ('(' + createAml.data + ',"' + element.uic + '","' + element.ma_transfers_id + '","' + element.transfer_amount + '","' + element.ma_beneficiaries_id + '","' + element.ma_user_id + '","' + element.userid + '","ONE2MANY"),')
              strCust += dataCust
            })
            strCust = strCust.substring(0, strCust.length - 1)
            queryfieldsCust = '(`ma_aml_threshold_id`,`uic`,`suspect_transaction_id`,`suspect_amount`,`ma_beneficiaries_id`,`ma_user_id`,`userid`,`type`)'
            this.TABLE_NAME = 'ma_aml_highlighted_details'
            const createAmlDetails = await this.createEntryBulk('', queryfieldsCust, strCust, connection)
            if (createAmlDetails.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAmlDetails
            }
            break
          }
        }
        if (thresholddata[index].operative_customer == 3 && thresholddata[index].operative_beneficiary == 1) {
          if (beneficiarysum >= thresholddata[index].amount) {
            const amlhighlighted = {
              uic: beneficiary[beneficiary.length - 1].uic,
              suspect_transaction_id: beneficiary[beneficiary.length - 1].ma_transfers_id,
              suspect_amount: beneficiary[beneficiary.length - 1].transfer_amount,
              ma_beneficiaries_id: beneficiary[beneficiary.length - 1].ma_beneficiaries_id,
              ma_user_id: beneficiary[beneficiary.length - 1].ma_user_id,
              userid: beneficiary[beneficiary.length - 1].userid,
              type: 'MANY2ONE'
            }
            this.TABLE_NAME = 'ma_aml_highlighted'
            const createAml = await this.createEntry('', amlhighlighted, connection)
            if (createAml.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAml
            }
            var strBen = ''
            var dataBen = ''
            var queryfieldsBen = ''
            beneficiary.forEach(function (element) {
              dataBen = ('(' + createAml.data + ',"' + element.uic + '","' + element.ma_transfers_id + '","' + element.transfer_amount + '","' + element.ma_beneficiaries_id + '","' + element.ma_user_id + '","' + element.userid + '","MANY2ONE"),')
              strBen += dataBen
            })
            strBen = strBen.substring(0, strBen.length - 1)
            queryfieldsBen = '(`ma_aml_threshold_id`,`uic`,`suspect_transaction_id`,`suspect_amount`,`ma_beneficiaries_id`,`ma_user_id`,`userid`,`type`)'
            this.TABLE_NAME = 'ma_aml_highlighted_details'
            const createAmlDetails = await this.createEntryBulk('', queryfieldsBen, strBen, connection)
            if (createAmlDetails.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAmlDetails
            }
            break
          }
        }
      } */

      this.TABLE_NAME = 'ma_customer_details'
      // const customerdata = await this.findMatching('', { uic: fields.uic })

      const customerdata = await this.rawQuery('SELECT mobile_number,uic,ma_user_id,userid FROM ma_customer_details  WHERE uic =' + fields.uic, connection)
      this.TABLE_NAME = 'ma_beneficiaries'
      // const beneficiarydata = await this.findMatching('', { ma_beneficiaries_id: fields.beneficiaryid })
      const beneficiarydata = await this.rawQuery('SELECT ben_mobile_number FROM ma_beneficiaries  WHERE ma_beneficiaries_id =' + fields.beneficiaryid, connection)
      console.log('---------Customer Mobile--------')
      console.log(customerdata[0].mobile_number)
      console.log('---------Bene Mobile--------')
      console.log(beneficiarydata[0].ben_mobile_number)
      if (customerdata[0].mobile_number > 0 && beneficiarydata[0].ben_mobile_number > 0) {
        if (customerdata[0].mobile_number === beneficiarydata[0].ben_mobile_number) {
          await mySQLWrapper.beginTransaction(connection)
          const d = new Date()
          const timestamp = Math.floor(d.getTime() / 1000)
          const amlhighlighted = {
            uic: customerdata[0].uic,
            suspect_transaction_id: fields.aggregator_order_id,
            suspect_amount: fields.amount,
            ma_beneficiaries_id: fields.beneficiaryid,
            ma_user_id: customerdata[0].ma_user_id,
            userid: customerdata[0].userid,
            type: 'SAMENUMBER'
          }
          this.TABLE_NAME = 'ma_aml_highlighted'
          const createAml = await this.createEntry('', amlhighlighted, connection)
          if (createAml.status === 400) {
            await mySQLWrapper.rollback(connection)
            return createAml
          }
          if (fields.ma_transfers_id) {
            this.TABLE_NAME = 'ma_transfers'
            // const currenttransferdata = await this.findMatching('', { ma_transfers_id: fields.ma_transfers_id })
            const currenttransferdata = await this.rawQuery('SELECT uic,ma_transfers_id,transfer_amount,ma_beneficiaries_id,ma_user_id,userid FROM ma_transfers  WHERE ma_transfers_id =' + fields.ma_transfers_id, connection)
            const amlhighlighteddetails = {
              ma_aml_threshold_id: createAml.data,
              uic: currenttransferdata[0].uic,
              suspect_transaction_id: currenttransferdata[0].ma_transfers_id,
              suspect_amount: currenttransferdata[0].transfer_amount,
              ma_beneficiaries_id: currenttransferdata[0].ma_beneficiaries_id,
              ma_user_id: currenttransferdata[0].ma_user_id,
              userid: currenttransferdata[0].userid,
              type: 'SAMENUMBER'
            }
            this.TABLE_NAME = 'ma_aml_highlighted_details'
            const createAmlDetails = await this.createEntry('', amlhighlighteddetails, connection)
            if (createAmlDetails.status === 400) {
              await mySQLWrapper.rollback(connection)
              return createAmlDetails
            }
          }
          await mySQLWrapper.commit(connection)
        }
      }
      return { status: true, message: 'success' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'thresholdDetails', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'thresholdDetails', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getTransfersData (uic, beneficiaryid, period, type, ma_user_id, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'getTransfersData', type: 'request', fields: { uic: uic, beneficiaryid: beneficiaryid, period: period, type: type, ma_user_id: ma_user_id } })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'getTransfersData', type: 'request', fields: { uic: uic, beneficiaryid: beneficiaryid, period: period, type: type, ma_user_id: ma_user_id } })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {
      // const datefrom = await this.formatDate('previous')
      // const dateto = await this.formatDate('current')
      var sql = ''
      if (type == 'threshold') {
        sql = `SELECT * from ma_transfers WHERE addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59') AND transfer_status = 'S' AND (uic='${uic}' OR ma_beneficiaries_id=${beneficiaryid})`
      } else if (type == 'spiketransfer') {
        sql = `SELECT SUM(transfer_amount) AS amount from ma_transfers WHERE addedon BETWEEN FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 00:00:00') AND FROM_UNIXTIME(UNIX_TIMESTAMP(),'%Y-%m-%d 23:59:59') AND transfer_status = 'S' AND ma_user_id = ${ma_user_id}`
      }
      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'amlController.js', action: 'getTransfersData', type: 'response', fields: queryResult })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'getTransfersData', type: 'response', fields: queryResult })
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransfersData', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getTransfersData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getBlacklistedData (ma_customer_details_id, ma_user_id, beneficiaryid, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'getBlacklistedData', type: 'request', fields: { ma_customer_details_id: ma_customer_details_id, ma_user_id: ma_user_id, beneficiaryid: beneficiaryid } })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'getBlacklistedData', type: 'request', fields: { ma_customer_details_id: ma_customer_details_id, ma_user_id: ma_user_id, beneficiaryid: beneficiaryid } })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {
      const sql = `SELECT blacklisted_id,blacklisted_type FROM ma_blacklisting WHERE blacklisted_id IN ('${ma_customer_details_id}','${ma_user_id}','${beneficiaryid}')`
      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'amlController.js', action: 'getBlacklistedData', type: 'response', fields: queryResult })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'getBlacklistedData', type: 'response', fields: queryResult })
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBlacklistedData', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBlacklistedData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getBlacklistedValuesData (accountno, mobileno, aadharcard, pancard, customer_mob, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'getBlacklistedValuesData', type: 'request', fields: { accountno: accountno, mobileno: mobileno, aadharcard: aadharcard, pancard: pancard } })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'getBlacklistedValuesData', type: 'request', fields: { accountno: accountno, mobileno: mobileno, aadharcard: aadharcard, pancard: pancard } })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }
    try {
      const sql = `SELECT value_type,blacklisted_value FROM ma_blacklisted_values WHERE blacklisted_value IN ('${accountno}','${mobileno}','${aadharcard}','${pancard}','${customer_mob}') AND record_status = 'A'  LIMIT 1`
      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'amlController.js', action: 'getBlacklistedValuesData', type: 'response', fields: queryResult })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'getBlacklistedValuesData', type: 'response', fields: queryResult })
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBlacklistedValuesData', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBlacklistedValuesData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async formatDate (type) {
    var d = new Date()
    if (type === 'previous') {
      d.setDate(d.getDate() - 1)
    }
    var month = '' + (d.getMonth() + 1)
    var day = '' + d.getDate()
    var year = d.getFullYear()
    if (month.length < 2) {
      month = '0' + month
    }
    if (day.length < 2) {
      day = '0' + day
    }
    return [year, month, day].join('-')
  }

  /**
   * This function is used add/update the snapshot value of customer/beneficiary
   * <AUTHOR> N
   * @param {*} ma_transfers_id
   * @param {*} aggregator_order_id
   */
  static async insertAmlSnapshot (fields, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'insertAmlSnapshot', type: 'request', fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'insertAmlSnapshot', type: 'request', fields })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }

    try {
      let getTransferSql = ''
      let queryResult = {}
      if (fields.ma_transfers_id) {
        // Get transfer details from ma_transfers
        getTransferSql = `SELECT 
                            trf.uic,
                            trf.ma_beneficiaries_id,
                            trf.transfer_amount,
                            bene.account_number
                          FROM ma_transfers AS trf
                          INNER JOIN ma_beneficiaries AS bene ON  trf.ma_beneficiaries_id = bene.ma_beneficiaries_id
                          WHERE trf.ma_transfers_id = '${fields.ma_transfers_id}'`
        console.log('----------getTransferSql------------')
        console.log(getTransferSql)
      } else {
        // Get success transfers by transaction orderd id
        getTransferSql = `SELECT tns.aggregator_order_id,
                            tnf.uic,
                            tnf.ma_beneficiaries_id,
                            bene.account_number,
                            SUM(tnf.transfer_amount) AS transfer_amount
                          FROM ma_transaction_master AS tns
                          INNER JOIN ma_transfers AS tnf ON tns.ma_transaction_master_id = tnf.ma_transaction_master_id
                          INNER JOIN ma_beneficiaries AS bene ON  tnf.ma_beneficiaries_id = bene.ma_beneficiaries_id
                          WHERE tns.aggregator_order_id = '${fields.aggregator_order_id}'
                          AND tnf.transfer_status = 'S'
                          GROUP BY tnf.uic`
        console.log('----------getTransferSql------------')
        console.log(getTransferSql)
      }
      queryResult = await this.rawQuery(getTransferSql, connection)
      log.logger({ pagename: 'amlController.js', action: 'insertAmlSnapshot - getTransferSql', type: 'response', fields: queryResult })
      // lokiLogger.info({ pagename: 'amlController.js', action: 'insertAmlSnapshot - getTransferSql', type: 'response', fields: queryResult })

      if (queryResult.length > 0) {
        await mySQLWrapper.beginTransaction(connection)
        // Insert/update transfer amount in snapshot table
        const snapShotQuery = `INSERT INTO ma_aml_threshold_snapshot
                                (snapshot_date, type, type_value, amount)
                              VALUES
                                (CURDATE(), '1', '${queryResult[0].uic}', ${queryResult[0].transfer_amount}),
                                (CURDATE(), '2', '${queryResult[0].account_number}', ${queryResult[0].transfer_amount})
                              ON DUPLICATE KEY 
                              UPDATE
                                amount = amount + ${queryResult[0].transfer_amount}`
        const snapShotQueryResult = await this.rawQuery(snapShotQuery, connection)
        // console.log('snapShotQuery')
        // console.log(snapShotQuery)
        log.logger({ pagename: 'amlController.js', action: 'insertAmlSnapshot - snapShotQuery', type: 'response', fields: snapShotQueryResult })
        // lokiLogger.info({ pagename: 'amlController.js', action: 'insertAmlSnapshot - snapShotQuery', type: 'response', fields: snapShotQueryResult })

        // Insert/update transfer amount in snapshot details table
        const snapShotDetailsQuery = `INSERT INTO ma_aml_threshold_snapshot_details
                                        (snapshot_date, uic, beneficiary_id, account_number, amount)
                                      VALUES
                                        (CURDATE(), '${queryResult[0].uic}', '${queryResult[0].ma_beneficiaries_id}', '${queryResult[0].account_number}', ${queryResult[0].transfer_amount})
                                      ON DUPLICATE KEY 
                                      UPDATE
                                        amount = amount + ${queryResult[0].transfer_amount}`
        const snapShotDetailsQueryResult = await this.rawQuery(snapShotDetailsQuery, connection)
        // console.log('snapShotDetailsQuery')
        // console.log(snapShotDetailsQuery)
        log.logger({ pagename: 'amlController.js', action: 'insertAmlSnapshot - snapShotDetailsQuery', type: 'response', fields: snapShotDetailsQueryResult })
        // lokiLogger.info({ pagename: 'amlController.js', action: 'insertAmlSnapshot - snapShotDetailsQuery', type: 'response', fields: snapShotDetailsQueryResult })
        await mySQLWrapper.commit(connection)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: 'amlController.js', action: 'insertAmlSnapshot - catch', type: 'response', fields: err })
      // lokiLogger.error({ pagename: 'amlController.js', action: 'insertAmlSnapshot - catch', type: 'response', fields: err })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + (err.sqlMessage) ? err.sqlMessage : err.message }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
   * This function is used to get the active Threshold limits for transfer
   * <AUTHOR> N
   */
  static async getThresholdLimits (connection) {
    try {
      const sql = `SELECT ma_aml_threshold_id,operative_customer,operative_beneficiary,amount,period,block_transaction
                  FROM ma_aml_threshold 
                  WHERE  threshold_status = 'A'
                  ORDER BY period ASC`

      const queryResult = await this.rawQuery(sql, connection)
      log.logger({ pagename: 'amlController.js', action: 'getThresholdLimits', type: 'response', fields: queryResult })

      if (queryResult.length > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], thresholdLimits: queryResult }
      }
      return { status: 200, respcode: 1119, message: errorMsg.responseCode[1119], thresholdLimits: [] }
    } catch (err) {
      log.logger({ pagename: 'amlController.js', action: 'getThresholdLimits - catch', type: 'response', fields: err })
      // lokiLogger.error({ pagename: 'amlController.js', action: 'getThresholdLimits - catch', type: 'response', fields: err })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + (err.sqlMessage) ? err.sqlMessage : err.message }
    }
  }

  /**
   * This function is used to check the threshold limits of every customer and uic on each transfers transaction
   * <AUTHOR> N
   */
  static async checkThresholdLimits (fields, connection = null) {
    log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits', type: 'request', fields })
    // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits', type: 'request', fields })
    var isSet = false
    if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    }

    try {
      // Get threshold limits
      const thresholdData = await this.getThresholdLimits(connection)
      if (thresholdData.status === 400) {
        return thresholdData // Return on error
      } else if ((thresholdData.thresholdLimits).length == 0) {
        log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits', type: 'response', fields: thresholdData })
        // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits', type: 'response', fields: thresholdData })
        return thresholdData // Return if no limits assigned
      }

      for (const thresholdLimit of thresholdData.thresholdLimits) {
        const thresoldSpan = thresholdLimit.period - 1
        const fromDate = `DATE_SUB(CURDATE(), INTERVAL ${thresoldSpan} DAY)`
        const toDate = 'CURDATE()'
        let snapShotAmount = 0
        let type = ''
        let remarks = ''
        let message = ''

        // ONE TO ONE
        if (thresholdLimit.operative_customer == 1 && thresholdLimit.operative_beneficiary == 1) {
          console.log('ONE2ONE : ' + thresholdLimit.period)
          const thresholdAmtQuery1 = `SELECT SUM(amount) AS amount
                                    FROM ma_aml_threshold_snapshot_details 
                                    WHERE uic = '${fields.uic}'
                                    AND account_number = '${fields.accountno}'
                                    AND snapshot_date >= ${fromDate} AND snapshot_date <= ${toDate}`
          // console.log('thresholdAmtQuery1')
          console.log(thresholdAmtQuery1)
          const thresholdAmtQueryResult1 = await this.rawQuery(thresholdAmtQuery1, connection)
          console.log(thresholdAmtQueryResult1)
          if (thresholdAmtQueryResult1.length > 0) {
            snapShotAmount = thresholdAmtQueryResult1[0].amount
          }
          type = 'ONE2ONE'
          remarks = `Customer's (${fields.uic}) total transfer amount of ${snapShotAmount} to Beneficiary ${fields.accountno} has exceded the threshold amount ${thresholdLimit.amount} for ${thresholdLimit.period} day(s)`
          message = errorMsg.responseCode[1123]
          message = message.replace('<period>', thresholdLimit.period)
          log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits - One to One - period : ' + thresholdLimit.period, type: 'response', fields: thresholdAmtQueryResult1 })
          // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits - One to One - period : ' + thresholdLimit.period, type: 'response', fields: thresholdAmtQueryResult1 })
        }

        // ONE TO MANY
        if (thresholdLimit.operative_customer == 1 && thresholdLimit.operative_beneficiary == 3) {
          console.log('ONE2MANY : ' + thresholdLimit.period)
          const thresholdAmtQuery2 = `SELECT SUM(amount) AS amount
                                    FROM ma_aml_threshold_snapshot 
                                    WHERE type = '1'
                                    AND type_value = '${fields.uic}'
                                    AND snapshot_date >= ${fromDate} AND snapshot_date <= ${toDate}`
          // console.log('thresholdAmtQuery2')
          console.log(thresholdAmtQuery2)
          const thresholdAmtQueryResult2 = await this.rawQuery(thresholdAmtQuery2, connection)
          console.log(thresholdAmtQueryResult2)
          if (thresholdAmtQueryResult2.length > 0) {
            snapShotAmount = thresholdAmtQueryResult2[0].amount
          }
          type = 'ONE2MANY'
          remarks = `Customer's (${fields.uic}) total transfer amount of ${snapShotAmount} to all Beneficiaries has exceded the threshold amount of ${thresholdLimit.amount} for ${thresholdLimit.period} day(s)`
          message = errorMsg.responseCode[1120]
          message = message.replace('<period>', thresholdLimit.period)
          log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits - One to Many - period : ' + thresholdLimit.period, type: 'response', fields: thresholdAmtQueryResult2 })
          // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits - One to Many - period : ' + thresholdLimit.period, type: 'response', fields: thresholdAmtQueryResult2 })
        }

        // MANY TO ONE
        if (thresholdLimit.operative_customer == 3 && thresholdLimit.operative_beneficiary == 1) {
          console.log('MANY2ONE : ' + thresholdLimit.period)
          const thresholdAmtQuery3 = `SELECT SUM(amount) AS amount
                                    FROM ma_aml_threshold_snapshot 
                                    WHERE type = '2'
                                    AND type_value = '${fields.accountno}'
                                    AND snapshot_date >= ${fromDate} AND snapshot_date <= ${toDate}`
          // console.log('thresholdAmtQuery3')
          console.log(thresholdAmtQuery3)
          const thresholdAmtQueryResult3 = await this.rawQuery(thresholdAmtQuery3, connection)
          console.log(thresholdAmtQueryResult3)
          if (thresholdAmtQueryResult3.length > 0) {
            snapShotAmount = thresholdAmtQueryResult3[0].amount
          }
          type = 'MANY2ONE'
          remarks = `Beneficiary (${fields.accountno}) total transfer amount of ${snapShotAmount} to all Customers has exceded the threshold amount of ${thresholdLimit.amount} for ${thresholdLimit.period} day(s)`
          message = errorMsg.responseCode[1121]
          message = message.replace('<period>', thresholdLimit.period)
          log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits - Many to One - period : ' + thresholdLimit.period, type: 'response', fields: thresholdAmtQueryResult3 })
          // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits - Many to One - period : ' + thresholdLimit.period, type: 'response', fields: thresholdAmtQueryResult3 })
        }

        if (type != '') {
          // If snapshot amount is present then add transfer amount to snapshot otherwise transfer amount will be snapshot amount
          if (snapShotAmount === 0 || snapShotAmount === null || snapShotAmount === undefined) {
            snapShotAmount = fields.amount
          } else {
            snapShotAmount = snapShotAmount + fields.amount
          }

          if (snapShotAmount > thresholdLimit.amount) {
            // console.log('----------------------------------------------------------------')
            // console.log(thresholdLimit)
            if (thresholdLimit.block_transaction === 'Y') { // Return error and block transaction
              log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits - ' + type, type: 'response', fields: { threshold_amount: thresholdLimit.amount, snapshot_amount: snapShotAmount } })
              // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits - ' + type, type: 'response', fields: { threshold_amount: thresholdLimit.amount, snapshot_amount: snapShotAmount } })
              console.log('-------------message---------------------')
              console.log(message)
              return { status: 200, respcode: 1126, message: message, block_transaction: thresholdLimit.block_transaction }
            } else {
              const exceededBy = snapShotAmount - thresholdLimit.amount
              const hightlightParams = {
                uic: fields.uic,
                suspect_transaction_id: fields.aggregator_order_id,
                suspect_amount: fields.amount,
                ma_beneficiaries_id: fields.beneficiaryId,
                ma_user_id: fields.ma_user_id,
                userid: fields.userid,
                type: type,
                period: thresholdLimit.period,
                remarks: remarks,
                threshold_id: thresholdLimit.ma_aml_threshold_id,
                exceeded_by: exceededBy
              }
              const amlHiglighted = await this.insertInAmlHighlighted(hightlightParams, connection)
              if (amlHiglighted.status === 400) {
                return amlHiglighted
              }
            }
          }
        } else {
          log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits - No type found', type: 'response', fields: { threshold_amount: thresholdLimit.amount, snapshot_amount: 0 } })
          // lokiLogger.info({ pagename: 'amlController.js', action: 'checkThresholdLimits - No type found', type: 'response', fields: { threshold_amount: thresholdLimit.amount, snapshot_amount: 0 } })
        }
      } // For loop ends

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], block_transaction: 'N' }
    } catch (err) {
      log.logger({ pagename: 'amlController.js', action: 'checkThresholdLimits - catch', type: 'response', fields: err })
      // lokiLogger.error({ pagename: 'amlController.js', action: 'checkThresholdLimits - catch', type: 'response', fields: err })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + (err.sqlMessage) ? err.sqlMessage : err.message }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
   * This function is used to insert record in AML Hightlighted Table
   * @param {*} fields
   * @param {*} connection
   */
  static async insertInAmlHighlighted (fields, connection) {
    try {
      this.TABLE_NAME = 'ma_aml_highlighted'
      const createAml = await this.createEntry('', fields, connection)
      if (createAml.status === 400) {
        // await mySQLWrapper.rollback(connection)
        return createAml
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: 'amlController.js', action: 'insertInAmlHighlighted - catch', type: 'response', fields: err })
      // lokiLogger.error({ pagename: 'amlController.js', action: 'insertInAmlHighlighted - catch', type: 'response', fields: err })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028] + (err.sqlMessage) ? err.sqlMessage : err.message }
    }
  }
}

module.exports = Aml
