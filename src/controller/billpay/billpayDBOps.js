const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class BillPayDBOps extends DAO {
  static async persistentReqResNow (persistentReqRes, reqDataObj, processBillRes, prevConn) {
    try {
      if (prevConn !== undefined && persistentReqRes !== undefined && reqDataObj !== undefined && processBillRes !== undefined) {
        if (persistentReqRes.mode === 'update' && (('request' in persistentReqRes) || ('response' in persistentReqRes))) {
          if ((persistentReqRes.primaryField in reqDataObj) && reqDataObj[persistentReqRes.primaryField] > 0) {
            let sql = `UPDATE ${persistentReqRes.table} `
            const where = ` WHERE ${persistentReqRes.primaryField} = ${reqDataObj[persistentReqRes.primaryField]} `
            let set = false
            if (('request' in persistentReqRes) && ('request' in processBillRes)) {
              set = ' SET '
              sql += set + ` ${persistentReqRes.request} = ${JSON.stringify(processBillRes.request)}  `
            }

            if (('response' in persistentReqRes) && ('response' in processBillRes)) {
              if (set) set = ''
              else set = ' SET '
              sql += set + ` ${persistentReqRes.request} = ${JSON.stringify(processBillRes.request)}  `
            }

            const finalQuery = sql + where
            console.log('finalReqReSQuery', finalQuery)
          }
        }
      }
    } catch (error) {
      console.log('persistentReqResNow', error)
    }
  }
}

module.exports = BillPayDBOps
