const BILLPAY_PROVIDER_DIR = './billpayProvider/'
const log = require('../../util/log')
const errorMsg = require('./billpayErrors')
const { loadClass, persistentReqRes } = require('./billpayConfig')
const billpayDbOps = require('./billpayDBOps')
// const { lokiLogger } = require('../../util/lokiLogger')

class Billpay {
  constructor (billpayProvider = null, prevConn = null) {
    const classFileName = loadClass[billpayProvider]
    billpayProvider = classFileName.toLowerCase()
    this._CURRENT_BILLPAY_PATH = BILLPAY_PROVIDER_DIR + billpayProvider + '/' + billpayProvider
    this.prevConn = prevConn
    if (billpayProvider in persistentReqRes) {
      this.persistentReqRes = persistentReqRes[billpayProvider]
    } else {
      this.persistentReqRes = null
    }
  }

  async requestToProvider (actionType, reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'requestToProvider', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'requestToProvider', type: 'request', fields: arguments })

    try {
      this.CURRENT_PROVIDER_CLASS = require(this._CURRENT_BILLPAY_PATH).BILLPAY

      console.log(this.CURRENT_PROVIDER_CLASS)
      this.CURRENT_PROVIDER_OBJECT = new this.CURRENT_PROVIDER_CLASS()
    } catch (error) {
      console.log('error', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'requestToProvider', type: 'error', fields: error.message })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'requestToProvider', type: 'error', fields: error.message })
      return { status: 400, message: errorMsg.responseCode[3002], respcode: 3002 }
    }

    switch (actionType) {
      case 'INIT_TRANSACTION':

        var processBillRess = this.CURRENT_PROVIDER_OBJECT.processBillPay(reqDataObj, this.prevConn)
        if (this.persistentReqRes !== null && ('INIT_TRANSACTION' in this.persistentReqRes)) {
          await billpayDbOps.persistentReqResNow(this.persistentReqRes.INIT_TRANSACTION, reqDataObj, processBillRess, this.prevConn)
        }
        return processBillRess
      case 'GET_BALANCE':
        return this.CURRENT_PROVIDER_OBJECT.getAccountBalance(reqDataObj, this.prevConn)
      case 'DOUBLE_VERIFICATION':
        return this.CURRENT_PROVIDER_OBJECT.doubleVerify(reqDataObj, this.prevConn)
      case 'REVALIDATE':
        return this.CURRENT_PROVIDER_OBJECT.revalidate(reqDataObj, this.prevConn)
      case 'REFUND':
        return this.CURRENT_PROVIDER_OBJECT.refund(reqDataObj, this.prevConn)
      default:
        return { status: 400, message: errorMsg.responseCode[3002], respcode: 3002 }
    }
  }

  async responseToProvider () {

  }
}

module.exports = Billpay
