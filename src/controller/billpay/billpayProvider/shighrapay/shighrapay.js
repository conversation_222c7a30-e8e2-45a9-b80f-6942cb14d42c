const { API_URL } = require('./shighrapayConfig').BILL_PAY_CONSTANTS
const { apiFormSumbitRequest } = require('../../../../util/formrequest')
const log = require('../../../../util/log')
const { pe } = require('../../billpayCommon')
const errorMsg = require('../../../../util/error')
const billPayErrorMsg = require('./shighrapayErrors')
const inputRules = require('./shighrapayInputRule')
// const { lokiLogger } = require('../../../../util/lokiLogger')

class Shighrapay {
  constructor () {
    this.sessionData = null
  }

  async processBillPay (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processBillPay', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'processBillPay', type: 'request', fields: arguments })
    const response = {
      request: '',
      response: ''
    }
    try {
      const payLoadJson = {
        api_token: reqDataObj.api_token,
        number: reqDataObj.customer_mobile_number,
        provider_id: reqDataObj.operator_code,
        amount: reqDataObj.amount,
        user_id: reqDataObj.gateway_user_id,
        client_id: reqDataObj.billpay_transaction_id
      }

      response.request = payLoadJson
      const apiCallingURL = API_URL + 'recharge'

      const responseRecharge = await apiFormSumbitRequest.post(apiCallingURL, payLoadJson)
      console.log(responseRecharge.data)
      if (pe('data', responseRecharge)) {
        response.response = responseRecharge.data
        if (pe('status', responseRecharge.data)) {
          const status = responseRecharge.data.status

          if (status == 1) {
            const { txnid } = responseRecharge.data
            response.status = 200
            response.apiStatus = 'S'
            response.respcode = 20000
            response.apiData = {
              billpay_ref_id: txnid
            }
            response.message = billPayErrorMsg.responseCode[20000]
          } else if (status == 2) {
            const { txnid } = responseRecharge.data
            response.status = 200
            response.apiStatus = 'F'
            response.respcode = 20000
            response.apiData = {
              billpay_ref_id: txnid
            }
            response.message = billPayErrorMsg.responseCode[20000]
          } else if (status == 3) {
            const { txnid } = responseRecharge.data
            response.status = 200
            response.apiStatus = 'P'
            response.respcode = 20000
            response.apiData = {
              billpay_ref_id: txnid
            }
            response.message = billPayErrorMsg.responseCode[20000]
          } else {
            const { txnid, message } = responseRecharge.data
            response.status = 200
            response.apiStatus = 'F'
            response.respcode = 20001
            response.message = billPayErrorMsg.responseCode[20001] + message
            response.apiData = {
              billpay_ref_id: txnid
            }
          }
        } else {
          response.status = 400
          response.respcode = 20001
          response.message = billPayErrorMsg.responseCode[20001] + ' status not found!'
        }
        return response
      } else {
        response.respcode = 20002
        response.message = billPayErrorMsg.responseCode[20002]
        return response
      }
    } catch (error) {
      // console.log(error)
      response.status = 400
      response.respcode = 20001
      response.response = { error: error.message }
      response.message = billPayErrorMsg.responseCode[20001] + error.message
      return response
    }
  }

  async getAccountBalance (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAccountBalance', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAccountBalance', type: 'request', fields: arguments })
    const response = {}

    // response.status = 200
    // response.apiStatus = 'S'
    // response.apiData = {
    //   balance: 900
    // }
    // response.respcode = 20000
    // response.message = billPayErrorMsg.responseCode[20000]

    // return response

    try {
      const payLoadJson = {
        api_token: reqDataObj.api_token,
        user_id: reqDataObj.gateway_user_id
      }

      const apiCallingURL = API_URL + 'get-balance'

      const responseRecharge = await apiFormSumbitRequest.post(apiCallingURL, payLoadJson)
      console.log(responseRecharge.data)
      if (pe('data', responseRecharge)) {
        if (pe('status', responseRecharge.data)) {
          const status = responseRecharge.data.status
          if (status == 1) {
            const { user_balance } = responseRecharge.data

            response.status = 200
            response.apiStatus = 'S'
            response.apiData = {
              balance: user_balance
            }
            response.respcode = 20000
            response.message = billPayErrorMsg.responseCode[20000]
          } else {
            response.status = 200
            response.apiStatus = 'F'
            response.respcode = 20001
            response.message = billPayErrorMsg.responseCode[20001]
          }
        } else {
          response.status = 400
          response.respcode = 20001
          response.message = billPayErrorMsg.responseCode[20001] + ' status not found!'
        }
        return response
      } else {
        response.respcode = 20002
        response.message = billPayErrorMsg.responseCode[20002]
        return response
      }
    } catch (error) {
      // console.log(error)
      response.status = 400
      response.respcode = 20001
      response.message = billPayErrorMsg.responseCode[20001] + ' ' + error.message
      return response
    }
  }

  async doubleVerify (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: arguments })
    const response = {
      request: '',
      response: ''
    }
    try {
      const payLoadJson = {
        api_token: reqDataObj.api_token,
        user_id: reqDataObj.gateway_user_id,
        txnid: reqDataObj.billpay_ref_id
      }

      response.request = payLoadJson
      const apiCallingURL = API_URL + 'rechargeCheckStatus'

      const responseRecharge = await apiFormSumbitRequest.post(apiCallingURL, payLoadJson)
      console.log(responseRecharge.data)
      if (pe('data', responseRecharge)) {
        response.response = responseRecharge.data
        if (pe('status', responseRecharge.data)) {
          const status = responseRecharge.data.status
          if (status == 1) {
            const { txnid, operator_ref } = responseRecharge.data

            response.status = 200
            response.apiStatus = 'S'
            response.respcode = 20000
            response.message = billPayErrorMsg.responseCode[20000]
          } else if (status == 2) {
            response.status = 200
            response.apiStatus = 'F'
            response.respcode = 20000
            response.message = billPayErrorMsg.responseCode[20000]
          } else if (status == 3) {
            response.status = 200
            response.apiStatus = 'P'
            response.respcode = 20000
            response.message = billPayErrorMsg.responseCode[20000]
          } else {
            response.status = 200
            response.apiStatus = 'F'
            response.respcode = 20001
            response.message = billPayErrorMsg.responseCode[20001]
          }
        } else {
          response.status = 400
          response.respcode = 20001
          response.message = billPayErrorMsg.responseCode[20001] + ' status not found!'
        }
        return response
      } else {
        response.respcode = 20002
        response.message = billPayErrorMsg.responseCode[20002]
        return response
      }
    } catch (error) {
      // console.log(error)
      response.status = 400
      response.respcode = 20001
      response.response = { error: error.message }
      response.message = billPayErrorMsg.responseCode[20001] + error.message
      return response
    }
  }
}

module.exports = {
  BILLPAY: Shighrapay
}
