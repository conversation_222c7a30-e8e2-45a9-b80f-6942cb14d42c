const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const common_fns = require('../../util/common_fns')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const loginData = {}
const validator = require('../../util/validator')
const util = require('../../util/util')
// const { lokiLogger } = require('../../util/lokiLogger')

class BankOnBoardDetails extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME () {
    return 'ma_bank_on_boarding_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_bank_on_boarding_id'
  }

  static async getBanksDetails (_, fields, con) {
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'request', fields: fields })
      const sql = 'SELECT mbobd.ma_bank_on_boarding_id,mbobd.otp_required,mbobd.fields_required_json FROM ma_bank_on_boarding_details as mbobd JOIN ma_bank_on_boarding as mbob ON  mbob.ma_bank_on_boarding_id = mbobd.ma_bank_on_boarding_id WHERE mbob.onboarding_status = "Y"  AND mbobd.ma_bank_on_boarding_id = "' + fields.ma_bank_on_boarding_id + '" AND entity_type = "' + fields.entity_type + '" LIMIT 1'
      const bankDetailsList = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'response', fields: bankDetailsList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'response', fields: bankDetailsList })

      if (bankDetailsList.length > 0) {
        return { status: 200, respcode: 1000, data: bankDetailsList }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'entity error', fields: errorMsg.responseCode[1077] + '[' + fields.entity_type + ']' })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'entity error', fields: errorMsg.responseCode[1077] + '[' + fields.entity_type + ']' })
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getBankDetailsList (_, fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'fields', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'fields', fields: fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'request', fields: fields })
      const sql = 'SELECT mbobd.ma_bank_on_boarding_id,mbobd.otp_required,mbobd.fields_required_json FROM ma_bank_on_boarding_details as mbobd JOIN ma_bank_on_boarding as mbob ON  mbob.ma_bank_on_boarding_id = mbobd.ma_bank_on_boarding_id WHERE mbobd.ma_bank_on_boarding_id IN (' + fields.ma_bank_on_boarding_id + ') AND entity_type = "' + fields.entity_type + '" '

      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'fieldssql', fields: sql })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'fieldssql', fields: sql })
      const bankDetailsList = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'response', fields: bankDetailsList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'response', fields: bankDetailsList })

      if (bankDetailsList.length > 0) {
        return { status: 200, respcode: 1000, data: bankDetailsList }
      }
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] + '~[' + fields.entity_type + ']' }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBanksDetails', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getOnboardBanksDetails (_, fields, prevConn) {
    let connection = null
    let isPreConn = false
    if (typeof (prevConn) === 'undefined' || prevConn === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
    } else {
      connection = prevConn
      isPreConn = true
    }
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getOnboardBanksDetails', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getOnboardBanksDetails', type: 'request', fields: fields })
      // connection = await mySQLWrapper.getConnectionFromPool()
      const sql = 'SELECT mbob.* FROM ma_bank_on_boarding as mbob WHERE  mbob.ma_bank_on_boarding_id = "' + fields.ma_bank_on_boarding_id + '" LIMIT 1'
      const bankDetailsList = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getOnboardBanksDetails', type: 'response', fields: bankDetailsList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getOnboardBanksDetails', type: 'response', fields: bankDetailsList })

      if (bankDetailsList !== undefined && bankDetailsList !== null && bankDetailsList.length > 0) {
        return { status: 200, respcode: 1000, data: bankDetailsList }
      }
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getOnboardBanksDetails', type: 'catcherror', fields: JSON.stringify(error) })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getOnboardBanksDetails', type: 'catcherror', fields: JSON.stringify(error) })
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } finally {
      if (!isPreConn) {
        connection.release()
      }
    }
  }

  static async getTransferDetails (_, fields, prevConn) {
    let connection = null
    let isPreConn = false
    if (typeof (prevConn) === 'undefined' || prevConn === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
    } else {
      connection = prevConn
      isPreConn = true
    }
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferDetails', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferDetails', type: 'request', fields: fields })
      // connection = await mySQLWrapper.getConnectionFromPool()
      const sql = `
      SELECT mt.utr_number,mtm.aggregator_order_id,mt.request_number,mt.ma_user_id,mt.userid,
      mt.ma_bank_on_boarding,mt.ma_transfers_id,mt.transfer_amount,mt.bank_charges,(mt.transfer_amount+mt.bank_charges) as total_amount FROM ma_transfers AS mt
      JOIN ma_transaction_master mtm ON mtm.ma_transaction_master_id=mt.ma_transaction_master_id
      WHERE ma_transfers_id = ${fields.ma_transfers_id} LIMIT 1
      `
      const transferDetailsList = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferDetails', type: 'response', fields: transferDetailsList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferDetails', type: 'response', fields: transferDetailsList })

      if (transferDetailsList !== undefined && transferDetailsList !== null && Object.keys(transferDetailsList).length > 0) {
        return { status: 200, respcode: 1000, data: transferDetailsList }
      }
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getTransferDetails', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1077, message: errorMsg.responseCode[1077] }
    } finally {
      if (!isPreConn) {
        connection.release()
      }
    }
  }

  static async getSenderAddressDetails (_, fields, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSenderAddressDetails', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSenderAddressDetails', type: 'request', fields: fields })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      /**
       * State Id Join Changes
       */
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      const sql = `SELECT maum.ma_user_master_id,
      maum.profileid,
      maum.userid,
      maum.user_password,
      maum.roleid,
      maum.distributer_user_master_id,
      maum.reseller_user_master_id,
      maum.email_id,
      maum.mobile_id,
      maum.loginkey,
      maum.firstname,
      maum.lastname,
      maum.address,
      maum.city,
      maum.state,
      maum.country,
      maum.pincode,
      maum.user_type,
      maum.user_status,
      maum.username,
      maum.password,
      maum.security_pin,
      maum.security_pin_attempts,
      maum.security_lock_expiry,
      maum.security_pin_expiry,
      maum.apikey,
      maum.last_login,
      maum.imei,
      maum.login_ip,
      maum.gst_number,
      CAST(AES_DECRYPT(maum.pan,'${decryptionKey}') AS CHAR) as pan,
      maum.aadhar_number,
      maum.company,
      maum.sales_id,
      maum.zone,
      maum.addedon,
      maum.updatedon,
      maum.jwtkey,
      maum.logo,
      maum.mer_user,
      maum.esign_link,msm.name as statename,mcm.name as cityname
      FROM ma_user_master as maum 
      JOIN ma_states_master as msm on msm.id = maum.state
      JOIN ma_cities_master as mcm on mcm.id = maum.city
      WHERE maum.user_status = "Y" 
      AND maum.userid = "${fields.userid}" LIMIT 1`

      // console.log(sql)
      const senderAddressDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSenderAddressDetails', type: 'response', fields: senderAddressDetails.map(e => common_fns.maskValue(e, 'pan')) })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSenderAddressDetails', type: 'response', fields: senderAddressDetails.map(e => common_fns.maskValue(e, 'pan')) })

      if (senderAddressDetails !== undefined && senderAddressDetails !== null && senderAddressDetails.length > 0) {
        return { status: 200, respcode: 1000, data: senderAddressDetails }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' sender details not found!' }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSenderAddressDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getSenderAddressDetails', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async getSessionDetails (_, fields, prevConn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'request', fields: fields })

    log.logger({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'loginData', fields: loginData })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'loginData', fields: loginData })

    const cacheKey = fields.ma_bank_on_boarding_id + '_' + fields.ma_user_id
    if (cacheKey in loginData) {
      const res = loginData[cacheKey]
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'cacheKeyResult', fields: res })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'cacheKeyResult', fields: res })
      return res
    }

    let connection = null
    let isSet = false
    if (!validator.definedVal(prevConn)) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    } else {
      connection = prevConn
    }
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'request', fields: fields })

      const sql = 'SELECT mauobbm.*,IF(mauobbm.sessionexpiry is not null,IF(mauobbm.sessionexpiry > CURRENT_TIMESTAMP,TRUE,FALSE),FALSE)  AS sessionStatus FROM ma_user_on_boarding_bank_mapping as mauobbm WHERE mauobbm.ma_user_id = "' + fields.ma_user_id + '" AND mauobbm.ma_bank_on_boarding_id = "' + fields.ma_bank_on_boarding_id + '"  LIMIT 1'
      const sessionDetails = await this.rawQuery(sql, connection)
      console.log('sql', sql)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'response', fields: sessionDetails })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'response', fields: sessionDetails })

      if (sessionDetails !== undefined && sessionDetails !== null) {
        const res = { status: 200, data: sessionDetails }
        if (!validator.definedVal(loginData[cacheKey])) {
          // loginData[cacheKey] = res // commented due to issue
        }
        return res
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } catch (error) {
      console.error(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getSessionDetails', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) {
        connection.release()
      }
    }
  }

  static async storeAgentSession (_, fields) {
    let connection = null
    let isSet = false
    if (fields.connection === undefined || fields.connection === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
    } else {
      console.log('storeAgentSession', 'Old Connection Received')
      connection = fields.connection
    }
    var response = {}
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'storeAgentSession', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeAgentSession', type: 'request', fields: fields })
      // connection = await mySQLWrapper.getConnectionFromPool()

      const { sessionid, sessionexpiry, ma_user_id, ma_bank_on_boarding_id } = fields
      if (typeof (sessionid) !== 'undefined' && typeof (sessionexpiry) !== 'undefined' && typeof (ma_user_id) !== 'undefined' && ma_user_id > 0) {
        console.time('TIMER_storeAgentSession_UPDATE')
        const sql = `UPDATE ma_user_on_boarding_bank_mapping set sessionid= "${sessionid}",sessionexpiry= "${sessionexpiry}" where ma_user_id = ${ma_user_id} AND ma_bank_on_boarding_id = ${ma_bank_on_boarding_id} `
        console.log(sql)
        await this.rawQuery(sql, connection)
        console.timeEnd('TIMER_storeAgentSession_UPDATE')
        log.logger({ pagename: require('path').basename(__filename), action: 'storeAgentSession', type: 'response', fields: sql })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeAgentSession', type: 'response', fields: sql })
      } else {
        throw new Error('Invalid Parameters for update')
      }
    } catch (error) {
      console.log(error)
      response = { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) {
        connection.release()
      } else {
        console.log('storeAgentSession', 'Old Connnection Not Release here')
      }
    }
  }

  // Get Details to Add bene to bank side
  static async getBeneficiaryDetails (_, fields, prevConn) {
    let connection = null
    let isPreConn = false
    if (typeof (prevConn) === 'undefined' || prevConn === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
    } else {
      connection = prevConn
      isPreConn = true
    }
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'request', fields: fields })

      const sql = `
      SELECT 
        b.beneficiary_name,
        b.ben_mobile_number,
        b.ma_bene_verification_id,
        mbm.bank_name AS bank,
        mcd.ma_user_id,
        mcd.mobile_number as sendermobilenumber,
        mbb.state,
        mbb.centre AS city,
        mbb.branch,
        mbb.address,
        mbb.ifsc AS ifscode,
        b.account_number AS accountnumber,
        b.mobile_number,
        mcd.uic,
        b.receiver_id,
        mcd_map.remitter_id,
        CONCAT(bank_name,', ',branch) AS bank_with_branch,
        vrfy.bank_benename,vrfy.bene_verify_status,
        mcd.remitter_name
        FROM ma_beneficiaries b 
        JOIN ma_bank_branch mbb ON mbb.ifsc = b.ifsc_code
        JOIN ma_bank_master mbm ON mbm.ma_bank_master_id = mbb.ma_bank_master_id         
        JOIN ma_customer_details mcd ON mcd.uic = b.uic
        JOIN ma_customer_details_bank_mapping mcd_map ON mcd_map.ma_customer_details_id = mcd_map.ma_customer_details_id AND mcd_map.uic = b.uic AND mcd_map.ma_bank_on_boarding_id = ${fields.ma_bank_on_boarding_id}
        LEFT JOIN ma_bene_verification as vrfy ON vrfy.ma_bene_verification_id = b.ma_bene_verification_id AND b.ma_bene_verification_id > 0
        WHERE mbm.bank_status = 1 and mbb.branch_status = 1 and b.ma_beneficiaries_id=${fields.beneficiaryId} LIMIT 1 `

      const beneficiaryList = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'beneficiaryList', fields: beneficiaryList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'beneficiaryList', fields: beneficiaryList })

      if (beneficiaryList.length) {
        return { status: 200, respcode: 1000, data: beneficiaryList }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (error) {
      console.error(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBeneficiaryDetails', type: 'catcherror', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (!isPreConn) {
        connection.release()
      }
    }
  }

  static async getTransactionDetail (prevConn, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionDetail', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransactionDetail', type: 'request', fields: fields })
    let connection = null
    let isPreConn = false
    if (typeof (prevConn) === 'undefined' || prevConn === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
    } else {
      connection = prevConn
      isPreConn = true
    }
    try {
      const sql = `
      SELECT 
        mcd.ma_user_id,
        b.mobile_number,
        b.ben_mobile_number,
        mcd.uic,
        mcd.mobile_number,
        ma_bank_master.bank_name,
        ma_bank_master.ma_bank_master_id,
        b.ifsc_code,
        if(vrfy.bene_verify_status = 'S',IF(vrfy.bank_benename!='',vrfy.bank_benename,b.beneficiary_name),b.beneficiary_name) as beneficiary_name,
        mcd.remitter_name as remitter_name,
        b.account_number
        FROM ma_beneficiaries b 
        JOIN ma_customer_details mcd ON mcd.uic = b.uic
        JOIN ma_bank_master on b.ma_bank_master_id = ma_bank_master.ma_bank_master_id
        LEFT JOIN ma_bene_verification as vrfy ON vrfy.ma_bene_verification_id = b.ma_bene_verification_id AND b.ma_bene_verification_id > 0
        WHERE mcd.uic='${fields.uic}' AND b.beneficiary_status = 'Y' AND mcd.customer_status = 'Y'  AND  ma_beneficiaries_id=${fields.maBeneficiariesId} LIMIT 1 `

      console.log('getTransactionDetailSQL', sql)
      const transferList = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionDetail', type: 'response', fields: transferList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransactionDetail', type: 'response', fields: transferList })

      if (transferList !== undefined && transferList !== null && Object.keys(transferList).length > 0) {
        return { status: 200, respcode: 1000, data: transferList }
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' Transfer sender,receiver not found!' }
    } catch (error) {
      console.error('getTransactionDetail::', error)
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionDetail', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getTransactionDetail', type: 'catcherror', fields: error })
    } finally {
      if (!isPreConn) {
        connection.release()
      }
    }
  }

  static async storeBeneficiaryId (_, fields) {
    let connection = null
    var response = {}
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'request', fields: fields })
      connection = await mySQLWrapper.getConnectionFromPool()

      const { beneficiaryid, maBeneficiaryId } = fields
      if (typeof (beneficiaryid) !== 'undefined' && typeof (maBeneficiaryId) !== 'undefined' && maBeneficiaryId > 0) {
        const sql = `UPDATE ma_beneficiaries set receiver_id= "${beneficiaryid}" where ma_beneficiaries_id = ${maBeneficiaryId} `
        console.log(sql)
        await this.rawQuery(sql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'response', fields: sql })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'response', fields: sql })
      } else {
        throw new Error('Invalid Parameters for update')
      }
    } catch (error) {
      console.log(error)
      response = { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }

  static async deleteBeneficiaryId (_, fields) {
    let connection = null
    let response = {}
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryId', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryId', type: 'request', fields: fields })
      connection = await mySQLWrapper.getConnectionFromPool()

      const { receiverid, maBeneficiaryId } = fields
      if (typeof (receiverid) !== 'undefined' && typeof (maBeneficiaryId) !== 'undefined' && maBeneficiaryId > 0) {
        const sql = `UPDATE ma_beneficiaries set beneficiary_status= "D" where ma_beneficiaries_id = ${maBeneficiaryId} `
        console.log('deleteBeneficiaryId : ', sql)
        await this.rawQuery(sql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryId', type: 'response', fields: sql })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryId', type: 'response', fields: sql })
      } else {
        throw new Error('Invalid Parameters for update')
      }
    } catch (error) {
      console.log(error)
      response = { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '::' + error.message }
      return response
    } finally {
      connection.release()
    }
  }

  static async storeTransferDetails (_, fields, prevConn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'storeTransferDetails', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeTransferDetails', type: 'request', fields: arguments })
    let connection = null
    let isPreConn = false
    if (typeof (prevConn) === 'undefined' || prevConn === null) {
      connection = await mySQLWrapper.getConnectionFromPool()
    } else {
      isPreConn = true
      connection = prevConn
    }

    var response = {}
    console.log(arguments)
    try {
      log.logger({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'request', fields: fields })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'request', fields: fields })
      const { nsdltransactionid, bankrefno, bcpartnerrefno, transactionId } = fields
      if (typeof (nsdltransactionid) !== 'undefined' && typeof (bankrefno) !== 'undefined' && typeof (bcpartnerrefno) !== 'undefined') {
        const sql = `UPDATE ma_transfers set utr_number= '${nsdltransactionid}',brn='${bankrefno}' where ma_transfers_id = ${transactionId} `
        console.log('UPDATE ma_transfers', sql)

        const updateRes = await this.rawQuery(sql, connection)
        console.log('updateRes||', updateRes)
        log.logger({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'response', fields: sql })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'storeBeneficiaryId', type: 'response', fields: sql })
      } else {
        throw new Error('Invalid Parameters for update')
      }
    } catch (error) {
      console.log(error)
      response = { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (!isPreConn) {
        connection.release()
      }
    }
  }
}

module.exports = BankOnBoardDetails
