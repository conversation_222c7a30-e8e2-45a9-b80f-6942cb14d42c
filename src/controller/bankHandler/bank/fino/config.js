const util = require('../../../../util/util')

const BANK_CONSTANTS = {
  BANK_API_URL: util.isProduction() ? 'https://fpbs.finopaymentbank.in/FinoMoneyTransferService/UIService.svc/FinoMoneyTransactionApi' : 'http://103.1.112.205:8024/PaymentBankDMTPublic/UIService.svc/FinoMoneyTransactionApi',
  PROXY_URL: 'https://finoabcdbank.com/Please/change/TXN/URL/for/staging',
  FINO_BANK_API_URL: util.isProduction() ? 'http://fpbs.finopaymentbank.in/FinoMoneyTransferService/UIService.svc/FinoMoneyTransactionApi' : 'http://103.1.112.205:8024/PaymentBankDMTPublic/UIService.svc/FinoMoneyTransactionApi',
  BANK_API_ENDPOINTS: {
    GET_REMITTER_BALANCE: 'ProcessCustomerLimitRequest',
    VALIDATE_BENEFICIARY_BEFORE_ADD: 'DMTBENERequest',
    VALIDATE_BENEFICIARY_BEFORE_ADD_FINO: 'DMTBENERequest',
    INIT_TRANSACTION_IMPS: 'IMPSrequest',
    INIT_TRANSACTION_IMPS_FINO: 'DMTIMPSRequest',
    INIT_TRANSACTION_NEFT: 'NEFTrequest',
    INIT_TRANSACTION_NEFT_FINO: 'DMTNEFTRequest',
    BULK_TRANSFER_IMPS: 'IMPSrequest',
    BULK_TRANSFER_NEFT: 'NEFTrequest',
    BULK_TRANSFER_IMPS_FINO: 'DMTIMPSRequest',
    BULK_TRANSFER_NEFT_FINO: 'DMTNEFTRequest',
    DOUBLE_VERIFICATION: 'TxnstatusRequest',
    PROCESSCUSTOMEREKYC: 'processCustomerEKYC'

  },
  BANK_API_ENDPOINTS_TIMEOUT: {
    VALIDATE_BENEFICIARY_BEFORE_ADD: 60000,
    INIT_TRANSACTION: 60000,
    BULK_TRANSFER: 60000,
    GET_REMITTER_BALANCE: 60000,
    DOUBLE_VERIFICATION: 60000,
    PROCESSCUSTOMEREKYC: 60000

  },
  TRANSFER_MODES: {
    IMPS: 'IMPS',
    IMPS_FINO: 'IMPS_FINO',
    NEFT: 'NEFT',
    NEFT_FINO: 'NEFT_FINO',
    BEFORE_ADD: 'BEFORE_ADD',
    BEFORE_ADD_FINO: 'BEFORE_ADD_FINO'

  },
  TRANSFER_DETAILS_MODE: {
    0: 'all',
    1: 'neft',
    2: 'imps'
  },
  TRANSACTION_TYPE: {
    B: 'Success',
    R: 'Rejection',
    C: 'Refund',
    F: 'Failure',
    BV: 'Bene Validation'
  },
  STATUS: {
    0: 'FAILURE',
    1: 'SUCCESS',
    '-1': 'TIMEOUT',
    91: 'UNKNOWN'
  },
  REMITTERSTATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  BENEFICIARY_STATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  IMPS_STATUS: {
    0: 'pending',
    1: 'active'
  },
  TRANSSTATUS: {
    0: 'Pending',
    1: 'Process',
    2: 'Credited',
    3: 'Rejected or Refund Processing',
    4: 'Refund Success or refund Processed',
    5: 'remitter Registration',
    6: 'failure',
    '-1': 'Unknown'
  },
  PAYMENTSTATUS: {
    0: 'pending',
    1: 'processing',
    2: 'credited',
    3: 'rejected',
    4: 'refund process',
    6: 'failure'
  },
  BANK_LIMIT: 25000,
  TRANSFER_STATUS_CODE: {
    FAILED_IMPS: ['1014', '100', 'R'],
    FAILED_NEFT: ['1014', '100', '12', '23', '21'],
    SUCCESS_IMPS: ['S'],
    SUCCESS_NEFT: ['26', '11']
  },
  verify_bene_status_code: {
    status_200: {
      0: 'success',
      1023: 'unknown',
      1030: 'failure',
      1032: 'failure',
      1033: 'failure',
      1043: 'duplicate',
      1064: 'failure',
      1065: 'failure',
      1103: 'failure',
      1104: 'failure',
      1107: 'pending',
      1108: 'failure',
      2003: 'unknown',
      8003: 'pending'
    },
    status_400: {
      0: 'failure'
    },
    status_401: {
      2001: 'failure'
    },
    status_500: {
      2002: 'unknown'
    }
  },
  HIDE_REMARKS: { 1067: 'Transaction Failed~[INSB]' }, // response_code,
  PASS_PHRASE_HEADER: util.isProduction() ? '982b0d01-b262-4ece-a2a2-45be82212ba1' : '982b0d01-b262-4ece-a2a2-45be82212ba1',
  PASS_PHRASE_BODY: util.isProduction() ? '982b0d01-b262-4ece-a2a2-45be82212ba1' : 'e4986d2a-01c4-425c-850f-0abc51d87dac'
}

module.exports = {
  BANK_CONSTANTS: BANK_CONSTANTS
}
