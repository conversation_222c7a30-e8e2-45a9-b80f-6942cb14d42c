const { TRANSFER_MODES, BANK_LIMIT, TRANSFER_STATUS_CODE, PROXY_URL } = require('./config').BANK_CONSTANTS
const log = require('../../../../util/log')
const { de, getHash512 } = require('../../bankcommon')
const bankOnBoardDetails = require('../../bankOnBoardingDetails')
const errorMsg = require('../../../../util/error')
const bankErrorMsg = require('./error')
const paytmrules = require('./inputRule')
const apiValidator = require('../../apiValidator')
const FinoRequest = require('./finorequest')
const sms = require('../../../../util/sms')
const otp = require('../../../otp/otpController')
const bankDetailController = require('../../../bankDetails/bankBranchController')
/* remitter name validation changes   */
const AirtelBank = require('../airtel/airtel')
// const { lokiLogger } = require('../../../../util/lokiLogger')
let { default: axios } = require("axios");
const crypto = require('crypto')

const bankInstance = new AirtelBank.BANK()

class FINO {
  constructor (conn = null) {
    this.conn = conn
    this.sessionData = null
  }

  async addRemitterSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: reqDataObj })
    try {
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id: reqDataObj.aggregator_order_id || null })
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitterReSendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'request', fields: reqDataObj })
    try {
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj
      const aggregator_order_id = reqDataObj.aggregator_order_id || null
      // For provider id store in ma_sent_sms_master TBL
      const getProviderIdForSms = await otp.getProviderIdForSms(sendermobilenumber, aggregator_order_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      const getProviderId = getProviderIdForSms.data.provider_id || null

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id, provider_id: getProviderId })

      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitter (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'request', fields: reqDataObj })
    try {
      return {
        status: 200,
        respcode: 1000,
        message: bankErrorMsg.responseCode[10000],
        remitter_name: `${reqDataObj.firstName} ${reqDataObj.lastName}`,
        senderid: reqDataObj.sendermobilenumber
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: reqDataObj })
    try {
      return {
        status: 200,
        respcode: 1000,
        message: bankErrorMsg.responseCode[10000],
        senderid: reqDataObj.sendermobilenumber,
        beneficiaryid: reqDataObj.beneficiaryId || ''
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiarySendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id: reqDataObj.aggregator_order_id || null })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'otpResp', fields: { otpResp: otpResp } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiaryReSendOtp (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { sendermobilenumber: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const { sendermobilenumber, otp_template, templateid, sms_type } = reqDataObj
      const aggregator_order_id = reqDataObj.aggregator_order_id || null
      // For provider id store in ma_sent_sms_master TBL
      const getProviderIdForSms = await otp.getProviderIdForSms(sendermobilenumber, aggregator_order_id)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'getProviderIdForSms', fields: getProviderIdForSms })
      const getProviderId = getProviderIdForSms.data.provider_id || null

      const otpResp = await sms.sentSmsAsync(otp_template, sendermobilenumber, templateid, sms_type, connection, { aggregator_order_id, provider_id: getProviderId })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'otpResp', fields: { otpResp: otpResp } })

      if (otpResp.status !== 200) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, response: otpResp.message }
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, response: otpResp.message }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: reqDataObj })
    try {
      return {
        status: 200,
        message: bankErrorMsg.responseCode[10000],
        respcode: 10000
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async initTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'request', fields: reqDataObj })

    const { ifsc_code, bank_name, request_number } = reqDataObj.request_data[reqDataObj.request_number]
    try {
      const actionType = reqDataObj.actionType ? reqDataObj.actionType : 'INIT_TRANSACTION'
      const customPayload = {
        amount: reqDataObj.amount,
        customerMobile: reqDataObj.senderid,
        name: reqDataObj.remitter_name,
        bankName: bank_name,
        ifsc: ifsc_code,
        beneAccNo: reqDataObj.beneAccNo,
        beneMobNo: reqDataObj.beneMobNo,
        beneficiaryId: reqDataObj.receiverid,
        beneName: reqDataObj.receivername,
        mode: reqDataObj.flag
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'customPayload', fields: customPayload })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'customPayload', fields: customPayload })
      const validationRes = await this.validateAPIInput(actionType, customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'validateAPIInput', fields: validationRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getSessionId', fields: currentSessionId })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const getMode = await this.transactionMode(customPayload.ifsc, customPayload.mode)
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getMode', fields: getMode })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getMode', fields: getMode })
      if (getMode.status != 200) {
        return getMode
      }

      const payLoadJson = {
        ClientUniqueID: request_number,
        CustomerMobileNo: customPayload.customerMobile,
        CustomerName: reqDataObj.remitter_name,
        BeneIFSCCode: customPayload.ifsc,
        BeneAccountNo: customPayload.beneAccNo,
        BeneName: customPayload.beneName,
        Amount: customPayload.amount,
        RFU1: '',
        RFU2: '',
        RFU3: '',
        MerchantMobileNo: '',
        PIDData: reqDataObj.PID || '',
        OtpRefrenceId: reqDataObj.otp_ref_id || '',
        OtpPin: reqDataObj.otp || '',
        Latitude: reqDataObj.latitude || '',
        longitude: reqDataObj.longitude || '',
        PublicIP: reqDataObj.public_ip || '',
        EKYCFlag: 2
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'payLoadJson', fields: payLoadJson })

      const requestStart = process.hrtime()
      console.time('TIMER_FINO_INIT_TRANSACTION')
      // const apiResponse = await FinoRequest.post({
      //   endPoint: actionType,
      //   payLoad: payLoadJson,
      //   keys: { ClientId: currentSessionId.ClientId, AuthKey: currentSessionId.AuthKey },
      //   mode: getMode.mode
      // })
      const token = await FinoRequest.getProxyHeader('token')
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'token', fields: token })

      const action_type = getMode.mode == 'IMPS' ? 'IMPSRequestNew' : getMode.mode == 'IMPS_FINO' ? 'DMTIMPSRequestNew' : getMode.mode == 'NEFT' ? 'NEFTRequestNew' : getMode.mode == 'NEFT_FINO' ? 'DMTNEFTRequestNew' : getMode.mode == 'BEFORE_ADD' ? 'DMTBENERequestNew' : ''
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'action_type', fields: action_type })
      const headers = {
        Authorization: `Bearer ${token.data}`,
        client_id: 'AIRPAY',
        action_type: action_type
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'headers', fields: headers })
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'PROXY_URL', fields: PROXY_URL })

      const apiResponse1 = await axios.post(PROXY_URL, payLoadJson, { headers: headers })
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'apiResponse1', fields: apiResponse1 })
      const apiResponse = apiResponse1.data
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'apiResponse', fields: apiResponse })
      if (apiResponse.ResponseCode == 0) {
        apiResponse.status = 200
        apiResponse.respcode = 10000
        apiResponse.response = {
          ...apiResponse,
          ResponseData: JSON.parse(FinoRequest.decryptBody(apiResponse.ResponseData))
        }
      } else {
        // For FINO ---> if ResponseCode value is OtherThan 0, then we need to failed the Transaction . Set transaction_status = 0 in order to fail the Transaction
        apiResponse.status = 400
        apiResponse.transaction_status = 0
        apiResponse.message = apiResponse.MessageString
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'Whole apiResponse', fields: apiResponse })

      console.timeEnd('TIMER_FINO_INIT_TRANSACTION')
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'bankResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'bankResponse', fields: apiResponse })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_FINO_INIT_TRANSACTION_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { ResponseCode, ClientUniqueID, MessageString } = apiResponse.response
      const { TxnID, ActCode, TotalAmount, BeneName, TxnDescription, AmountRequested, ChargesDeducted } = apiResponse.response.ResponseData

      /* status : Success  */
      if (ResponseCode == 0 && ActCode == 0) {
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: 1,
              request_number: request_number,
              amount: AmountRequested,
              bank_service_charges: ChargesDeducted,
              bank_gst: 0,
              bank_gross_amount: TotalAmount,
              bankrefno: TxnID,
              bankTransactionid: ClientUniqueID,
              transactionId: reqDataObj.ma_transfers_id,
              message: MessageString || bankErrorMsg.responseCode[10000],
              rrn: TxnID,
              benename: BeneName
            }]
          }
        }
      }
      /* status : Failure  */
      if (ResponseCode == 1) {
        return {
          status: 200,
          respcode: 1012,
          message: errorMsg.responseCode[1012],
          bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: 0,
              request_number: request_number,
              amount: AmountRequested,
              bank_service_charges: ChargesDeducted,
              bank_gst: 0,
              bank_gross_amount: TotalAmount,
              bankrefno: TxnID,
              bankTransactionid: ClientUniqueID,
              transactionId: reqDataObj.ma_transfers_id,
              message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10012],
              rrn: TxnID,
              benename: BeneName
            }]
          }
        }
      }
      /* status : Timeout/Pending  */
      if (ResponseCode == 0 && ActCode != 0) {
        return {
          status: 200,
          respcode: 10013,
          message: bankErrorMsg.responseCode[10013],
          bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: -1,
              request_number: request_number,
              amount: AmountRequested,
              bank_service_charges: ChargesDeducted,
              bank_gst: 0,
              bank_gross_amount: TotalAmount,
              bankrefno: TxnID,
              bankTransactionid: ClientUniqueID,
              transactionId: reqDataObj.ma_transfers_id,
              message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10039],
              rrn: TxnID,
              benename: BeneName
            }]
          }
        }
      }
      /* Default Case */
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        bulkData: {
          finalStatus: 1,
          transferBulkData: [{
            status: -1,
            message: TxnDescription ? `${bankErrorMsg.responseCode[10001]} :: ${TxnDescription}` : bankErrorMsg.responseCode[10013],
            request_number: request_number
          }]
        }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async bulkTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'request', fields: reqDataObj })
    const resultTransactionArr = []
    // process.exit()
    try {
      if (!reqDataObj && !('request_data' in reqDataObj) && Object.keys(reqDataObj.request_data).length == 0) {
        return { status: 400, respcode: 10034, message: bankErrorMsg.responseCode[10034], transaction_status: 0 }
      }

      /** ** Get/Set Agent session **/
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'getSessionId', fields: currentSessionId })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      /** ** Get/Set Agent session **/

      const requestData = reqDataObj.request_data
      const customPayloadList = []
      const transferResponsePromiseObject = {}

      for (const transferDataKey in requestData) {
        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'requestData', fields: requestData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'requestData', fields: requestData })
        const { ifsc_code, bank_name, transfer_amount, request_number } = reqDataObj.request_data[transferDataKey]
        const customPayload = {
          amount: transfer_amount,
          customerMobile: reqDataObj.senderid,
          name: reqDataObj.remitter_name,
          bankName: bank_name,
          ifsc: ifsc_code,
          beneAccNo: reqDataObj.beneAccNo,
          beneMobNo: reqDataObj.beneMobNo,
          beneName: reqDataObj.receivername,
          beneficiaryId: reqDataObj.receiverid,
          txnReqId: request_number,
          mode: reqDataObj.flag
        }

        const validationRes = await this.validateAPIInput('BULK_TRANSFER', customPayload)
        if (validationRes.status !== 200) {
          log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'validateAPIInput', fields: validationRes })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'validateAPIInput', fields: validationRes })
          return validationRes
        }

        const getMode = await this.transactionMode(customPayload.ifsc, customPayload.mode)
        log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getMode', fields: getMode })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'getMode', fields: getMode })
        if (getMode.status != 200) {
          return getMode
        }

        customPayload.getMode = getMode.mode

        customPayloadList.push(customPayload)
      }

      customPayloadList.forEach((customPayload) => {
        const payLoadJson = {
          ClientUniqueID: customPayload.txnReqId,
          CustomerMobileNo: customPayload.customerMobile,
          CustomerName: reqDataObj.remitter_name,
          BeneIFSCCode: customPayload.ifsc,
          BeneAccountNo: customPayload.beneAccNo,
          BeneName: customPayload.beneName,
          Amount: customPayload.amount,
          RFU1: '',
          RFU2: '',
          RFU3: ''
        }

        const apiResponse = FinoRequest.post({
          endPoint: 'BULK_TRANSFER',
          payLoad: payLoadJson,
          keys: { ClientId: currentSessionId.ClientId, AuthKey: currentSessionId.AuthKey },
          mode: customPayload.getMode
        })

        transferResponsePromiseObject[customPayload.txnReqId] = apiResponse

        try {
          console.log('API_[' + sessionRQ + ']_FINO_BULK_TRANSFER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']')
        } catch (error) {
          console.log(error)
        }
      })

      const requestStart = process.hrtime()
      const _responsePromiseList = await Promise.all(Object.values(transferResponsePromiseObject))
      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_FINO_BULK_TRANSFER_REQUEST_[' + JSON.stringify(customPayloadList) + ']_RESPONSE_[' + JSON.stringify(_responsePromiseList) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'bankResponse', fields: _responsePromiseList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'bankResponse', fields: _responsePromiseList })

      const transactionRequestIds = Object.keys(transferResponsePromiseObject)

      const transactionResultList = []
      _responsePromiseList.forEach((apiResponse, index) => {
        let transactionResult = {}

        if (apiResponse.status != 200) {
          transactionResult = {
            status: -1,
            message: bankErrorMsg.responseCode[10013],
            finalStatus: 0,
            request_number: transactionRequestIds[index]
          }
        }

        const { ResponseCode, ClientUniqueID, MessageString } = apiResponse.response
        const { TxnID, ActCode, TotalAmount, BeneName, TxnDescription, AmountRequested, ChargesDeducted } = apiResponse.response.ResponseData
        /* status : unknow  */
        if (!ActCode || !ResponseCode) {
          transactionResult = {
            status: -1,
            message: bankErrorMsg.responseCode[10013],
            finalStatus: 0,
            request_number: transactionRequestIds[index]
          }
        }
        /* status : Success  */
        if (ResponseCode == 0 && ActCode == 0) {
          transactionResult = {
            status: 1,
            request_number: transactionRequestIds[index],
            amount: AmountRequested,
            bank_service_charges: ChargesDeducted,
            bank_gst: 0,
            bank_gross_amount: TotalAmount,
            bankrefno: TxnID,
            bankTransactionid: ClientUniqueID,
            transactionId: reqDataObj.ma_transfers_id,
            message: MessageString || bankErrorMsg.responseCode[10000],
            rrn: TxnID,
            benename: BeneName,
            finalStatus: 1
          }
        }
        /* status : Failure  */
        if (ResponseCode == 1) {
          transactionResult = {
            status: 0,
            request_number: transactionRequestIds[index],
            amount: AmountRequested,
            bank_service_charges: ChargesDeducted,
            bank_gst: 0,
            bank_gross_amount: TotalAmount,
            bankrefno: TxnID,
            bankTransactionid: ClientUniqueID,
            transactionId: reqDataObj.ma_transfers_id,
            message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10012],
            rrn: TxnID,
            benename: BeneName,
            finalStatus: 1
          }
        }
        /* status : Timeout  */
        if (ResponseCode == 0 && ActCode != 0) {
          transactionResult = {
            status: -1,
            request_number: transactionRequestIds[index],
            amount: AmountRequested,
            bank_service_charges: ChargesDeducted,
            bank_gst: 0,
            bank_gross_amount: TotalAmount,
            bankrefno: TxnID,
            bankTransactionid: ClientUniqueID,
            transactionId: reqDataObj.ma_transfers_id,
            message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10039],
            rrn: TxnID,
            benename: BeneName,
            finalStatus: 1
          }
        }

        transactionResultList.push(transactionResult)
      })

      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        bulkData: {
          finalStatus: 1,
          transferBulkData: transactionResultList
        }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async doubleVerify (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: reqDataObj })

    try {
      const customPayload = {
        externalRefNo: reqDataObj.bcpartnerrefno,
        mode: reqDataObj.mode
      }

      const validationRes = await this.validateAPIInput('DOUBLE_VERIFICATION', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'validateAPIInput', fields: validationRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'getSessionId', fields: currentSessionId })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }
      const payLoadJson = {
        ClientUniqueID: customPayload.externalRefNo
      }

      const requestStart = process.hrtime()
      console.time('FINO_TIMER_DOUBLE_VERIFICATION')
      const apiResponse = await FinoRequest.post({
        endPoint: 'DOUBLE_VERIFICATION',
        payLoad: payLoadJson,
        keys: { ClientId: currentSessionId.ClientId, AuthKey: currentSessionId.AuthKey }
      })
      console.timeEnd('FINO_TIMER_DOUBLE_VERIFICATION')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_DOUBLE_VERIFICATION_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { ResponseCode, ClientUniqueID, MessageString } = apiResponse.response
      const { TxnID, ActCode, TotalAmount, BeneName, TxnDescription, AmountRequested, ChargesDeducted } = apiResponse.response.ResponseData

      /* status : Success  */
      if (ResponseCode == 0 && TRANSFER_STATUS_CODE[`SUCCESS_${customPayload.mode}`].includes(ActCode)) {
        return {
          status: 1,
          request_number: customPayload.externalRefNo,
          amount: AmountRequested,
          bank_service_charges: ChargesDeducted,
          bank_gst: 0,
          bank_gross_amount: TotalAmount,
          bankrefno: TxnID,
          bankTransactionid: ClientUniqueID,
          transactionId: reqDataObj.ma_transfers_id,
          message: MessageString || bankErrorMsg.responseCode[10000],
          rrn: TxnID,
          benename: BeneName
        }
      }
      /* status : Failure  */
      if (ResponseCode == 0 && TRANSFER_STATUS_CODE[`FAILED_${customPayload.mode}`].includes(ActCode)) {
        return {
          status: 0,
          request_number: customPayload.externalRefNo,
          amount: AmountRequested,
          bank_service_charges: ChargesDeducted,
          bank_gst: 0,
          bank_gross_amount: TotalAmount,
          bankrefno: TxnID,
          bankTransactionid: ClientUniqueID,
          transactionId: reqDataObj.ma_transfers_id,
          message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10012],
          rrn: TxnID,
          benename: BeneName,
          refund: false,
          autorefundcredit: true
        }
      }
      /* status : Timeout/Pending  */
      if (ResponseCode == 1 || ResponseCode == 0) {
        return {
          status: -1,
          request_number: customPayload.externalRefNo,
          amount: AmountRequested,
          bank_service_charges: ChargesDeducted,
          bank_gst: 0,
          bank_gross_amount: TotalAmount,
          bankrefno: TxnID,
          bankTransactionid: ClientUniqueID,
          transactionId: reqDataObj.ma_transfers_id,
          message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10039],
          rrn: TxnID,
          benename: BeneName
        }
      }
      /* Default Case */
      return {
        status: -1,
        message: TxnDescription ? `${bankErrorMsg.responseCode[10001]} :: ${TxnDescription}` : bankErrorMsg.responseCode[10013],
        request_number: customPayload.externalRefNo
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async refund () {
    log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'refund', type: 'request', fields: arguments })
    try {
      return { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000] }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'error', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'refund', type: 'error', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async verifyOTPRefund () {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'request', fields: arguments })
    try {
      return { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'error', fields: JSON.stringify(error) })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'error', fields: JSON.stringify(error) })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async preValidateRemitter (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: reqDataObj })
    try {
      return {
        status: 200,
        respcode: 1000,
        message: bankErrorMsg.responseCode[10000],
        senderid: reqDataObj.customerMobile,
        remitter_name: reqDataObj.remitter_name
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async validateBeneficiaryBeforeAdd (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'request', fields: reqDataObj })
    try {
      reqDataObj.amount = 1 // set penny drop amount
      reqDataObj.flag = TRANSFER_MODES.IMPS // validate beneficiary
      const requestNumber = reqDataObj.request_number // initTransaction IMPS
      reqDataObj.request_data = {}
      reqDataObj.request_data[requestNumber] = {
        ifsc_code: reqDataObj.ifsc_code,
        bank_name: reqDataObj.bank_name,
        request_number: requestNumber
      }

      const modeResp = await this.transactionMode(reqDataObj.ifsc_code, TRANSFER_MODES.IMPS)

      if (modeResp.status == 200 && modeResp.isFino == 1) {
        reqDataObj.flag = TRANSFER_MODES.BEFORE_ADD
        reqDataObj.actionType = 'VALIDATE_BENEFICIARY'
      }
      const transactionResponse = await this.initTransaction(reqDataObj, sessionRQ, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'initTransaction-response', fields: transactionResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'initTransaction-response', fields: transactionResponse })

      if (transactionResponse.status != 200) {
        return transactionResponse
      }
      const { bulkData } = transactionResponse

      if (bulkData.transferBulkData.length == 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }

      return bulkData.transferBulkData[0]
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async validateAPIInput (actionType, reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateAPIInput', type: 'request', fields: { validateAPIInput: 'validateAPIInput', actionType, reqDataObj } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'validateAPIInput', type: 'request', fields: { validateAPIInput: 'validateAPIInput', actionType, reqDataObj } })
    const defaultResponse = {
      status: 200,
      respcode: 1001,
      message: 'API valid input'
    }
    if (typeof (actionType) === 'undefined' && typeof (reqDataObj) === 'undefined') {
      return defaultResponse
    }
    if (paytmrules == null && Object.keys(paytmrules).length == 0 && !(actionType in paytmrules)) {
      return defaultResponse
    }
    const currentRules = paytmrules[actionType]
    /**
     * Loop through fields
     */
    for (const [field, fieldRules] of Object.entries(currentRules)) {
      // console.log('currentRules fields', field, fieldRules)
      /**
           * Check Rule field exist in reqDataObj if exist then validate
           */
      if (field in reqDataObj) {
        /**
           * Loop throgh field Rule
           */
        // console.log('fieldRules fields', Object.entries(fieldRules))
        for (const rulename in fieldRules) {
          const rulevalue = fieldRules[rulename]
          // for (var (rulename, rulevalue) in fieldRules ) {
          //  console.log('currentRules fields', rulename, rulevalue)
          const isCurrentFieldValid = apiValidator.validInput(rulename, reqDataObj[field], rulevalue)
          log.logger(field, rulename, reqDataObj[field], rulevalue, isCurrentFieldValid)
          if (isCurrentFieldValid === false) {
            const invValidMsg = `::: ${field} invalid value ${reqDataObj[field]} for rule ${rulename} `
            defaultResponse.status = 400
            defaultResponse.respcode = 10011
            defaultResponse.message = bankErrorMsg.responseCode[10011] + invValidMsg
            break
          }
        }
      }
    }

    return defaultResponse
  }

  async getRemitterAvailableBalance (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber
      }

      const validationRes = await this.validateAPIInput('GET_REMITTER_BALANCE', customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'validateAPIInput', fields: validationRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      }

      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'getSessionId', fields: currentSessionId })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const payLoadJson = {
        CustomerMobileNo: customPayload.customerMobile
      }

      const requestStart = process.hrtime()
      console.time('TIMER_FINO_REMITTER_LIMIT')
      const apiResponse = await FinoRequest.post({
        endPoint: 'GET_REMITTER_BALANCE',
        payLoad: payLoadJson,
        keys: { ClientId: currentSessionId.ClientId, AuthKey: currentSessionId.AuthKey }
      })
      console.timeEnd('TIMER_FINO_REMITTER_LIMIT')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_FINO_REMITTER_LIMIT_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { ResponseCode, ResponseData, MessageString } = apiResponse.response

      if (ResponseCode != 0) {
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + MessageString || '' }
      }
      // CustomerLimit : It  is consumed monthly customer limit
      const { CustomerLimit } = ResponseData

      const maxLimit = BANK_LIMIT

      return {
        status: 200,
        respcode: 1000,
        message: MessageString,
        remitterDetail: true,
        consumedLimit: CustomerLimit,
        remainingLimit: maxLimit - CustomerLimit,
        senderid: customPayload.customerMobile
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async doubleVerifyBene (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'request', fields: reqDataObj })
    try {
      reqDataObj.mode = TRANSFER_MODES.IMPS // default mode for bene verification
      return await this.doubleVerify(reqDataObj, sessionRQ, connection)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async remitterNameValidation (reqDataObj, sessionRQ, connection) {
    try {
      /* request changes : workdround for call FINO api */
      reqDataObj.ma_bank_on_boarding_id = 10
      const result = await bankInstance.remitterNameValidation(reqDataObj, sessionRQ, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'FINO-result', fields: result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'FINO-result', fields: result })

      if (result.status != 200) {
        return result
      }

      return result
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10033] }
    }
  }

  async getBeneficiaryList (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'request', fields: reqDataObj || '' })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'request', fields: reqDataObj || '' })
    return {
      status: 200,
      respcode: 1000,
      beneList: []
    }
  }

  async getSessionId (maUserId, maBankOnBoardingId, prevConn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'request', fields: { maUserId, maBankOnBoardingId } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'request', fields: { maUserId, maBankOnBoardingId } })
    try {
      const sessionData = await bankOnBoardDetails.getSessionDetails(null, { ma_user_id: maUserId, ma_bank_on_boarding_id: maBankOnBoardingId }, prevConn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'response', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'response', fields: sessionData })
      if (sessionData.status !== 200 && Object.keys(sessionData.data).length == 0) {
        return { status: 400, respcode: 10003, message: bankErrorMsg.responseCode[10003] }
      }
      const tmpSessionData = sessionData.data[0]
      const locationidStr = tmpSessionData.locationid
      if (!de(locationidStr) || locationidStr.indexOf('::') === -1) {
        return { status: 400, respcode: 10027, message: bankErrorMsg.responseCode[10027] }
      }

      const [ClientId, AuthKey, _] = locationidStr.split('::')

      const hashData = { status: 200, respcode: 1000, ClientId, AuthKey }
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'hashData', fields: { accessTokenData: 'accessTokenData', hashData } })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'hashData', fields: { accessTokenData: 'accessTokenData', hashData } })
      return hashData
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'catcherror', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async transactionMode (ifsc_code, mode, prevConn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transactionMode', type: 'request', fields: { ifsc_code, mode } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'transactionMode', type: 'request', fields: { ifsc_code, mode } })
    try {
      const bankDetailDataResp = await bankDetailController.getBranchByIfsc('_', {
        ifsc: ifsc_code
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionMode', type: 'bankDetailDataResp', fields: bankDetailDataResp })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'transactionMode', type: 'bankDetailDataResp', fields: bankDetailDataResp })
      const { status, bank_name } = bankDetailDataResp
      if (status != 200) return bankDetailDataResp
      if (bank_name.match(/fino/ig)) return { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000], mode: TRANSFER_MODES[`${mode}_FINO`], isFino: 1 }
      return { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000], mode }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transactionMode', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'transactionMode', type: 'catcherror', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async customerGenerateOTP (obj, req_no, conn) {
    try {
      log.logger({ pagename: 'fino.js', action: 'customerGenerateOTP', type: 'req', fields: obj })
      if (!obj.otp_type || (obj.otp_type != 1 && obj.otp_type != 5)) {
        return { status: 400, message: 'OTP Type is not valid', respcode: 1001 }
      }
      // Create a checksum
      // let array = [fields.mobile_number]
      const array = [obj.mobile_number, obj.otp_type]
      // if (fields.otp_type == 2) array.push(fields.beneAccNo)
      // array.push(fields.otp_type)
      const checksum = await this.checksumOTP(array)
      // console.log('checksum', checksum.data)
      if (checksum.status != 200) return checksum

      const body = {
        CustomerMobileNumber: obj.mobile_number,
        CustomerName: obj.customerName,
        OTPType: obj.otp_type,
        CheckSum: crypto.createHash('sha256').update(checksum.data).digest('hex'),
        MerchantMobileNo: obj.merchantMobileNo,
        BeneName: obj.beneName ? obj.beneName : '',
        BeneAccountNo: obj.beneAccNo ? obj.beneAccNo : '',
        Latitude: obj.latitude ? obj.latitude : '',
        longitude: obj.longitude ? obj.longitude : '',
        PublicIP: ''
      }

      const response = await FinoRequest.proxyRequest('ProcessGenerateOTP', body)
      if (response.status != 200) return { status: 400, respcode: 10001, message: 'OTP not generate' }

      if (response.response.ResponseCode == 1) return { status: 400, respcode: 10001, message: response.response.DisplayMessage }

      const enc_otp = JSON.parse(FinoRequest.decryptBody(response.response.ResponseData))
      return { status: 200, respcode: 10000, message: 'OTP generate successfully', otp_id: enc_otp }
    } catch (err) {
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: err }
    }
  }

  async checksumOTP (arr) {
    try {
      log.logger({ pagename: 'transfersController.js', action: 'checksumOTP', type: 'request', fields: arr })
      const val = arr.join('')
      log.logger({ pagename: 'transfersController.js', action: 'checksumOTP', type: 'response', fields: val })
      return { status: 200, message: 'Checksum generate properly', respcode: 1000, data: val }
    } catch (err) {
      log.logger({ pagename: 'transfersController.js', action: 'checksumOTP', type: 'error', fields: err })
      return { status: 400, message: 'Checksum not generate properly', respcode: 1001 }
    }
  }

  async processCustomerEKYC (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'request', fields: reqDataObj })
    try {
      // Get session id
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      const payload = {
        ClientUniqueID: reqDataObj.ClientUniqueID,
        CustomerMobileNo: reqDataObj.CustomerMobileNo,
        MerchantMobileNo: reqDataObj.MerchantMobileNo,
        AadharNo: reqDataObj.AadharNo,
        Pid: reqDataObj.Pid,
        Latitude: reqDataObj.Latitude,
        longitude: reqDataObj.longitude,
        PublicIP: reqDataObj.PublicIP,
        KYCTypeFlag: reqDataObj.KYCTypeFlag
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'payload', fields: payload })

      const requestStart = process.hrtime()
      console.time('TIMER_PROCESS_CUSTOMER_EKYC')
      const apiResponse = await FinoRequest.proxyRequest('processCustomerEKYC', payload)
      console.log('API Result: ', apiResponse)
      console.timeEnd('TIMER_PROCESS_CUSTOMER_EKYC')

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_PROCESS_CUSTOMER_EKYC_[' + JSON.stringify(payload) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'bankResponse', fields: apiResponse })

      // if (apiResponse.response.ResponseCode === 1) {
      //   apiResponse.status = 400
      //   return apiResponse
      // }

      // const { ResponseCode, ResponseData, MessageString } = apiResponse.response

      // if (ResponseCode != 0) {
      //   return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + MessageString || '' }
      // }

      if (apiResponse.response.ResponseCode == 0) {
        const finoRequestId = JSON.parse(FinoRequest.decryptBody(apiResponse.response.ResponseData))

        return {
          status: apiResponse.status,
          respcode: apiResponse.respcode,
          requestID: finoRequestId.RequestId || '',
          clientUniqueID: reqDataObj.ClientUniqueID || '',
          message: apiResponse.response.MessageString || '',
          displayMessage: apiResponse.response.DisplayMessage || '',
          responseData: apiResponse.response.ResponseData || '',
          navigation: 'OTP',
          sessionRQ: sessionRQ || '',
          bankID: reqDataObj.ma_bank_on_boarding_id || '',
          bankResp: apiResponse
        }
      } else {
        // Insert bank response on failure (already registered with bank)
        const bank_response = JSON.stringify(apiResponse)
        const errorCompare = 'Sorry! Since you already have an existing relationship with the bank'
        if (apiResponse.response.ResponseCode == 1 && apiResponse.response.MessageString == errorCompare) {
          const insertBankFailQuery = `INSERT INTO ma_dmt_bank_fail_response (ma_user_id, userid, mobile_number, ma_bank_on_boarding_id, bank_response) VALUES (${reqDataObj.ma_user_id}, ${reqDataObj.userid}, '${reqDataObj.CustomerMobileNo}', ${reqDataObj.ma_bank_on_boarding_id}, ${bank_response})`
          log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'insert record query', fields: insertBankFailQuery })

          const insertBankFailure = await this.rawQuery(insertBankFailQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'insert record for bank failure response', fields: insertBankFailure })
        }

        return {
          status: 400,
          respcode: 1001,
          message: 'Fail: Fino - ' + apiResponse.response.MessageString,
          displayMessage: 'EKYC registration failed',
          navigation: 'show_aadhar',
          sessionRQ: sessionRQ,
          bankResp: apiResponse
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerEKYC', type: 'catcherror', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async processCustomerRegistration (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'request', fields: reqDataObj })

    try {
      const actionType = reqDataObj.actionType ? reqDataObj.actionType : 'processCustomerRegistration'

      /* const customPayload = {
        amount: reqDataObj.amount,
        customerMobile: reqDataObj.senderid,
        name: reqDataObj.remitter_name,
        bankName: bank_name,
        ifsc: ifsc_code,
        beneAccNo: reqDataObj.beneAccNo,
        beneMobNo: reqDataObj.beneMobNo,
        beneficiaryId: reqDataObj.receiverid,
        beneName: reqDataObj.receivername,
        mode: reqDataObj.flag
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'customPayload', fields: customPayload })
      const validationRes = await this.validateAPIInput(actionType, customPayload)
      if (validationRes.status !== 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'validateAPIInput', fields: validationRes })
        return validationRes
      } */

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'getSessionId', fields: currentSessionId })
        return currentSessionId
      }

      /* const getMode = await this.transactionMode(customPayload.ifsc, customPayload.mode)
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'getMode', fields: getMode })
      if (getMode.status != 200) {
        return getMode
      }
      ClientUniqueID

      CustomerMobileNo
      MerchantMobileNo
      OTPRequestId
      OTPPin
      KYCRequestId
      */

      const payLoadJson = {
        ClientUniqueID: reqDataObj.request_number,
        CustomerMobileNo: reqDataObj.customer_mobile_number,
        OTPRequestId: reqDataObj.otp_request_id,
        OTPPin: reqDataObj.otp_pin,
        KYCRequestId: reqDataObj.kyc_request_id
        /* BeneName: customPayload.beneName,
        RFU1: '',
        RFU2: '',
        RFU3: '' */
      }

      const requestStart = process.hrtime()
      console.time('TIMER_FINO_CUSTOMER_REGISTRATION')
      /* const apiResponse = await FinoRequest.post({
        endPoint: actionType,
        payLoad: payLoadJson,
        keys: { ClientId: currentSessionId.ClientId, AuthKey: currentSessionId.AuthKey }
        // mode: getMode.mode
      }) */
      const apiResponse = await FinoRequest.proxyRequest(
        actionType, payLoadJson
        // mode: getMode.mode
      )
      console.timeEnd('TIMER_FINO_CUSTOMER_REGISTRATION')
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'bankResponse', fields: apiResponse })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_TIMER_FINO_CUSTOMER_REGISTRATION_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'bankResponse', fields: apiResponse })

      if (apiResponse.status != 200) {
        apiResponse.status = 400
        return apiResponse
      }

      const { ResponseCode, ClientUniqueID, MessageString } = apiResponse.response
      // const { TxnID, ActCode, TotalAmount, BeneName, TxnDescription, AmountRequested, ChargesDeducted } = apiResponse.response.ResponseData

      /* status : Success  */
      if (ResponseCode == 0) {
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          sessionRQ: sessionRQ
          /* bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: 1,
              request_number: request_number,
              amount: AmountRequested,
              bank_service_charges: ChargesDeducted,
              bank_gst: 0,
              bank_gross_amount: TotalAmount,
              bankrefno: TxnID,
              bankTransactionid: ClientUniqueID,
              transactionId: reqDataObj.ma_transfers_id,
              message: MessageString || bankErrorMsg.responseCode[10000],
              rrn: TxnID,
              benename: BeneName
            }]
          } */
        }
      }
      /* status : Failure  */
      if (ResponseCode == 1) {
        return {
          status: 400,
          respcode: 1001,
          message: MessageString,
          sessionRQ: sessionRQ
          /* bulkData: {
            finalStatus: 1,
            transferBulkData: [{
              status: 0,
              request_number: request_number,
              amount: AmountRequested,
              bank_service_charges: ChargesDeducted,
              bank_gst: 0,
              bank_gross_amount: TotalAmount,
              bankrefno: TxnID,
              bankTransactionid: ClientUniqueID,
              transactionId: reqDataObj.ma_transfers_id,
              message: MessageString ? `${bankErrorMsg.responseCode[10001]} :: ${MessageString}` : bankErrorMsg.responseCode[10012],
              rrn: TxnID,
              benename: BeneName
            }]
          } */
        }
      }

      /* Default Case */
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        sessionRQ: sessionRQ
        /* bulkData: {
          finalStatus: 1,
          transferBulkData: [{
            status: -1,
            message: TxnDescription ? `${bankErrorMsg.responseCode[10001]} :: ${TxnDescription}` : bankErrorMsg.responseCode[10013],
            request_number: request_number
          }]
        } */
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'processCustomerRegistration', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }
}

module.exports = {
  BANK: FINO
}
