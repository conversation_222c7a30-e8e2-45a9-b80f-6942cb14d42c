const util = require('../../../../util/util')

const BANK_CONSTANTS = {
  // BANK_API_URL: !util.isProduction() ? 'http://202.54.157.77/wsnsdlmoneytransfer/default.aspx' : 'https://agentbanking.nsdlbank.co.in/wsnsdlmoneytransfer/default.aspx',
  BANK_API_URL: !util.isProduction() ? 'http://125.17.19.77/wsinstantnsdlmoneytransfer/default.aspx' : 'https://agentbanking.nsdlbank.co.in/wsnsdlmoneytransfer/default.aspx',
  CHANNEL_ID: !util.isProduction() ? 'e3186961469e142852c6df4f4702f827' : 'Prod Value',
  PARTNER_ID: !util.isProduction() ? '372a8f4c6340706ff527c4489077ecaf' : 'Prod',
  PARTNER_REF_URL: !util.isProduction() ? 'https://sampleurl/need/to/change' : 'Prod',
  PARTNER_CALLBACK_URL: !util.isProduction() ? 'https://sample_callback_url/need/to/change' : 'Prod',
  NSDL_EKYC_CALLBACK_URL: util[process.env.NODE_ENV || 'development'].nsdlKycUrl,
  BANK_API_URL_NEW: 'https://jiffyuat.nsdlbank.co.in/partnerbank/dmt/',
  ENC_PASS: 'J0HQY6y1wk3hJOXoxpasPF5fiEXo8pGbZxDKXMYvZgGJJZIRKaFRGhbUWFGzfjYIPKiquV9tXLpHuEb4C8Uk0lrn1CnhnbQMrOPDKBNhIkcmzSikNeZRRk8mf5i46e5Y',
  BANK_API_ENDPOINTS: {
    GENRATE_OTP: 'verifyCustOnboardingMobileReqOTP',
    VERIFY_OTP: 'verifyCustOnboardingMobileVerifyOTP',
    CUST_ONBOARDING: 'reqCustOnboarding',
    CUST_KYC_ONBOARDING: 'getReqCustOnboardingDtls',
    GET_CUST_ONBRD_DETAILS: 'getCustOnboardingDtls',
    CUST_ONBOARDING_UID_DTLS_FETCH: 'verifyCustOnboardingUidDtlsfetch',
    CUST_ONBOARDING_BIO_DTLS: 'verifyCustOnboardingBioDtls',
    DETAILS_CONFIRM: 'updateCustOnboardingDetailsConfirm'
  },
  PAYMENT_MODES: {
    0: 'neft',
    2: 'imps'
  },
  TRANSFER_MODES: {
    NEFT: 0,
    IMPS: 2
  },
  TRANSFER_DETAILS_MODE: {
    0: 'all',
    1: 'neft',
    2: 'imps'
  },
  TRANSACTION_TYPE: {
    B: 'Success',
    R: 'Rejection',
    C: 'Refund',
    F: 'Failure',
    BV: 'Bene Validation'
  },
  STATUS: {
    0: 'FAILURE',
    1: 'SUCCESS',
    '-1': 'TIMEOUT',
    91: 'UNKNOWN'
  },
  REMITTERSTATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  BENEFICIARY_STATUS: {
    0: 'pending',
    1: 'Active',
    2: 'Reject'
  },
  IMPS_STATUS: {
    0: 'pending',
    1: 'active'
  },
  TRANSSTATUS: {
    0: 'Pending',
    1: 'Process',
    2: 'Credited',
    3: 'Rejected or Refund Processing',
    4: 'Refund Success or refund Processed',
    5: 'remitter Registration',
    6: 'failure',
    '-1': 'Unknown'
  },
  PAYMENTSTATUS: {
    0: 'pending',
    1: 'processing',
    2: 'credited',
    3: 'rejected',
    4: 'refund process',
    6: 'failure'
  },
  TIMEOUT_VALS: {
    LOGIN: 60000,
    DVERIFY: 60000,
    REMITTER: 60000,
    BENE: 60000,
    BENEVERIFY: util.isProduction() ? 60000 : 60000,
    DBENE: 60000,
    TSINGLE: util.isProduction() ? 60000 : 60000,
    TBULK: util.isProduction() ? 60000 : 60000,
    REFUND: 60000,
    VREFUND: 60000,
    AGENTDETAIL: 60000
  },
  HIDE_REMARKS: ['CS99~ Insufficient Balance.'],
  HIDE_REMARKS_REPLACE: ['Transaction Failed~[INSB]'],
  KYC_TYPE: {
    PAN: 1,
    BIO: 2
  }
}

module.exports = {
  BANK_CONSTANTS: BANK_CONSTANTS
}
