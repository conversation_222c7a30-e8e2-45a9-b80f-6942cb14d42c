module.exports = {
  SEND_OTP_ADD_REMITTER: {
    sendermobilenumber: {
      required: true,
      mobilecheck: true
    }
  },
  ADD_REMITTER: {
    sendermobilenumber: {
      required: true,
      numberonly: true,
      indianmobile: true
    },
    sendername: {
      required: true,
      alphasinglespace: true,
      minlength: 5,
      maxlength: 49
    },
    senderaddress1: {
      required: true,
      alphanumericspacecomma: true,
      minlength: 5,
      maxlength: 199
    },
    pincode: {
      required: true,
      numberonly: true,
      minlength: 1,
      maxlength: 10
    },
    cityname: {
      required: true,
      alphasinglespace: true,
      minlength: 5,
      maxlength: 49
    },
    statename: {
      required: true,
      alphasinglespace: true,
      minlength: 5,
      maxlength: 49
    }
  },
  DELETE_BENEFICIARY: {
    senderid: {
      required: true,
      numberonly: true
    },
    receiverid: {
      required: true,
      numberonly: true
    }
  },
  SEND_OTP_DELETE_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  },
  KYC_UPDATE: {
    senderid: {
      required: true,
      numberonly: true
    },
    kyctype: {
      required: true
    }

  },
  KYC_UPDATE_PAN: {
    pan: {
      required: true
    }
  },
  KYC_UPDATE_BIO: {
  },
  KYC_STATUS_CHECK: {
    mobilenumber: {
      required: true,
      mobilecheck: true
    },
    senderid: {
      required: true,
      numberonly: true
    }
  },
  VIEW_BENEFICIARY: {
    customerMobile: {
      required: true,
      mobilecheck: true
    }
  }
}
