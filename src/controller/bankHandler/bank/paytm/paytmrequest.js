'use strict'

const { BANK_API_URL, TRANSFER_MODES, BANK_API_ENDPOINTS, BANK_API_ENDPOINTS_TIMEOUT, HIDE_REMARKS } = require('./config').BANK_CONSTANTS
const { apiJsonRequest } = require('../../../../util/jsonrequest')
const log = require('../../../../util/log')
const bankErrorMsg = require('./error')
const jwtObj = require('jsonwebtoken')
const moment = require('moment-timezone')
const qs = require('qs')
const { isJson } = require('../../bankcommon')
// const { lokiLogger } = require('../../../../util/lokiLogger')
const HideRemarkCode = Object.keys(HIDE_REMARKS)

class PaytmRequest {
  static async post (OP_NAME, PARAMS, { TOKENDATA }) {
    const requestStart = process.hrtime()
    let paytmPostResponse = {}
    // console.time('TIMER_PAYTM_' + OP_NAME)
    try {
      const TOKEN = await this.generateAccessToken(TOKENDATA)
      console.log('TOKEN_DATA |', TOKEN)
      if (TOKEN.status == 400) {
        return TOKEN
      }

      console.log('BANK_API_URL + BANK_API_ENDPOINTS[OP_NAME]', BANK_API_URL + BANK_API_ENDPOINTS[OP_NAME])
      console.log('PARAMS', PARAMS)

      paytmPostResponse = await apiJsonRequest.post(BANK_API_URL + BANK_API_ENDPOINTS[OP_NAME], PARAMS, {
        headers: { Authorization: TOKEN.accessToken },
        timeout: BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] ? BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] : 0
      })

      console.log('PAYTM' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify(paytmPostResponse.data || {}))
      const httpResponseCode = paytmPostResponse.status
      const returnObj = { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000] }
      returnObj.HttpResponseCode = httpResponseCode
      if (paytmPostResponse.status == 200) {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_SUCCESS_' + OP_NAME + '_' + timeInMs + 'ms')

        returnObj.response = paytmPostResponse.data
        if (typeof (returnObj.response) == 'object' && ('response_code' in returnObj.response) && HideRemarkCode.indexOf(returnObj.response.response_code) > -1) { // Insufficent Balance
          returnObj.message = HIDE_REMARKS[returnObj.response.response_code]
        }
        returnObj.timetaken = timeInMs
        return returnObj
      } else {
        console.log('paytmPostResponse>>', paytmPostResponse)
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_NOTSUCESS_' + OP_NAME + '_' + timeInMs + 'ms')
        const returnObj = { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
        returnObj.response = paytmPostResponse.data
        returnObj.timetaken = timeInMs
        return returnObj
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'paytmcatcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'paytmcatcherror', fields: error })
      const res = {}
      if (error.isAxiosError && error.code == 'ECONNABORTED') {
        error.response = error.response || {}
        error.response.status = 408
        error.response.statusText = 'Request Timeout response'
        error.response.data = {}
      }

      if (!(error.response && error.response.data)) {
        error.response.data = {}
        error.response.data.message = error.response.statusText || '~Something went wrong!'
      }

      if (error.data) {
        res.response = error.data
      } else if (error.response && error.response.data) {
        res.response = error.response.data
      } else {
        res.response = error.response && error.response.statusText ? error.response.statusText : ''
      }
      console.log('PAYTM_CATCH' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify({ res: res }))
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_CATCH_' + OP_NAME + '' + timeInMs + 'ms')

      let errorMessage = ''
      const httpResponseCode = error.response.status
      const returnObj = { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] + ':' + errorMessage }
      returnObj.HttpResponseCode = error.response.status
      returnObj.timetaken = timeInMs
      returnObj.response = error.response.data ? error.response.data : {}
      if (!isJson(returnObj.response)) {
        returnObj.response = {
          response: returnObj.response
        }
      }
      if (error.isAxiosError) {
        if (error.code == 'ECONNABORTED') {
          errorMessage = bankErrorMsg.responseCode[10031]
          returnObj.respcode = 10031
          returnObj.message = bankErrorMsg.responseCode[10031]
        } else if ('errorCode' in error.response.data) { // Common Response codes for all APIs jwt errors
          errorMessage = ('errorMessage' in error.response.data) ? error.response.data.errorMessage : ''
          const errorCode = ('errorCode' in error.response.data) ? error.response.data.errorCode : ''
          returnObj.respcode = 10035
          errorMessage = errorCode + '~' + errorMessage
          returnObj.message = errorMessage
        } else if ('message' in error.response.data) { // Failed with Status
          const tmp = []
          if ('status' in error.response.data) {
            tmp.push(error.response.data.status)
          }
          if ('response_code' in error.response.data) {
            tmp.push(error.response.data.response_code)
          }
          if ('message' in error.response.data) {
            tmp.push(error.response.data.message)
          }

          errorMessage = tmp.join('~')
          returnObj.respcode = 10032
          returnObj.message = bankErrorMsg.responseCode[10001] + ':' + errorMessage
        }
      }
      return returnObj
    } finally {
      // console.timeEnd('TIMER_PAYTM_' + OP_NAME)
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_' + OP_NAME + '' + timeInMs + 'ms')
    }
  }

  static async get (OP_NAME, PARAMS, { TOKENDATA }) {
    const requestStart = process.hrtime()
    let paytmPostResponse = {}
    try {
      const TOKEN = await this.generateAccessToken(TOKENDATA)
      console.log('TOKEN_DATA_', TOKEN)
      if (TOKEN.status == 400) {
        return TOKEN
      }
      // console.time('TIMER_PAYTM_' + OP_NAME)

      const postData = qs.stringify(PARAMS)
      paytmPostResponse = await apiJsonRequest.get(BANK_API_URL + BANK_API_ENDPOINTS[OP_NAME] + '?' + postData, {
        headers: { Authorization: TOKEN.accessToken },
        timeout: BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] ? BANK_API_ENDPOINTS_TIMEOUT[OP_NAME] : 0
      })
      // console.timeEnd('TIMER_PAYTM_' + OP_NAME)

      console.log('PAYTM' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify(paytmPostResponse.data || {}))
      const httpResponseCode = paytmPostResponse.status
      const returnObj = { status: 200, respcode: 10000, message: bankErrorMsg.responseCode[10000] }
      returnObj.HttpResponseCode = httpResponseCode
      if (paytmPostResponse.status == 200) {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_SUCCESS_' + OP_NAME + '_' + timeInMs + 'ms')

        returnObj.response = paytmPostResponse.data
        if (typeof (returnObj.response) == 'object' && ('response_code' in returnObj.response) && HideRemarkCode.indexOf(returnObj.response.response_code) > -1) { // Insufficent Balance
          returnObj.message = HIDE_REMARKS[returnObj.response.response_code]
        }
        // response_code
        returnObj.timetaken = timeInMs
        return returnObj
      } else {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('TIMER_NOTSUCESS_' + OP_NAME + '_' + timeInMs + 'ms')
        const returnObj = { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
        returnObj.response = paytmPostResponse.data
        returnObj.timetaken = timeInMs
        return returnObj
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'paytmcatcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'paytmcatcherror', fields: error })
      const res = {}
      if (error.data) {
        res.response = error.data
      } else if (error.response && error.response.data) {
        res.response = error.response.data
      } else {
        res.response = error.response && error.response.statusText ? error.response.statusText : ''
      }
      console.log('PAYTM_CATCH' + '_$$$_' + OP_NAME + '_$$$_' + JSON.stringify(PARAMS) + '_$$$_' + JSON.stringify({ res: res }))

      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_CATCH_' + OP_NAME + '_' + timeInMs + 'ms')
      log.logger({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'paytmcatcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: OP_NAME, type: 'paytmcatcherror', fields: error })
      let errorMessage = ''
      const httpResponseCode = error.response.status
      const returnObj = { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] + ':' + errorMessage }
      returnObj.HttpResponseCode = error.response.status
      returnObj.timetaken = timeInMs
      returnObj.response = error.response.data ? error.response.data : {}
      if (!isJson(returnObj.response)) {
        returnObj.response = {
          response: returnObj.response
        }
      }
      if (error.isAxiosError) {
        if (error.code == 'ECONNABORTED') {
          errorMessage = bankErrorMsg.responseCode[10031]
          returnObj.respcode = 10031
          returnObj.message = bankErrorMsg.responseCode[10031]
        } else if (error.response && error.response.data && ('errorCode' in error.response.data)) { // Common Response codes for all APIs jwt errors
          errorMessage = ('errorMessage' in error.response.data) ? error.response.data.errorMessage : ''
          const errorCode = ('errorCode' in error.response.data) ? error.response.data.errorCode : ''
          returnObj.respcode = 10035
          errorMessage = errorCode + '~' + errorMessage
          returnObj.message = errorMessage
        } else if ('message' in error.response.data) { // Failed with Status
          const tmp = []
          if ('status' in error.response.data) {
            tmp.push(error.response.data.status)
          }
          if ('response_code' in error.response.data) {
            tmp.push(error.response.data.response_code)
          }
          if ('message' in error.response.data) {
            tmp.push(error.response.data.message)
          }

          errorMessage = tmp.join('~')
          returnObj.respcode = 10032
          returnObj.message = bankErrorMsg.responseCode[10001] + ':' + errorMessage
        }
      }
      console.log('returnObj>>CATCH>>>>>>>>>>>>>>>>', returnObj)
      return returnObj
    } finally {
      const requestEnd = process.hrtime(requestStart)
      const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
      console.log('TIMER_FINAALY_' + OP_NAME + '_' + timeInMs + 'ms')
    }
  }

  static async generateAccessToken (tokenData) {
    try {
      const locationidStr = tokenData.locationid
      const secretLoginArr = locationidStr.split('::')
      const timestamp = moment.tz('Asia/Kolkata').unix() * 1000 //
      const requestReferenceId = 'APYTM' + timestamp
      const partnerId = secretLoginArr[0] // 'DMT_i30_000200'
      const clientSecret = secretLoginArr[1] // 'j4azz1yn1VCshRwwEF0MCMcJB5kc-_5eKaSjJD0aC2Y='
      const partnerSubId = secretLoginArr[2] // '8'

      const payload = {
        iss: 'PAYTM',
        timestamp: timestamp,
        partnerId: partnerId,
        partnerSubId: partnerSubId,
        requestReferenceId: requestReferenceId
      }

      // create the access token with the shorter lifespan
      const accessToken = jwtObj.sign(payload, clientSecret, {
        algorithm: 'HS256',
        noTimestamp: true
      })

      return {
        status: 200,
        respcode: 10001,
        message: bankErrorMsg.responseCode[10000],
        accessToken: accessToken
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateAccessToken', type: 'paytmcatcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'generateAccessToken', type: 'paytmcatcherror', fields: error })
      return {
        status: 400,
        respcode: 10033,
        message: bankErrorMsg.responseCode[10033],
        accessToken: null
      }
    }
  }
}

module.exports = PaytmRequest
