
const crypto = require('crypto')
const salt = '7fabdc58'
// /* channel + “#” + partnerId + “#” + customerId + “#” + amount + “#” + ifsc + “#” +beneAccNo + ”#” + salt */
// const hashedStr = `EXTP#**********#**********#1#SBIN0016654#123456789000#${salt}` // IMPS Transaction
// /* channel + “#” + partnerId + “#” + externalRefNo + "#" + salt */
/* (channel + “#” + partnerId
+ “#” + externalRefNo + "#" + salt */
const hashedStr = `EXTP#**********#TAIRTEL1195${salt}` // Transaction Iquiry
// /* partnerId + “#” + customerId+”#”+feSessionId + "#”+ salt */
// // const hashedStr = `**********#**********#SEL7689099${salt}` // sender limit
// /partners/remittance-services/api/v1/customers/**********#salt // remitter check
// const hashedStr = `/partners/remittance-services/api/v1/customers/**********#${salt}`
// otp#partnerId#Salt
// const hashedStr = `886355#**********#${salt}`
// partnerId#beneAccount#beneIfscCode#Salt
// const hashedStr = `**********#*************#PYTM0123456#${salt}`
// /partners/remittance-services/api/v1/customers/**********#salt
// const hashedStr = `/partners/remittance-services/api/v1/customers/**********#${salt}`
// custMsisdn#firstName#dob#pinCode#Salt -> send remitter otp
// const hashedStr = `**********#swarup##313002#${salt}`
console.log(crypto.createHash('sha512').update(hashedStr).digest('hex'))

// recharge
/*
Privatekey:
hash('sha256', <-secret key->@<-username->:|:<-password->)

secret key: Merchant Api key
username: Merchant Username
password: Merchant Password
*/
/*
Checksum :

hash('sha256', $apicallon.$mid.$requestPk);
$apicallon = Current date(Ymd)
$mid = Merchant id
$requestPk = Privatekey

*/
// const today = new Date()
// const Privatekey = '1@M29188:|:Test9876'
// console.log(crypto.createHash('sha256').update(Privatekey).digest('hex'))
// const Checksum = `${today.getFullYear()}${today.getMonth()}${today.getDate()}29188${crypto.createHash('sha256').update(Privatekey).digest('hex')}`
// console.log(crypto.createHash('sha256').update(Checksum).digest('hex'))

// const dmtSession = require('../../../session/dmt_session')
// const mySQLWrapper = require('../../../../lib/mysqlWrapper')

// async function insertDmt () {
//   const connection = await mySQLWrapper.getConnectionFromPool()
//   const insertData = {
//     bank_priority_json: '[{"ma_bank_on_boarding_id":10,"bank_name":"AIRTEL","bank_logo":"https://i.ibb.co/sv8GsNS/Airtel-Payments-Bank-Logo.jpg","priority":1,"ma_bank_type_id":16,"transaction_type":"IMPS","bene_verification":"Y","locationid":"**********::7fabdc58::28479","min_amount":100,"max_amount":25000,"bank_charges":2.5,"bene_charges":2.5},{"ma_bank_on_boarding_id":10,"bank_name":"AIRTEL","bank_logo":"https://i.ibb.co/sv8GsNS/Airtel-Payments-Bank-Logo.jpg","priority":1,"ma_bank_type_id":17,"transaction_type":"IMPS…ion_type":"IMPS","bene_verification":"Y","locationid":"DMT_i30_000200::j4azz1yn1VCshRwwEF0MCMcJB5kc-_5eKaSjJD0aC2Y=::9","min_amount":100,"max_amount":25000,"bank_charges":2.5,"bene_charges":2.5},{"ma_bank_on_boarding_id":9,"bank_name":"PAYTM","bank_logo":null,"priority":2,"ma_bank_type_id":15,"transaction_type":"NEFT","bene_verification":"Y","locationid":"DMT_i30_000200::j4azz1yn1VCshRwwEF0MCMcJB5kc-_5eKaSjJD0aC2Y=::9","min_amount":100,"max_amount":25000,"bank_charges":2.5,"bene_charges":2.5}]',
//     bene_bank_priority_json: '[{"bank_logo":"https://i.ibb.co/sv8GsNS/Airtel-Payments-Bank-Logo.jpg","bank_name":"AIRTEL","bene_charges":2.5,"ma_bank_on_boarding_id":10},{"bank_logo":null,"bank_name":"NSDL","bene_charges":2.5,"ma_bank_on_boarding_id":5},{"bank_logo":null,"bank_name":"PAYTM","bene_charges":2.5,"ma_bank_on_boarding_id":9}]',
//     ma_bank_on_boarding_id: 10,
//     ma_user_id: 28479,
//     mobile_number: '**********',
//     remitter_name: 'swarup Das',
//     session_hash: 'ba7c2e43cd3d83c0dbcf53ee5e30d905',
//     sessionexpiry: '2021-06-24 20:02:44',
//     uic: '***************',
//     userid: 7556
//   }
//   const session_hash = await dmtSession.insertData(connection, insertData)
//   return session_hash
// }

// insertDmt().then(data => {
//   console.log(data)
// }).catch(error => {
//   console.log(error)
// })
