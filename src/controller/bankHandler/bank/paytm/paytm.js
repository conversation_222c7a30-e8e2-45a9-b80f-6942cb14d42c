const { BANK_API_URL, TRANSFER_MODES, BANK_LIMIT, BANK_API_ENDPOINTS_TIMEOUT } = require('./config').BANK_CONSTANTS
const log = require('../../../../util/log')
const { propExist, de } = require('../../bankcommon')
const bankOnBoardDetails = require('../../bankOnBoardingDetails')
const errorMsg = require('../../../../util/error')
// const validator = require('../../../../util/validator')
const bankErrorMsg = require('./error')
const paytmrules = require('./inputRule')
const apiValidator = require('../../apiValidator')
const PaytmRequest = require('./paytmrequest')
const util = require('../../../../util/util')

/* remitter name validation changes   */
const AirtelBank = require('../airtel/airtel')
// const { lokiLogger } = require('../../../../util/lokiLogger')

const bankInstance = new AirtelBank.BANK()

class PAYTMBank {
  constructor (conn = null) {
    this.conn = conn
    this.sessionData = null
  }

  async addRemitterSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        otpType: 'registrationOtp',
        properties: {}
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('SEND_OTP_ADD_REMITTER', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_SEND_OTP_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'apiResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"txn_id":"D0KBV10L0GUAR","state":"495c97aa-aa05-4e05-87ef-0c177edebbd8"}
          if (apiResponse.response.status == 'success') {
            const { txn_id, state } = apiResponse.response
            const addSenderResp = {
              status: 200,
              respcode: 10000,
              message: apiResponse.response.status,
              senderid: customPayload.customerMobile,
              transactionId: txn_id,
              state: state
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            return apiResponse
          }
        } else {
          return apiResponse
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitterReSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        otpType: 'registrationOtp',
        properties: {}
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('SEND_OTP_ADD_REMITTER', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_RESEND_OTP_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"txn_id":"D0KBV10L0GUAR","state":"495c97aa-aa05-4e05-87ef-0c177edebbd8"}
          if (apiResponse.response.status == 'success') {
            const { txn_id, state } = apiResponse.response
            const addSenderResp = {
              status: 200,
              respcode: 10000,
              message: apiResponse.response.status,
              senderid: customPayload.customerMobile,
              transactionId: txn_id,
              state: state
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            return apiResponse
          }
        } else {
          return apiResponse
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitterReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addRemitter (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      const { state } = reqDataObj.bankOTPRsponse || {}

      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber,
        otp: reqDataObj.otp,
        state: state,
        firstName: reqDataObj.firstName,
        lastName: reqDataObj.lastName,
        address1: reqDataObj.senderaddress1.replace(/[^a-z0-9,-/]+/gi, ' '),
        address2: reqDataObj.senderaddress2.replace(/[^a-z0-9,-/]+/gi, ' '),
        city: reqDataObj.cityname,
        country: reqDataObj.country ? reqDataObj.country : 'INDIA', // to do
        State: reqDataObj.statename,
        pin: util.isStaging() ? '134117' : reqDataObj.pincode,
        name: reqDataObj.firstName + ' ' + reqDataObj.lastName
      }
      /***
       * Merge Custom fields
       */
      // console.log('reqDataObj.customFields', reqDataObj.customFields)
      for (const [key, value] of Object.entries(reqDataObj.customFields)) {
        // log.logger(`${key}: ${value}`)
        if (key in customPayload) {
          customPayload[key] = value
        }
      }

      const validationRes = await this.validateAPIInput('ADD_REMITTER', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        otp: customPayload.otp,
        state: customPayload.state,
        name:
        {
          firstName: customPayload.firstName,
          lastName: customPayload.lastName
        },
        address: {
          name: customPayload.name,
          address1: customPayload.address1,
          address2: customPayload.address2,
          city: customPayload.city,
          state: customPayload.State,
          country: customPayload.country, // to do check country
          pin: customPayload.pin,
          mobile: customPayload.customerMobile
        }
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('ADD_REMITTER', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_ADD_REMITTER_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          if (apiResponse.response.status == 'success') {
            const { customerMobile, message, txn_id } = apiResponse.response
            const addSenderResp = {
              senderid: customPayload.customerMobile, // to do need to check with paytm why not return customerMobile
              remitter_name: customPayload.name,
              status: 200,
              message: bankErrorMsg.responseCode[10000],
              respcode: 10000
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            apiResponse.status = 400
            return apiResponse
          }
        } else {
          apiResponse.status = 400
          return apiResponse
        }
      } else {
        apiResponse.status = 400
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiarySendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber, benAccNum: reqDataObj.accountnumber }

      const validationRes = await this.validateAPIInput('SEND_OTP_ADD_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        otpType: 'beneficiaryOtp',
        properties: {
          benAccNum: customPayload.benAccNum
        }
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('SEND_OTP_ADD_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_SEND_OTP_ADD_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"txn_id":"D0KBV10L0GUAR","state":"495c97aa-aa05-4e05-87ef-0c177edebbd8"}
          if (apiResponse.response.status == 'success') {
            const { txn_id, state } = apiResponse.response
            const addSenderResp = {
              status: 200,
              respcode: 10000,
              message: apiResponse.response.status,
              senderid: customPayload.customerMobile,
              transactionId: txn_id,
              state: state
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            return apiResponse
          }
        } else {
          return apiResponse
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiarySendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async addBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        accountNumber: reqDataObj.accountnumber,
        bankName: reqDataObj.bank, // to do bank to be get
        benIfsc: reqDataObj.ifscode,
        name: reqDataObj.receivername,
        nickName: reqDataObj.receivername,
        customerMobile: reqDataObj.senderid,
        otp: reqDataObj.otp || '', // optional
        state: reqDataObj.otpstate || '' // optional
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const validationRes = await this.validateAPIInput('ADD_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        beneficiaryDetails: {
          accountNumber: customPayload.accountNumber,
          bankName: customPayload.bankName,
          benIfsc: customPayload.benIfsc,
          name: customPayload.name,
          nickName: customPayload.nickName
        },
        customerMobile: customPayload.customerMobile,
        otp: customPayload.otp,
        state: customPayload.state
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('ADD_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_ADD_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"customerMobile":"**********","beneficiaryId":"332c7d4802c848ea937f76ef25ab1249"}
          // apiResponse.response = {
          //   status: 'failure',
          //   response_code: '1038',
          //   txn_id: 'as',
          //   message: ''
          // }
          if (apiResponse.response.status == 'success') {
            const { customerMobile, beneficiaryId } = apiResponse.response

            const addBeneficaryResp = {
              senderid: customerMobile,
              beneficiaryid: beneficiaryId,
              maBeneficiaryId: reqDataObj.beneficiaryId,
              status: 200,
              respcode: 10000,
              message: bankErrorMsg.responseCode[10000]
            }
            // await bankOnBoardDetails.storeBeneficiaryId(null, addBeneficaryResp)
            return addBeneficaryResp
          } else {
            if (apiResponse.response.status == 'failure') {
              const { response_code, message } = apiResponse.response
              // check bene exists error if any
              if (response_code && response_code == 1038) {
                log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'check bene exists', fields: apiResponse })
                // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'check bene exists', fields: apiResponse })
                const beneData = await this.syncBeneficaryId(reqDataObj, sessionRQ, connection)
                log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'check bene exists api beneData', fields: beneData })
                // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'check bene exists api beneData', fields: beneData })
                if (beneData.status == 200) {
                  log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: ' beneData status 200 ', fields: beneData })
                  // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: ' beneData status 200 ', fields: beneData })
                  return beneData
                }
              }
              const addBeneficaryResp = {
                senderid: customPayload.customerMobile,
                beneficiaryid: '',
                maBeneficiaryId: reqDataObj.beneficiaryId,
                status: 400,
                respcode: 10004,
                message: bankErrorMsg.responseCode[10007] + '::' + response_code + '~' + message
              }
              // await bankOnBoardDetails.storeBeneficiaryId(null, addBeneficaryResp)
              return addBeneficaryResp
            }
            // Status not found in paytm response
            return apiResponse
          }
        } else {
          apiResponse.status = 400
          apiResponse.respcode = 10001
          return apiResponse
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiary', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiarySendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'customPayload', fields: customPayload })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'customPayload', fields: customPayload })

      const validationRes = await this.validateAPIInput('SEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        client: 'pass',
        otpType: 'deletionOtp',
        properties: {}
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('SEND_OTP_DELETE_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_SEND_OTP_DELETE_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"txn_id":"D0KBV10L0GUAR","state":"495c97aa-aa05-4e05-87ef-0c177edebbd8"}
          if (apiResponse.response.status == 'success') {
            const { txn_id, state } = apiResponse.response
            const addSenderResp = {
              status: 200,
              respcode: 10000,
              message: apiResponse.response.status,
              senderid: customPayload.customerMobile,
              transactionId: txn_id,
              state: state
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            return apiResponse
          }
        } else {
          return apiResponse
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneficiarySendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiaryReSendOtp (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'customPayload', fields: customPayload })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'customPayload', fields: customPayload })

      const validationRes = await this.validateAPIInput('RESEND_OTP_DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        client: 'pass',
        otpType: 'deletionOtp',
        properties: {}
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('RESEND_OTP_DELETE_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_RESEND_OTP_DELETE_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"txn_id":"D0KBV10L0GUAR","state":"495c97aa-aa05-4e05-87ef-0c177edebbd8"}
          if (apiResponse.response.status == 'success') {
            const { txn_id, state } = apiResponse.response
            const addSenderResp = {
              status: 200,
              respcode: 10000,
              message: apiResponse.response.status,
              senderid: customPayload.customerMobile,
              transactionId: txn_id,
              state: state
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            return apiResponse
          }
        } else {
          return apiResponse
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBeneficiaryReSendOtp', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async deleteBeneficiary (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBeneficiary', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      const { state } = reqDataObj.bankOTPRsponse || {}

      const customPayload = {
        beneficiaryId: reqDataObj.receiverid,
        otp: reqDataObj.otp,
        state: state,
        customerMobile: reqDataObj.senderid
      }

      const validationRes = await this.validateAPIInput('DELETE_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const payLoadJson = {
        beneficiaryId: customPayload.beneficiaryId,
        client: 'pass',
        otp: customPayload.otp,
        state: customPayload.state,
        customerMobile: customPayload.customerMobile
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('DELETE_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_DELETE_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          if (apiResponse.response.status == 'success') {
            const { customerMobile, message, txn_id } = apiResponse.response
            const delBeneficaryResp = {
              receiverid: customPayload.beneficiaryId,
              maBeneficiaryId: reqDataObj.beneficiaryId,
              status: 200,
              message: bankErrorMsg.responseCode[10000],
              respcode: 10000
            }
            //  await bankOnBoardDetails.deleteBeneficiaryId(null, delBeneficaryResp)
            return delBeneficaryResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else if (apiResponse.response.status == 'unknown') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10035, message: bankErrorMsg.responseCode[10035] + '::: ' + message }
          } else {
            return apiResponse
          }
        } else {
          const message = apiResponse.message || ''
          return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  // Single Transaction
  async initTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'request', fields: reqDataObj })
    const resultTransactionArr = []
    try {
      const customPayload = {
        amount: reqDataObj.amount,
        beneficiaryId: reqDataObj.receiverid,
        customerMobile: reqDataObj.senderid,
        txnReqId: reqDataObj.request_number,
        mode: TRANSFER_MODES[reqDataObj.flag]
      }

      const validationRes = await this.validateAPIInput('INIT_TRANSACTION', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const payLoadJson = {
        amount: customPayload.amount,
        beneficiaryId: customPayload.beneficiaryId,
        channel: 'S2S',
        customerMobile: reqDataObj.senderid,
        transactionType: 'CORPORATE_DOMESTIC_REMITTANCE',
        txnReqId: reqDataObj.request_number,
        mode: TRANSFER_MODES[reqDataObj.flag],
        ifscBased: 'false' // flag to be false as per bank replyed on called by atul
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.post('INIT_TRANSACTION', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_INIT_TRANSACTION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        let TransferResp = {}
        if ('status' in apiResponse.response) {
          // {"status":"failure","message":"Remitter account has a problem. Please reach out to our helpdesk","amount":5000,"customerMobile":"**********","response_code":1108,"txn_id":"TPAYTM82340826","mw_txn_id":"***********","extra_info":{"mode":"imps","totalAmount":"5050.00","beneficiaryName":"Test Beneficiary","commission":"50.00"},"rrn":"************","transactionDate":"Thu Dec 31 15:56:35 IST 2020"}
          if (apiResponse.response.status == 'success') {
            const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
            const { totalAmount, beneficiaryName, commission } = extra_info || {}
            TransferResp = {
              request_number: txn_id,
              amount: amount,
              bank_service_charges: commission,
              bank_gst: 0,
              bank_gross_amount: totalAmount,
              bankrefno: mw_txn_id,
              bankTransactionid: mw_txn_id,
              transactionId: reqDataObj.ma_transfers_id,
              status: 1,
              message: bankErrorMsg.responseCode[10000],
              rrn: rrn,
              benename: beneficiaryName
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(beneficiaryName)) {
              TransferResp.benename = ''
            }

            if (de(message)) {
              TransferResp.message = message
            }
          } else if (apiResponse.response.status == 'pending') {
            const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
            const { totalAmount, beneficiaryName, commission } = extra_info || {}
            TransferResp = {
              request_number: txn_id,
              amount: amount,
              bank_service_charges: commission,
              bank_gst: 0,
              bank_gross_amount: totalAmount,
              bankrefno: mw_txn_id,
              bankTransactionid: mw_txn_id,
              transactionId: reqDataObj.ma_transfers_id,
              status: -1,
              message: bankErrorMsg.responseCode[10013],
              rrn: rrn,
              benename: beneficiaryName
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(beneficiaryName)) {
              TransferResp.benename = ''
            }

            if (de(message)) {
              TransferResp.message = message
            }
          } else if (apiResponse.response.status == 'failure') {
            const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
            const { totalAmount, beneficiaryName, commission } = extra_info || {}
            TransferResp = {
              request_number: txn_id,
              amount: amount,
              bank_service_charges: commission,
              bank_gst: 0,
              bank_gross_amount: totalAmount,
              bankrefno: mw_txn_id,
              bankTransactionid: mw_txn_id,
              transactionId: reqDataObj.ma_transfers_id,
              status: 0,
              message: bankErrorMsg.responseCode[10012],
              rrn: rrn,
              benename: beneficiaryName
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(beneficiaryName)) {
              TransferResp.benename = ''
            }

            if (de(message)) {
              TransferResp.message = message
            }
          } else if (apiResponse.response.status == 'unknown') {
            const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
            const { totalAmount, beneficiaryName, commission } = extra_info || {}
            TransferResp = {
              request_number: txn_id,
              amount: amount,
              bank_service_charges: commission,
              bank_gst: 0,
              bank_gross_amount: totalAmount,
              bankrefno: mw_txn_id,
              bankTransactionid: mw_txn_id,
              transactionId: reqDataObj.ma_transfers_id,
              status: -1,
              message: bankErrorMsg.responseCode[10013],
              rrn: rrn,
              benename: beneficiaryName,
              finalStatus: 0
            }

            if (!de(rrn)) {
              TransferResp.rrn = ''
            }

            if (!de(beneficiaryName)) {
              TransferResp.benename = ''
            }

            if (de(message)) {
              TransferResp.message = message
            }
          } else {
            TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10013]
            }
          }

          TransferResp.request_number = reqDataObj.request_number
          resultTransactionArr.push(TransferResp)
          log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })
          return {
            status: 200,
            respcode: 1000,
            message: errorMsg.responseCode[1000],
            bulkData: {
              finalStatus: 1,
              transferBulkData: resultTransactionArr
            }
          }
        } else {
          // return apiResponse
          TransferResp = {
            status: -1,
            message: bankErrorMsg.responseCode[10013]
          }

          TransferResp.request_number = reqDataObj.request_number

          resultTransactionArr.push(TransferResp)

          return {
            status: 200,
            respcode: 10012,
            message: errorMsg.responseCode[10012],
            bulkData: {
              finalStatus: 1,
              transferBulkData: resultTransactionArr
            }
          }
        }
      } else {
        apiResponse.status = 400
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'initTransaction', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async bulkTransaction (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'request', fields: reqDataObj })
    const resultTransactionArr = []
    // process.exit()
    try {
      if (reqDataObj && ('request_data' in reqDataObj) && Object.keys(reqDataObj.request_data).length > 0) {
        const transferResponsePromiseArr = {}
        const requestData = reqDataObj.request_data

        /**
       * Get/Set Agent session
       */
        const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
        if (currentSessionId.status != 200) {
          return currentSessionId
        }
        /** ** Get/Set Agent session **/

        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'currentSessionId', fields: currentSessionId })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'currentSessionId', fields: currentSessionId })

        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'requestData', fields: requestData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'requestData', fields: requestData })
        /** Validation the request */
        const customPayloadArr = []
        for (const transferDataKey in requestData) {
          const transferData = requestData[transferDataKey]
          const customPayload = {
            amount: transferData.transfer_amount,
            beneficiaryId: reqDataObj.receiverid,
            customerMobile: reqDataObj.senderid,
            txnReqId: transferData.request_number,
            mode: TRANSFER_MODES[reqDataObj.flag]
          }

          const validationRes = await this.validateAPIInput('INIT_TRANSACTION', customPayload)
          if (validationRes.status !== 200) {
            return validationRes
          }

          customPayloadArr.push(customPayload)
        }

        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'customPayloadArr', fields: customPayloadArr })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'customPayloadArr', fields: customPayloadArr })
        for (let index2 = 0; index2 < customPayloadArr.length; index2++) {
          const transferData = customPayloadArr[index2]
          const customPayload = {
            amount: transferData.amount,
            beneficiaryId: transferData.beneficiaryId,
            customerMobile: transferData.customerMobile,
            txnReqId: transferData.txnReqId, // transferData.txnReqId
            mode: transferData.mode
          }

          // Process validation or any parameters modificaiton here

          const payLoadJson = {
            amount: customPayload.amount,
            beneficiaryId: customPayload.beneficiaryId,
            channel: 'S2S',
            customerMobile: customPayload.customerMobile,
            transactionType: 'CORPORATE_DOMESTIC_REMITTANCE',
            txnReqId: customPayload.txnReqId,
            mode: customPayload.mode,
            ifscBased: 'false' // to do what is this flags
          }

          // OP_NAME, PARAMS, { TOKENDATA }
          const apiResponse = PaytmRequest.post('BULK_TRANSFER', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

          transferResponsePromiseArr[transferData.txnReqId] = apiResponse

          try {
            console.log('API_[' + sessionRQ + ']_PAYTM_BULK_TRANSFER_SINGLE_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']')
          } catch (error) {
            console.log(error)
          }
        }

        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: '_responsePromiseArr', fields: transferResponsePromiseArr })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: '_responsePromiseArr', fields: transferResponsePromiseArr })

        // Concurrent request to Single Transfer
        const requestStart = process.hrtime()
        const _responsePromiseArr = await Promise.all(Object.values(transferResponsePromiseArr))
        try {
          const requestEnd = process.hrtime(requestStart)
          const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
          console.log('API_[' + sessionRQ + ']_PAYTM_BULK_TRANSFER_REQUEST_[' + JSON.stringify(customPayloadArr) + ']_RESPONSE_[' + JSON.stringify(_responsePromiseArr) + ']_TIMER_[' + timeInMs + ']ms')
        } catch (error) {
          console.log(error)
        }

        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: '_responsePromiseArr', fields: _responsePromiseArr })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: '_responsePromiseArr', fields: _responsePromiseArr })

        const requestNoKeys = Object.keys(transferResponsePromiseArr)
        const requestDataRes = reqDataObj.request_data
        for (let index3 = 0; index3 < requestNoKeys.length; index3++) {
          const apiResponse = _responsePromiseArr[index3]
          const requestNo = requestNoKeys[index3]
          const transferData = requestDataRes[requestNo]

          if (apiResponse.status == 200) {
            let TransferResp = {}
            if ('status' in apiResponse.response) {
              // {"status":"failure","message":"Remitter account has a problem. Please reach out to our helpdesk","amount":5000,"customerMobile":"**********","response_code":1108,"txn_id":"TPAYTM82340826","mw_txn_id":"***********","extra_info":{"mode":"imps","totalAmount":"5050.00","beneficiaryName":"Test Beneficiary","commission":"50.00"},"rrn":"************","transactionDate":"Thu Dec 31 15:56:35 IST 2020"}
              if (('HttpResponseCode' in apiResponse) && apiResponse.HttpResponseCode == 200) {
                console.log('BULK BLOCK HttpResponseCode 200')
                if (apiResponse.response.status == 'success') {
                  const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
                  const { totalAmount, beneficiaryName, commission } = extra_info || {}
                  TransferResp = {
                    request_number: txn_id,
                    amount: amount,
                    bank_service_charges: commission,
                    bank_gst: 0,
                    bank_gross_amount: totalAmount,
                    bankrefno: mw_txn_id,
                    bankTransactionid: mw_txn_id,
                    transactionId: transferData.ma_transfers_id,
                    status: 1,
                    message: bankErrorMsg.responseCode[10000],
                    rrn: rrn,
                    benename: beneficiaryName,
                    finalStatus: 1
                  }

                  if (!de(rrn)) {
                    TransferResp.rrn = ''
                  }

                  if (!de(beneficiaryName)) {
                    TransferResp.benename = ''
                  }

                  if (de(message)) {
                    TransferResp.message = message
                  }
                } else if (apiResponse.response.status == 'pending') {
                  const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
                  const { totalAmount, beneficiaryName, commission } = extra_info || {}
                  TransferResp = {
                    request_number: txn_id,
                    amount: amount,
                    bank_service_charges: commission,
                    bank_gst: 0,
                    bank_gross_amount: totalAmount,
                    bankrefno: mw_txn_id,
                    bankTransactionid: mw_txn_id,
                    transactionId: transferData.ma_transfers_id,
                    status: -1,
                    message: bankErrorMsg.responseCode[10013],
                    rrn: rrn,
                    benename: beneficiaryName,
                    finalStatus: 1
                  }

                  if (!de(rrn)) {
                    TransferResp.rrn = ''
                  }

                  if (!de(beneficiaryName)) {
                    TransferResp.benename = ''
                  }
                } else if (apiResponse.response.status == 'failure') {
                  const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
                  const { totalAmount, beneficiaryName, commission } = extra_info || {}
                  TransferResp = {
                    request_number: txn_id,
                    amount: amount,
                    bank_service_charges: commission,
                    bank_gst: 0,
                    bank_gross_amount: totalAmount,
                    bankrefno: mw_txn_id,
                    bankTransactionid: mw_txn_id,
                    transactionId: transferData.ma_transfers_id,
                    status: 0,
                    message: bankErrorMsg.responseCode[10012],
                    rrn: rrn,
                    benename: beneficiaryName,
                    finalStatus: 1
                  }

                  if (!de(rrn)) {
                    TransferResp.rrn = ''
                  }

                  if (!de(beneficiaryName)) {
                    TransferResp.benename = ''
                  }

                  if (de(message)) {
                    TransferResp.message = message
                  }
                } else if (apiResponse.response.status == 'unknown') {
                  const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
                  const { totalAmount, beneficiaryName, commission } = extra_info || {}
                  TransferResp = {
                    request_number: txn_id,
                    amount: amount,
                    bank_service_charges: commission,
                    bank_gst: 0,
                    bank_gross_amount: totalAmount,
                    bankrefno: mw_txn_id,
                    bankTransactionid: mw_txn_id,
                    transactionId: transferData.ma_transfers_id,
                    status: -1,
                    message: bankErrorMsg.responseCode[10013],
                    rrn: rrn,
                    benename: beneficiaryName,
                    finalStatus: 0
                  }

                  if (!de(rrn)) {
                    TransferResp.rrn = ''
                  }

                  if (!de(beneficiaryName)) {
                    TransferResp.benename = ''
                  }
                } else {
                  TransferResp = {
                    status: -1,
                    message: bankErrorMsg.responseCode[10013],
                    finalStatus: 0
                  }
                }
              } else {
                log.logger({ msg: 'BULK BLOCK HttpResponseCode NOT 200', apiResponse })
                TransferResp = {
                  status: -1,
                  message: bankErrorMsg.responseCode[10013],
                  finalStatus: 0
                }
              }

              TransferResp.request_number = requestNo
              resultTransactionArr.push(TransferResp)
              log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })
            } else {
              console.log('BULK BLOCK STATUS KEY NOT FOUND 200')
              // return apiResponse
              TransferResp = {
                status: -1,
                message: bankErrorMsg.responseCode[10013],
                finalStatus: 0
              }

              TransferResp.request_number = requestNo

              resultTransactionArr.push(TransferResp)
            }
          } else {
            console.log('BULK BLOCK STATUS NOT 200')
            let TransferResp = {}

            if (('HttpResponseCode' in apiResponse) && apiResponse.HttpResponseCode == 400) {
              console.log('BULK BLOCK HttpResponseCode  IS 400')
              if (apiResponse.response && 'status' in apiResponse.response) {
                // {"status":"failure","message":"Remitter account has a problem. Please reach out to our helpdesk","amount":5000,"customerMobile":"**********","response_code":1108,"txn_id":"TPAYTM82340826","mw_txn_id":"***********","extra_info":{"mode":"imps","totalAmount":"5050.00","beneficiaryName":"Test Beneficiary","commission":"50.00"},"rrn":"************","transactionDate":"Thu Dec 31 15:56:35 IST 2020"}
                if (apiResponse.response.status == 'failure') {
                  const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
                  const { totalAmount, beneficiaryName, commission } = extra_info || {}
                  TransferResp = {
                    request_number: txn_id,
                    amount: amount,
                    bank_service_charges: commission,
                    bank_gst: 0,
                    bank_gross_amount: totalAmount,
                    bankrefno: mw_txn_id,
                    bankTransactionid: mw_txn_id,
                    transactionId: reqDataObj.ma_transfers_id,
                    status: 0,
                    message: bankErrorMsg.responseCode[10012],
                    rrn: rrn,
                    benename: beneficiaryName,
                    finalStatus: 1
                  }

                  if (!de(rrn)) {
                    TransferResp.rrn = ''
                  }

                  if (!de(beneficiaryName)) {
                    TransferResp.benename = ''
                  }

                  if (de(message)) {
                    TransferResp.message += message
                  }
                } else if (apiResponse.response.status == 'pending') {
                  const { txn_id, amount, mw_txn_id, rrn, extra_info, message } = apiResponse.response
                  const { totalAmount, beneficiaryName, commission } = extra_info || {}
                  TransferResp = {
                    request_number: txn_id,
                    amount: amount,
                    bank_service_charges: commission,
                    bank_gst: 0,
                    bank_gross_amount: totalAmount,
                    bankrefno: mw_txn_id,
                    bankTransactionid: mw_txn_id,
                    transactionId: reqDataObj.ma_transfers_id,
                    status: -1,
                    message: bankErrorMsg.responseCode[10013],
                    rrn: rrn,
                    benename: beneficiaryName,
                    finalStatus: 1
                  }

                  if (!de(rrn)) {
                    TransferResp.rrn = ''
                  }

                  if (!de(beneficiaryName)) {
                    TransferResp.benename = ''
                  }
                } else {
                  TransferResp = {
                    status: -1,
                    message: bankErrorMsg.responseCode[10013],
                    finalStatus: 0
                  }
                }
              } else {
                // return apiResponse
                TransferResp = {
                  status: -1,
                  message: bankErrorMsg.responseCode[10013],
                  finalStatus: 0
                }
              }
            } else {
              console.log('BULK BLOCK HttpResponseCode  NOT 400')
              TransferResp = {
                status: -1,
                message: bankErrorMsg.responseCode[10013],
                finalStatus: 0
              }

              if (propExist('message', apiResponse)) {
                TransferResp.message = apiResponse.message
              }
            }

            TransferResp.request_number = requestNo

            resultTransactionArr.push(TransferResp)
          }
        }

        log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'resultTransactionArr', fields: resultTransactionArr })

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          bulkData: {
            finalStatus: 1,
            transferBulkData: resultTransactionArr
          }
        }
      } else {
        return { status: 400, respcode: 10034, message: bankErrorMsg.responseCode[10034], transaction_status: 0 }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'bulkTransaction', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async doubleVerify (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'request', fields: reqDataObj })

    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      const customPayload = {
        txnReqId: reqDataObj.bcpartnerrefno
      }

      const validationRes = await this.validateAPIInput('DOUBLE_VERIFICATION', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const payLoadJson = {
        transactionType: 'CORPORATE_DOMESTIC_REMITTANCE',
        txnReqId: customPayload.txnReqId
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.get('DOUBLE_VERIFICATION', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_DOUBLE_VERIFICATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'apiResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.status == 200) {
        // {"status":"pending","mode":"imps","response_code":0,"txn_id":"TPAYTM69644502","mw_txn_id":"***********","rrn":"************"}
        if ('status' in apiResponse.response) {
          if (apiResponse.response.status == 'success' && apiResponse.HttpResponseCode == 200) {
            const { status, rrn, mw_txn_id, txn_id } = apiResponse.response
            // const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responseDoubleVerify.data.transactionrequeryres

            const TransferResp = {
              bcpartnerrefno: txn_id,
              amount: 0,
              bank_service_charges: 0,
              bank_gst: 0,
              bank_gross_amount: 0,
              bankrefno: mw_txn_id || '',
              benename: '',
              bankTransactionid: mw_txn_id || '',
              transactionId: reqDataObj.ma_transfers_id,
              status: 1,
              message: 'Success',
              refund: false,
              rrn: rrn || '',
              remarks: status || ''
            }

            return TransferResp
          } else if (apiResponse.response.status == 'failure' && apiResponse.HttpResponseCode == 200) {
            const { status, rrn, mw_txn_id, txn_id } = apiResponse.response
            // const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responseDoubleVerify.data.transactionrequeryres

            const TransferResp = {
              bcpartnerrefno: txn_id,
              amount: 0,
              bank_service_charges: 0,
              bank_gst: 0,
              bank_gross_amount: 0,
              bankrefno: mw_txn_id || '',
              benename: '',
              bankTransactionid: mw_txn_id || '',
              transactionId: reqDataObj.ma_transfers_id,
              status: 0,
              message: 'Failure',
              refund: false,
              rrn: rrn || '',
              remarks: status || '',
              autorefundcredit: true
            }

            return TransferResp
          } else {
            const TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10025]
            }

            return TransferResp
          }
        } else {
          const TransferResp = {
            status: -1,
            message: bankErrorMsg.responseCode[10025]
          }

          return TransferResp
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'doubleVerify', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async refund (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'refund', type: 'request', fields: arguments })
    try {
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    } catch (error) {
      console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'refund', type: 'error', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'refund', type: 'error', fields: error })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async verifyOTPRefund (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'request', fields: arguments })
    try {
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'error', fields: JSON.stringify(error) })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'verifyOTPRefund', type: 'error', fields: JSON.stringify(error) })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  /**
   * Not in used currently
   */
  async preValidateRemitter (reqDataObj, sessionRQ, connection) { // to do dmtRequestNo, Connection to be passs
    log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'request', fields: reqDataObj })
    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      // validation format
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('PRE_VALIDATE', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.get('PRE_VALIDATE', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_PRE_VALIDATE_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status == 200) {
        if ('status' in apiResponse.response) {
          // {"status":"success","response_code":0,"firstName":"Sanjays","lastName":"Sinalkars","customerMobile":"**********","limitLeft":24800.00}
          if (apiResponse.response.status == 'success') {
            const { response_code, firstName, lastName, customerMobile, limitLeft } = apiResponse.response

            let addSenderResp = {}
            // Customer already register at paytm end
            if (response_code == 0) {
              addSenderResp = {
                status: 200,
                respcode: 10000,
                message: apiResponse.response.status,
                senderid: customerMobile || customPayload.customerMobile,
                firstName: firstName,
                lastName: lastName,
                remitter_name: firstName + ' ' + lastName,
                limit: limitLeft
              }
            } else {
              addSenderResp = {
                status: 400,
                respcode: 10000,
                message: apiResponse.response.status,
                senderid: customerMobile || customPayload.customerMobile,
                firstName: firstName,
                lastName: lastName,
                remitter_name: firstName + ' ' + lastName,
                limit: limitLeft
              }
            }

            return addSenderResp
          } else if (apiResponse.response.status == 'failure') {
            const message = apiResponse.response.message || ''
            return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
          } else {
            apiResponse.status = 400
            return apiResponse
          }
        } else {
          apiResponse.status = 400
          return apiResponse
        }
      } else {
        apiResponse.status = 400
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'preValidateRemitter', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  /**
   * To do
   */
  async syncRemitterId (reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'request', fields: arguments })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'request', fields: arguments })
    try {
      //
      const remitterDetails = await this.getRemitterDetails(reqDataObj)
      return remitterDetails
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'error', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'syncRemitterId', type: 'error', fields: error })
    }
  }

  async syncBeneficaryId (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        customerMobile: reqDataObj.senderid,
        accountNumber: reqDataObj.accountnumber
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const validationRes = await this.validateAPIInput('VIEW_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        accountNumber: customPayload.accountNumber,
        customerMobile: customPayload.customerMobile,
        limit: 1,
        offset: 0
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      try {
        // OP_NAME, PARAMS, { TOKENDATA }
        const requestStart = process.hrtime()
        const apiResponse = await PaytmRequest.get('VIEW_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

        try {
          const requestEnd = process.hrtime(requestStart)
          const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
          console.log('API_[' + sessionRQ + ']_PAYTM_VIEW_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
        } catch (error) {
          console.log(error)
        }

        if (apiResponse.status == 200) {
          if ('status' in apiResponse.response) {
            // {"status":"success","response_code":0,"customerMobile":"**********","limit":25000.00}
            if (apiResponse.response.status == 'success' && apiResponse.response.response_code == 0) {
              const { customerMobile, beneficiaries, totalCount } = apiResponse.response
              if (beneficiaries && Array.isArray(beneficiaries) && beneficiaries.length > 0) {
                for (let index = 0; index < beneficiaries.length; index++) {
                  const currentBene = beneficiaries[index]
                  if (currentBene && ('beneficiaryId' in currentBene) && ('accountDetail' in currentBene)) {
                    if (currentBene.accountDetail && ('accountNumber' in currentBene.accountDetail) && ('ifscCode' in currentBene.accountDetail) && reqDataObj.ifscode == currentBene.accountDetail.ifscCode) {
                      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'currentBene', fields: currentBene })

                      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'currentBene', fields: currentBene })

                      const { beneficiaryId, accountDetail } = currentBene
                      // const { accountNumber, ifscCode, bankName, accountHolderName } = accountDetail || {}
                      const addBeneficaryResp = {
                        senderid: customerMobile,
                        beneficiaryid: beneficiaryId,
                        maBeneficiaryId: reqDataObj.beneficiaryId,
                        status: 200,
                        respcode: 10000,
                        message: bankErrorMsg.responseCode[10000]
                      }
                      // await bankOnBoardDetails.storeBeneficiaryId(null, addBeneficaryResp)
                      return addBeneficaryResp
                    }
                  }
                }
              }
              return { status: 400, respcode: 10007, message: bankErrorMsg.responseCode[10007] + '::: ' + 'Bene not found !' }
            } else if (apiResponse.response.status == 'failure') {
              const message = apiResponse.response.message || ''
              return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
            }
            apiResponse.status = 400
            return apiResponse
          } else {
            // Status not found in paytm response
            apiResponse.status = 400
            return apiResponse
          }
        } else {
          apiResponse.status = 400
          return apiResponse
        }
      } catch (error) {
        log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'catcherror', fields: error })
        // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'catcherror', fields: error })
        return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] + ':' + error.message }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'syncBeneficaryId', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getSessionId (maUserId, maBankOnBoardingId, prevConn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'request', fields: { maUserId, maBankOnBoardingId } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'request', fields: { maUserId, maBankOnBoardingId } })
    try {
      const sessionData = await bankOnBoardDetails.getSessionDetails(null, { ma_user_id: maUserId, ma_bank_on_boarding_id: maBankOnBoardingId }, prevConn)
      log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'response', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'response', fields: sessionData })
      if (sessionData.status === 200 && Object.keys(sessionData.data).length > 0) {
        const tmpSessionData = sessionData.data[0]
        const locationidStr = tmpSessionData.locationid
        if (locationidStr === undefined || locationidStr === null || locationidStr === '' || locationidStr.indexOf('::') === -1) {
          return { status: 400, respcode: 10027, message: bankErrorMsg.responseCode[10027] }
        }

        const tokenData = { status: 200, respcode: 10000, data: { tokenData: tmpSessionData } }
        log.logger({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'accessTokenData', fields: { accessTokenData: 'accessTokenData', tokenData } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getSessionId', type: 'accessTokenData', fields: { accessTokenData: 'accessTokenData', tokenData } })
        return tokenData
      } else {
        return { status: 400, respcode: 10003, message: bankErrorMsg.responseCode[10003] }
      }
    } catch (error) {
      console.log(error)
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001], fields: error }
    }
  }

  async validateBeneficiaryBeforeAdd (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        accountNumber: reqDataObj.accountnumber,
        bankName: reqDataObj.bankname,
        benIfsc: reqDataObj.ifsccode,
        customerMobile: reqDataObj.senderid,
        txnReqId: reqDataObj.request_number
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const validationRes = await this.validateAPIInput('VALIDATE_BENEFICIARY_BEFORE_ADD', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        beneficiaryDetails: {
          accountNumber: customPayload.accountNumber,
          bankName: customPayload.bankName,
          benIfsc: customPayload.benIfsc
        },
        channel: 'S2S',
        customerMobile: customPayload.customerMobile,
        transactionType: 'CORPORATE_PENNY_DROP',
        txnReqId: customPayload.txnReqId
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      try {
        // OP_NAME, PARAMS, { TOKENDATA }
        const requestStart = process.hrtime()
        const apiResponse = await PaytmRequest.post('VALIDATE_BENEFICIARY_BEFORE_ADD', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

        try {
          const requestEnd = process.hrtime(requestStart)
          const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
          console.log('API_[' + sessionRQ + ']_PAYTM_VALIDATE_BENEFICIARY_BEFORE_ADD_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
        } catch (error) {
          console.log(error)
        }

        try {
          console.log('AAPI_[' + sessionRQ + ']_PAYTM_VALIDATE_BENEFICIARY_BEFORE_ADD_REQUEST_[' + reqDataObj.bankname + ']_[' + reqDataObj.bankifsc + ']_[' + JSON.stringify(payLoadJson) + ']_[' + JSON.stringify(apiResponse) + ']')
        } catch (e) {
          console.log('consoleerror >>', e)
        }

        if (apiResponse.status == 200) {
          if ('status' in apiResponse.response) {
            let TransferResp = {}

            // {"status":"success","message":"Transfer Successful","amount":4.0,"customerMobile":"**********","response_code":0,"txn_id":"TBEN67272917","mw_txn_id":"***********","extra_info":{"beneficiaryName":null},"rrn":"************","transactionDate":"Thu Dec 31 13:15:25 IST 2020"}
            if (apiResponse.response.status == 'success' && apiResponse.response.response_code == 0) {
              const { txn_id, mw_txn_id, amount, extra_info, rrn, message } = apiResponse.response
              const { beneficiaryName } = extra_info || {}
              TransferResp = {
                request_number: txn_id,
                bankTransactionid: mw_txn_id,
                amount: amount,
                bank_service_charges: 0,
                bank_gross_amount: 0,
                benename: beneficiaryName,
                status: 1,
                kycstatus: 0,
                message: bankErrorMsg.responseCode[10000],
                rrn: rrn,
                apiFailed: false,
                bank_response: apiResponse.response
              }

              if (!de(rrn)) {
                TransferResp.rrn = ''
              }

              if (!de(beneficiaryName)) {
                TransferResp.benename = ''
              }

              if (de(message)) {
                TransferResp.message = message
              }
            } else if (apiResponse.response.status == 'failure' || apiResponse.response.status == 'duplicate') {
              // {"status":"failure","message":"Remitter account has a problem. Please reach out to our helpdesk","amount":4,"customerMobile":"**********","response_code":1108,"txn_id":"TBEN26672559","mw_txn_id":"***********","extra_info":{"beneficiaryName":null},"rrn":"************","transactionDate":"Wed Dec 30 17:58:32 IST 2020"}
              const { txn_id, mw_txn_id, amount, extra_info, rrn, message } = apiResponse.response
              const { beneficiaryName } = extra_info || {}
              TransferResp = {
                request_number: txn_id,
                bankTransactionid: mw_txn_id,
                amount: amount,
                bank_service_charges: 0,
                bank_gross_amount: 0,
                benename: beneficiaryName,
                status: 0, /// testing purpose later need to remove
                kycstatus: 0,
                message: bankErrorMsg.responseCode[10029],
                rrn: rrn,
                apiFailed: true,
                bank_response: apiResponse.response
              }

              if (!de(rrn)) {
                TransferResp.rrn = ''
              }

              if (!de(beneficiaryName)) {
                TransferResp.benename = ''
              }

              if (de(message)) {
                TransferResp.message += '~' + message
              }

              TransferResp.apiFailed = true
            } else {
              TransferResp = {
                status: -1,
                message: 'Pending or Unknown',
                apiFailed: false,
                bank_response: apiResponse.response
              }
            }

            return TransferResp
          } else {
            // Status not found in paytm response
            return apiResponse
          }
        } else {
          const TransferResp = {
            status: 0,
            message: bankErrorMsg.responseCode[10029] + '~Internal Error~',
            apiFailed: true,
            bank_response: apiResponse.response
          }
          if (apiResponse.HttpResponseCode == 400 || apiResponse.HttpResponseCode == 401) {
            TransferResp.status = 0
            TransferResp.message = bankErrorMsg.responseCode[10029] + '~failure~'
          } else if (apiResponse.HttpResponseCode == 500) {
            TransferResp.status = -1
            TransferResp.apiFailed = false
            TransferResp.message = bankErrorMsg.responseCode[10035] + '~Internal Server Error~'
          }
          return TransferResp
        }
      } catch (error) {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'catcherror', fields: error })
        // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'catcherror', fields: error })
        return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] + ':' + error.message }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async validateAPIInput (actionType, reqDataObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'validateAPIInput', type: 'request', fields: { validateAPIInput: 'validateAPIInput', actionType, reqDataObj } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'validateAPIInput', type: 'request', fields: { validateAPIInput: 'validateAPIInput', actionType, reqDataObj } })
    const defaultResponse = {
      status: 200,
      respcode: 1001,
      message: 'API valid input'
    }
    if (typeof (actionType) !== 'undefined' && typeof (reqDataObj) !== 'undefined') {
      if (paytmrules !== null && Object.keys(paytmrules).length > 0 && (actionType in paytmrules)) {
        const currentRules = paytmrules[actionType]
        /**
         *  Loop through fields
         */
        for (const [field, fieldRules] of Object.entries(currentRules)) {
          // console.log('currentRules fields', field, fieldRules)
          /**
           * Check Rule field exist in reqDataObj if exist then validate
           */
          if (field in reqDataObj) {
            /**
           * Loop throgh field Rule
           */
            // console.log('fieldRules fields', Object.entries(fieldRules))
            for (const rulename in fieldRules) {
              const rulevalue = fieldRules[rulename]
              // for (var (rulename, rulevalue) in fieldRules ) {
              //  console.log('currentRules fields', rulename, rulevalue)
              const isCurrentFieldValid = apiValidator.validInput(rulename, reqDataObj[field], rulevalue)
              // log.logger(field, rulename, reqDataObj[field], rulevalue, isCurrentFieldValid)
              if (isCurrentFieldValid === false) {
                const invValidMsg = `::: ${field} invalid value ${reqDataObj[field]} for rule ${rulename} `
                defaultResponse.status = 400
                defaultResponse.respcode = 10011
                defaultResponse.message = bankErrorMsg.responseCode[10011] + invValidMsg
                break
              }
            }
          }
        }
      }
    }
    return defaultResponse
  }

  async getRemitterAvailableBalance (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getAvailableBalance', type: 'request', fields: reqDataObj })
    try {
      const customPayload = {
        customerMobile: reqDataObj.sendermobilenumber
      }

      /**
       * Validing Input Parameters as per Rules before sending request
       */
      const validationRes = await this.validateAPIInput('GET_REMITTER_BALANCE', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      try {
        // OP_NAME, PARAMS, { TOKENDATA }
        const requestStart = process.hrtime()
        const apiResponse = await PaytmRequest.get('GET_REMITTER_BALANCE', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

        try {
          const requestEnd = process.hrtime(requestStart)
          const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
          console.log('API_[' + sessionRQ + ']_PAYTM_GET_REMITTER_BALANCE_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
        } catch (error) {
          console.log(error)
        }

        if (apiResponse.status == 200) {
          if ('status' in apiResponse.response) {
            // {"status":"success","response_code":0,"customerMobile":"**********","limit":25000.00}
            if (apiResponse.response.status == 'success') {
              const { limit } = apiResponse.response
              const paytmLimit = BANK_LIMIT
              const addSenderResp = {
                remitterDetail: true,
                consumedLimit: paytmLimit - limit,
                remainingLimit: limit,
                status: 200,
                message: 'Success',
                senderid: customPayload.customerMobile
              }
              // console.log('addSenderResp:::', JSON.stringify(addSenderResp))
              return addSenderResp
            } else if (apiResponse.response.status == 'failure') {
              const message = apiResponse.response.message || ''
              return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
            }
            apiResponse.status = 400
            return apiResponse
          } else {
            // Status not found in paytm response
            apiResponse.status = 400
            return apiResponse
          }
        } else {
          apiResponse.status = 400
          return apiResponse
        }
      } catch (error) {
        log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'catcherror', fields: error })
        // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'catcherror', fields: error })
        return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] + ':' + error.message }
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'validateBeneficiaryBeforeAdd', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async doubleVerifyBene (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'request', fields: reqDataObj })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'request', fields: reqDataObj })

    try {
      /**
       * Validing Input Parameters as per Rules before sending request
       */

      const customPayload = {
        txnReqId: reqDataObj.bcpartnerrefno
      }

      const validationRes = await this.validateAPIInput('DOUBLE_VERIFICATION', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      const payLoadJson = {
        transactionType: 'CORPORATE_PENNY_DROP',
        txnReqId: customPayload.txnReqId
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }
      /** ** Get/Set Agent session **/

      // OP_NAME, PARAMS, { TOKENDATA }
      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.get('DOUBLE_VERIFICATION', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_DOUBLE_VERIFICATION_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'apiResponse', fields: apiResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.status == 200) {
        // {"status":"pending","mode":"imps","response_code":0,"txn_id":"TPAYTM69644502","mw_txn_id":"***********","rrn":"************"}
        if ('status' in apiResponse.response) {
          if (apiResponse.response.status == 'success' && apiResponse.HttpResponseCode == 200) {
            const { status, rrn, mw_txn_id, txn_id } = apiResponse.response
            // const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responsedoubleVerifyBene.data.transactionrequeryres

            const TransferResp = {
              bcpartnerrefno: txn_id,
              amount: 0,
              bank_service_charges: 0,
              bank_gst: 0,
              bank_gross_amount: 0,
              bankrefno: mw_txn_id || '',
              benename: '',
              bankTransactionid: mw_txn_id || '',
              transactionId: reqDataObj.ma_transfers_id,
              status: 1,
              message: 'Success',
              refund: false,
              rrn: rrn || '',
              remarks: status || ''
            }

            return TransferResp
          } else if (apiResponse.response.status == 'failure' && apiResponse.HttpResponseCode == 200) {
            const { status, rrn, mw_txn_id, txn_id } = apiResponse.response
            // const { bcpartnerrefno, nsdltransactionid, grossamount, amount, servicecharge, gst, bankrefno, benename, rrn, remarks } = responsedoubleVerifyBene.data.transactionrequeryres

            const TransferResp = {
              bcpartnerrefno: txn_id,
              amount: 0,
              bank_service_charges: 0,
              bank_gst: 0,
              bank_gross_amount: 0,
              bankrefno: mw_txn_id || '',
              benename: '',
              bankTransactionid: mw_txn_id || '',
              transactionId: reqDataObj.ma_transfers_id,
              status: 0,
              message: 'Failure',
              refund: false,
              rrn: rrn || '',
              remarks: status || '',
              autorefundcredit: true
            }

            return TransferResp
          } else {
            const TransferResp = {
              status: -1,
              message: bankErrorMsg.responseCode[10025]
            }

            return TransferResp
          }
        } else {
          const TransferResp = {
            status: -1,
            message: bankErrorMsg.responseCode[10025]
          }

          return TransferResp
        }
      } else {
        return apiResponse
      }
    } catch (error) {
      // console.log(error)
      log.logger({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'doubleVerifyBene', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async getBeneficiaryList (reqDataObj, sessionRQ, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'request', fields: reqDataObj || '' })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'request', fields: reqDataObj || '' })
    try {
      const customPayload = { customerMobile: reqDataObj.sendermobilenumber }

      const validationRes = await this.validateAPIInput('VIEW_BENEFICIARY', customPayload)
      if (validationRes.status !== 200) {
        return validationRes
      }

      // final payload format
      const payLoadJson = {
        customerMobile: customPayload.customerMobile,
        limit: 20,
        offset: 0
      }

      /**
       * Get/Set Agent session
       */
      const currentSessionId = await this.getSessionId(reqDataObj.ma_user_id, reqDataObj.ma_bank_on_boarding_id, connection)
      if (currentSessionId.status != 200) {
        return currentSessionId
      }

      const requestStart = process.hrtime()
      const apiResponse = await PaytmRequest.get('VIEW_BENEFICIARY', payLoadJson, { TOKENDATA: currentSessionId.data.tokenData })

      try {
        const requestEnd = process.hrtime(requestStart)
        const timeInMs = (requestEnd[0] * ********** + requestEnd[1]) / 1000000
        console.log('API_[' + sessionRQ + ']_PAYTM_VIEW_BENEFICIARY_REQUEST_[' + JSON.stringify(payLoadJson) + ']_RESPONSE_[' + JSON.stringify(apiResponse) + ']_TIMER_[' + timeInMs + ']ms')
      } catch (error) {
        console.log(error)
      }

      if (apiResponse.status != 200) {
        return apiResponse
      }

      if (!('status' in apiResponse.response)) {
        apiResponse.status = 400
        return apiResponse
      }

      if (apiResponse.response.status == 'failure') {
        const message = apiResponse.response.message || ''
        return { status: 400, respcode: 10005, message: bankErrorMsg.responseCode[10005] + '::: ' + message }
      }

      if (!apiResponse.response.beneficiaries) {
        return apiResponse
      }

      const beneList = []
      const beneListLength = apiResponse.response.beneficiaries.length

      for (let index = 0; index < beneListLength; index++) {
        /* bene name blank case handle while sync bene */
        // if Bene name is blank.. still we will show the bene details
        if (
          apiResponse.response.beneficiaries[index].accountDetail.accountNumber != '' &&
          apiResponse.response.beneficiaries[index].accountDetail.ifscCode != ''
          // && apiResponse.response.beneficiaries[index].accountDetail.accountHolderName != ''
        ) {
          beneList.push({
            accountNumber: apiResponse.response.beneficiaries[index].accountDetail.accountNumber,
            ifscCode: apiResponse.response.beneficiaries[index].accountDetail.ifscCode,
            beneficiaryMobileNumber: 0,
            beneficiaryName: apiResponse.response.beneficiaries[index].accountDetail.accountHolderName,
            bankName: apiResponse.response.beneficiaries[index].accountDetail.bankName,
            beneficiaryId: apiResponse.response.beneficiaries[index].beneficiaryId
          })
        }
      }

      return {
        status: 200,
        respcode: 1000,
        beneList: beneList
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getBeneficiaryList', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10001] }
    }
  }

  async remitterNameValidation (reqDataObj, sessionRQ, connection) {
    try {
      /* request changes : workdround for call airtel api */
      reqDataObj.ma_bank_on_boarding_id = 10
      const result = await bankInstance.remitterNameValidation(reqDataObj, sessionRQ, connection)

      if (result.status != 200) {
        return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10036] }
      }

      /* IF NAME NOT VALID THEN THROW BANK SIDE ERROR */
      if (result.description != undefined || result.code != undefined) {
        return result
      }

      /* IF API ERROR THEN DON'T SHOW BANK ERROR */
      if (result.status != 200) {
        return {
          status: 200,
          respcode: 1000,
          message: bankErrorMsg.responseCode[10000]
        }
      }

      return result
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'error', fields: { error: error } })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'remitterNameValidation', type: 'error', fields: { error: error } })
      return { status: 400, respcode: 10001, message: bankErrorMsg.responseCode[10036] }
    }
  }
}

module.exports = {
  BANK: PAYTMBank
}
