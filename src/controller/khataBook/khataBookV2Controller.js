const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const validator = require('../../util/validator')
const transactionController = require('../transaction/transactionController')
const axios = require('axios')
const util = require('../../util/util')
const qs = require('qs')
const sms = require('../../util/sms')
const mailer = require('../../util/sendEmails')
const moment = require('moment')
const { generateOrderchecksum } = require('../../util/checksum')
// const {lokiLogger} = require('../../util/lokiLogger');

class KhataBook extends DAO {
  get TABLE_NAME () {
    return 'ma_kb_account_master'
  }

  get PRIMARY_KEY () {
    return 'ma_kb_account_master_id'
  }

  static async addKhata (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addKhata', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.business_name.length < 3 || fields.business_name.length > 25) {
        return { status: 400, respcode: 1146, message: errorMsg.responseCode[1146], action_code: 1001 }
      }
      var sqlKhataExist = `SELECT * from ma_kb_account_master WHERE ma_user_id  = '${fields.ma_user_id}' AND business_name = '${fields.business_name}' AND account_status = 'active'`
      const KhataExistresult = await this.rawQuery(sqlKhataExist, connection)
      if (KhataExistresult.length > 0) {
        return { status: 400, respcode: 1142, message: errorMsg.responseCode[1142], action_code: 1001 }
      }
      const userSql = `SELECT mobile_id, CONCAT(firstname,' ',lastname) AS name, sales_id FROM ma_user_master WHERE userid = ${fields.userid} LIMIT 1`
      const userData = await this.rawQuery(userSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'addKhata', type: 'response-userData', fields: userData })
      if (userData.length > 0) {
        fields.name = userData[0].name
        fields.mobile = userData[0].mobile_id
        fields.sales_id = userData[0].sales_id
        fields.account_status = 'active'
        if (userData[0].sales_id != undefined && userData[0].sales_id != null && userData[0].sales_id > 0) {
          const hierarchySql = `select sales_hierarchy_details_id from sales_hierarchy_details where TSM_id=${userData[0].sales_id} and record_status='active'`
          const hierarchyDetails = await this.rawQuery(hierarchySql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'addKhata', type: 'response-hierarchyDetails', fields: hierarchyDetails })
          if (hierarchyDetails.length > 0) {
            fields.hierarchy_id = hierarchyDetails[0].sales_hierarchy_details_id
          }
        }
        this.TABLE_NAME = 'ma_kb_account_master'
        const _result = await this.insert(connection, {
          data: fields
        })
        if (_result.affectedRows > 0) {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], ma_kb_account_master_id: _result.insertId, action_code: 1000 }
        } else {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      } else {
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003], action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addKhata', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async editKhata (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'editKhata', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.business_name.length < 3 || fields.business_name.length > 20) {
        return { status: 400, respcode: 1146, message: errorMsg.responseCode[1146], action_code: 1001 }
      }
      var sqlKhataExist = `SELECT * from ma_kb_account_master WHERE ma_user_id  = '${fields.ma_user_id}' AND business_name = '${fields.business_name}' AND account_status = 'active'`
      const KhataExistresult = await this.rawQuery(sqlKhataExist, connection)
      if (KhataExistresult.length > 0) {
        return { status: 400, respcode: 1142, message: errorMsg.responseCode[1142], action_code: 1001 }
      }
      const updateData = {
        business_name: fields.business_name
      }
      this.TABLE_NAME = 'ma_kb_account_master'
      const _result = await this.updateWhere(connection, {
        id: fields.ma_kb_account_master_id,
        where: 'ma_kb_account_master_id',
        data: updateData
      })
      if (_result.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'editKhata', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async deleteKhata (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteKhata', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const updateData = {
        account_status: 'inactive'
      }
      this.TABLE_NAME = 'ma_kb_account_master'
      const _result = await this.updateWhere(connection, {
        id: fields.ma_kb_account_master_id,
        where: 'ma_kb_account_master_id',
        data: updateData
      })
      if (_result.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteKhata', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  /**
   * Get khata accounts list
   * @param {fields} _
   * @param {*} fields
   * @returns khatadata
   */
  static async getKhataList (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.limit > 1000) {
        return { status: 400, message: errorMsg.responseCode[1138], respcode: 1138, action_code: 1001 }
      }
      var sqlKhata = `SELECT  SQL_CALC_FOUND_ROWS * from ma_kb_account_master WHERE ma_user_id  = '${fields.ma_user_id}' AND account_status = 'active'`
      if (fields.filtertype == 'USERWISE') {
        sqlKhata += ` AND userid = '${fields.userid}'`
      }
      if (typeof fields.business_name !== 'undefined' && fields.business_name != null) {
        sqlKhata += ` AND business_name like '%${fields.business_name}%'`
      }
      sqlKhata += `  ORDER BY addedon desc LIMIT ${fields.offset},${fields.limit}`
      log.logger({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'khatalist-query', fields: sqlKhata })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'khatalist-query', fields: sqlKhata })
      const result = await this.rawQuery(sqlKhata, connection)
      console.log('resultInner =>', result)
      let nextFlag = false
      if (result.length > 0) {
        const countSql = 'SELECT FOUND_ROWS() AS total'
        const countResult = await this.rawQuery(countSql, connection)
        if (countResult.length > 0) {
          const total = countResult[0].total
          if ((fields.limit + fields.offset) < total) {
            nextFlag = true
          }
        }
      } else {
        // Returning users company name as default account
        const userSql = `SELECT mobile_id, company, CONCAT(firstname,' ',lastname) AS name, sales_id FROM ma_user_master WHERE userid = ${fields.userid} LIMIT 1`
        const userData = await this.rawQuery(userSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'addKhata', type: 'response-userData', fields: userData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addKhata', type: 'response-userData', fields: userData })
        if (userData.length > 0) {
          fields.name = userData[0].name
          fields.mobile = userData[0].mobile_id
          fields.sales_id = userData[0].sales_id
          fields.business_name = userData[0].company
          fields.account_status = 'active'
          fields.addedon = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')
          delete fields.limit
          delete fields.offset
          delete fields.filtertype
          if (userData[0].sales_id != undefined && userData[0].sales_id != null && userData[0].sales_id > 0) {
            const hierarchySql = `select sales_hierarchy_details_id from sales_hierarchy_details where TSM_id=${userData[0].sales_id} and record_status='active'`
            const hierarchyDetails = await this.rawQuery(hierarchySql, connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'response-hierarchyDetails', fields: hierarchyDetails })
            // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'response-hierarchyDetails', fields: hierarchyDetails })
            if (hierarchyDetails.length > 0) {
              fields.hierarchy_id = hierarchyDetails[0].sales_hierarchy_details_id
            }
          }
          console.log('fielda-><><>', fields)
          this.TABLE_NAME = 'ma_kb_account_master'
          const _result = await this.insert(connection, {
            data: fields
          })
          if (_result.affectedRows > 0) {
            fields.ma_kb_account_master_id = _result.insertId
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, khatadata: [fields], action_code: 1000 }
          } else {
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
          }
        } else {
          return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003], action_code: 1001 }
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, khatadata: result, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getKhataList', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async addCustomer (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const validate = validator.validateMobile(fields.mobile, 'IN')
      log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-mobilevalidate', fields: validate })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-mobilevalidate', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033], action_code: 1001 }
      }
      const validateusername = validator.validatekhatabook('username',fields.name)
      if (!validateusername) {
        console.log('username not valid')
        return { status: 400, respcode: 1001, message: "username not valid. Please check and try again.", action_code: 1001 }
      }
      const validatemail = validator.validatekhatabook('email',fields.email)
      if (!validatemail) {
        console.log("email not valid")
        return { status: 400, respcode: 1001, message: "email not valid. Please check and try again.", action_code: 1001 }
      }
      const userSql = `SELECT  sales_id FROM ma_user_master WHERE userid = ${fields.userid} LIMIT 1`
      const userData = await this.rawQuery(userSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-userData', fields: userData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-userData', fields: userData })
      if (userData.length > 0) {
        const validateSql = `SELECT * from ma_kb_customer_master where ma_kb_account_master_id = '${fields.ma_kb_account_master_id}' AND mobile = '${fields.mobile}' AND customer_status = 'active'`
        const validateData = await this.rawQuery(validateSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-validateData', fields: validateData })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-validateData', fields: validateData })
        if (validateData.length > 0) {
          return { status: 400, message: errorMsg.responseCode[1060], respcode: 1060, action_code: 1001 }
        }
        fields.sales_id = userData[0].sales_id
        fields.customer_status = 'active'
        fields.record_status = 'active'
        fields.uuid = await this.generateUUID()
        if (userData[0].sales_id != undefined && userData[0].sales_id != null && userData[0].sales_id > 0) {
          const hierarchySql = `select sales_hierarchy_details_id from sales_hierarchy_details where TSM_id=${userData[0].sales_id} and record_status='active'`
          const hierarchyDetails = await this.rawQuery(hierarchySql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-hierarchyDetails', fields: hierarchyDetails })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'response-hierarchyDetails', fields: hierarchyDetails })
          if (hierarchyDetails.length > 0) {
            fields.hierarchy_id = hierarchyDetails[0].sales_hierarchy_details_id
          }
        }
        this.TABLE_NAME = 'ma_kb_customer_master'
        const _result = await this.insert(connection, {
          data: fields
        })
        if (_result.affectedRows > 0) {
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
        } else {
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      } else {
        return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003], action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async editCustomer (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'editCustomer', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const validate = validator.validateMobile(fields.mobile, 'IN')
      log.logger({ pagename: require('path').basename(__filename), action: 'editCustomer', type: 'response-mobilevalidate', fields: validate })
      if (validate.status === false) {
        return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033], action_code: 1001 }
      }

      const sqlold = `Select * from ma_kb_customer_master where uuid = '${fields.uuid}' AND customer_status = 'active'`

      const oldData = await this.rawQuery(sqlold, connection)
      var updateData = {}
      if (fields.attachment && (typeof fields.attachment == 'undefined')) {
        fields.attachment = oldData[0].attachment
        fields.uploadid = oldData[0].uploadid
      }

      await mySQLWrapper.beginTransaction(connection)
      updateData.customer_status = 'inactive'
      this.TABLE_NAME = 'ma_kb_customer_master'
      const _resultUpd = await this.updateWhere(connection, {
        id: fields.uuid,
        where: 'uuid',
        data: updateData
      })
      if (_resultUpd.affectedRows > 0) {
        const userSql = `SELECT  sales_id FROM ma_user_master WHERE userid = ${fields.userid} LIMIT 1`
        const userData = await this.rawQuery(userSql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'editCustomer', type: 'response-userData', fields: userData })
        if (userData.length > 0) {
          const validateSql = `SELECT * from ma_kb_customer_master where ma_kb_account_master_id = '${fields.ma_kb_account_master_id}' AND mobile = '${fields.mobile}' AND customer_status = 'active'`
          const validateData = await this.rawQuery(validateSql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'editCustomer', type: 'response-validateData', fields: validateData })
          if (validateData.length > 0) {
            return { status: 400, message: errorMsg.responseCode[1060], respcode: 1060, action_code: 1001 }
          }
          fields.sales_id = userData[0].sales_id
          fields.customer_status = 'active'
          fields.record_status = 'active'
          if (userData[0].sales_id != undefined && userData[0].sales_id != null && userData[0].sales_id > 0) {
            const hierarchySql = `select sales_hierarchy_details_id from sales_hierarchy_details where TSM_id=${userData[0].sales_id} and record_status='active'`
            const hierarchyDetails = await this.rawQuery(hierarchySql, connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'editCustomer', type: 'response-hierarchyDetails', fields: hierarchyDetails })
            if (hierarchyDetails.length > 0) {
              fields.hierarchy_id = hierarchyDetails[0].sales_hierarchy_details_id
            }
          }

          if (oldData.length > 0) {
            fields.business_name = fields.business_name ? fields.business_name : oldData[0].business_name
            fields.email = fields.email ? fields.email : oldData[0].email
            fields.address1 = fields.address1 ? fields.address1 : oldData[0].address1
            fields.address2 = fields.address2 ? fields.address2 : oldData[0].address2
            fields.pincode = fields.pincode ? fields.pincode : oldData[0].pincode
            fields.state = fields.state ? fields.state : oldData[0].state
            fields.attachment = fields.attachment ? fields.attachment : oldData[0].attachment
            fields.city = fields.city ? fields.city : oldData[0].city
            fields.uploadid = fields.uploadid ? fields.uploadid : oldData[0].uploadid
          }
          this.TABLE_NAME = 'ma_kb_customer_master'
          const _result = await this.insert(connection, {
            data: fields
          })
          if (_result.affectedRows > 0) {
            await mySQLWrapper.commit(connection)
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
          } else {
            await mySQLWrapper.rollback(connection)
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
          }
        } else {
          await mySQLWrapper.rollback(connection)
          return { status: 400, respcode: 1003, message: errorMsg.responseCode[1003], action_code: 1001 }
        }
      } else {
        await mySQLWrapper.rollback(connection)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'editCustomer', type: 'catcherror', fields: err })
      await mySQLWrapper.rollback(connection)
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async deleteCustomer (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteCustomer', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const updateData = {
        record_status: 'inactive',
        customer_status: 'inactive'
      }
      this.TABLE_NAME = 'ma_kb_customer_master'
      const _result = await this.updateWhere(connection, {
        id: fields.uuid,
        where: 'uuid',
        data: updateData
      })
      if (_result.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteCustomer', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getCustomerList (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.limit > 1000) {
        return { status: 400, message: errorMsg.responseCode[1138], respcode: 1138, action_code: 1001 }
      }
      const limit = fields.limit
      const offset = fields.offset
      delete fields.limit
      delete fields.offset
      delete fields.userid
      var sqlKhata = 'SELECT SQL_CALC_FOUND_ROWS a.*,b.name as state_name,c.name as city_name from ma_kb_customer_master a left join ma_states_master b ON a.state = b.id left join ma_cities_master c ON a.city = c.id WHERE  '
      Object.keys(fields).forEach((key, index) => {
        sqlKhata += `${key} = "${fields[key]}"`
        if (index + 1 !== Object.keys(fields).length) {
          sqlKhata += ' AND '
        }
      })
      sqlKhata += `  AND a.customer_status = 'active'  AND a.record_status = 'active'  ORDER BY a.addedon desc LIMIT ${offset},${limit}`
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerList', type: 'customerlist-query', fields: sqlKhata })
      const result = await this.rawQuery(sqlKhata, connection)
      let nextFlag = false
      if (result.length > 0) {
        const countSql = 'SELECT FOUND_ROWS() AS total'
        const countResult = await this.rawQuery(countSql, connection)
        if (countResult.length > 0) {
          const total = countResult[0].total
          if ((limit + offset) < total) {
            nextFlag = true
          }
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, Customerdata: result, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerList', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async createTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createTransaction', type: 'request', fields: fields })
    var isSet = false
    // const connection = await mySQLWrapper.getConnectionFromPool()
    var connection
    try {
      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
        delete fields.connection
      }
      var amount = fields.amount
      var closebal = 0  
      const validatedetails = validator.validatekhatabook('khatabookDetails',fields.description)
      if (!validatedetails) {
        console.log('details not valid')
        return { status: 400, respcode: 1001, message: "Details not valid. Please check and try again.", action_code: 1001 }
      }
      const sqlClosBal = `Select closing_balance from ma_kb_transaction_master where uuid = '${fields.uuid}' order by addedon desc limit 1`
      const dataClosBal = await this.rawQuery(sqlClosBal, connection)
      if (dataClosBal.length > 0) {
        if (fields.credit_type === 'CR') {
          closebal = (dataClosBal[0].closing_balance - fields.amount)
        } else {
          closebal = (dataClosBal[0].closing_balance + fields.amount)
        }
      } else {
        if (fields.credit_type === 'CR') {
          closebal = -fields.amount
        } else {
          closebal = fields.amount
        }
      }
      if (fields.credit_type === 'CR') {
        fields.amount = -fields.amount
      }
      fields.closing_balance = closebal
      if (isSet) await mySQLWrapper.beginTransaction(connection)
      this.TABLE_NAME = 'ma_kb_transaction_master'
      const _result = await this.insert(connection, {
        data: fields
      })
      if (_result.affectedRows > 0) {
        const fieldshistory = Object.assign({}, fields)
        this.TABLE_NAME = 'ma_kb_transaction_master_history'
        fieldshistory.operation = 'INSERT'
        fieldshistory.ma_kb_transaction_master_id = _result.insertId
        const _resulthistory = await this.insert(connection, {
          data: fieldshistory
        })
        if (_resulthistory.affectedRows > 0) {
          const sqlBal = `Select * from ma_kb_transaction_master_balance where uuid = '${fields.uuid}'`
          const dataBal = await this.rawQuery(sqlBal, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'createTransaction', type: 'transactionbalance-data', fields: dataBal })
          if (dataBal.length > 0) {
            var updateData = {}
            if (fields.credit_type === 'CG') {
              updateData.total_amount_given = dataBal[0].total_amount_given + amount
            } else {
              updateData.total_amount_received = dataBal[0].total_amount_received + amount
            }

            this.TABLE_NAME = 'ma_kb_transaction_master_balance'
            const _resultUpd = await this.updateWhere(connection, {
              id: fields.uuid,
              where: 'uuid',
              data: updateData
            })
            if (_resultUpd.affectedRows > 0) {
              if (isSet) await mySQLWrapper.commit(connection)
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], insertid: _result.insertId, action_code: 1000 }
            } else {
              if (isSet) await mySQLWrapper.rollback(connection)
              return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
            }
          } else {
            var balfields = {
              ma_user_id: fields.ma_user_id,
              userid: fields.userid,
              uuid: fields.uuid,
              ma_kb_account_master_id: fields.ma_kb_account_master_id,
              flag: 'active'
            }
            if (fields.credit_type === 'CG') {
              balfields.total_amount_given = amount
              balfields.total_amount_received = 0
            } else {
              balfields.total_amount_given = 0
              balfields.total_amount_received = amount
            }
            this.TABLE_NAME = 'ma_kb_transaction_master_balance'
            const _resultins = await this.insert(connection, {
              data: balfields
            })
            if (_resultins.affectedRows > 0) {
              if (isSet) await mySQLWrapper.commit(connection)
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], insertid: _result.insertId, action_code: 1000 }
            } else {
              if (isSet) await mySQLWrapper.rollback(connection)
              return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
            }
          }
        }
        // check if customer exist in balnce table if yes update else insert
        // update in balance table
      } else {
        if (isSet) await mySQLWrapper.rollback(connection)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createTransaction', type: 'catcherror', fields: err })
      if (isSet) await mySQLWrapper.rollback(connection)
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async deleteTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteTransaction', type: 'request', fields: fields })
    var isSet = false
    // const connection = await mySQLWrapper.getConnectionFromPool()
    var connection
    try {
      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
        delete fields.connection
      }
      const sqlRecord = `Select * from ma_kb_transaction_master where ma_kb_transaction_master_id = '${fields.ma_kb_transaction_master_id}'`
      const dataRecord = await this.rawQuery(sqlRecord, connection)
      if (isSet) await mySQLWrapper.beginTransaction(connection)
      const updateData = {
        transaction_status: 'inactive'
      }
      this.TABLE_NAME = 'ma_kb_transaction_master'
      const _resultUpd = await this.updateWhere(connection, {
        id: fields.ma_kb_transaction_master_id,
        where: 'ma_kb_transaction_master_id',
        data: updateData
      })
      if (_resultUpd.affectedRows > 0) {
        const fieldshistory = dataRecord[0]
        delete fieldshistory.addedon
        delete fieldshistory.updatedon
        delete fieldshistory.due_date
        delete fieldshistory.transaction_status
        this.TABLE_NAME = 'ma_kb_transaction_master_history'
        fieldshistory.operation = 'DELETE'
        const _resulthistory = await this.insert(connection, {
          data: fieldshistory
        })
        if (_resulthistory.affectedRows > 0) {
          const sqlBal = `Select * from ma_kb_transaction_master_balance where uuid = '${fields.uuid}'`
          const dataBal = await this.rawQuery(sqlBal, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'deleteTransaction', type: 'transactionbalance-data', fields: dataBal })
          if (dataBal.length > 0) {
            var updateDataBal = {}
            // var diff = fields.amount - Math.abs(dataRecord[0].amount)
            if (dataRecord[0].credit_type === 'CG') {
              updateDataBal.total_amount_given = dataBal[0].total_amount_given - Math.abs(dataRecord[0].amount)
            } else {
              updateDataBal.total_amount_received = dataBal[0].total_amount_received - Math.abs(dataRecord[0].amount)
            }

            this.TABLE_NAME = 'ma_kb_transaction_master_balance'
            const _resultUpd = await this.updateWhere(connection, {
              id: fields.uuid,
              where: 'uuid',
              data: updateDataBal
            })
            if (_resultUpd.affectedRows > 0) {
              if (isSet) await mySQLWrapper.commit(connection)
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
            } else {
              if (isSet) await mySQLWrapper.rollback(connection)
              return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
            }
          }
          if (isSet) await mySQLWrapper.commit(connection)
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
        } else {
          if (isSet) await mySQLWrapper.rollback(connection)
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      } else {
        if (isSet) await mySQLWrapper.rollback(connection)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteTransaction', type: 'catcherror', fields: err })
      if (isSet) await mySQLWrapper.rollback(connection)
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async editTransaction (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'editTransaction', type: 'request', fields: fields })
    var isSet = false
    // const connection = await mySQLWrapper.getConnectionFromPool()
    var connection
    try {
      if (fields.connection === null || fields.connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      } else {
        connection = fields.connection
        delete fields.connection
      }
      const sqlRecord = `Select * from ma_kb_transaction_master where ma_kb_transaction_master_id = '${fields.ma_kb_transaction_master_id}'`
      const dataRecord = await this.rawQuery(sqlRecord, connection)
      if (isSet) await mySQLWrapper.beginTransaction(connection)
      const updateData = {}
      if (dataRecord[0].credit_type === 'CR') {
        updateData.amount = -fields.amount
      } else {
        updateData.amount = fields.amount
      }

      updateData.transaction_date = fields.transaction_date
      if (typeof fields.description !== 'undefined' && fields.description != null) {
        updateData.description = fields.description
      }
      if (typeof fields.attachment !== 'undefined' && fields.attachment != null) {
        updateData.attachment = fields.attachment
        updateData.uploadid = fields.uploadid
      }

      this.TABLE_NAME = 'ma_kb_transaction_master'

      const _resultUpd = await this.updateWhere(connection, {
        id: fields.ma_kb_transaction_master_id,
        where: 'ma_kb_transaction_master_id',
        data: updateData
      })
      if (_resultUpd.affectedRows > 0) {
        const fieldshistory = dataRecord[0]
        delete fieldshistory.addedon
        delete fieldshistory.updatedon
        delete fieldshistory.due_date
        delete fieldshistory.transaction_status
        this.TABLE_NAME = 'ma_kb_transaction_master_history'
        fieldshistory.operation = 'UPDATE'
        const _resulthistory = await this.insert(connection, {
          data: fieldshistory
        })
        if (_resulthistory.affectedRows > 0) {
          const sqlBal = `Select * from ma_kb_transaction_master_balance where uuid = '${fields.uuid}'`
          const dataBal = await this.rawQuery(sqlBal, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'editTransaction', type: 'transactionbalance-data', fields: dataBal })
          if (dataBal.length > 0) {
            var updateDataBal = {}
            var diff = fields.amount - Math.abs(dataRecord[0].amount)
            if (dataRecord[0].credit_type === 'CG') {
              updateDataBal.total_amount_given = dataBal[0].total_amount_given + diff
            } else {
              updateDataBal.total_amount_received = dataBal[0].total_amount_received + diff
            }

            this.TABLE_NAME = 'ma_kb_transaction_master_balance'
            const _resultUpd = await this.updateWhere(connection, {
              id: fields.uuid,
              where: 'uuid',
              data: updateDataBal
            })
            if (_resultUpd.affectedRows > 0) {
              if (isSet) await mySQLWrapper.commit(connection)
              return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
            } else {
              if (isSet) await mySQLWrapper.rollback(connection)
              return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
            }
          }
          if (isSet) await mySQLWrapper.commit(connection)
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000 }
        } else {
          if (isSet) await mySQLWrapper.rollback(connection)
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      } else {
        if (isSet) await mySQLWrapper.rollback(connection)
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'editTransaction', type: 'catcherror', fields: err })
      if (isSet) await mySQLWrapper.rollback(connection)
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getTransactionList (_, fields) {
    // process.env.NODE_ENV = 'staging'
    log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionList', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.limit > 1000) {
        return { status: 400, message: errorMsg.responseCode[1138], respcode: 1138, action_code: 1001 }
      }
      const limit = fields.limit
      const offset = fields.offset
      delete fields.limit
      delete fields.offset
      delete fields.userid
      var filterCond = ''
      if (typeof fields.amount !== 'undefined' && fields.amount != null) {
        filterCond += ` AND ABS(amount) = '${fields.amount}' `
        delete fields.amount
      }
      if (typeof fields.datefrom !== 'undefined' && fields.datefrom != null && typeof fields.dateto !== 'undefined' && fields.dateto != null) {
        filterCond += ` AND transaction_date between  '${fields.datefrom}' AND '${fields.dateto}' `
        delete fields.datefrom
        delete fields.dateto
      }
      var sqlTxn = 'SELECT SQL_CALC_FOUND_ROWS ma_kb_transaction_master_id,ma_kb_account_master_id,uuid,ma_user_id,userid,ABS(amount) as amount,closing_balance,credit_type,bill_no,description,transaction_date,due_date,attachment,uploadid,transaction_id,addedon,updatedon,transaction_status from ma_kb_transaction_master WHERE  '
      Object.keys(fields).forEach((key, index) => {
        sqlTxn += `${key} = "${fields[key]}"`
        if (index + 1 !== Object.keys(fields).length) {
          sqlTxn += ' AND '
        }
      })
      sqlTxn += `  ${filterCond} AND transaction_status = 'active'  ORDER BY transaction_date desc LIMIT ${offset},${limit}`
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionList', type: 'transactionlist-query', fields: sqlTxn })
      const result = await this.rawQuery(sqlTxn, connection)
      for (var j = 0; j < result.length; j++) {
        result[j].attachment = (result[j].attachment != null && result[j].attachment != '') ? util[process.env.NODE_ENV].bucketurl + result[j].attachment : result[j].attachment
      }
      let nextFlag = false
      if (result.length > 0) {
        console.log('res length', result.length)
        const countSql = 'SELECT FOUND_ROWS() AS total'
        const countResult = await this.rawQuery(countSql, connection)
        console.log('total', countResult[0].total)
        if (countResult.length > 0) {
          const total = countResult[0].total
          if ((limit + offset) < total) {
            nextFlag = true
          }
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, Transactiondata: result, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransactionList', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getCustomerWiseBalance (_, fields) {
    // process.env.NODE_ENV = 'staging'
    log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerWiseBalance', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      if (fields.limit > 1000) {
        return { status: 400, message: errorMsg.responseCode[1138], respcode: 1138, action_code: 1001 }
      }
      const limit = fields.limit
      const offset = fields.offset
      delete fields.limit
      delete fields.offset
      delete fields.userid
      var sqlTxn = `SELECT * from ma_kb_transaction_master_balance  WHERE ma_user_id = ${fields.ma_user_id}  AND ma_kb_account_master_id = '${fields.ma_kb_account_master_id}'`
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerWiseBalance', type: 'balancelist-query', fields: sqlTxn })
      const result = await this.rawQuery(sqlTxn, connection)
      var sqlCust = `SELECT SQL_CALC_FOUND_ROWS ma_user_id,uuid,updatedon,name as customer_name,email,attachment,mobile from ma_kb_customer_master WHERE ma_user_id = ${fields.ma_user_id}  AND ma_kb_account_master_id = '${fields.ma_kb_account_master_id}' AND customer_status = 'active' AND record_status = 'active'`
      var filterCond = ''
      if (typeof fields.search !== 'undefined' && fields.search != null) {
        filterCond = `AND  (name like '%${fields.search}%' OR mobile like '%${fields.search}%')`
      }
      // if (typeof fields.customer_name !== 'undefined' && fields.customer_name != null) {
      //   filterCond = ` AND name like '%${fields.customer_name}%'`
      // }
      // if (typeof fields.mobile !== 'undefined' && fields.mobile != null) {
      //   filterCond = ` AND mobile like '%${fields.mobile}%'`
      // }
      sqlCust += ` ${filterCond} ORDER BY addedon desc LIMIT ${offset},${limit}`
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerWiseBalance', type: 'customerdata-query', fields: sqlCust })
      const result_cust = await this.rawQuery(sqlCust, connection)
      let nextFlag = false
      if (result_cust.length > 0) {
        var final_bal
        var flag
        const custbalobj = {}
        for (var k = 0; k < result.length; k++) {
          final_bal = result[k].total_amount_given - result[k].total_amount_received
          if (final_bal > 0) {
            flag = 'CG'
          } else {
            flag = 'CR'
          }
          custbalobj[result[k].uuid] = { total_amount_given: result[k].total_amount_given, total_amount_received: result[k].total_amount_received, final_bal: Math.abs(final_bal), flag: flag }
        }
        console.log('custbalobj', result_cust)

        for (var j = 0; j < result_cust.length; j++) {
          // console.log('custbalobj val',custbalobj[result_cust[j].uuid])
          result_cust[j].total_amount_given = custbalobj[result_cust[j].uuid] ? custbalobj[result_cust[j].uuid].total_amount_given : 0
          result_cust[j].total_amount_received = custbalobj[result_cust[j].uuid] ? custbalobj[result_cust[j].uuid].total_amount_received : 0
          result_cust[j].final_bal = custbalobj[result_cust[j].uuid] ? custbalobj[result_cust[j].uuid].final_bal : 0
          result_cust[j].flag = custbalobj[result_cust[j].uuid] ? custbalobj[result_cust[j].uuid].flag : 'ZC'
          result_cust[j].attachment = (result_cust[j].attachment != null && result_cust[j].attachment != '') ? util[process.env.NODE_ENV].bucketurl + result_cust[j].attachment : result_cust[j].attachment
        }
        console.log(result_cust)
        const countSql = 'SELECT FOUND_ROWS() AS total'
        const countResult = await this.rawQuery(countSql, connection)
        if (countResult.length > 0) {
          const total = countResult[0].total
          if ((limit + offset) < total) {
            nextFlag = true
          }
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], nextFlag, CustomerList: result_cust, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerWiseBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getAccountWiseBalance (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAccountWiseBalance', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // var sqlBalAcc = `SELECT sum(total_amount_given) as credit_given, sum(total_amount_received) as credit_received from ma_kb_transaction_master_balance  WHERE ma_user_id = ${fields.ma_user_id}  AND ma_kb_account_master_id = '${fields.ma_kb_account_master_id}' group by ma_kb_account_master_id`

      var sqlBalAcc = `SELECT total_amount_given - total_amount_received as amount from ma_kb_transaction_master_balance  WHERE ma_user_id = ${fields.ma_user_id}  AND ma_kb_account_master_id = '${fields.ma_kb_account_master_id}'`
      log.logger({ pagename: require('path').basename(__filename), action: 'getAccountWiseBalance', type: 'accountbalance-query', fields: sqlBalAcc })
      const result = await this.rawQuery(sqlBalAcc, connection)
      var credit_given = 0
      var credit_received = 0
      if (result.length > 0) {
        for (var j = 0; j < result.length; j++) {
          if (result[j].amount > 0) {
            credit_given += result[j].amount
          } else {
            credit_received += result[j].amount
          }
        }
      }
      credit_received = Math.abs(credit_received)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], credit_given, credit_received, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAccountWiseBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async setDueDate (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'setDueDate', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // delete fields.userid
      const yesterday = new Date(fields.due_date)
      yesterday.setDate(yesterday.getDate() - 1)
      var dateprev = yesterday.toISOString().split('T')[0]

      const tomorrow = new Date(fields.due_date)
      tomorrow.setDate(tomorrow.getDate() + 1)
      var datenext = tomorrow.toISOString().split('T')[0]
      const sqlExist = `Select * from ma_kb_collection_queue where uuid = '${fields.uuid}' and notification_status = 'P' and record_status = 'active'`
      const dataExist = await this.rawQuery(sqlExist, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'setDueDate', type: 'duedate-result', fields: dataExist })
      await mySQLWrapper.beginTransaction(connection)
      if (dataExist.length > 0) {
        const updateData = {
          record_status: 'inactive'
        }
        this.TABLE_NAME = 'ma_kb_collection_queue'
        const _resultUpd = await this.updateWhere(connection, {
          id: fields.uuid,
          where: 'uuid',
          data: updateData
        })
        if (_resultUpd.affectedRows > 0) {
          this.TABLE_NAME = 'ma_kb_collection_queue'
          const queryColumns = '(`ma_user_id`,`userid`,`uuid`,`ma_kb_account_master_id`,`due_date`,`notification_status`,`reminder_type`,`record_status`)'
          const queryData = '(' + fields.ma_user_id + ',' + fields.userid + ',"' + fields.uuid + '","' + fields.ma_kb_account_master_id + '","' + fields.due_date + '","P","CRON","active"),(' + fields.ma_user_id + ',' + fields.userid + ',"' + fields.uuid + '","' + fields.ma_kb_account_master_id + '","' + dateprev + '","P","CRON","active"),(' + fields.ma_user_id + ',' + fields.userid + ',"' + fields.uuid + '","' + fields.ma_kb_account_master_id + '","' + datenext + '","P","CRON","active")'
          const _result = await this.insertBulk(connection, { fields: queryColumns, data: queryData })
          // insert

          if (_result.affectedRows > 0) {
            await mySQLWrapper.commit(connection)
            return { status: 200, respcode: 1159, message: errorMsg.responseCode[1159], action_code: 1000 }
          } else {
            await mySQLWrapper.rollback(connection)
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
          }
        } else {
          await mySQLWrapper.rollback(connection)
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      } else {
        // insert
        this.TABLE_NAME = 'ma_kb_collection_queue'
        const queryColumns = '(`ma_user_id`,`userid`,`uuid`,`ma_kb_account_master_id`,`due_date`,`notification_status`,`reminder_type`,`record_status`)'
        const queryData = '(' + fields.ma_user_id + ',' + fields.userid + ',"' + fields.uuid + '","' + fields.ma_kb_account_master_id + '","' + fields.due_date + '","P","CRON","active"),(' + fields.ma_user_id + ',' + fields.userid + ',"' + fields.uuid + '","' + fields.ma_kb_account_master_id + '","' + dateprev + '","P","CRON","active"),(' + fields.ma_user_id + ',' + fields.userid + ',"' + fields.uuid + '","' + fields.ma_kb_account_master_id + '","' + datenext + '","P","CRON","active")'
        const _result = await this.insertBulk(connection, { fields: queryColumns, data: queryData })
        // insert
        if (_result.affectedRows > 0) {
          await mySQLWrapper.commit(connection)
          return { status: 200, respcode: 1159, message: errorMsg.responseCode[1159], action_code: 1000 }
        } else {
          await mySQLWrapper.rollback(connection)
          return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        }
      }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'setDueDate', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async sentPaymentReminder (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sentPaymentReminder', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // const userid = fields.userid
      // delete fields.userid
      const today = new Date()
      fields.due_date = today.toISOString().split('T')[0]
      fields.notification_status = 'P'
      fields.reminder_type = 'MANUAL'
      fields.record_status = 'active'
      this.TABLE_NAME = 'ma_kb_collection_queue'
      const _result = await this.insert(connection, {
        data: fields
      })
      if (_result.affectedRows > 0) {
        fields.insertid = _result.insertId
        // fields.userid = userid
        const payment_resp = await this.paymentProcess(fields, connection)
        return payment_resp
      } else {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sentPaymentReminder', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async paymentProcess (fields, connection) {
    try {
      console.log("----------- khatabook V2 payment process-------------");
      const amtSql = `SELECT a.total_amount_given as credit_amount_given ,a.total_amount_received as credit_amount_received,b.mobile as mobile,b.email as email,b.name as name,c.business_name from ma_kb_transaction_master_balance a join ma_kb_customer_master b ON a.uuid = b.uuid  JOIN ma_kb_account_master c ON c.ma_kb_account_master_id = b.ma_kb_account_master_id  WHERE a.ma_user_id = ${fields.ma_user_id}  AND a.uuid = '${fields.uuid}' AND b.customer_status = 'active'`
      const amtData = await this.rawQuery(amtSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'paymentProcess', type: 'customerdata-response', fields: amtData })
      if (amtData.length > 0) {
        const amount = (amtData[0].credit_amount_given - amtData[0].credit_amount_received)
        const mobile_number = amtData[0].mobile
        const email = amtData[0].email
        const name = amtData[0].name
        const business_name = amtData[0].business_name
        if (amount > 0) {
          var rand = await this.generateUUID()
          const orderid = `MAKB${rand}`
          const req = {
            ma_user_id: fields.ma_user_id,
            userid: fields.userid,
            aggregator_order_id: orderid,
            amount: amount,
            transaction_type: '10',
            remarks: 'COLLECTMONEY KHATABOOK TRANSACTION',
            transaction_status: 'I',
            mobile_number: mobile_number.toString(),
            uuid: fields.uuid,
            connection
          }
          const txnResp = await transactionController.initiateTransaction('_', req)
          log.logger({ pagename: require('path').basename(__filename), action: 'paymentProcess', type: 'transaction-response', fields: txnResp })
          if (txnResp.status === 400) {
            const updateData = {
              record_status: 'inactive',
              notification_status: 'F'
            }
            this.TABLE_NAME = 'ma_kb_collection_queue'
            const _resultUpd = await this.updateWhere(connection, {
              id: fields.insertid,
              where: 'ma_kb_collection_queue_id',
              data: updateData
            })
            return txnResp
          }
          var reqemail = ''
          if (email != undefined && email != null && email != '') {
            reqemail = email
          }
          const postParams = {
            mid: fields.ma_user_id,
            userid: fields.userid,
            order_id: orderid,
            amount: amount,
            contact_no: mobile_number,
            mer_dom: util.mer_domain,
            email: reqemail,
            call_type: 'invoicepay',
            module: 'khatabook'
            // airpayId: fields.aggregator_txn_id ? fields.aggregator_txn_id : ''
          }

          // Call payments api for invoice pay
          // const postData = qs.stringify(postParams)
          const postData = await generateOrderchecksum(this, postParams)
          console.log("post data",postData)
          if (postData.status != 200) {
            return { status: postData.status, respcode: postData.respcode, message: postData.message }
          }
          console.log(typeof postData.data)
          const response = await axios({
            method: 'post',
            url: util.paymentsUrl + 'api/generateOrder.php',
            data: postData.data,
            headers: { 'Content-Type': 'application/json' }
          })
          console.log('response>>>>>>>>>>', response)
          log.logger({ pagename: require('path').basename(__filename), action: 'paymentProcess', type: 'paymentapi-response', fields: response.data })
          var updData = {}
          var finalResp = {}
          if (response.status === 200 && response.data.status == '200') {
            var message = util.INVOICEPAYCOMM
            message = message.replace('<Sir/Madam>', name)
            message = message.replace('<RS AMOUNT>', 'Rs. ' + amount)
            message = message.replace('<RETAILER BUSINESS NAME>', business_name)
            message = message.replace('<PAYMENT LINK>', response.data.payment_url)
            message = message.replace('<signature>', util.kbsignature)
            await sms.sentSmsAsync(message, mobile_number, util.INVOICEPAYCOMMTID)
            if (email != undefined && email != null && email != '') {
              const mailerData = {
                template: 'khatabook.ejs',
                content: {
                  customername: name,
                  businessname: business_name,
                  amount: amount,
                  paymentlink: response.data.payment_url,
                  signature: util.kbsignature
                },
                from: '"Airpay" <<EMAIL>>',
                to: [email],
                cc: [],
                bcc: [],
                subject: 'Payment Due Reminder',
                attachments: []
              }
              console.log("mailer data",mailerData)
              const mailResponse = await mailer(mailerData)
              console.log('mailResponse', mailResponse)
            }
            updData.record_status = 'inactive'
            updData.notification_status = 'S'
            finalResp.status = 200
            finalResp.respcode = 1158
            finalResp.message = errorMsg.responseCode[1158]
            finalResp.action_code = 1000
          } else {
            updData.record_status = 'inactive'
            updData.notification_status = 'F'
            finalResp.status = 400
            finalResp.respcode = 1001
            finalResp.message = errorMsg.responseCode[1001]
            finalResp.action_code = 1001
          }
          this.TABLE_NAME = 'ma_kb_collection_queue'
          const _resultUpd = await this.updateWhere(connection, {
            id: fields.insertid,
            where: 'ma_kb_collection_queue_id',
            data: updData
          })
          return finalResp
          // call the collect money api
          // update status in queue table
        } else {
          const updateData = {
            record_status: 'inactive',
            notification_status: 'F'
          }
          this.TABLE_NAME = 'ma_kb_collection_queue'
          const _resultUpd = await this.updateWhere(connection, {
            id: fields.insertid,
            where: 'ma_kb_collection_queue_id',
            data: updateData
          })
          return { status: 400, message: errorMsg.responseCode[1139], respcode: 1139, action_code: 1001 }
        }
      } else {
        const updateData = {
          record_status: 'inactive',
          notification_status: 'F'
        }
        this.TABLE_NAME = 'ma_kb_collection_queue'
        const _resultUpd = await this.updateWhere(connection, {
          id: fields.insertid,
          where: 'ma_kb_collection_queue_id',
          data: updateData
        })
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }
    } catch (err) {
      const updateData = {
        record_status: 'inactive',
        notification_status: 'F'
      }
      this.TABLE_NAME = 'ma_kb_collection_queue'
      const _resultUpd = await this.updateWhere(connection, {
        id: fields.insertid,
        where: 'ma_kb_collection_queue_id',
        data: updateData
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'paymentProcess', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async reminderCron (_, fields) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      console.log("----------- khatabook V2 controller -------------");
      const sqlReminder = 'SELECT * from ma_kb_collection_queue where reminder_type = \'CRON\' AND record_status = \'active\' AND notification_status = \'P\' AND DATE(due_date) = CURDATE()'
      log.logger({ pagename: require('path').basename(__filename), action: 'reminderCron', type: 'remindercron-sql', fields: sqlReminder })
      const dataReminder = await this.rawQuery(sqlReminder, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'reminderCron', type: 'remindercron-data', fields: dataReminder })
      if (dataReminder.length > 0) {
        for (var k = 0; k < dataReminder.length; k++) {
          var cronData = {}
          cronData.ma_user_id = dataReminder[k].ma_user_id
          cronData.userid = dataReminder[k].userid
          cronData.uuid = dataReminder[k].uuid
          cronData.insertid = dataReminder[k].ma_kb_collection_queue_id
          await this.paymentProcess(cronData, connection)
        }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'reminderCron', type: 'catcherror', fields: err })
    } finally {
      connection.release()
    }
  }

  static async generateUUID () {
    const timeStamp = Math.floor(Date.now() / 1000)
    var digits = '0123456789'
    var random = ''
    for (var i = 0; i < 6; i++) {
      random += digits[Math.floor(Math.random() * 10)]
    }
    return `${timeStamp}${random}`
  }

  static async getCustomerBalance (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerBalance', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      var sqlTxn = `SELECT * from ma_kb_transaction_master_balance  WHERE ma_user_id = ${fields.ma_user_id}  AND uuid = '${fields.uuid}'`
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerBalance', type: 'balance-query', fields: sqlTxn })
      const result = await this.rawQuery(sqlTxn, connection)
      var final_bal = 0
      var credit_given = 0
      var credit_received = 0
      var flag = 'ZC'
      if (result.length > 0) {
        credit_given = result[0].total_amount_given
        credit_received = result[0].total_amount_received
        final_bal = result[0].total_amount_given - result[0].total_amount_received
        if (final_bal > 0) {
          flag = 'CG'
        } else {
          flag = 'CR'
        }
        final_bal = Math.abs(final_bal)
      }
      var sqlCust = `SELECT * from ma_kb_customer_master  WHERE ma_user_id = ${fields.ma_user_id}  AND uuid = '${fields.uuid}' AND customer_status = 'active' AND record_status = 'active'`
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerBalance', type: 'balance-query', fields: sqlCust })
      const custResult = await this.rawQuery(sqlCust, connection)
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], credit_given, credit_received, flag, final_bal, Customerdata: custResult, action_code: 1000 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getCustomerBalance', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }
}

module.exports = KhataBook
