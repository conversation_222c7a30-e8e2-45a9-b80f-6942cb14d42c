const path = require('path')
const Joi = require('joi')
const customJoi = require('../../customJoi')
const moment = require('moment')

const CONFIG = require('../config')
const Provider = require('../provider')
const PMTRequest = require('./pmtrequest')
const { SOAP_ACTION, API_PROVIDER, PROVIDER_KYC_ENDPOINT, PARTNER_PAYMENT_MODE_CODE, MODE_CODE, TRANSFER_STATUS, PROVIDER_MERCHANT_ONBOARIND_URL } = require('./config')
const logs = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const ApiError = require('../../../../util/errors/ApiError')
const ApplicationError = require('../../../../util/errors/ApplicationErrorFile')
const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const commonFns = require('../../../../util/common_fns')
const common = require('../../../../util/common')
const util = require('../../../../util/util')
const { generateRandom } = require('../../utilFunction')

class PMT extends Provider {
  static async mockApiFlag ({ connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const mockApiFlagResp = await common.getSystemCodes(this, util.mock_api_flag, conn)

      logs.logger({ pagename: path.basename(__filename), action: 'mockApiFlagResp', type: 'value', fields: mockApiFlagResp })

      if (mockApiFlagResp == 'Y') return true

      return false
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'mockApiFlag', type: 'error', fields: error })
      return false
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * CHECK IF REMITTER EXISTS AND ALSO GET REMITTER DETAIL
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async getRemitterDetail ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        ma_user_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: ma_user_id is required field and must be number')),
        userid: Joi.number().required().error(new ApplicationError(2001, 'Fail: userid is required field and must be number'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        'tem:GetCustomerByMobile': {
          'tem:GetCustomerByMobileRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:CustomerMobile': apiParams.mobile_number
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.GetCustomerByMobile })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.GetCustomerByMobile, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'response', fields: response })

      if (!response.GetCustomerByMobileResponse || !response.GetCustomerByMobileResponse.GetCustomerByMobileResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Customers': Customers } = response.GetCustomerByMobileResponse.GetCustomerByMobileResult

      if (Code == '000') {
        const { 'a:Customer': Customer } = Customers

        if (Customer['a:Status'] != 'Verified') return {}

        let IDType = ''
        let IDNumber = ''

        if (Customer['a:Ids'] && Customer['a:Ids']['a:Id']) {
          IDType = Customer['a:Ids']['a:Id']['a:IdType'] || ''
          IDNumber = Customer['a:Ids']['a:Id']['a:IdNumber'] || ''
        }

        const customerFields = {
          customer_provider_reference_id: Customer['a:CustomerId'],
          customer_provider_status: 'Y',
          customer_kyc_status: Customer['a:EKYCStatus'] == 'Verified' ? 'Y' : 'N',
          customer_onboarding_status: Customer['a:OnboardingStatus'] == 'Success' ? 'Y' : 'N',
          customer_name: Customer['a:Name'],
          mobile_number: Customer['a:Mobile'],
          customer_nationality: Customer['a:Nationality'],
          customer_address: `${Customer['a:Address']} ${Customer['a:District']} ${Customer['a:City']} ${Customer['a:State']}`,
          customer_data_json: JSON.stringify({ Address: Customer['a:Address'], District: Customer['a:District'], City: Customer['a:City'], State: Customer['a:State'], Email: Customer['a:Email'] || '', Gender: Customer['a:Gender'], Employer: Customer['a:Employer'], IncomeSource: Customer['a:IncomeSource'], IDType: IDType, IDNumber: IDNumber, DOB: Customer['a:Dob'] }),
          customer_address_data: Customer['a:Address'] || '',
          district: Customer['a:District'] || '',
          city: Customer['a:City'] || '',
          customer_state: Customer['a:State'] || '',
          country: '',
          email: Customer['a:Email'] || '',
          gender: Customer['a:Gender'] || '',
          employer: Customer['a:Employer'] || '',
          income_source: Customer['a:IncomeSource'] || '',
          id_type: IDType,
          id_number: IDNumber,
          dob: Customer['a:Dob'] || ''
        }

        return { ...Customer, ...customerFields }
      }

      /* Default remitter limit  */
      if (Code == '666' || Code != '000') return {}
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * PMT Fetch Static Data : 'Gender', 'IDType', 'Nationality', 'IncomeSource', 'Relationship', 'PaymentMode', 'RemittanceReason'
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async getRemitterLimit ({ apiParams, configuredLimit, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const { per_day_limit: MAX_DAILY_LIMIT, per_month_limit: MAX_MONTHLY_LIMIT, per_yearly_limit: MAX_YEARLY_LIMIT } = configuredLimit['TRANSACTION-KYC'] //  must be configured

      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'request', fields: { apiParams, credentials } })

      const customerDetail = await this.getRemitterDetail({ apiParams, credentials })

      if (Object.keys(customerDetail).length == 0) {
        return {
          availableAmountLimitDaily: CONFIG.NOT_APPLICABLE,
          availableAmountLimitMonthly: CONFIG.NOT_APPLICABLE,
          availableAmountLimitYearly: CONFIG.NOT_APPLICABLE,
          availableTransactionLimitDaily: MAX_DAILY_LIMIT,
          availableTransactionLimitMonthly: MAX_MONTHLY_LIMIT,
          availableTransactionLimitYearly: MAX_YEARLY_LIMIT
        }
      }

      const { 'a:TransactionCount': TransactionCount } = customerDetail
      if (!TransactionCount || TransactionCount.constructor.name != 'Object') throw new ApiError(10001, API_PROVIDER, 'API Error')
      return {
        availableAmountLimitDaily: CONFIG.NOT_APPLICABLE,
        availableAmountLimitMonthly: CONFIG.NOT_APPLICABLE,
        availableAmountLimitYearly: CONFIG.NOT_APPLICABLE,
        availableTransactionLimitDaily: parseInt(MAX_DAILY_LIMIT) - parseInt(TransactionCount['a:Day'] || 0),
        availableTransactionLimitMonthly: parseInt(MAX_MONTHLY_LIMIT) - parseInt(TransactionCount['a:Month'] || 0),
        availableTransactionLimitYearly: parseInt(MAX_YEARLY_LIMIT) - parseInt(TransactionCount['a:Year'] || 0)
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * REMITTER EXISTING BENE LIST AT PROVIDER END
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   * @returns
   */
  static async getBeneficiaries ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getBeneficiaries', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'getBeneficiaries', type: 'request', fields: { apiParams, credentials } })

      const customerDetail = await this.getRemitterDetail({ apiParams, credentials })
      console.log('CUSTOMERRRR: ', customerDetail)

      const beneList = []

      if (Object.keys(customerDetail).length == 0) {
        return beneList
      }

      console.log('TRUE||FALSE: ', (!customerDetail['a:Receivers'] || !('a:Receiver' in customerDetail['a:Receivers'])))
      // if (!customerDetail['a:Receivers'] || !('a:Receiver' in customerDetail['a:Receivers'])) throw new ApiError(10001, API_PROVIDER, 'API Error')

      // IF NO RECEIVER FROM PROVIDER THEN JUST RETURN EMPTY ARRAY
      if (customerDetail['a:Receivers'] && ('a:Receiver' in customerDetail['a:Receivers'])) {
        logs.logger({ pagename: path.basename(__filename), action: 'getBeneficiaries', type: 'response', fields: customerDetail['a:Receivers']['a:Receiver'] })
        const Receiver = customerDetail['a:Receivers']['a:Receiver']
        if (!Receiver) throw new ApiError(10001, API_PROVIDER, 'API Error')
        logs.logger({ pagename: path.basename(__filename), action: 'getBeneficiaries', type: 'receivers', fields: Receiver })
        // const beneList = []

        // If Receiver is an object
        if (Receiver.constructor.name == 'Object') {
          const beneficiary_object = {
            bene_provider_mapping_status: 'Y',
            bene_provider_reference_id: Receiver['a:ReceiverId'],
            beneficiary_mobile: Receiver['a:Mobile'],
            beneficiary_name: Receiver['a:Name'],
            beneficiary_details: {
              Gender: Receiver['a:Gender'],
              Relationship: Receiver['a:Relationship'],
              address: Receiver['a:Address'],
              beneficiary_name: Receiver['a:Name'],
              beneficiary_mobile: Receiver['a:Mobile']
            },
            PaymentMode: PARTNER_PAYMENT_MODE_CODE[Receiver['a:PaymentMode'].toString().toUpperCase()],
            relationship: Receiver['a:Relationship'],
            gender: Receiver['a:Gender'],
            beneficiary_address: Receiver['a:Address'],
            bank_branch_name: Receiver['a:BankBranchName'] ? Receiver['a:BankBranchName'] : null,
            bank_name: Receiver['a:BankName'] ? Receiver['a:BankName'] : null
          }
          if (Receiver['a:PaymentMode'] === 'Account Deposit') {
            beneficiary_object.account_number = Receiver['a:AcNumber']
            beneficiary_object.ifsc_code = Receiver['a:BankBranchId']
            beneficiary_object.beneficiary_details.bankBranchName = Receiver['a:BankBranchName']
            beneficiary_object.beneficiary_details.bank_name = Receiver['a:BankName']
          }
          beneList.push(beneficiary_object)
        }

        // If Receiver is an array
        if (Array.isArray(Receiver)) {
          beneList.push(
            ...Receiver.map(bene => {
              const beneData = {
                bene_provider_mapping_status: 'Y',
                bene_provider_reference_id: bene['a:ReceiverId'],
                beneficiary_mobile: bene['a:Mobile'],
                beneficiary_name: bene['a:Name'],
                beneficiary_details: {
                  Gender: bene['a:Gender'],
                  Relationship: bene['a:Relationship'],
                  address: bene['a:Address'],
                  beneficiary_name: bene['a:Name'],
                  beneficiary_mobile: bene['a:Mobile']
                },
                PaymentMode: PARTNER_PAYMENT_MODE_CODE[bene['a:PaymentMode'].toString().toUpperCase()],
                relationship: bene['a:Relationship'],
                gender: bene['a:Gender'],
                beneficiary_address: bene['a:Address'],
                bank_branch_name: bene['a:BankBranchName'] ? bene['a:BankBranchName'] : null,
                bank_name: bene['a:BankName'] ? bene['a:BankName'] : null
              }
              if (bene['a:PaymentMode'] === 'Account Deposit') {
                beneData.account_number = bene['a:AcNumber']
                beneData.ifsc_code = bene['a:BankBranchId']
                beneData.beneficiary_details.bankBranchName = bene['a:BankBranchName']
                beneData.beneficiary_details.bank_name = bene['a:BankName']
              }
              return beneData
            })
          )
        }
      }

      return beneList
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBeneficiaries', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * PMT Fetch Static Data : 'Gender', 'IDType', 'Nationality', 'IncomeSource', 'Relationship', 'PaymentMode', 'RemittanceReason'
   * @param {{type:string,credentials:{username:string,password:string}}} param0
   */
  static async getStaticData ({ type, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { type, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'request', fields: { type, credentials } })

      const schema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await schema.validateAsync(credentials)

      if (!['Gender', 'IDType', 'Nationality', 'IncomeSource', 'Relationship', 'PaymentMode', 'RemittanceReason'].includes(type)) throw new ApplicationError(2001, 'Fail: Invalid Type')

      const payLoad = {
        'tem:GetStaticData': {
          'tem:GetStaticDataRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Type': type
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.GetStaticData })

      /* save logs */
      try {
        const fields = { ma_user_id: 0, userid: 0, mobile_number: '0000', session_hash: 'internal', request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.GetStaticData, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'response', fields: response })

      if (!response.GetStaticDataResponse || !response.GetStaticDataResponse.GetStaticDataResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.GetStaticDataResponse.GetStaticDataResult

      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message)

      return { type: type, resp: response.GetStaticDataResponse.GetStaticDataResult, request: payLoad }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * PMT Fetch Static Data : Account Pay provider Branch List
   * @param {{credentials:{username:string,password:string}}} param0
   * @returns
   */
  static async getAcPayproviderBranchList ({ credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getAcPayproviderBranchList', type: 'request', fields: { credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const schema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await schema.validateAsync(credentials)

      const payLoad = {
        'tem:AcPayBankBranchList': {
          'tem:AcPayBankBranchListRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Country': 'Nepal'
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.AcPayBankBranchList })

      /* save logs */
      try {
        const fields = { ma_user_id: 0, userid: 0, mobile_number: '0000', session_hash: 'internal', request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.AcPayproviderBranchList, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      logs.logger({ pagename: path.basename(__filename), action: 'getAcPayproviderBranchList', type: 'response', fields: response })

      if (!response.AcPayBankBranchListResponse || !response.AcPayBankBranchListResponse.AcPayBankBranchListResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.AcPayBankBranchListResponse.AcPayBankBranchListResult

      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message)

      return { type: 'AcPayproviderBranchList', resp: response.AcPayBankBranchListResponse.AcPayBankBranchListResult, request: payLoad }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getAcPayproviderBranchList', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * PMT Fetch Static Data : Get State District
   * @param {{country:string,credentials:{username:string,password:string}}} param0
   * @returns
   */
  static async getStateDistrict ({ country, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { country, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const schema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await schema.validateAsync(credentials)

      logs.logger({ pagename: path.basename(__filename), action: 'getStateDistrict', type: 'request', fields: { country, credentials } })
      if (!['India', 'Nepal'].includes(country)) throw new ApplicationError(2001, 'Fail: Invalid Country')

      const payLoad = {
        'tem:GetStateDistrict': {
          'tem:GetStateDistrictRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Country': country
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.GetStateDistrict })

      /* save logs */
      try {
        const fields = { ma_user_id: 0, userid: 0, mobile_number: '0000', session_hash: 'internal', request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.GetStateDistrict, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      logs.logger({ pagename: path.basename(__filename), action: 'getStateDistrict', type: 'response', fields: response })

      if (!response.GetStateDistrictResponse || !response.GetStateDistrictResponse.GetStateDistrictResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.GetStateDistrictResponse.GetStateDistrictResult

      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message)

      return { type: `COUNTRY_${country.toUpperCase()}`, resp: response.GetStateDistrictResponse.GetStateDistrictResult, request: payLoad }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getStateDistrict', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * CREATE NEW REMITTER SEND OTP
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async addRemitterSendOtp ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'addRemitterSendOtp', type: 'request', fields: { apiParams, credentials } })

      apiParams.operation = 'CreateCustomer'
      return await this.sendOTP({ apiParams, credentials, connection: conn })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'addRemitterSendOtp', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * CREATE NEW REMITTER RESEND OTP
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async addRemitterResendOtp ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'addRemitterResendOtp', type: 'request', fields: { apiParams, credentials } })
    return await this.addRemitterSendOtp({ apiParams, credentials, connection })
  }

  /**
   *  REGISTER NEW REMITTER //masking.
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async addRemitter ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        Name: Joi.string().required().error(new ApplicationError(2001, 'Fail: Name is required field and must be string')),
        Gender: Joi.string().required().error(new ApplicationError(2001, 'Fail: Gender is required field and must be string')),
        DOB: Joi.string().required().error(new ApplicationError(2001, 'Fail: DOB is required field and must be string')),
        Address: Joi.string().required().error(new ApplicationError(2001, 'Fail: Address is required field and must be string')),
        State: Joi.string().required().error(new ApplicationError(2001, 'Fail: State is required field and must be string')),
        District: Joi.string().required().error(new ApplicationError(2001, 'Fail: District is required field and must be string')),
        City: Joi.string().required().error(new ApplicationError(2001, 'Fail: City is required field and must be string')),
        Nationality: Joi.string().required().error(new ApplicationError(2001, 'Fail: Nationality is required field and must be string')),
        // Email: Joi.string().email().optional().error(new ApplicationError(2001, 'Fail: Email is required field and must be Email')),
        Employer: Joi.string().required().error(new ApplicationError(2001, 'Fail: Employer is required field and must be Email')),
        IDType: Joi.string().required().error(new ApplicationError(2001, 'Fail: IDType is required field and must be Email')),
        IDNumber: Joi.string().required().error(new ApplicationError(2001, 'Fail: IDNumber is required field and must be Email')),
        IncomeSource: Joi.string().required().error(new ApplicationError(2001, 'Fail: IncomeSource is required field and must be Email')),
        otp: Joi.string().required().error(new ApplicationError(2001, 'Fail: otp is required field and must be Email')),
        OTPProcessId: Joi.string().required().error(new ApplicationError(2001, 'Fail: OTPProcessId is required field and must be Email'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        'tem:CreateCustomer': {
          'tem:CreateCustomerRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Name': apiParams.Name,
            'rem:Gender': apiParams.Gender,
            'rem:Dob': apiParams.DOB,
            'rem:Address': apiParams.Address,
            'rem:Mobile': apiParams.mobile_number,
            'rem:State': apiParams.State,
            'rem:District': apiParams.District,
            'rem:City': apiParams.City,
            'rem:Nationality': apiParams.Nationality,
            'rem:Email': '',
            'rem:Employer': apiParams.Employer,
            'rem:IDType': apiParams.IDType,
            'rem:IDNumber': apiParams.IDNumber,
            'rem:IncomeSource': apiParams.IncomeSource,
            'rem:OTPProcessId': apiParams.OTPProcessId,
            'rem:OTP': apiParams.otp,
            // 'rem:CSPCode': credentials.AgentCode,
            'rem:CSPCode': apiParams.ma_user_id
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.CreateCustomer })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.CreateCustomer, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'response', fields: response })

      if (!response.CreateCustomerResponse || !response.CreateCustomerResponse.CreateCustomerResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message, 'a:CustomerId': CustomerId } = response.CreateCustomerResponse.CreateCustomerResult

      if (Code == '000') {
        const IDNumber = commonFns.maskValue({ IDNumber: apiParams.IDNumber }, 'IDNumber').IDNumber
        return {
          customer_provider_reference_id: CustomerId,
          customer_provider_status: 'Y',
          customer_kyc_status: 'N',
          customer_onboarding_status: 'N',
          customer_name: apiParams.Name,
          mobile_number: apiParams.mobile_number,
          customer_nationality: apiParams.Nationality,
          customer_address: `${apiParams.Address} ${apiParams.District} ${apiParams.City} ${apiParams.State}`,
          customer_data_json: JSON.stringify({ Address: apiParams.Address, District: apiParams.District, City: apiParams.City, State: apiParams.State, Email: apiParams.Email || '', Gender: apiParams.Gender, Employer: apiParams.Employer, IncomeSource: apiParams.IncomeSource, IDType: apiParams.IDType, IDNumber, DOB: apiParams.DOB }),
          customer_address_data: apiParams.Address,
          district: apiParams.District,
          city: apiParams.City,
          customer_state: apiParams.State,
          country: apiParams.Country || '',
          email: apiParams.Email || '',
          gender: apiParams.Gender,
          employer: apiParams.Employer,
          income_source: apiParams.IncomeSource,
          id_type: apiParams.IDType,
          id_number: IDNumber,
          dob: apiParams.DOB
        }
      }

      /* Default remitter limit  */
      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * SEND OTP
   * @param {{apiParams:{mobile_number:string,operation:string},params:Object,credentials:{username:string,password:string}}} param0
   */
  static async sendOTP ({ apiParams, credentials, connection, params = {} }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        operation: Joi.string().required().error(new ApplicationError(2001, 'Fail: operation is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        'tem:SendOTP': {
          'tem:SendOTPRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Operation': apiParams.operation,
            'rem:Mobile': apiParams.mobile_number,
            ...params
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.SendOTP })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: `${SOAP_ACTION.SendOTP}-${apiParams.operation.toUpperCase()}`, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'response', fields: response })

      if (!response.SendOTPResponse || !response.SendOTPResponse.SendOTPResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.SendOTPResponse.SendOTPResult

      if (Code == '000') {
        return response.SendOTPResponse.SendOTPResult
      }

      /* Default remitter limit  */
      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async generateKycLink ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        customer_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: customer_provider_reference_id is required field and must be number'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        CustomerId: apiParams.customer_provider_reference_id,
        PartnerUniqueRefNo: `APSPE${generateRandom(10)}` /* staging */
        // PartnerUniqueRefNo: `AIRPE${generateRandom(10)}` /* production */
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.EKYC_INITIATE })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.EKYC_INITIATE, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'response', fields: response })

      const { StatusCode, Url, ResponseMessage } = response

      if (StatusCode != '1') this.errorMessageHandler({ ResponseMessage })

      return { Url, ReferenceID: payLoad.PartnerUniqueRefNo, request: requestBody, response: responseBody }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async kycReferenceStatus ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        customer_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: customer_provider_reference_id is required field and must be number')),
        ReferenceID: Joi.string().required().error(new ApplicationError(2001, 'Fail: ReferenceID is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        CustomerId: apiParams.customer_provider_reference_id,
        PartnerUniqueRefNo: apiParams.ReferenceID
      }

      let AuthenticationToken = ''

      if (!apiParams.AuthenticationToken) {
        AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })
      }

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.EKYC_UNIQUE_REFSTATUS })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.EKYC_UNIQUE_REFSTATUS, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'response', fields: response })

      const { StatusCode } = response

      if (StatusCode == '1') return { verified: 'Y', ReferenceID: payLoad.PartnerUniqueRefNo }

      return { verified: 'N', ReferenceID: payLoad.PartnerUniqueRefNo }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async kycEnrollment ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'kycEnrollment', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        EncryptedPid: Joi.string().required().error(new ApplicationError(2001, 'Fail: EncryptedPid is required field and must be string')),
        EncryptedHmac: Joi.string().required().error(new ApplicationError(2001, 'Fail: EncryptedHmac is required field and must be string')),
        SessionKeyValue: Joi.string().required().error(new ApplicationError(2001, 'Fail: SessionKeyValue is required field and must be string')),
        CertificateIdentifier: Joi.number().required().error(new ApplicationError(2001, 'Fail: CertificateIdentifier is required field and must be number')),
        RegisteredDeviceServiceId: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceServiceId is required field and must be string')),
        RegisteredDeviceServiceVersion: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceServiceVersion is required field and must be string')),
        RegisteredDeviceProviderId: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceProviderId is required field and must be string')),
        RegisteredDeviceCode: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceCode is required field and must be string')),
        RegisteredDeviceModelId: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceModelId is required field and must be string')),
        RegisteredDevicePublicKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDevicePublicKey is required field and must be string')),
        customer_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: customer_provider_reference_id is required field and must be number')),
        ReferenceID: Joi.string().required().error(new ApplicationError(2001, 'Fail: ReferenceID is required field and must be string'))
      })

      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        EncryptedPid: apiParams.EncryptedPid,
        EncryptedHmac: apiParams.EncryptedHmac,
        SessionKeyValue: apiParams.SessionKeyValue,
        CertificateIdentifier: apiParams.CertificateIdentifier,
        RegisteredDeviceServiceId: apiParams.RegisteredDeviceServiceId,
        RegisteredDeviceServiceVersion: apiParams.RegisteredDeviceServiceVersion,
        RegisteredDeviceProviderId: apiParams.RegisteredDeviceProviderId,
        RegisteredDeviceCode: apiParams.RegisteredDeviceCode,
        RegisteredDeviceModelId: apiParams.RegisteredDeviceModelId,
        RegisteredDevicePublicKey: apiParams.RegisteredDevicePublicKey,
        CustomerId: apiParams.customer_provider_reference_id,
        PartnerUniqueRefNo: apiParams.ReferenceID
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.EKYC_ENROLLMENT })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.EKYC_ENROLLMENT, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      /* Timeout Case */
      if (api_status == 408) {
        const remitterDetail = await this.getRemitterDetail({ apiParams, credentials, connection: conn })
        if (remitterDetail.customer_kyc_status == 'Y') return true
        throw new ApplicationError(1001, 'Fail: Please Try After Sometime.')
      }

      if (api_status == 400) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'kycEnrollment', type: 'response', fields: response })

      const { StatusCode, ResponseMessage } = response

      if (StatusCode != '1') this.errorMessageHandler({ ResponseMessage })

      return true
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'kycEnrollment', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async remitterOnboarding ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        CustomerType: Joi.number().required().error(new ApplicationError(2001, 'Fail: CustomerType is required field and must be number')),
        SourceIncomeType: Joi.number().required().error(new ApplicationError(2001, 'Fail: SourceIncomeType is required field and must be number')),
        AnnualIncome: Joi.number().required().error(new ApplicationError(2001, 'Fail: AnnualIncome is required field and must be number')),
        customer_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: customerId is required field and must be number')),
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        sessionID: Joi.string().required().error(new ApplicationError(2001, 'Fail: sessionID is required field and must be string'))
      })

      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        CustomerType: apiParams.CustomerType,
        SourceIncomeType: apiParams.SourceIncomeType,
        AnnualIncome: apiParams.AnnualIncome,
        CustomerId: apiParams.customer_provider_reference_id,
        PartnerUniqueRefNo: `APSPC${generateRandom(10)}` /* staging */
        // PartnerUniqueRefNo: `AIRPC${generateRandom(10)}` /* production */
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.CUSTOMER_ONBOARDING })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.CUSTOMER_ONBOARDING, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'response', fields: response })

      const { StatusCode, Url, ResponseMessage } = response

      if (StatusCode != '1') this.errorMessageHandler({ ResponseMessage })

      return true
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async generateToken ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        UserName: credentials.username,
        Password: credentials.password
      }

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.GENERATE_TOKEN })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.GENERATE_TOKEN, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'response', fields: response })

      const { StatusCode, AccessToken, ResponseMessage } = response

      if (StatusCode != '1') this.errorMessageHandler({ ResponseMessage })

      return AccessToken
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'generateToken', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * TRANSFER SEND OTP
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async transactionSendOtp ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'transactionSendOtp', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        customer_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: customer_provider_reference_id is required field and must be number')),
        bene_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: bene_provider_reference_id is required field and must be number')),
        paymentMode: Joi.string().required().error(new ApplicationError(2001, 'Fail: paymentMode is required field and must be string')),
        amount: Joi.required().error(new ApplicationError(2001, 'Fail: amount is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      apiParams.operation = 'SendTransaction'
      const params = {
        'rem:CustomerId': apiParams.customer_provider_reference_id,
        'rem:ReceiverId': apiParams.bene_provider_reference_id,
        'rem:PaymentMode': MODE_CODE[apiParams.paymentMode],
        'rem:SendAmount': apiParams.amount
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return { 'a:Code': '000', 'a:Message': 'OTP Sent Successfully', 'a:ProcessId': '1afe0743-7373-4251-baff-7081bdadccd5' }
      }

      return await this.sendOTP({ apiParams, credentials, connection: conn, params })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'transactionSendOtp', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * TRANSFER RESEND OTP
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async transactionResendOtp ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'transactionResendOtp', type: 'request', fields: { apiParams, credentials } })
    return await this.transactionSendOtp({ apiParams, credentials, connection })
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async transfers ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        customer_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: customer_provider_reference_id is required field and must be number')),
        bene_provider_reference_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: bene_provider_reference_id is required field and must be number')),
        customer_data_json: Joi.required().error(new ApplicationError(2001, 'Fail: customer_data_json is required field')),
        beneficiary_details: Joi.required().error(new ApplicationError(2001, 'Fail: beneficiary_details is required field')),
        otp: Joi.string().required().error(new ApplicationError(2001, 'Fail: otp is required field and must be string')),
        OTPProcessId: Joi.string().required().error(new ApplicationError(2001, 'Fail: OTPProcessId is required field and must be string')),
        customer_nationality: Joi.string().required().error(new ApplicationError(2001, 'Fail: customer_nationality is required field and must be string')),
        beneficiary_name: Joi.string().required().error(new ApplicationError(2001, 'Fail: beneficiary_name is required field and must be string')),
        mode_code: Joi.string().required().error(new ApplicationError(2001, 'Fail: mode_code is required field and must be string')),
        amount: Joi.number().required().error(new ApplicationError(2001, 'Fail: amount is required field and must be string')),
        commission_amount: Joi.number().required().error(new ApplicationError(2001, 'Fail: commission_amount is required field and must be string')),
        request_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: request_number is required field and must be string')),
        RemittanceReason: Joi.string().required().error(new ApplicationError(2001, 'Fail: RemittanceReason is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      if (MODE_CODE[apiParams.mode_code] == MODE_CODE.DEPOSIT) {
        const apiParamsSchema = customJoi.object({
          account_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: account_number is required field and must be string')),
          ifsc_code: Joi.string().required().error(new ApplicationError(2001, 'Fail: ifsc_code is required field and must be string'))
        })
        await apiParamsSchema.validateAsync(apiParams)
      }

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const customerDataJson = (typeof apiParams.customer_data_json == 'string') ? JSON.parse(apiParams.customer_data_json) : apiParams.customer_data_json
      const beneDataJson = (typeof apiParams.beneficiary_details == 'string') ? JSON.parse(apiParams.beneficiary_details) : apiParams.beneficiary_details

      // const transfersCharge = this.transfersCharge({ apiParams, credentials, connection: conn })
      // logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'transfersCharge', fields: transfersCharge })

      const payLoad = {
        'tem:SendTransaction': {
          'tem:SendTransactionRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:CustomerId': apiParams.customer_provider_reference_id,
            'rem:SenderName': apiParams.customer_name,
            'rem:SenderGender': customerDataJson.Gender,
            'rem:SenderDoB': customerDataJson.DOB,
            'rem:SenderAddress': customerDataJson.Address,
            'rem:SenderPhone': '',
            'rem:SenderMobile': apiParams.mobile_number,
            'rem:SenderCity': customerDataJson.City,
            'rem:SenderDistrict': customerDataJson.District,
            'rem:SenderState': customerDataJson.State,
            'rem:SenderNationality': apiParams.customer_nationality,
            'rem:Employer': customerDataJson.Employer,
            'rem:SenderIDType': customerDataJson.IDType,
            'rem:SenderIDNumber': customerDataJson.IDNumber.substr(-4),
            'rem:SenderIDExpiryDate': '',
            'rem:SenderIDIssuedPlace': '',
            'rem:ReceiverId': apiParams.bene_provider_reference_id,
            'rem:ReceiverName': apiParams.beneficiary_name,
            'rem:ReceiverGender': beneDataJson.Gender,
            'rem:ReceiverAddress': beneDataJson.address,
            'rem:ReceiverMobile': apiParams.beneficiary_mobile,
            'rem:ReceiverCity': customerDataJson.City,
            'rem:SendCountry': 'India',
            'rem:PayoutCountry': 'Nepal',
            'rem:PaymentMode': MODE_CODE[apiParams.mode_code],
            'rem:CollectedAmount': (parseFloat(apiParams.amount) + parseFloat(apiParams.commission_amount)).toFixed(0),
            'rem:ServiceCharge': apiParams.commission_amount,
            'rem:SendAmount': parseFloat(apiParams.amount).toFixed(4),
            'rem:SendCurrency': 'INR',
            'rem:PayAmount': (parseFloat(apiParams.conversion_inr_rate) * parseFloat(apiParams.amount)).toFixed(4),
            'rem:PayCurrency': 'NPR',
            'rem:ExchangeRate': apiParams.conversion_inr_rate,
            'rem:BankBranchId': MODE_CODE[apiParams.mode_code] == MODE_CODE.CASH ? '' : apiParams.ifsc_code,
            'rem:AccountNumber': MODE_CODE[apiParams.mode_code] == MODE_CODE.CASH ? '' : apiParams.account_number,
            'rem:AccountType': '',
            'rem:NewAccountRequest': '',
            'rem:PartnerPinNo': apiParams.request_number,
            'rem:IncomeSource': customerDataJson.IncomeSource,
            'rem:RemittanceReason': apiParams.RemittanceReason,
            'rem:Relationship': beneDataJson.Relationship,
            // 'rem:CSPCode': credentials.AgentCode,
            'rem:CSPCode': apiParams.ma_user_id,
            'rem:OTPProcessId': apiParams.OTPProcessId,
            'rem:OTP': apiParams.otp
          }
        }
      }

      if (await this.mockApiFlag({ connection: conn })) {
        // throw new ApiError(1004, API_PROVIDER, 'OTPVefification Failed: Invalid Mobile OTP [002]' || 'Invalid OTP')
        return {
          provider_request: '{}',
          provider_response: '{}',
          provider_service_charges: apiParams.commission_amount,
          provider_gross_amount: parseFloat(apiParams.amount) + parseFloat(apiParams.commission_amount),
          provider_response_message: 'Success',
          request_number: apiParams.request_number,
          brn: 18730,
          rrn: 1111235388972640,
          transfer_status: TRANSFER_STATUS.HOLD.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.HOLD.TRANSACTION_STATUS,
          transfer_remarks: 'Success'
        }
        /* return {
          provider_gross_amount: parseFloat(apiParams.amount) + parseFloat(apiParams.commission_amount),
          provider_request: '{}',
          provider_response: '{}',
          provider_service_charges: apiParams.commission_amount,
          provider_response_message: 'Pending',
          request_number: apiParams.request_number,
          brn: '',
          rrn: '',
          transfer_status: TRANSFER_STATUS.PENDING.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.PENDING.TRANSACTION_STATUS,
          transfer_remarks: ''
        } */
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.SendTransaction })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.SendTransaction, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'response', fields: response })

      if (!response.SendTransactionResponse || !response.SendTransactionResponse.SendTransactionResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message, 'a:TrnsactionId': TrnsactionId, 'a:PinNo': PinNo } = response.SendTransactionResponse.SendTransactionResult

      /* SUCCESS */
      if (Code == '000') {
        return {
          provider_request: requestBody,
          provider_response: responseBody,
          provider_service_charges: apiParams.commission_amount,
          provider_gross_amount: parseFloat(apiParams.amount) + parseFloat(apiParams.commission_amount),
          provider_response_message: Message || '',
          request_number: apiParams.request_number,
          brn: TrnsactionId || '',
          rrn: PinNo || '',
          transfer_status: TRANSFER_STATUS.HOLD.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.HOLD.TRANSACTION_STATUS,
          transfer_remarks: Message || ''
        }
      }

      /* PENDING */
      if (Code == '777') {
        return {
          provider_gross_amount: parseFloat(apiParams.amount) + parseFloat(apiParams.commission_amount),
          provider_request: requestBody,
          provider_response: responseBody,
          provider_service_charges: apiParams.commission_amount,
          provider_response_message: Message || '',
          request_number: apiParams.request_number,
          brn: TrnsactionId || '',
          rrn: PinNo || '',
          transfer_status: TRANSFER_STATUS.PENDING.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.PENDING.TRANSACTION_STATUS,
          transfer_remarks: Message || ''
        }
      }

      /* INVALID OTP */
      if (Code == '071') {
        // throw new ApiError(1004, API_PROVIDER, Message || 'Invalid OTP')
        throw new ApplicationError(1004, 'Fail: Invalid OTP')
      }

      /* FAILED */
      return {
        provider_gross_amount: parseFloat(apiParams.amount) + parseFloat(apiParams.commission_amount),
        provider_request: requestBody,
        provider_response: responseBody,
        provider_service_charges: apiParams.commission_amount,
        provider_response_message: Message || '',
        request_number: apiParams.request_number,
        brn: TrnsactionId || '',
        rrn: PinNo || '',
        transfer_status: TRANSFER_STATUS.FAILED.STATUS_CODE,
        transaction_status: TRANSFER_STATUS.FAILED.TRANSACTION_STATUS,
        transfer_remarks: Message || ''
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async verifyTransfer ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        rrn: Joi.string().required().error(new ApplicationError(2001, 'Fail: rrn is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        'tem:VerifyTransaction': {
          'tem:VerifyTransactionRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:PinNo': apiParams.rrn
          }
        }
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return {
          transfer_status: TRANSFER_STATUS.APPROVED.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.APPROVED.TRANSACTION_STATUS
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.VerifyTransaction })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.VerifyTransaction, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'response', fields: response })

      if (!response.VerifyTransactionResponse || !response.VerifyTransactionResponse.VerifyTransactionResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.VerifyTransactionResponse.VerifyTransactionResult

      /* SUCCESS */
      if (Code == '000') {
        return {
          transfer_status: TRANSFER_STATUS.APPROVED.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.APPROVED.TRANSACTION_STATUS
        }
      }

      /* PENDING */
      if (Code == '777') {
        return {
          transfer_status: TRANSFER_STATUS.PENDING.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.PENDING.TRANSACTION_STATUS
        }
      }

      /* FAILED */
      if (Code != '000') {
        return {
          transfer_status: TRANSFER_STATUS.PENDING.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.PENDING.TRANSACTION_STATUS
        }
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async transferReQuery ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await credentialsSchema.validateAsync(credentials)

      const filterParams = {}

      if (apiParams.rrn) filterParams['rem:PinNo'] = apiParams.rrn
      if (!apiParams.rrn) filterParams['rem:PartnerPinNo'] = apiParams.request_number

      const payLoad = {
        'tem:SearchTransaction': {
          'tem:SearchTransactionRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            ...filterParams
          }
        }
      }

      if (await this.mockApiFlag({ connection: conn })) {
        // return {
        //   transfer_status: TRANSFER_STATUS.APPROVED.STATUS_CODE,
        //   transaction_status: TRANSFER_STATUS.APPROVED.TRANSACTION_STATUS
        // }
        return {
          transfer_status: TRANSFER_STATUS.HOLD.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.HOLD.TRANSACTION_STATUS,
          brn: 18730,
          rrn: 1111235388972640
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.SearchTransaction })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.SearchTransaction, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'response', fields: response })

      if (!response.SearchTransactionResponse || !response.SearchTransactionResponse.SearchTransactionResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message, 'a:Transactions': Transactions } = response.SearchTransactionResponse.SearchTransactionResult

      /* FAILED */
      if (Code == '666') {
        return {
          transfer_status: TRANSFER_STATUS.FAILED.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.FAILED.TRANSACTION_STATUS,
          provider_response_message: Message || ''
        }
      }
      /* FAILED */
      if (Code != '000') {
        return {
          transfer_status: TRANSFER_STATUS.PENDING.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.PENDING.TRANSACTION_STATUS,
          provider_response_message: Message || ''
        }
      }

      const { 'a:TxnStatus': txnStatus, 'a:TransactionId': TransactionId, 'a:PinNo': PinNo } = Transactions['a:Transaction']
      const STATUS = TRANSFER_STATUS[txnStatus.toUpperCase().replace(/\s/i, '_')]

      if (STATUS == undefined) {
        return {
          transfer_status: TRANSFER_STATUS.PENDING.STATUS_CODE,
          transaction_status: TRANSFER_STATUS.PENDING.TRANSACTION_STATUS,
          provider_response_message: Message || '',
          brn: TransactionId,
          rrn: PinNo
        }
      }

      return {
        transfer_status: STATUS.STATUS_CODE,
        transaction_status: STATUS.TRANSACTION_STATUS,
        provider_response_message: Message || '',
        brn: TransactionId,
        rrn: PinNo
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *  Transfer Charge
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async transfersCharge ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getRemitterDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        provider_branch_id: Joi.string().required().error(new ApplicationError(2001, 'Fail: mode_code is required field and must be string')),
        mode_code: Joi.string().required().error(new ApplicationError(2001, 'Fail: mode_code is required field and must be string')),
        amount: Joi.string().required().error(new ApplicationError(2001, 'Fail: amount is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        'tem:GetServiceCharge': {
          'tem:GetServiceChargeRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Country': 'Nepal',
            'rem:PaymentMode': MODE_CODE[apiParams.mode_code],
            'rem:TransferAmount': apiParams.mode_code == MODE_CODE.DEPOSIT ? apiParams.amount : '',
            'rem:PayoutAmount': apiParams.mode_code == MODE_CODE.CASH ? apiParams.amount : '',
            'rem:BankBranchId': apiParams.provider_branch_id
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.GetServiceCharge })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.GetServiceCharge, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'response', fields: response })

      if (!response.GetServiceChargeResponse || !response.GetServiceChargeResponse.GetServiceChargeResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.GetServiceChargeResponse.GetServiceChargeResult

      /* Default remitter limit  */
      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message || 'API Error')

      return response.GetServiceChargeResponse.GetServiceChargeResult
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getRemitterLimit', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  static async createBeneficiary ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'createBeneficiary', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await credentialsSchema.validateAsync(credentials)

      const apiParamsSchema = customJoi.object({
        ma_user_id: Joi.number().required().error(new ApplicationError(2001, 'Fail: ma_user_id is required field and must be number')),
        userid: Joi.number().required().error(new ApplicationError(2001, 'Fail: userid is required field and must be number')),
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        customerId: Joi.string().required().error(new ApplicationError(2001, 'Fail: customerID is a required field and must be a string')),
        beneficiary_name: Joi.string().required().error(new ApplicationError(2001, 'Fail: name is a required field and must be a string')),
        Gender: Joi.string().required().error(new ApplicationError(2001, 'Fail: Gender is a required field')),
        beneficiary_mobile: Joi.string().required().error(new ApplicationError(2001, 'Fail: Mobile is a required field and must be a string')),
        Relationship: Joi.string().required().error(new ApplicationError(2001, 'Fail: Relationship is required')),
        address: Joi.string().required().error(new ApplicationError(2001, 'Fail: Address is required')),
        PaymentMode: Joi.string().required().error(new ApplicationError(2001, 'Fail: Payment Mode is required')),
        BankBranchId: Joi.string().optional(),
        account_number: Joi.string().optional()
      })

      await apiParamsSchema.validateAsync(apiParams)

      const payLoad = {
        'tem:CreateReceiver': {
          'tem:CreateReceiverRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:CustomerId': apiParams.customerId,
            'rem:Name': apiParams.beneficiary_name,
            'rem:Gender': apiParams.Gender,
            'rem:Mobile': apiParams.beneficiary_mobile,
            'rem:Relationship': apiParams.Relationship,
            'rem:Address': apiParams.address,
            'rem:PaymentMode': apiParams.PaymentMode,
            'rem:BankBranchId': apiParams.BankBranchId || '',
            'rem:AccountNumber': apiParams.account_number || '',
            'rem:OTPProcessId': '',
            'rem:OTP': ''
          }
        }
      }
      // if (apiParams.account_number && apiParams.BankBranchId) {
      //   payLoad['tem:CreateReceiver']['tem:CreateReceiverRequest']['rem:AccountNumber'] = apiParams.account_number
      //   payLoad['tem:CreateReceiver']['tem:CreateReceiverRequest']['rem:BankBranchId'] = apiParams.BankBranchId
      // }

      // const response = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.SendOTP })
      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.CreateReceiver })
      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.CreateReceiver, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'createBeneficiary', type: 'response', fields: response })

      if (!response.CreateReceiverResponse || !response.CreateReceiverResponse.CreateReceiverResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message, 'a:ReceiverId': ReceiverId } = response.CreateReceiverResponse.CreateReceiverResult

      if (Code == '000') {
        // { 'a:Code': '000', 'a:Message': 'Success', 'a:ReceiverId': 12704 }

        return {
          bene_provider_mapping_status: 'Y',
          bene_provider_reference_id: ReceiverId,
          beneficiary_details: {
            Gender: apiParams.Gender,
            Relationship: apiParams.Relationship,
            address: apiParams.address,
            name: apiParams.beneficiary_name,
            beneficiary_mobile: apiParams.beneficiary_mobile,
            bankBranchName: apiParams.bankBranchName ? apiParams.bankBranchName : null,
            bank_name: apiParams.bank_name ? apiParams.bank_name : null,
            country: apiParams.country
          },
          paymentCode: PARTNER_PAYMENT_MODE_CODE[apiParams.PaymentMode.toString().toUpperCase()],
          gender: apiParams.Gender,
          beneficiary_address: apiParams.address,
          bank_branch_name: apiParams.bankBranchName ? apiParams.bankBranchName : null,
          bank_name: apiParams.bank_name ? apiParams.bank_name : null,
          country: apiParams.country
        }
      }

      /* Default remitter limit  */
      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'createBeneficiary', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  static async cancelTransaction ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'cancelTransaction', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection

    try {
      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await credentialsSchema.validateAsync(credentials)

      const apiParamsSchema = customJoi.Object({
        transaction_pin: Joi.string().required().error(new ApplicationError(2001, 'Fail: Transaction Pin required')),
        cancellationReason: Joi.string().required().error(new ApplicationError(2001, 'Fail: Cancellation reason is required field')),
        otp: Joi.string().required().error(new ApplicationError(2001, 'Fail: otp is required field and must be Email')),
        OTPProcessId: Joi.string().required().error(new ApplicationError(2001, 'Fail: OTPProcessId is required field and must be Email'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const payLoad = {
        '<tem:CancelTransaction>': {
          '<tem:CancelTransactionRequest>': {
            '<rem:UserName>': credentials.username,
            '<rem:Password>': credentials.password,
            '<rem:PinNo>': apiParams.transaction_pin,
            '<rem:ReasonForCancellation>': apiParams.cancellationReason,
            '<rem:OTPProcessId>': apiParams.OTPProcessId,
            '<rem:OTP>': apiParams.otp
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.CancelTransaction })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: SOAP_ACTION.CancelTransaction, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'cancelTransaction', type: 'response', fields: response })

      if (!response.CancelTransactionResponse || !response.CancelTransactionResponse.CancelTransactionResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message, 'a:PinNo': PinNo } = response.CancelTransactionResponse.CancelTransactionResult

      if (Code === '000') {
        return 'Transaction cancelled!'
      }

      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'cancelTransaction', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * REST API Error Messsage Handler
   * @param {{ResponseMessage:string}} param0
   */
  static errorMessageHandler ({ ResponseMessage }) {
    let parseResponseMessage = {}
    try {
      parseResponseMessage = JSON.parse(ResponseMessage)
    } catch (error) {
      if (typeof ResponseMessage == 'string') throw new ApiError(10001, API_PROVIDER, ResponseMessage)
      throw new ApiError(10001, API_PROVIDER, 'API Error')
    }
    if (parseResponseMessage.errorres && parseResponseMessage.errorres.description) throw new ApiError(10001, API_PROVIDER, parseResponseMessage.errorres.description)
    throw new ApiError(10001, API_PROVIDER, 'API Error')
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async getMerchantDetail ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetail', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        MobileNumber: apiParams.mobile_number
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.SEARCH_CSP })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.SEARCH_CSP, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetail', type: 'response', fields: response })

      const { StatusCode, CSPDetail } = response

      if (StatusCode != '1' || CSPDetail.length == 0) return {}
      const merchant = CSPDetail[0]

      const merchantFields = {
        registration_status: 'Y',
        kyc_status: merchant.EKYAStatus == 'Verified' ? 'Y' : 'N',
        onboarding_status: merchant.RBLOnboardingStatus == 'Success' ? 'Y' : 'N',
        pan_validation_status: merchant.RBLPANValidationStatus == 'Success' ? 'Y' : 'N',
        otp_consent_status: merchant.OTPConsentStatus == 'Success' ? 'Y' : 'N',
        provider_branch_code: merchant.BranchCode,
        Category: merchant.Category,
        Fathernameorspousename: merchant.FatherNameOrSpouseName,
        Physicallyhandicapped: merchant.PhysicallyHandicapped,
        Alternateoccupationtype: merchant.AlternateOccupationType,
        Highesteducationqualification: merchant.HighestEducationQualification,
        Operatinghoursfrom: merchant.OperatingHoursFrom,
        Operatinghoursto: merchant.OperatingHoursTo,
        Course: merchant.Course,
        Dateofpassing: moment(merchant.DateOfPassing, 'MM/DD/YYYY').format('MM/DD/YYYY'),
        Dob: moment(merchant.DOB, 'MM/DD/YYYY').format('MM/DD/YYYY'),
        Institutename: merchant.InstituteName,
        Devicename: merchant.DeviceName,
        Connectivitytype: merchant.ConnectivityType,
        Provider: merchant.Provider,
        Entitytype: merchant.EntityType,
        Expectedannualturnover: merchant.ExpectedAnnualTurnover,
        Expectedannualincome: merchant.ExpectedAnnualIncome,
        Shopaddress: merchant.ShopAddress,
        Shoparea: merchant.ShopArea,
        Shopcity: merchant.ShopCity,
        Shopdistrict: merchant.ShopDistrict,
        Shopstate: merchant.ShopState,
        Shoppincode: merchant.ShopPinCode,
        CorporateindividualBC: merchant.CorporateIndividualBC
      }

      return { ...merchantFields, ...merchant }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getMerchantDetail', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * SEND OTP
   * @param {{apiParams:{mobile_number:string,operation:string},params:Object,credentials:{username:string,password:string}}} param0
   */
  static async sendMerchantOTP ({ apiParams, credentials, connection, params = {} }) {
    logs.logger({ pagename: path.basename(__filename), action: 'sendMerchantOTP', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendMerchantOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        operation: Joi.string().required().error(new ApplicationError(2001, 'Fail: operation is required field and must be string')),
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        'tem:SendOTP': {
          'tem:SendOTPRequest': {
            'rem:UserName': credentials.username,
            'rem:Password': credentials.password,
            'rem:Operation': apiParams.operation,
            'rem:Mobile': apiParams.mobile_number,
            ...params
          }
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.XMLRequest({ payLoad, SOAPAction: SOAP_ACTION.SendOTP })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: `${SOAP_ACTION.SendOTP}-${apiParams.operation.toUpperCase()}`, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'sendMerchantOTP', type: 'response', fields: response })

      if (!response.SendOTPResponse || !response.SendOTPResponse.SendOTPResult) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { 'a:Code': Code, 'a:Message': Message } = response.SendOTPResponse.SendOTPResult

      if (Code == '000') {
        return response.SendOTPResponse.SendOTPResult
      }

      /* Default remitter limit  */
      if (Code != '000') throw new ApiError(10001, API_PROVIDER, Message || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'sendMerchantOTP', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * MERCHANT RESEND REGISTERED OTP
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async sendMerchantRegisterOTP ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'sendMerchantRegisterOTP', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        firstname: Joi.string().required().error(new ApplicationError(2001, 'Fail: firstname is required field and must be string')),
        lastname: Joi.string().required().error(new ApplicationError(2001, 'Fail: lastname is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      apiParams.operation = 'CreateCSP'
      const params = {
        'rem:CSPMobile': apiParams.mobile_number,
        'rem:CSPName': `${apiParams.firstname} ${apiParams.lastname}`
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return { 'a:Code': '000', 'a:Message': 'OTP Sent Successfully', 'a:ProcessId': '1afe0743-7373-4251-baff-7081bdadccd5' }
      }

      return await this.sendMerchantOTP({ apiParams, credentials, connection: conn, params })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'sendMerchantRegisterOTP', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   * MERCHANT RESEND REGISTERED OTP
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string,AgentCode:number}}} param0
   */
  static async resendMerchantRegisterOTP ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'resendMerchantRegisterOTP', type: 'request', fields: { apiParams, credentials } })
    return await this.sendMerchantRegisterOTP({ apiParams, credentials, connection })
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async addMerchant ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'addMerchant', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        FirstName: Joi.string().required().error(new ApplicationError(2001, 'Fail: FirstName is required field and must be string')),
        // LastName: Joi.string().required().error(new ApplicationError(2001, 'Fail: LastName is required field and must be string')),
        // Middlename: Joi.string().required().error(new ApplicationError(2001, 'Fail: MiddleName is required field and must be string')),
        // GSTIN: Joi.string().required().error(new ApplicationError(2001, 'Fail: GSTIN is required field and must be string')),
        Companyname: Joi.string().required().error(new ApplicationError(2001, 'Fail: CompanyName is required field and must be string')),
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        Localaddress: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalAddress is required field and must be string')),
        Localarea: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalArea is required field and must be string')),
        Localcity: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalCity is required field and must be string')),
        Localdistrict: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalDistrict is required field and must be string')),
        Localstate: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalState is required field and must be string')),
        Emailid: Joi.string().required().error(new ApplicationError(2001, 'Fail: EmailId is required field and must be string')),
        Localpincode: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalPinCode is required field and must be string')),
        Dob: Joi.string().required().error(new ApplicationError(2001, 'Fail: DOB is required field and must be string')),
        Pancard: Joi.string().required().error(new ApplicationError(2001, 'Fail: PanCard is required field and must be string')),
        Shopaddress: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopAddress is required field and must be string')),
        Shoparea: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopArea is required field and must be string')),
        Shopcity: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopCity is required field and must be string')),
        Shopdistrict: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopDistrict is required field and must be string')),
        Shopstate: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopState is required field and must be string')),
        Shoppincode: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopPinCode is required field and must be string')),
        Ifsccode: Joi.string().required().error(new ApplicationError(2001, 'Fail: IFSCCode is required field and must be string')),
        Accountnumber: Joi.string().required().error(new ApplicationError(2001, 'Fail: AccountNumber is required field and must be string')),
        Agentaccountname: Joi.string().required().error(new ApplicationError(2001, 'Fail: AgentAccountName is required field and must be string')),
        Gender: Joi.string().required().error(new ApplicationError(2001, 'Fail: Gender is required field and must be string')),
        Category: Joi.string().required().error(new ApplicationError(2001, 'Fail: Category is required field and must be string')),
        Fathernameorspousename: Joi.string().required().error(new ApplicationError(2001, 'Fail: FatherNameOrSpouseName is required field and must be string')),
        Physicallyhandicapped: Joi.string().required().error(new ApplicationError(2001, 'Fail: PhysicallyHandicapped is required field and must be string')),
        Alternateoccupationtype: Joi.string().required().error(new ApplicationError(2001, 'Fail: AlternateOccupationType is required field and must be string')),
        Highesteducationqualification: Joi.string().required().error(new ApplicationError(2001, 'Fail: HighestEducationQualification is required field and must be string')),
        Operatinghoursfrom: Joi.string().required().error(new ApplicationError(2001, 'Fail: OperatingHoursFrom is required field and must be string')),
        Operatinghoursto: Joi.string().required().error(new ApplicationError(2001, 'Fail: OperatingHoursTo is required field and must be string')),
        Course: Joi.string().required().error(new ApplicationError(2001, 'Fail: Course is required field and must be string')),
        // Dateofpassing: Joi.string().required().error(new ApplicationError(2001, 'Fail: DateOfPassing is required field and must be string')),
        // Institutename: Joi.string().required().error(new ApplicationError(2001, 'Fail: InstituteName is required field and must be string')),
        Connectivitytype: Joi.string().required().error(new ApplicationError(2001, 'Fail: ConnectivityType is required field and must be string')),
        Provider: Joi.string().required().error(new ApplicationError(2001, 'Fail: Provider is required field and must be string')),
        Entitytype: Joi.string().required().error(new ApplicationError(2001, 'Fail: EntityType is required field and must be string')),
        Weeklyoff: Joi.string().required().error(new ApplicationError(2001, 'Fail: WeeklyOff is required field and must be string')),
        Bankname: Joi.string().required().error(new ApplicationError(2001, 'Fail: BankName is required field and must be string')),
        Branchname: Joi.string().required().error(new ApplicationError(2001, 'Fail: BranchName is required field and must be string')),
        Expectedannualturnover: Joi.number().required().error(new ApplicationError(2001, 'Fail: ExpectedAnnualTurnover is required field and must be number')),
        Expectedannualincome: Joi.number().required().error(new ApplicationError(2001, 'Fail: ExpectedAnnualIncome is required field and must be number'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        IsOwnBranch: 'N',
        GSTIN: apiParams.GSTIN || '',
        IsMainBranch: 'N',
        OTPProcessId: apiParams.OTPProcessId,
        OTP: apiParams.otp,
        PartnerIDCode: apiParams.ma_user_id,
        Firstname: apiParams.FirstName,
        Middlename: apiParams.Middlename || '',
        Lastname: apiParams.LastName || '',
        Companyname: apiParams.Companyname,
        Mobilenumber: apiParams.mobile_number,
        Localaddress: apiParams.Localaddress,
        Localarea: apiParams.Localarea,
        Localcity: apiParams.Localcity,
        Localdistrict: apiParams.Localdistrict,
        Localstate: apiParams.Localstate,
        Localpincode: apiParams.Localpincode,
        Telephone: apiParams.mobile_number,
        Alternatenumber: apiParams.mobile_number,
        Emailid: apiParams.Emailid,
        Dob: apiParams.Dob,
        Pancard: apiParams.Pancard,
        Shopaddress: apiParams.Shopaddress,
        Shoparea: apiParams.Shoparea,
        Shopcity: apiParams.Shopcity,
        Shopdistrict: apiParams.Shopdistrict,
        Shopstate: apiParams.Shopstate,
        Shoppincode: apiParams.Shoppincode,
        Ifsccode: apiParams.Ifsccode,
        Accountnumber: apiParams.Accountnumber,
        Agentaccountname: apiParams.Agentaccountname,
        Gender: apiParams.Gender,
        Category: apiParams.Category,
        Fathernameorspousename: apiParams.Fathernameorspousename,
        Physicallyhandicapped: apiParams.Physicallyhandicapped,
        Alternateoccupationtype: apiParams.Alternateoccupationtype,
        Alternateoccupationdescription: apiParams.Alternateoccupationdescription || '',
        Highesteducationqualification: apiParams.Highesteducationqualification,
        CorporateindividualBC: 'Individual',
        Operatinghoursfrom: apiParams.Operatinghoursfrom,
        Operatinghoursto: apiParams.Operatinghoursto,
        Course: apiParams.Course,
        Dateofpassing: apiParams.Dateofpassing || '',
        Institutename: apiParams.Institutename || '',
        Devicename: apiParams.Devicename,
        Connectivitytype: apiParams.Connectivitytype,
        Provider: apiParams.Provider,
        Entitytype: apiParams.Entitytype,
        Weeklyoff: apiParams.Weeklyoff,
        Bankname: apiParams.Bankname,
        Branchname: apiParams.Branchname,
        ListofotherbankstheCSPworkswith: 'NA',
        Natureofbusiness: 'Individual',
        Expectedannualturnover: apiParams.Expectedannualturnover,
        Expectedannualincome: apiParams.Expectedannualincome
      }

      this.removeSpecialCharacters({ object: payLoad })

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return {
          registration_status: 'Y',
          kyc_status: 'N',
          onboarding_status: 'N',
          pan_validation_status: 'N',
          otp_consent_status: 'N',
          provider_branch_code: 13299,
          ...payLoad
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.CREATE_CSP })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.CREATE_CSP, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'addMerchant', type: 'response', fields: response })

      const { StatusCode, ResponseMessage } = response

      if (StatusCode != '1') throw new ApiError(10001, API_PROVIDER, ResponseMessage || 'API Error')

      return {
        registration_status: 'Y',
        kyc_status: response.EKYAStatus == 'Verified' ? 'Y' : 'N',
        onboarding_status: response.OnBoardingStatus == 'Success' ? 'Y' : 'N',
        pan_validation_status: response.PANValidationStatus == 'Success' ? 'Y' : 'N',
        otp_consent_status: response.OTPConsentStatus == 'Success' ? 'Y' : 'N',
        provider_branch_code: response.BranchCode,
        ...payLoad
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'addMerchant', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:Object,credentials:{username:string,password:string, connection}}} param0
   * @returns
   */
  static async merchantKycReferenceStatus ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantKycReferenceStatus', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'sendOTP', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        provider_branch_code: Joi.number().required().error(new ApplicationError(2001, 'Fail: provider_branch_code is required field and must be string')),
        PartnerUniqueRefNo: Joi.string().required().error(new ApplicationError(2001, 'Fail: PartnerUniqueRefNo is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))

      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        PartnerUniqueRefNo: apiParams.PartnerUniqueRefNo,
        BranchCode: apiParams.provider_branch_code
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return { verified: 'Y', ReferenceID: payLoad.PartnerUniqueRefNo }
      }

      let AuthenticationToken = ''

      if (!apiParams.AuthenticationToken) {
        AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })
      }

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.MERCHANT_EKYC_UNIQUE_REFSTATUS })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.MERCHANT_EKYC_UNIQUE_REFSTATUS, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'merchantKycReferenceStatus', type: 'response', fields: response })

      const { StatusCode } = response

      if (StatusCode == '1') return { verified: 'Y', ReferenceID: payLoad.PartnerUniqueRefNo }

      return { verified: 'N', ReferenceID: payLoad.PartnerUniqueRefNo }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantKycReferenceStatus', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async merchantGenerateKycLink ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantGenerateKycLink', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantGenerateKycLink', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        provider_branch_code: Joi.number().required().error(new ApplicationError(2001, 'Fail: provider_branch_code is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        PartnerUniqueRefNo: `APSPE${generateRandom(10)}`, /* staging */
        // PartnerUniqueRefNo: `AIRPE${generateRandom(10)}` /* production */
        BranchCode: apiParams.provider_branch_code
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.MERCHANT_EKYC_INITIATE })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.MERCHANT_EKYC_INITIATE, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'merchantGenerateKycLink', type: 'response', fields: response })

      const { StatusCode, Url, ResponseMessage } = response

      if (StatusCode != '1') this.errorMessageHandler({ ResponseMessage })

      return { Url, ReferenceID: payLoad.PartnerUniqueRefNo, request: requestBody, response: responseBody }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantGenerateKycLink', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async kycMerchantEnrollment ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        EncryptedPid: Joi.string().required().error(new ApplicationError(2001, 'Fail: EncryptedPid is required field and must be string')),
        EncryptedHmac: Joi.string().required().error(new ApplicationError(2001, 'Fail: EncryptedHmac is required field and must be string')),
        SessionKeyValue: Joi.string().required().error(new ApplicationError(2001, 'Fail: SessionKeyValue is required field and must be string')),
        CertificateIdentifier: Joi.number().required().error(new ApplicationError(2001, 'Fail: CertificateIdentifier is required field and must be number')),
        RegisteredDeviceServiceId: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceServiceId is required field and must be string')),
        RegisteredDeviceServiceVersion: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceServiceVersion is required field and must be string')),
        RegisteredDeviceProviderId: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceProviderId is required field and must be string')),
        RegisteredDeviceCode: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceCode is required field and must be string')),
        RegisteredDeviceModelId: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDeviceModelId is required field and must be string')),
        RegisteredDevicePublicKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: RegisteredDevicePublicKey is required field and must be string')),
        PartnerUniqueRefNo: Joi.string().required().error(new ApplicationError(2001, 'Fail: PartnerUniqueRefNo is required field and must be string')),
        provider_branch_code: Joi.number().required().error(new ApplicationError(2001, 'Fail: provider_branch_code is required field and must be string'))

      })

      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        EncryptedPid: apiParams.EncryptedPid,
        EncryptedHmac: apiParams.EncryptedHmac,
        SessionKeyValue: apiParams.SessionKeyValue,
        CertificateIdentifier: apiParams.CertificateIdentifier,
        RegisteredDeviceServiceId: apiParams.RegisteredDeviceServiceId,
        RegisteredDeviceServiceVersion: apiParams.RegisteredDeviceServiceVersion,
        RegisteredDeviceProviderId: apiParams.RegisteredDeviceProviderId,
        RegisteredDeviceCode: apiParams.RegisteredDeviceCode,
        RegisteredDeviceModelId: apiParams.RegisteredDeviceModelId,
        RegisteredDevicePublicKey: apiParams.RegisteredDevicePublicKey,
        PartnerUniqueRefNo: apiParams.PartnerUniqueRefNo,
        BranchCode: apiParams.provider_branch_code
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.MERCHANT_EKYC_ENROLLMENT })

      if (await this.mockApiFlag({ connection: conn })) {
        return true
      }

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.MERCHANT_EKYC_ENROLLMENT, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      /* Timeout Case */
      if (api_status == 408) {
        const merchantDetail = await this.getMerchantDetail({ apiParams, credentials, connection: conn })
        if (merchantDetail.kyc_status == 'Y') return true
        throw new ApplicationError(408, 'Fail: Please Try After Sometime.')
      }

      if (api_status == 400) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'response', fields: response })

      const { StatusCode, ResponseMessage } = response

      if (StatusCode != '1') this.errorMessageHandler({ ResponseMessage })

      return true
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async bioKYCRequery ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'request', fields: { apiParams, credentials } })

      const apiParamsSchema = customJoi.object({
        PartnerUniqueRefNo: Joi.string().required().error(new ApplicationError(2001, 'Fail: PartnerUniqueRefNo is required field and must be string')),
        BranchCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: BranchCode is required field and must be string'))
      })

      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        PartnerUniqueRefNo: apiParams.PartnerUniqueRefNo,
        BranchCode: apiParams.BranchCode
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.BIO_KYC_REQUERY })

      if (await this.mockApiFlag({ connection: conn })) {
        return true
      }

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.BIO_KYC_REQUERY, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const { status, responsemessage } = response

      if (status == '1' && responsemessage.toLowerCase() == 'approved') return true

      throw new ApiError(10001, API_PROVIDER, responsemessage || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'kycMerchantEnrollment', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async merchantOnboarding ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantOnboarding', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        FirstName: Joi.string().required().error(new ApplicationError(2001, 'Fail: FirstName is required field and must be string')),
        // LastName: Joi.string().required().error(new ApplicationError(2001, 'Fail: LastName is required field and must be string')),
        // Middlename: Joi.string().required().error(new ApplicationError(2001, 'Fail: MiddleName is required field and must be string')),
        // GSTIN: Joi.string().required().error(new ApplicationError(2001, 'Fail: GSTIN is required field and must be string')),
        Companyname: Joi.string().required().error(new ApplicationError(2001, 'Fail: CompanyName is required field and must be string')),
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        Localaddress: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalAddress is required field and must be string')),
        Localarea: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalArea is required field and must be string')),
        Localcity: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalCity is required field and must be string')),
        Localdistrict: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalDistrict is required field and must be string')),
        Localstate: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalState is required field and must be string')),
        Emailid: Joi.string().required().error(new ApplicationError(2001, 'Fail: EmailId is required field and must be string')),
        Localpincode: Joi.string().required().error(new ApplicationError(2001, 'Fail: LocalPinCode is required field and must be string')),
        Dob: Joi.string().required().error(new ApplicationError(2001, 'Fail: DOB is required field and must be string')),
        Pancard: Joi.string().required().error(new ApplicationError(2001, 'Fail: PanCard is required field and must be string')),
        Shopaddress: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopAddress is required field and must be string')),
        Shoparea: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopArea is required field and must be string')),
        Shopcity: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopCity is required field and must be string')),
        Shopdistrict: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopDistrict is required field and must be string')),
        Shopstate: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopState is required field and must be string')),
        Shoppincode: Joi.string().required().error(new ApplicationError(2001, 'Fail: ShopPinCode is required field and must be string')),
        Ifsccode: Joi.string().required().error(new ApplicationError(2001, 'Fail: IFSCCode is required field and must be string')),
        Accountnumber: Joi.string().required().error(new ApplicationError(2001, 'Fail: AccountNumber is required field and must be string')),
        Agentaccountname: Joi.string().required().error(new ApplicationError(2001, 'Fail: AgentAccountName is required field and must be string')),
        Gender: Joi.string().required().error(new ApplicationError(2001, 'Fail: Gender is required field and must be string')),
        Category: Joi.string().required().error(new ApplicationError(2001, 'Fail: Category is required field and must be string')),
        Fathernameorspousename: Joi.string().required().error(new ApplicationError(2001, 'Fail: FatherNameOrSpouseName is required field and must be string')),
        Physicallyhandicapped: Joi.string().required().error(new ApplicationError(2001, 'Fail: PhysicallyHandicapped is required field and must be string')),
        Alternateoccupationtype: Joi.string().required().error(new ApplicationError(2001, 'Fail: AlternateOccupationType is required field and must be string')),
        Highesteducationqualification: Joi.string().required().error(new ApplicationError(2001, 'Fail: HighestEducationQualification is required field and must be string')),
        Operatinghoursfrom: Joi.string().required().error(new ApplicationError(2001, 'Fail: OperatingHoursFrom is required field and must be string')),
        Operatinghoursto: Joi.string().required().error(new ApplicationError(2001, 'Fail: OperatingHoursTo is required field and must be string')),
        Course: Joi.string().required().error(new ApplicationError(2001, 'Fail: Course is required field and must be string')),
        // Dateofpassing: Joi.string().required().error(new ApplicationError(2001, 'Fail: DateOfPassing is required field and must be string')),
        // Institutename: Joi.string().required().error(new ApplicationError(2001, 'Fail: InstituteName is required field and must be string')),
        Connectivitytype: Joi.string().required().error(new ApplicationError(2001, 'Fail: ConnectivityType is required field and must be string')),
        Provider: Joi.string().required().error(new ApplicationError(2001, 'Fail: Provider is required field and must be string')),
        Entitytype: Joi.string().required().error(new ApplicationError(2001, 'Fail: EntityType is required field and must be string')),
        Weeklyoff: Joi.string().required().error(new ApplicationError(2001, 'Fail: WeeklyOff is required field and must be string')),
        Bankname: Joi.string().required().error(new ApplicationError(2001, 'Fail: BankName is required field and must be string')),
        Branchname: Joi.string().required().error(new ApplicationError(2001, 'Fail: BranchName is required field and must be string')),
        Expectedannualturnover: Joi.number().required().error(new ApplicationError(2001, 'Fail: ExpectedAnnualTurnover is required field and must be number')),
        Expectedannualincome: Joi.number().required().error(new ApplicationError(2001, 'Fail: ExpectedAnnualIncome is required field and must be number')),
        provider_branch_code: Joi.number().required().error(new ApplicationError(2001, 'Fail: provider_branch_code is required field and must be string'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        PartnerIDCode: apiParams.ma_user_id,
        Firstname: apiParams.FirstName,
        Middlename: apiParams.Middlename || '',
        Lastname: apiParams.LastName || '',
        CompanyName: apiParams.Companyname,
        MobileNumber: apiParams.mobile_number,
        LocalAddress: apiParams.Localaddress,
        Localarea: apiParams.Localarea,
        LocalCity: apiParams.Localcity,
        LocalDistrict: apiParams.Localdistrict,
        LocalState: apiParams.Localstate,
        LocalPinCode: apiParams.Localpincode,
        Telephone: apiParams.mobile_number,
        AlternateNumber: apiParams.mobile_number,
        EmailId: apiParams.Emailid,
        DOB: apiParams.Dob,
        PanCard: apiParams.Pancard,
        ShopAddress: apiParams.Shopaddress,
        ShopArea: apiParams.Shoparea,
        ShopCity: apiParams.Shopcity,
        ShopDistrict: apiParams.Shopdistrict,
        ShopState: apiParams.Shopstate,
        ShopPincode: apiParams.Shoppincode,
        IFSCCode: apiParams.Ifsccode,
        AccountNumber: apiParams.Accountnumber,
        AgentAccountName: apiParams.Agentaccountname,
        Gender: apiParams.Gender,
        Category: apiParams.Category,
        FatherNameOrSpouseName: apiParams.Fathernameorspousename,
        PhysicallyHandicapped: apiParams.Physicallyhandicapped,
        AlternateOccupationType: apiParams.Alternateoccupationtype,
        AlternateOccupationDescription: apiParams.Alternateoccupationdescription || '',
        HighestEducationQualification: apiParams.Highesteducationqualification,
        CorporateindividualBC: 'Individual',
        OperatingHoursFrom: apiParams.Operatinghoursfrom,
        OperatingHoursTo: apiParams.Operatinghoursto,
        Course: apiParams.Course,
        DateOfPassing: apiParams.Dateofpassing || '',
        InstituteName: apiParams.Institutename || '',
        DeviceName: apiParams.Devicename,
        ConnectivityType: apiParams.Connectivitytype,
        Provider: apiParams.Provider,
        EntityType: apiParams.Entitytype,
        WeeklyOff: apiParams.Weeklyoff,
        BankName: apiParams.Bankname,
        BranchName: apiParams.Branchname,
        ListofotherbankstheCSPworkswith: 'NA',
        Natureofbusiness: 'Individual',
        ExpectedAnnualTurnover: apiParams.Expectedannualturnover,
        ExpectedAnnualIncome: apiParams.Expectedannualincome,
        PartnerUniqueRefNo: `APSPC${generateRandom(10)}`, /* staging */
        // PartnerUniqueRefNo: `AIRPC${generateRandom(10)}`, /* production */
        BranchCode: apiParams.provider_branch_code
      }

      this.removeSpecialCharacters({ object: payLoad })

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      if (await this.mockApiFlag({ connection: conn }) && apiParams.ma_user_id == '28627') {
        throw new ApiError(CONFIG.MERCHANT_PMT_RETRY_CODE, API_PROVIDER, 'PAN Validation Failed')
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return {
          onboarding_status: 'Y',
          pan_validation_status: 'Y',
          PANValidationstatus: 'FAILED',
          ...apiParams
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.MERCHANT_ONBOARDING })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.MERCHANT_ONBOARDING, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'merchantOnboarding', type: 'response', fields: response })

      const { StatusCode, ResponseMessage, OnBoardingStatus, PANValidationstatus } = response

      /* BIO KCY Requery Case */
      if (StatusCode == '0' && ResponseMessage.toLowerCase() == 'aadhaarrefkey not generated.') {
        return await this.retryMerchantOnboarding({ payLoad, payLoadHeader, apiParams, credentials, connection: conn })
      }

      if (StatusCode != '1') throw new ApiError(10001, API_PROVIDER, ResponseMessage)

      if (PANValidationstatus && PANValidationstatus.toLowerCase().indexOf('reinitiate')) throw new ApiError(CONFIG.MERCHANT_PMT_RETRY_CODE, API_PROVIDER, PANValidationstatus || 'PAN Validation Failed')

      return {
        onboarding_status: OnBoardingStatus == 'Success' ? 'Y' : 'N',
        pan_validation_status: PANValidationstatus == 'Success' ? 'Y' : 'N',
        PANValidationstatus,
        ...apiParams
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantOnboarding', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{payLoad, payLoadHeader, apiParams, credentials, connection}} param0
   */
  static async retryMerchantOnboarding ({ payLoad, payLoadHeader, apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantOnboarding', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const bioKYCRequeryResp = await this.bioKYCRequery({ apiParams: apiParams.kycBioRequest, credentials, connection: conn })
      logs.logger({ pagename: path.basename(__filename), action: 'merchantOnboarding', type: 'bioKYCRequeryResp', fields: bioKYCRequeryResp })
      if (!bioKYCRequeryResp) throw new ApiError(10001, API_PROVIDER, 'API Error')

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.MERCHANT_ONBOARDING })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.MERCHANT_ONBOARDING, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'merchantOnboarding', type: 'response', fields: response })

      const { StatusCode, ResponseMessage, OnBoardingStatus, PANValidationstatus } = response

      if (StatusCode == '0' && ResponseMessage.toLowerCase() == 'aadhaarrefkey not generated.') {
        throw new ApiError(10001, API_PROVIDER, ResponseMessage || 'API Error')
      }

      if (StatusCode != '1') throw new ApiError(10001, API_PROVIDER, ResponseMessage)

      if (PANValidationstatus && PANValidationstatus.toLowerCase().indexOf('reinitiate')) throw new ApiError(CONFIG.MERCHANT_PMT_RETRY_CODE, API_PROVIDER, PANValidationstatus || 'PAN Validation Failed')

      return {
        onboarding_status: OnBoardingStatus == 'Success' ? 'Y' : 'N',
        pan_validation_status: PANValidationstatus == 'Success' ? 'Y' : 'N',
        PANValidationstatus,
        ...apiParams
      }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'retryMerchantOnboarding', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async merchantMappingStatus ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantMappingStatus', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        pan: Joi.string().required().error(new ApplicationError(2001, 'Fail: pan is required field and must be string')),
        provider_branch_code: Joi.required().error(new ApplicationError(2001, 'Fail: provider_branch_code is required field'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        PartnerIDCode: apiParams.ma_user_id,
        MobileNumber: apiParams.mobile_number,
        PanCard: apiParams.pan,
        BranchCode: apiParams.provider_branch_code
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return true
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.CSP_MAPPING })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.CSP_MAPPING, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'merchantMappingStatus', type: 'response', fields: response })

      const { StatusCode, ResponseMessage } = response

      if (StatusCode != '1') throw new ApiError(10001, API_PROVIDER, ResponseMessage)

      return true
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantMappingStatus', type: 'error', fields: error })
      // throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
      return false
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async agentConsentStatus ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'agentConsentStatus', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiParamsSchema = customJoi.object({
        mobile_number: Joi.string().required().error(new ApplicationError(2001, 'Fail: mobile_number is required field and must be string')),
        provider_branch_code: Joi.required().error(new ApplicationError(2001, 'Fail: provider_branch_code is required field'))
      })
      await apiParamsSchema.validateAsync(apiParams)

      const credentialsSchema = customJoi.object({
        username: Joi.string().required().error(new ApplicationError(2001, 'Fail: username is required field and must be string')),
        password: Joi.string().required().error(new ApplicationError(2001, 'Fail: password is required field and must be string')),
        APIKey: Joi.string().required().error(new ApplicationError(2001, 'Fail: APIKey is required field and must be string')),
        AgentCode: Joi.number().required().error(new ApplicationError(2001, 'Fail: AgentCode is required field and must be number'))
      })
      await credentialsSchema.validateAsync(credentials)

      const payLoad = {
        MobileNumber: apiParams.mobile_number,
        BranchCode: apiParams.provider_branch_code
      }

      const AuthenticationToken = await this.generateToken({ apiParams, credentials, connection: conn })

      const payLoadHeader = {
        APIKey: credentials.APIKey,
        AgentCode: credentials.AgentCode,
        AuthenticationToken,
        RequestBy: credentials.username
      }

      if (await this.mockApiFlag({ connection: conn })) {
        return {
          StatusCode: 1,
          ResponseMessage: 'Success',
          BankApprovalStatus: 'Approved',
          PANValidationStatus: 'Success',
          Remarks: 'Agent(eKYC)'
        }
      }

      const { api_status, response, requestBody, responseBody, url } = await PMTRequest.RESTPostRequest({ baseURL: PROVIDER_MERCHANT_ONBOARIND_URL, payLoad, payLoadHeader, action: PROVIDER_KYC_ENDPOINT.OTP_AGENT_CONSENT })

      /* save logs */
      try {
        const fields = { ...apiParams, request: requestBody, response: responseBody, api_status, request_type: PROVIDER_KYC_ENDPOINT.OTP_AGENT_CONSENT, url }
        const result = await this.saveApiLog({ fields, connection: conn })
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'result', fields: result })
      } catch (error) {
        logs.logger({ pagename: path.basename(__filename), action: 'saveApiLog', type: 'error', fields: error })
      }

      if (api_status != 200) throw new ApiError(10001, API_PROVIDER, 'API Error')

      logs.logger({ pagename: path.basename(__filename), action: 'agentConsentStatus', type: 'response', fields: response })

      const { StatusCode, ResponseMessage } = response

      if (StatusCode != '1') throw new ApiError(10001, API_PROVIDER, ResponseMessage)

      return response
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'agentConsentStatus', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async isPanVerified ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'isPanVerified', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiResponse = await this.agentConsentStatus({ apiParams, credentials, connection: conn })
      logs.logger({ pagename: path.basename(__filename), action: 'isPanVerified', type: 'apiResponse', fields: apiResponse })

      if (await this.mockApiFlag({ connection: conn }) && apiParams.ma_user_id == '28791') {
        throw new ApiError(CONFIG.MERCHANT_PMT_PAN_VALIDATION_CODE, API_PROVIDER, apiResponse.Remarks || 'API Error')
      }

      if (apiResponse.BankApprovalStatus == 'Approved' && apiResponse.PANValidationStatus == 'Success') return true

      throw new ApiError(CONFIG.MERCHANT_PMT_PAN_VALIDATION_CODE, API_PROVIDER, apiResponse.Remarks || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'isPanVerified', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {{apiParams:{mobile_number:string},credentials:{username:string,password:string}}} param0
   */
  static async isOTPConsentVerified ({ apiParams, credentials, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'isOTPConsentVerified', type: 'request', fields: { apiParams, credentials } })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const apiResponse = await this.agentConsentStatus({ apiParams, credentials, connection: conn })
      logs.logger({ pagename: path.basename(__filename), action: 'isOTPConsentVerified', type: 'apiResponse', fields: apiResponse })

      if (apiResponse.BankApprovalStatus == 'Approved' && apiResponse.PANValidationStatus == 'Success' &&
      (apiResponse.Remarks == 'Agent(eKYC)' || apiResponse.Remarks == 'Approved by RBLOPS')
      ) {
        return true
      } else if (apiResponse.PANValidationStatus == 'Success' &&
        apiResponse.OTPConsentStatus == 'Pending') {
      // else if (apiResponse.PANValidationStatus == 'Success' &&
      // apiResponse.Remarks == 'OTP Consent Pending') {
        return false
      }

      throw new ApiError(10001, API_PROVIDER, apiResponse.Remarks || 'API Error')
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'isOTPConsentVerified', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  static removeSpecialCharacters ({ object }) {
    for (const key in object) {
      Joi.string().email()
      if (typeof (object[key]) == 'string') {
        /* ignore date fields */
        if (key == 'OTPProcessId') continue
        /* ignore date fields */
        if (object[key].match(/\//g)) continue
        /* ignore time field */
        if (object[key].match(/:/g)) continue
        /* ignore email field */
        if (!('error' in Joi.string().email().validate(object[key]))) continue
        object[key] = object[key].replace(/[^A-Za-z0-9\s]/g, '')
      }
    }
    return object
  }
}

module.exports = PMT
