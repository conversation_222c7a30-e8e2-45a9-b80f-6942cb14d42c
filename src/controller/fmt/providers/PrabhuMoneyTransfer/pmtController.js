const path = require('path')

const mySQLWrapper = require('../../../../lib/mysqlWrapper')
const logs = require('../../../../util/log')
const errorMsg = require('../../../../util/error')
const PMT = require('./pmt')
const ApplicationError = require('../../../../util/errors/ApplicationErrorFile')
const ProviderController = require('../providerController')
const CONFIG = require('../PrabhuMoneyTransfer/config')
const { ENTITY_TYPES } = require('../config')
const FormdataController = require('../formdataController')
const ProviderMasterController = require('./providerMasterController')
const TransferActivityLogController = require('../../transfers/transferActivityLogController')
const MerchantRegisterController = require('../../merchant/merchantRegisterController')

class PMTController extends ProviderMasterController {
  static get TABLE_NAME () {
    return 'ma_pmt_static_data'
  }

  static get PRIMARY_KEY () {
    return 'ma_pmt_static_data_id'
  }

  /**
   *
   * @param {*} _
   * @param {{ma_user_id:Number,userid:Number,static_data_type:String}} fields
   */
  static async getStaticData (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const data = await this.getStaticDataByKey({ static_data_type: fields.static_data_type, connectionRead })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data, static_data_type: fields.static_data_type, action_code: 1000 }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  static async getStaticDataByKey ({ static_data_type, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'request', fields: static_data_type })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const fetchStaticQuery = `SELECT static_data_response FROM ma_pmt_static_data WHERE static_data_type= '${static_data_type}'`
      const fetchStaticResult = await this.rawQuery(fetchStaticQuery, connectionRead)
      logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'fetchStaticResult', fields: fetchStaticResult })
      if (fetchStaticResult.length == 0) return []

      const JSONParseData = JSON.parse(fetchStaticResult[0].static_data_response)
      if (!('a:DataList' in JSONParseData) || !('a:Data' in JSONParseData['a:DataList'])) throw new ApplicationError(1001, errorMsg.responseCode[1001])
      let DataList = []
      const { 'a:Data': Data } = JSONParseData['a:DataList']
      DataList = Data
      if (DataList.constructor.name == 'Object') DataList = [Data]
      const data = DataList.map(data => ({ value: data['a:Label'].trim(), key: data['a:Value'].trim() }))
      logs.logger({ pagename: path.basename(__filename), action: 'getStaticData', type: 'fetchStaticResult', fields: data })
      return data
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBank', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {{ma_user_id:Number,userid:Number,country:String}} fields
  */
  static async getState (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getState', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const fetchStaticQuery = `SELECT static_data_response FROM ma_pmt_static_data WHERE static_data_type='COUNTRY_${fields.country.toUpperCase()}'`
      const fetchStaticResult = await this.rawQuery(fetchStaticQuery, connectionRead)

      logs.logger({ pagename: path.basename(__filename), action: 'getState', type: 'fetchStaticResult', fields: fields })

      if (fetchStaticResult.length == 0) throw new ApplicationError(1001, `${errorMsg.responseCode[1028]} : invalid country`)

      const JSONParseData = JSON.parse(fetchStaticResult[0].static_data_response)
      if (!('a:Data' in JSONParseData) || !('a:StateDistrict' in JSONParseData['a:Data'])) throw new ApplicationError(1001, errorMsg.responseCode[1001])
      const { 'a:StateDistrict': DataList } = JSONParseData['a:Data']
      const stateSet = new Set()
      DataList.forEach(State => {
        stateSet.add(State['a:State'])
      })
      const data = []
      stateSet.forEach(state => data.push({ value: state.trim(), key: state.trim() }))

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data, static_data_type: 'state', action_code: 1000 }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getState', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {{ma_user_id:Number,userid:Number,country:String,state:string}} fields
   */
  static async getStateDistrict (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getStateDistrict', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const fetchStaticQuery = `SELECT static_data_response FROM ma_pmt_static_data WHERE static_data_type='COUNTRY_${fields.country.toUpperCase()}'`
      const fetchStaticResult = await this.rawQuery(fetchStaticQuery, connectionRead)
      if (fetchStaticResult.length == 0) throw new ApplicationError(1001, `${errorMsg.responseCode[1028]} : invalid country`)

      logs.logger({ pagename: path.basename(__filename), action: 'getStateDistrict', type: 'fetchStaticResult', fields: fetchStaticResult })
      const JSONParseData = JSON.parse(fetchStaticResult[0].static_data_response)
      if (!('a:Data' in JSONParseData) || !('a:StateDistrict' in JSONParseData['a:Data'])) throw new ApplicationError(1001, errorMsg.responseCode[1001])

      const { 'a:StateDistrict': DataList } = JSONParseData['a:Data']
      const data = DataList
        .filter(district => (district['a:State'] == fields.state))
        .map(data => ({ value: data['a:District'].trim(), key: data['a:District'].trim() }))

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data, static_data_type: 'statedistrict', action_code: 1000 }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getStateDistrict', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connectionRead.release()
    }
  }

  /**
  * UPDATE Static Data Periodically
  */
  static async updateStaticData () {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const providerDetail = await ProviderController.getProviderDetail({ providerCode: CONFIG.PROVIDER_CODE.PMT })
      logs.logger({ pagename: path.basename(__filename), action: 'providerDetail', type: 'response', fields: providerDetail })
      const credentials = JSON.parse(providerDetail.provider_credential)
      /* PMT : Static Data */
      const types = ['Gender', 'IDType', 'Nationality', 'IncomeSource', 'Relationship', 'PaymentMode', 'RemittanceReason']
      const requests = types.map(type => PMT.getStaticData({ type, credentials, connection }))
      /* PMT : AcPayproviderBranchList */
      requests.push(PMT.getAcPayproviderBranchList({ credentials, connection }))
      requests.push(PMT.getStateDistrict({ country: 'India', credentials, connection }))
      requests.push(PMT.getStateDistrict({ country: 'Nepal', credentials, connection }))
      const responses = await Promise.all(requests)
      logs.logger({ pagename: path.basename(__filename), action: 'updateStaticData', type: 'responses', fields: responses })
      /* UPDATE/INSERT */
      const insertOrUpdateRequests = []
      for (let index = 0; index < responses.length; index++) {
        const insertOrUpdateQuery = `INSERT INTO ${this.TABLE_NAME}(static_data_type,static_data_request,static_data_response) VALUES('${responses[index].type}','${JSON.stringify(responses[index].request)}','${JSON.stringify(responses[index].resp)}') ON DUPLICATE KEY UPDATE static_data_request='${JSON.stringify(responses[index].request)}',static_data_response='${JSON.stringify(responses[index].resp)}'`
        // const insertOrUpdateResult = await this.rawQuery(insertOrUpdateQuery, connection)
        // logs.logger({ pagename: path.basename(__filename), action: 'updateStaticData', type: 'insertOrUpdateResult', fields: insertOrUpdateResult })
        insertOrUpdateRequests.push(this.rawQuery(insertOrUpdateQuery, connection))
      }

      const insertOrUpdateResults = await Promise.all(insertOrUpdateRequests)
      logs.logger({ pagename: path.basename(__filename), action: 'updateStaticData', type: 'insertOrUpdateResults', fields: insertOrUpdateResults })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'updateStaticData', type: 'error', fields: error })
      const defaultErrorResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error.code && error.message) {
        defaultErrorResponse.message = error.message
        defaultErrorResponse.respcode = error.code
      }
      return defaultErrorResponse
    } finally {
      connection.release()
    }
  }

  /**
   *
   * @param {*} param0
   * @returns
   */
  static async registerRemitter ({ fields, verifyOTPResult, provider, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'registerRemitter', type: 'request', fields: JSON.stringify({ fields, verifyOTPResult }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const { response } = verifyOTPResult
      const responseJson = JSON.parse(response)
      logs.logger({ pagename: path.basename(__filename), action: 'registerRemitter', type: 'request', fields: responseJson })

      const { 'a:ProcessId': OTPProcessId } = responseJson.response

      if (!OTPProcessId) throw new ApplicationError(1001, errorMsg.responseCode[1001])

      const formDataRegister = await FormdataController.fetchFormData({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        sessionID: fields.sessionID,
        form_data_type: ENTITY_TYPES.REMITTER_REGISTRATION,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'registerRemitter', type: 'request', fields: formDataRegister })
      if (formDataRegister == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formDataAddress = await FormdataController.fetchFormData({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        sessionID: fields.sessionID,
        form_data_type: ENTITY_TYPES.REMITTER_REGISTRATION_ADDRESS,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'registerRemitter', type: 'request', fields: formDataAddress })
      if (formDataAddress == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formData = { ...JSON.parse(formDataRegister.form_data), ...JSON.parse(formDataAddress.form_data) }

      logs.logger({ pagename: path.basename(__filename), action: 'registerRemitter', type: 'request', fields: JSON.stringify(fields) })

      const apiParams = { ...formData, otp: fields.otp, ma_user_id: fields.ma_user_id, userid: fields.userid, sessionID: fields.sessionID, mobile_number: fields.mobile_number, OTPProcessId }
      const credentials = JSON.parse(provider.provider_credential)
      return await PMT.addRemitter({ apiParams, credentials, connection: conn })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'registerRemitter', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {*} param0
   * @returns
   */
  static async transfers ({ params, verifyOTPResult, provider, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'request', fields: JSON.stringify({ params, verifyOTPResult }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const { response } = verifyOTPResult
      const responseJson = JSON.parse(response)
      logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'request', fields: responseJson })

      const { 'a:ProcessId': OTPProcessId } = responseJson.response

      const transferFormData = await FormdataController.fetchFormData({
        ma_user_id: params.ma_user_id,
        userid: params.userid,
        mobile_number: params.mobile_number,
        sessionID: params.sessionID,
        form_data_type: ENTITY_TYPES.TRANSFER,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'request', fields: transferFormData })
      if (transferFormData == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formData = JSON.parse(transferFormData.form_data)

      const apiParams = { ...params, OTPProcessId, ...formData }
      const credentials = JSON.parse(provider.provider_credential)
      return await PMT.transfers({ apiParams, credentials, connection: conn })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'transfers', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {*} param0
   * @returns
   */
  static async remitterOnboarding ({ fields, provider, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'request', fields: JSON.stringify({ fields }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const formDataRegister = await FormdataController.fetchFormData({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        sessionID: fields.sessionID,
        form_data_type: ENTITY_TYPES.REMITTER_REGISTRATION,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'request', fields: formDataRegister })
      if (formDataRegister == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formDataAddress = await FormdataController.fetchFormData({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        sessionID: fields.sessionID,
        form_data_type: ENTITY_TYPES.REMITTER_ONBOARDING,
        connectionRead: connRead
      })
      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'request', fields: formDataAddress })
      if (formDataAddress == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formData = { ...JSON.SON.parse(formDataRegister.form_data), ...JSON.SON.parse(formDataAddress.form_data) }

      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'request', fields: JSON.stringify(fields) })

      const apiParams = { ...formData, otp: fields.otp, ma_user_id: fields.ma_user_id, userid: fields.userid, sessionID: fields.sessionID, mobile_number: fields.mobile_number, customer_provider_reference_id: fields.customer_provider_reference_id }
      const credentials = JSON.parse(provider.provider_credential)
      return await PMT.remitterOnboarding({ apiParams, credentials, connection: conn })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'remitterOnboarding', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {*} param0
   * @returns
   */
  static async checkTransfersStatusAtBankEnd ({ request, provider, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'checkTransfersStatusAtBankEnd', type: 'request', fields: request })
    logs.logger({ pagename: path.basename(__filename), action: 'checkTransfersStatusAtBankEnd', type: 'provider', fields: provider })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      let isVerificationRequired = false
      const apiParams = { ...request, sessionID: 'internal' }
      const credentials = JSON.parse(provider.provider_credential)
      const apiResponse = await PMT.transferReQuery({ apiParams, credentials, connection: conn })
      logs.logger({ pagename: path.basename(__filename), action: 'checkTransfersStatusAtBankEnd', type: 'transferReQuery', fields: apiResponse })

      if (apiResponse.transfer_status == CONFIG.TRANSFER_STATUS.HOLD.STATUS_CODE) {
        isVerificationRequired = true
      }

      if (apiResponse.transfer_status == CONFIG.TRANSFER_STATUS.PAID.STATUS_CODE) {
        /* UPDATE Activity Log */
        const requestFields = {
          ma_fmt_transfers_id: request.ma_fmt_transfers_id,
          aggregator_order_id: request.aggregator_order_id,
          transfer_status: CONFIG.TRANSFER_STATUS.PAID.STATUS_CODE,
          transaction_status: CONFIG.TRANSFER_STATUS.PAID.TRANSACTION_STATUS
        }
        await TransferActivityLogController.insertActivityLog({ data: requestFields, connection: conn })
        /*
        * PMT NOTE : Once the payment collected in pay-out side then the transaction status will change to Paid.
        * Paid stands for collected by receiver or deposited into receiver’s account.
        */
        apiResponse.transfer_status = 'S'
        apiResponse.transaction_status = 'S'
      }

      return { isVerificationRequired, ...apiResponse }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'checkTransfersStatusAtBankEnd', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {*} param0
   * @returns
   */
  static async verifyTransfer ({ ma_user_id, userid, mobile_number, sessionID, provider, transferResult, connection }) {
    logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'request', fields: { ma_user_id, userid, mobile_number, sessionID } })
    logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'transferResult', fields: JSON.stringify(transferResult) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      if (transferResult.transfer_status != CONFIG.TRANSFER_STATUS.HOLD.STATUS_CODE) return false

      const apiParams = { ma_user_id, userid, mobile_number, sessionID, rrn: transferResult.rrn }
      const credentials = JSON.parse(provider.provider_credential)

      const apiResponse = await PMT.verifyTransfer({ apiParams, credentials, connection: conn })

      logs.logger({ pagename: path.basename(__filename), action: 'verifiedTransfer', type: 'response', fields: apiResponse })
      return apiResponse
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'verifyTransfer', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {Object} fields
   * @returns
   */
  static async getBank (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getBank', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const fetchQuery = "SELECT static_data_response FROM ma_pmt_static_data WHERE static_data_type = 'AcPayproviderBranchList'"
      const fetchBankResult = await this.rawQuery(fetchQuery, connectionRead)
      if (fetchBankResult.length == 0) {
        throw new ApplicationError(1001, errorMsg.responseCode[1001])
      }

      const JSONParseData = JSON.parse(fetchBankResult[0].static_data_response)
      logs.logger({ pagename: path.basename(__filename), action: 'getBank', type: 'JSONParseData', fields: JSONParseData })

      if (!('a:BankBranches' in JSONParseData) || !('a:BankBranch' in JSONParseData['a:BankBranches'])) throw new ApplicationError(1001, errorMsg.responseCode[1001])
      const { 'a:BankBranch': DataList } = JSONParseData['a:BankBranches']

      logs.logger({ pagename: path.basename(__filename), action: 'getBank', type: 'DataList', fields: DataList })

      const branchSet = new Set()

      DataList.forEach(branch => {
        console.log('branch: ', branch)
        branchSet.add(branch['a:BankName'])
      })
      console.log('SET: ', branchSet)
      const data = []
      branchSet.forEach(branch => data.push({ value: branch.trim(), key: branch.trim() }))

      console.log('FINAL DATA: ', data)
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data, static_data_type: 'branch', action_code: 1000 }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBank', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {Object} fields
   * @returns
   */
  static async getBankBranch (_, fields) {
    logs.logger({ pagename: path.basename(__filename), action: 'getBankBranch', type: 'request', fields: fields })
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const fetchQuery = "SELECT static_data_response FROM ma_pmt_static_data WHERE static_data_type = 'AcPayproviderBranchList'"
      const fetchBankResult = await this.rawQuery(fetchQuery, connectionRead)
      if (fetchBankResult.length == 0) {
        throw new ApplicationError(1001, errorMsg.responseCode[1001])
      }

      const JSONParseData = JSON.parse(fetchBankResult[0].static_data_response)
      logs.logger({ pagename: path.basename(__filename), action: 'getBankBranch', type: 'parsed-data', fields: JSONParseData })

      if (!('a:BankBranches' in JSONParseData) || !('a:BankBranch' in JSONParseData['a:BankBranches'])) throw new ApplicationError(1001, errorMsg.responseCode[1001])

      const { 'a:BankBranch': DataList } = JSONParseData['a:BankBranches']
      logs.logger({ pagename: path.basename(__filename), action: 'getBankBranch', type: 'DATALIST', fields: DataList })

      const data = DataList
        .filter(bank => (bank['a:BankName'] == fields.bankName))
        .map(data => ({ value: data['a:BranchName'].trim(), key: data['a:BankBranchId'] }))

      logs.logger({ pagename: path.basename(__filename), action: 'getBankBranch', type: 'response', fields: data })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data, static_data_type: 'getBankBranch', action_code: 1000 }
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getBankBranch', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      connectionRead.release()
    }
  }

  /**
   *
   * @param {*} _
   * @param {Object} fields
   * @returns
   */
  static async getPaymentModes ({ paymentMode, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'getPaymentModes', type: 'request', fields: { paymentMode } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      console.log('paymentMode: ', paymentMode.toUpperCase())
      console.log('MODE: ', CONFIG.PARTNER_PAYMENT_MODE_CODE[paymentMode.toUpperCase()])
      const sql = `SELECT ma_fmt_provider_payment_mode_id from ma_fmt_provider_payment_modes where mode_code = '${paymentMode}' and mode_status = 'Y'`
      console.log('query: ', sql)
      const res = await this.rawQuery(sql, connRead)
      if (res.length == 0) throw new ApplicationError(1001, errorMsg.responseCode[1001])
      return res[0].ma_fmt_provider_payment_mode_id
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getPaymentModes', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object, verifyOTPResult:Object, provider:Object,merchantFields:Object, connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async merchantRemitter ({ fields, verifyOTPResult, provider, merchantFields, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantRemitter', type: 'request', fields: JSON.stringify({ fields, verifyOTPResult }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const { response } = verifyOTPResult
      const responseJson = JSON.parse(response)
      logs.logger({ pagename: path.basename(__filename), action: 'merchantRemitter', type: 'request', fields: responseJson })

      const { 'a:ProcessId': OTPProcessId } = responseJson.response

      if (!OTPProcessId) throw new ApplicationError(1001, errorMsg.responseCode[1001])

      const formDataRegister = await FormdataController.fetchFormData({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        sessionID: fields.sessionID,
        form_data_type: ENTITY_TYPES.MERCHANT_REGISTRATION,
        connectionRead: connRead
      })

      logs.logger({ pagename: path.basename(__filename), action: 'merchantRemitter', type: 'request', fields: formDataRegister })
      if (formDataRegister == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formDataRegisterAddreses = await MerchantRegisterController.fetchAddresses({
        ...fields,
        connectionRead: connRead
      })

      if (formDataRegisterAddreses == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formData = { ...JSON.parse(formDataRegister.form_data), ...formDataRegisterAddreses }

      logs.logger({ pagename: path.basename(__filename), action: 'merchantRemitter', type: 'formData', fields: JSON.stringify(formData) })

      const merchantData = {
        FirstName: merchantFields.firstname,
        Middlename: merchantFields.middlename || '',
        LastName: merchantFields.lastname || '',
        Companyname: merchantFields.company,
        mobile_number: fields.mobile_number,
        Emailid: merchantFields.email_id,
        Dob: merchantFields.dob,
        Pancard: merchantFields.pan,
        Ifsccode: merchantFields.ifsc_code,
        Accountnumber: merchantFields.account_no,
        Agentaccountname: merchantFields.account_name,
        Gender: merchantFields.gender,
        Bankname: merchantFields.bank_name,
        Branchname: merchantFields.branch
      }

      const apiParams = { ...merchantData, ...formData, otp: fields.otp, ma_user_id: fields.ma_user_id, userid: fields.userid, sessionID: fields.sessionID, mobile_number: fields.mobile_number, OTPProcessId }
      const credentials = JSON.parse(provider.provider_credential)
      return await PMT.addMerchant({ apiParams, credentials, connection: conn })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantRemitter', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{fields:Object, verifyOTPResult:Object,kycBioRequest:Object provider:Object,merchantFields:Object, connection:Promise<mySQLWrapper.getConnectionFromPool()>,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>}} param0
   * @returns
   */
  static async merchantOboarding ({ fields, provider, kycBioRequest, merchantFields, merchantMappingData, connection, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'merchantOboarding', type: 'request', fields: JSON.stringify({ fields }) })
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const formDataRegister = await FormdataController.fetchFormData({
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        sessionID: fields.sessionID,
        form_data_type: ENTITY_TYPES.MERCHANT_REGISTRATION,
        connectionRead: connRead
      })

      logs.logger({ pagename: path.basename(__filename), action: 'merchantOboarding', type: 'fetchFormData', fields: formDataRegister })
      if (formDataRegister == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formDataRegisterAddreses = await MerchantRegisterController.fetchAddresses({
        ...fields,
        connectionRead: connRead
      })

      if (formDataRegisterAddreses == false) throw new ApplicationError(1002, errorMsg.responseCode[1002])

      const formData = { ...JSON.parse(formDataRegister.form_data), ...formDataRegisterAddreses }
      console.log('merchantMappingData',merchantMappingData)
      const parsedDataFromsearchCsp =  JSON.parse(merchantMappingData.merchant_data_json)
      console.log('parsedDataFromsearchCsp',parsedDataFromsearchCsp)

      logs.logger({ pagename: path.basename(__filename), action: 'merchantOboarding', type: 'formData', fields: JSON.stringify(formData) })
      merchantFields.branch = merchantFields.branch.replace('/', '')
      // const merchantData = {
      //   FirstName: merchantFields.firstname,
      //   Middlename: merchantFields.middlename,
      //   LastName: merchantFields.lastname,
      //   Companyname: merchantFields.company,
      //   mobile_number: fields.mobile_number,
      //   Emailid: merchantFields.email_id,
      //   Dob: merchantFields.dob,
      //   Pancard: merchantFields.pan,
      //   Ifsccode: merchantFields.ifsc_code,
      //   Accountnumber: merchantFields.account_no,
      //   Agentaccountname: merchantFields.account_name,
      //   Gender: merchantFields.gender,
      //   Bankname: merchantFields.bank_name,
      //   Branchname: merchantFields.branch
      // }

      const merchantData = {
        FirstName: parsedDataFromsearchCsp.Firstname,
        Middlename: parsedDataFromsearchCsp.Middlename || '',
        LastName: parsedDataFromsearchCsp.Lastname || '',
        Companyname: parsedDataFromsearchCsp.Companyname,
        mobile_number: parsedDataFromsearchCsp.Mobilenumber,
        Emailid: parsedDataFromsearchCsp.Emailid,
        Dob: parsedDataFromsearchCsp.Dob,
        Pancard: parsedDataFromsearchCsp.Pancard,
        Ifsccode: parsedDataFromsearchCsp.Ifsccode,
        Accountnumber: parsedDataFromsearchCsp.Accountnumber,
        Agentaccountname: parsedDataFromsearchCsp.Agentaccountname,
        Gender: parsedDataFromsearchCsp.Gender,
        Bankname: parsedDataFromsearchCsp.BankName,
        Branchname: parsedDataFromsearchCsp.BranchName
      }

      console.log('merchantData>>',merchantData)

      // const apiParams = { ...merchantData, ...formData, ...fields, kycBioRequest }
      const apiParams = { ...merchantData, ...parsedDataFromsearchCsp, ...fields, kycBioRequest }
      console.log('>>',apiParams)
      const credentials = JSON.parse(provider.provider_credential)
      return await PMT.merchantOnboarding({ apiParams, credentials, connection: conn })
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'merchantOboarding', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {{mobile_number:string,ma_fmt_provider_master_id:number, customFields:Object,connectionRead:Promise<mySQLWrapper.getConnectionFromReadReplica()>,}} param0
   * @returns
   */
  static async updateCustomFieldsValues ({ ma_user_id, ma_fmt_provider_master_id, merchantMappingData, customFields, connectionRead }) {
    logs.logger({ pagename: path.basename(__filename), action: 'updateCustomFieldsValues', type: 'request', fields: { ma_user_id, ma_fmt_provider_master_id, customFields } })
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead
    try {
      const merchantJsonData = JSON.parse(merchantMappingData.merchant_data_json)

      logs.logger({ pagename: path.basename(__filename), action: 'updateCustomFieldsValues', type: 'request', fields: { ma_user_id, ma_fmt_provider_master_id, customFields } })

      const updateCustomFields = customFields[0].fields.map(field => {
        const newFields = { ...field }
        console.log('newFields>>>>', newFields)
        newFields.value = this.getFieldValue(newFields.postKey, merchantJsonData, field)
        console.log('newFields.value>>>', newFields.value)
        if (newFields.value) newFields.isEditable = false
        if (newFields.value == 0 || newFields.value == '') newFields.isEditable = true
        console.log('newFieldsresult>>>>', newFields)
        return newFields
      })

      return [{ ...customFields[0], fields: updateCustomFields }]
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'updateCustomFieldsValues', type: 'error', fields: error })
      throw new ApplicationError(error.code || 1001, error.message || errorMsg.responseCode[1001])
    } finally {
      if (isSetRead) connRead.release()
    }
  }

  /**
   *
   * @param {String} key
   * @param {Object} data
   * @param {Object} field
   * @returns
   */
  static getFieldValue (key, data, field) {
    logs.logger({ pagename: path.basename(__filename), action: 'getFieldValue', type: 'request', fields: { key, data, field } })
    try {
      if (field.type == 'DD') {
        const values = field.ddvalue.map(val => val.value)
        if (data[key] && values.includes(data[key])) {
          return data[key]
        } else {
          return ''
        }
      }
      if (data[key]) return data[key]
      return ''
    } catch (error) {
      logs.logger({ pagename: path.basename(__filename), action: 'getFieldValue', type: 'catchError', fields: error })
      return ''
    }
  }
}

module.exports = PMTController
