const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const errorMsg = require('../../util/error')
const crypto = require('crypto')
const util = require('../../util/util')
const cashAccount = require('../creditDebit/cashAccountController')
const pointsAccount = require('../creditDebit/pointsAccountController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const commissionController = require('../commission/commissionController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const balanceController = require('../balance/balanceController')
const pointsRateController = require('../pointsRate/pointsRateController')
const transactionController = require('../transaction/transactionController')

class adhoc extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'adhoc_entries'
  }

  static get PRIMARY_KEY () {
    return 'adhoc_entries_id'
  }

  static async adhocEntries (_, fields, airpaykey) {
    log.logger({ pagename: require('path').basename(__filename), action: 'adhocEntries', type: 'request', fields: fields })
    fields = JSON.parse(fields)
    if (fields.ma_user_id == null || fields.ma_user_id == undefined || fields.ma_user_id == '') {
      return { status: 400, respcode: 1028, message: 'MerchantId is mandatory' }
    }

    if (fields.txn_referenceid == null || fields.txn_referenceid == undefined || fields.txn_referenceid == '') {
      return { status: 400, respcode: 1028, message: 'txn_referenceid is mandatory' }
    }

    fields.amount = parseFloat(fields.amount)
    if (fields.amount == null || fields.amount == undefined || isNaN(fields.amount) || fields.amount == '' || fields.amount <= 0 || fields.amount > util.zeroSettlementThreshold) {
      return { status: 400, respcode: 1028, message: 'Invalid Amount' }
    }
    if (fields.mode != 'CREDIT' && fields.mode != 'DEBIT') {
      return { status: 400, respcode: 1028, message: 'Invalid Mode' }
    }
    if (fields.remarks == null || fields.remarks == undefined || fields.remarks == '') {
      return { status: 400, respcode: 1028, message: 'Remark is mandatory' }
    }
    if (fields.checksum == null || fields.checksum == undefined || fields.checksum == '') {
      return { status: 400, respcode: 1028, message: 'Checksum is mandatory' }
    }
    const stringHere = fields.ma_user_id.toString() + fields.amount.toString() + fields.mode + fields.txn_referenceid.toString() + airpaykey.checksumkey
    const generatedchecksum = await crypto.createHash('md5').update(stringHere).digest('hex')
    console.log('generated checksum', generatedchecksum)
    if (fields.checksum != generatedchecksum) {
      return { status: 400, respcode: 1028, message: 'Checksum Mismatch' }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT ma_user_master_id,userid,mobile_id FROM ma_user_master WHERE profileid = '${fields.ma_user_id}' AND mer_user = 'mer' limit 1`
      const userData = await this.rawQuery(sql, connection)
      if (userData.length <= 0) {
        return { status: 400, respcode: 1028, message: 'fail : User does not exists.' }
      }
      const random = await this.generateRandom(4)
      const timestamp = await this.getTimestamp('')
      let orderId = `MAADH${random}${timestamp}`
      let cashType = ''
      let maStatus = ''
      let pointsDetailsEntries = {}
      let dr_mechant_id = ''
      let dr_corresponding_merchant_id = ''
      let cr_merchant_id = ''
      let cr_corresponding_merchant_id = ''
      let descPointLedgers = ''
      if (fields.mode == 'CREDIT') {
        cashType = 'CL'
        maStatus = 'S'
        dr_mechant_id = util.airpayUserId
        dr_corresponding_merchant_id = fields.ma_user_id
        cr_merchant_id = fields.ma_user_id
        cr_corresponding_merchant_id = util.airpayUserId
        descPointLedgers = 'Credit-Adhoc'
      } else {
        cashType = 'CW'
        maStatus = 'R'
        orderId = 'R-' + orderId
        dr_mechant_id = fields.ma_user_id
        dr_corresponding_merchant_id = util.airpayUserId
        cr_merchant_id = util.airpayUserId
        cr_corresponding_merchant_id = fields.ma_user_id
        pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
          ma_user_id: fields.ma_user_id,
          amount: fields.amount,
          transactionType: '1',
          connection: connection
        })
        if (pointsDetailsEntries.status === 400) {
          return pointsDetailsEntries
        }
        console.log('Wallet balances', pointsDetailsEntries)
        descPointLedgers = 'Debit-Adhoc'
      }

      /* CREATE TRANSACTION */
      const commissionVal = 0
      var pointsFactor = 1
      const pointsFactorData = await pointsRateController.getGlobalPointsRate(_, { connection })
      if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
        pointsFactor = pointsFactorData.points_value
      }
      await mySQLWrapper.beginTransaction(connection)
      // Create transaction with I status
      const transferFields = {
        ma_user_id: fields.ma_user_id,
        userid: userData[0].userid,
        amount: fields.amount,
        aggregator_order_id: orderId,
        transaction_id: fields.txn_referenceid,
        commission_amount: commissionVal,
        points_factor: pointsFactor,
        transaction_status: 'P',
        transaction_type: '27',
        mobile_number: userData[0].mobile_id,
        remarks: descPointLedgers + fields.remarks
      }
      console.time('Timer_ConfirmAmount_CreateTransaction')
      const transaction = await transactionController.createTransaction(_, transferFields, connection)
      console.timeEnd('Timer_ConfirmAmount_CreateTransaction')

      log.logger({ pagename: require('path').basename(__filename), action: 'adhocEntries', type: 'transaction', fields: transaction })

      if (transaction.status === 400) {
        await mySQLWrapper.rollback(connection)
        return transaction
      }

      await mySQLWrapper.commit(connection)

      fields.ma_transaction_master_id = transaction.transaction_id

      await mySQLWrapper.beginTransaction(connection)
      const cashCredit = await cashAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: maStatus,
        transaction_type: cashType,
        orderid: orderId,
        userid: userData[0].userid,
        corresponding_id: fields.ma_user_id,
        connection: connection
      })
      if (cashCredit.status === 400) {
        await mySQLWrapper.rollback(connection)
        return cashCredit
      }
      const pointsCredit = await pointsAccount.createEntry('_', {
        ma_user_id: util.airpayUserId,
        amount: fields.amount,
        ma_status: maStatus,
        transaction_type: cashType,
        orderid: orderId,
        userid: userData[0].userid,
        corresponding_id: fields.ma_user_id,
        connection: connection
      })
      if (pointsCredit.status === 400) {
        await mySQLWrapper.rollback(connection)
        return pointsCredit
      }

      const debitPoints = await pointsLedger.createEntry('_', {
        ma_user_id: dr_mechant_id,
        amount: fields.amount,
        mode: 'dr',
        transaction_type: '27',
        description: descPointLedgers + '-' + fields.remarks,
        orderid: orderId,
        userid: userData[0].userid,
        corresponding_id: dr_corresponding_merchant_id,
        ma_status: maStatus,
        connection: connection
      }
      )
      if (debitPoints.status === 400) {
        await mySQLWrapper.rollback(connection)
        return debitPoints
      }

      if (fields.mode == 'DEBIT') {
        for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
          const entry = await pointsDetailsController.createEntry(_, {
            ma_user_id: dr_mechant_id,
            amount: pointsDetailsEntries.details[i].deductionAmount,
            wallet_type: pointsDetailsEntries.details[i].wallet_type,
            ma_points_ledger_master_id: debitPoints.id,
            orderid: orderId,
            userid: userData[0].userid,
            ma_status: 'S',
            connection: connection
          })
          if (entry.status === 400) {
            await mySQLWrapper.rollback(connection)
            return entry
          }
        }
      }

      const creditPoints = await pointsLedger.createEntry('_', {
        ma_user_id: cr_merchant_id,
        amount: fields.amount,
        mode: 'cr',
        transaction_type: '27',
        description: descPointLedgers + '-' + fields.remarks,
        orderid: orderId,
        userid: userData[0].userid,
        corresponding_id: cr_corresponding_merchant_id,
        ma_status: maStatus,
        connection: connection
      }
      )
      if (creditPoints.status === 400) {
        await mySQLWrapper.rollback(connection)
        return creditPoints
      }
      await mySQLWrapper.commit(connection)

      const data = { transaction_status: 'S' }
      // const updateTransaction = await transactionController.updateWhere(connection, { data, id: fields.ma_transaction_master_id, where: 'ma_transaction_master_id' })

      /* RISK MANAGEMENT Changes */
      const updateTransactionResult = await transactionController.updateWhereData(connection, {
        data,
        id: fields.ma_transaction_master_id,
        where: 'ma_transaction_master_id'
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'updateTransactionData', type: 'response', fields: updateTransactionResult })

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'adhocEntries', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
    } finally {
      connection.release()
    }
  }

  static async generateRandom (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  static async getTimestamp () {
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    const timeStamp = Math.floor(Date.now() / 1000)
    return `${timeStamp}`
  }
}

module.exports = adhoc
