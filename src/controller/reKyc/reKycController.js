/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const path = require('path')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const common = require('../../util/common')
const util = require('../../util/util')
const axios = require('axios')
const airpaychecksum = require('../../util/airpaychecksum')
const configData = require('./config')
const crypto = require('crypto')
const common_fns = require('../../util/common_fns')
const _ = require('lodash')
const profileController = require('../login/profileController')
const moment = require('moment')
const momentTimezone = require('moment-timezone')
class rekycController extends DAO {
  /**
         * Overrides TABLE_NAME with this class' backing table at MySQL
         */
  get TABLE_NAME () {
    return 'ma_kyc_campaign_details'
  }

  static get PRIMARY_KEY () {
    return 'ma_kyc_campaign_details_id'
  }

  static async addformValue (fieldsData, fieldsValue) {
    try {
      // const profileData = fieldsData
      // console.log('fieldsData==', fieldsData)
      // console.log('profileData====', profileData)
      // for (const key in profileData) {
      //   console.log('key===', key)
      //   if (key.toLowerCase() in fieldsValue) {
      //     const valueObj = profileData[key]
      //     console.log('valueObj===', valueObj)
      //     valueObj.value = fieldsValue[key]
      //   }
      // }

      // return fieldsData

      for (let i = 0; i < fieldsData.length; i++) {
        const value = (fieldsData[i].name)
        console.log('value==', value)
        for (const key in fieldsValue) {
          console.log('keyValue===', key)
          if (value == key) {
            fieldsData[i].value = fieldsValue[key]
          }
        }
      }
      return fieldsData
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'addformValue', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  // mask value function
  static async generateMaskedvalue (value) {
    try {
      let maskedNumber = value

      maskedNumber = maskedNumber.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, 'X')

      return maskedNumber
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'generateMaskedvalue', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  // to show the campign details

  static async getrekycData (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'fetchCampaignFormData', type: 'request', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    // Validate All Required Fields - Add parameter name in array for field validation
    const validatorResponse = validator.validateFields(fields, ['ma_user_id'])
    if (validatorResponse.status != 200) return validatorResponse
    try {
      const { ma_user_id } = fields

      // const stateId = sqlResult[0].state
      // const userType = sqlResult[0].user_type
      // Check whether the banner is active or not,check whether the banner is only visible between the start and end time.

      const campaignquery = `select
      ma_kyc_campaign_details_id,campaign_name,ma_kyc_elements,priority,skip_count
      from
      ma_kyc_campaign_details 
      where
      campaign_status = 'A' and ((now() between start_date and end_date) or (now() between start_date and end_date is null))  limit 1`

      log.logger({ pagename: path.basename(__filename), action: 'campaignquery', type: 'campaignquery', fields: campaignquery })
      const campaignQueryResult = await this.rawQuery(campaignquery, connection)
      console.log('campaignQueryResult', campaignQueryResult)
      log.logger({ pagename: path.basename(__filename), action: 'campaignQueryResult', type: 'campaignQueryResult', fields: campaignQueryResult })
      if (campaignQueryResult.length == 0) return { status: 400, respcode: 1001, action_code: 1001, message: 'No active campaigns available ' }

      //  skip count expiry time check

      const skipCountExpiryTime = await common.getSystemCodes(this, util.skip_count_expiry_time, connection)
      const skipCountTime = `SELECT TIMESTAMPDIFF(HOUR,skip_expiry_time,CURRENT_TIMESTAMP) AS timeDif
     FROM ma_kyc_campaign_merchants_details 
     WHERE ma_kyc_campaign_details_id = ${campaignQueryResult[0].ma_kyc_campaign_details_id} and ma_user_id = ${ma_user_id} limit 1`
      log.logger({ pagename: path.basename(__filename), action: 'getrekycData', type: 'skipCountTime', fields: skipCountTime })
      const skipCountTimeResult = await this.rawQuery(skipCountTime, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getrekycData', type: 'skipCountTimeResult', fields: skipCountTimeResult })

      console.log('skipCountTimeResult=====', skipCountTimeResult)
      const skipCountValue = skipCountTimeResult[0].timeDif

      if (skipCountValue >= skipCountExpiryTime) {
        return { status: 400, respcode: 1001, action_code: 1001, message: 'No active campaigns available ' }
      }

      const sqlQuery = `SELECT ma_kyc_campaign_merchants_details_id,ma_kyc_campaign_details_id,skip_count from ma_kyc_campaign_merchants_details WHERE ma_kyc_campaign_details_id = ${campaignQueryResult[0].ma_kyc_campaign_details_id} and ma_user_id = ${ma_user_id} and merchant_action='P' limit 1`
      log.logger({ pagename: path.basename(__filename), action: 'getrekycData', type: 'sqlQuery', fields: sqlQuery })
      const sqlResult = await this.rawQuery(sqlQuery, connection)
      console.log('merchantdetails', sqlResult)
      log.logger({ pagename: path.basename(__filename), action: 'getrekycData', type: 'sqlResult', fields: sqlResult })
      if (sqlResult.length <= 0) return { status: 400, respcode: 1001, action_code: 1001, message: 'No active campaigns available ' }
      // const form = JSON.parse(campaignQueryResult[0].api_params)
      const campaignId = sqlResult[0].ma_kyc_campaign_details_id
      const campaignName = campaignQueryResult[0].campaign_name
      const priorityStatus = campaignQueryResult[0].priority
      const maxCount = campaignQueryResult[0].skip_count
      const skipCount = sqlResult[0].skip_count
      const data = JSON.parse(campaignQueryResult[0].ma_kyc_elements)
      console.log({ data: data, skip: skipCount, priorityStatus: priorityStatus })

      // Get Profile Data
      const getprofiles = await profileController.getProfileDetails('_', fields)
      // let profileData = []

      console.log('getformValue===', getprofiles)
      const personalDetails = getprofiles.profileDetails
      const maskedAdharNumber = personalDetails.aadharNumber != null ? await this.generateMaskedvalue(personalDetails.aadharNumber) : null
      const maskedPan = personalDetails.pan != null ? await this.generateMaskedvalue(personalDetails.pan) : null
      const personalData = [{
        first_name: personalDetails.firstName,
        last_name: personalDetails.lastName,
        address: personalDetails.address,
        city: personalDetails.city,
        state: personalDetails.state,
        country: personalDetails.country,
        email: personalDetails.email,
        mobile: personalDetails.mobile,
        pincode: personalDetails.pincode,
        pan: maskedPan,
        aadhar: maskedAdharNumber

      }]
      console.log('personalData===', personalData)
      const addformValueResult = await this.addformValue(data, personalData[0])
      console.log('addformValueResult===', addformValueResult)

      // show skip button
      let showSkipButton = true
      if (priorityStatus == 'H' || skipCount >= maxCount) {
        showSkipButton = false
      }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, priority: priorityStatus, campaign_id: campaignId, skipCount: skipCount, show_skip_button: showSkipButton, profile_data: personalData, campaignDataList: [{ ma_campaign_id: campaignId, campaign_name: campaignName, form_data: addformValueResult }] }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getrekycData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (connection) { connection.release() }
    }
  }

  // Checksum function logic

  static async concatenateFormData (formData) {
    try {
      const merchId = formData.merchant_id
      let result = merchId
      const formDataObj = formData.personaldtls
      for (const key in formDataObj) {
        console.log('key==', key)
        const value = formDataObj[key]
        console.log('value==', value)
        result += value
      }
      return result + util.secretkey
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'concatenateFormData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  //  encrypt api call
  static async encryptedData (value) {
    log.logger({ pagename: path.basename(__filename), action: 'encryptedData', type: 'request', fields: { ...value, ...common_fns.maskValue(value.personaldtls, ['pan', 'uid']) } })
    try {
      const config = {
        method: 'POST',
        url: configData.encryptionUrl,
        headers: {
          'Content-Type': 'application/json',
          AFFILIATE: configData.configEncryption.AFFILIATE,
          AIRPAYKEY: configData.configEncryption.AIRPAYKEY,
          CHECKSUM: configData.configEncryption.CHECKSUM
        },
        data: value
      }
      log.logger({ pagename: path.basename(__filename), action: 'encryptedData', type: 'config', fields: { config, ...common_fns.maskValue(config.data.personaldtls, ['pan', 'uid']) } })
      const res = await axios(config)
      log.logger({ pagename: path.basename(__filename), action: 'encryptedData', type: 'res', fields: { ...common_fns.maskValue(res.config.data.personaldtls, ['pan', 'uid']) } })

      if (_.get(res, 'data.query', null)) return { status: 200, data: res.data.query }
      return { status: 400, data: null }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'encryptedData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  // decrypt api call
  static async decryptedData (value) {
    try {
      const config = {
        method: 'POST',
        url: configData.decryptionUrl,
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          query: value
        }
      }
      log.logger({ pagename: path.basename(__filename), action: 'decryptedData', type: 'config', fields: config })
      const res = await axios(config)
      log.logger({ pagename: path.basename(__filename), action: 'decryptedData', type: 'res', fields: res })
      if (res.status != 200) return res
      return res.data
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'decryptedData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  // digilocker api call

  static async digilockerApiCall (_, fields) {
    try {
      const { ma_user_id, userid, mobile_no, stage } = fields
      console.log({ mobile: mobile_no, stage: stage })
      const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'mobile_no', 'stage'])
      log.logger({ pagename: path.basename(__filename), action: 'digilockerApiCall', type: 'validatorResponse', fields: validatorResponse })
      if (validatorResponse.status != 200) return validatorResponse
      const checksumString = mobile_no + stage + util.secretkey
      log.logger({ pagename: require('path').basename(__filename), action: 'digilockerApiCall', type: 'checksumString', fields: checksumString })
      const checksum = crypto.createHash('sha256').update(checksumString).digest('hex')
      log.logger({ pagename: require('path').basename(__filename), action: 'digilockerApiCall', type: 'checksum', fields: checksum })
      console.log('Checksum:', checksum)
      const encryptResult = await this.encryptedData({ mobile_no, stage })
      console.log('encryptResult', encryptResult)

      const config = {
        method: 'POST',
        url: configData.digilockerUrl,
        headers: {
          'Content-Type': 'application/json',
          AIRPAYKEY: configData.configDigilocker.AIRPAYKEY,
          AFFILIATE: configData.configDigilocker.AFFILIATE,
          CHECKSUM: checksum
        },
        data: {
          query: encryptResult.data
        }
      }
      log.logger({ pagename: path.basename(__filename), action: 'digilockerApiCall', type: 'request', fields: config })
      const res = await axios(config)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerApiCall', type: 'res', fields: res })
      if (res.status != 200) return res
      console.log('digilockerresult', res)
      const decryptedResult = await this.decryptedData(res.data.query)
      // if (decryptedResult.status != 200) return decryptedResult
      console.log('decryptedResult:', decryptedResult)
      log.logger({ pagename: path.basename(__filename), action: 'digilockerApiCall', type: 'decryptedResult', fields: decryptedResult })
      return { status: 200, respcode: 1000, action_code: 1000, message: errorMsg.responseCode[1000], data: decryptedResult.data }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'digilockerApiCall', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  // validate form data
  static async validateFormData (data, users) {
    try {
      let isValid = 'valid'
      for (const property in data) {
        console.log('property', property)

        const findByName = _.filter(users, { name: property })

        if (findByName.length > 0) {
          console.log(findByName[0])

          if (_.has(findByName[0], 'validation')) {
            console.log('apply validation rules')

            if (_.has(findByName[0].validation, 'min')) {
              if (data[property].length < findByName[0].validation.min) {
                isValid = 'Min length is not valid'
              }
              // console.log('min = ', findByName[0].validation.min)
              // console.log('data ', property, ' = ', data[property])
            }

            if (_.has(findByName[0].validation, 'max')) {
              console.log('max = ', findByName[0].validation.max)

              console.log('data ', property, ' = ', data[property])
              if (data[property].length > findByName[0].validation.max) {
                isValid = 'Max length is not valid'
              }
            }
            if (_.has(findByName[0].validation, 'regex')) {
              console.log('regex = ', findByName[0].validation.regex)
              const regexValue = findByName[0].validation.regex
              const regexPattern = new RegExp(regexValue)
              if (!regexPattern.test(data[property])) {
                isValid = 'Invalid regex'
              }
            }
          }
        }
      }
      return isValid
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'validateFormData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  // Save form details by calling ms api
  static async saveReKycData (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'request', fields })
    const isSet = (fields.connection === null || fields.connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : fields.connection

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'form_data', 'action_type', 'campaign_id', 'merch_action'])
    log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'validatorResponse', fields: validatorResponse })

    if (validatorResponse.status != 200) return validatorResponse
    try {
      const { ma_user_id, userid, form_data, action_type, campaign_id, merch_action } = fields
      const formData = JSON.parse(Buffer.from(form_data, 'base64').toString('ascii'))
      console.log('FORM DATA', formData)
      // const formData = JSON.parse(form_data)
      // const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'

      const getFormData = `SELECT ma_kyc_elements,skip_count from ma_kyc_campaign_details where ma_kyc_campaign_details_id = ${campaign_id} `
      log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'getmaUserId', fields: getFormData })
      const getFormDataResult = await this.rawQuery(getFormData, conn)
      log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'getFormDataResult', fields: getFormDataResult })
      const maxCount = getFormDataResult[0].skip_count == 0 ? 3 : getFormDataResult[0].skip_count
      console.log('maxCount===', maxCount)
      console.log('getFormData', getFormDataResult)
      const formDataObject = JSON.parse(getFormDataResult[0].ma_kyc_elements)
      console.log('formDataObject', formDataObject)

      const aadharNumber = formData.personaldtls.uid
      const panNumber = formData.personaldtls.pan
      if (action_type == 'skip') {
        // checking the skip counter
        const getSkipCounter = `Select skip_count from ma_kyc_campaign_merchants_details where ma_kyc_campaign_details_id = ${campaign_id} and ma_user_id = ${ma_user_id} limit 1`
        log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'getSkipCounter', fields: getSkipCounter })
        const getSkipCounterResult = await this.rawQuery(getSkipCounter, conn)
        console.log('skipCount', getSkipCounterResult)
        log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'getSkipCounterResult', fields: getSkipCounterResult })

        // checking the condition for skip count
        if (getSkipCounterResult[0].skip_count < maxCount) {
          const updateQuery = `Update ma_kyc_campaign_merchants_details set skip_count = ${getSkipCounterResult[0].skip_count + 1},skip_expiry_time = CURRENT_TIMESTAMP where ma_kyc_campaign_details_id = ${campaign_id} and ma_user_id = ${ma_user_id}  limit 1 `
          const updateQueryResult = await this.rawQuery(updateQuery, conn)
          console.log('skip counter updated by 1')
          log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'updateQueryResult', fields: updateQueryResult })
          return { status: 200, message: errorMsg.responseCode[1000], action_code: 1000, respcode: 1000 }
        }
        return { status: 400, message: errorMsg.responseCode[140010], action_code: 1001, respcode: 1001 }
      } else if (action_type == 'confirm') {
        const isValid = await this.validateFormData(formData.personaldtls, formDataObject.campaign_name)
        if (isValid != 'valid') {
          return { status: 400, respcode: 1001, action_code: 1001, message: isValid }
        } else {
          let encryptResult = await this.encryptedData(formData)
          console.log('encryptResult', encryptResult)
          if (encryptResult.status != 200) return encryptResult
          encryptResult = encryptResult.data

          console.log('urlrekyc', configData.rekycUrl)
          console.log('Airpaykey', configData.configRekyc.AIRPAYKEY)
          console.log('affkey', configData.configRekyc.AFFILIATE)
          // console.log('checksum', configIns.configValue.CHECKSUM)

          // Checksum

          const checksumString = await this.concatenateFormData(formData)
          console.log('checkchecksumString', checksumString)
          log.logger({ pagename: require('path').basename(__filename), action: 'saveRekycConfirmChecksum', type: 'checksumString', fields: checksumString })
          const checksum = crypto.createHash('sha256').update(checksumString).digest('hex')
          console.log('checksum', checksum)

          // calling rekycUpdate api
          const config = {
            method: 'POST',
            url: configData.rekycUrl,
            headers: {
              'Content-Type': 'application/json',
              AIRPAYKEY: configData.configRekyc.AIRPAYKEY,
              AFFILIATE: configData.configRekyc.AFFILIATE,
              CHECKSUM: checksum
            },

            data: {
              query: encryptResult
            }
          }
          log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'confirmApiCall', fields: config })
          const res = await axios(config)
          if (res.status != 200) return res
          console.log('result', res)
          const decryptedResult = await this.decryptedData(res.data.query)
          log.logger({ pagename: path.basename(__filename), action: 'rekyc', type: 'rekycUpdateDecrypt-response', fields: decryptedResult })

          // generating mask value
          const maskedAdharNumber = await this.generateMaskedvalue(aadharNumber)
          const maskedPanNumber = await this.generateMaskedvalue(panNumber)
          console.log({ aadhar: maskedAdharNumber, pan: maskedPanNumber })
          formData.personaldtls.uid = maskedAdharNumber
          formData.personaldtls.pan = maskedPanNumber

          // save form data
          const updateFormData = `Update ma_kyc_campaign_merchants_details set merchant_action = '${merch_action}',details_changed = '${merch_action == 'M' ? 'Y' : 'N'}', form_data = '${JSON.stringify(formData)}' where ma_kyc_campaign_details_id = ${campaign_id} and ma_user_id = ${ma_user_id} limit 1 `
          log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'updateFormData', fields: updateFormData })
          const updateFormDataResult = await this.rawQuery(updateFormData, conn)
          console.log('updateFormDataResult:', updateFormDataResult)
          log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'updateFormDataResult', fields: updateFormDataResult })
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], action_code: 1000, data: decryptedResult }
        }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'saveReKycData', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = rekycController
