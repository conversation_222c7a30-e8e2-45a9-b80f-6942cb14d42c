module.exports = {
  rekycUrl: process.env.NODE_ENV === 'production' ? 'https://leads.airpay.ninja/api/common/rekycUpdate' : 'https://leads.airpay.ninja/api/common/rekycUpdate',
  digilockerUrl: process.env.NODE_ENV === 'production' ? 'https://leads.airpay.ninja/api/personal/digiLockerAadhar' : 'https://leads.airpay.ninja/api/personal/digiLockerAadhar',
  encryptionUrl: process.env.NODE_ENV === 'production' ? 'http://leads.airpay.ninja/api/common/encrypt' : 'http://leads.airpay.ninja/api/common/encrypt',
  decryptionUrl: process.env.NODE_ENV === 'production' ? 'http://leads.airpay.ninja/api/common/decrypt' : 'http://leads.airpay.ninja/api/common/decrypt',
  // logo_url: 'https://retailappdocs.s3.ap-south-1.amazonaws.com/Recharges/operators/logos/shighrapay-v2-png.png',
  configRekyc: {
    AIRPAYKEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXEnc==',
    AFFILIATE: 'a5018c4e6770be90ceb89171fd8a1b61'

  },
  configDigilocker: {
    AIRPAYKEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXEnc==',
    AFFILIATE: 'f767644e159268bb3fb7720604fff4c7'
  },
  configEncryption: {
    AIRPAYKEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXEnc==',
    AFFILIATE: 'f767644e159268bb3fb7720604fff4c7',
    CHECKSUM: '76a9024905b1b3d01278dbf90e3393f7e8e5be080f725665154831e1f1e92368'

  }
}
