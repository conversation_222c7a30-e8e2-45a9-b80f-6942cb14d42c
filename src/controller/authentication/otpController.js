/* eslint-disable camelcase */
const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const checksum = require('../../util/checksum')
const balance = require('../balance/balanceController')
const transaction = require('../transaction/transactionController')
const sms = require('../../util/sms')
const jwt = require('../../util/token')
const comission = require('../commission/commissionController')
const pointsledger = require('../creditDebit/pointsLedgerController')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const bbpsinecntive = require('../incentive/bbpsIncentiveController')
const rechargeIncentiveController = require('../incentive/rechargeIncentiveController')
const integrated = require('../integrated/integratedController')
const log = require('../../util/log')

// const { lokiLogger } = require('../../util/lokiLogger')

class Otp extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  static get TABLE_NAME() {
    return 'ma_otp_master'
  }

  static get PRIMARY_KEY() {
    return 'ma_otp_master_id'
  }

  /*
    Fetch user details from mobile number
  */
  static async getUser(mobile, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      const sql = `Select profileid,userid,distributer_user_master_id,user_type,state, CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan from ma_user_master where mobile_id=${mobile} AND user_status = "Y"`
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getUser', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getUser', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  /*
    Fetch data for verify otp
  */
  static async getVerifyData(aggregator_order_id, connection = null) {
    var isSet = false
    try {
      // if (connection === null || connection === undefined) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
      // }
      console.log('getVerifyDataConn>>', connection)
      const sql = 'SELECT * FROM ma_otp_master WHERE aggregator_order_id = "' + aggregator_order_id + '"  AND otp_type = "BBPS"  AND expiry > CURRENT_TIMESTAMP ORDER BY ma_otp_master_id DESC LIMIT 1'
      console.log('getVerifyDataConn>>', connection)
      console.log('getVerifyDataSQL>>', sql)
      const queryResult = await this.rawQuery(sql, connection)
      console.log('getVerifyDataSQLQueryResult>>', queryResult)
      return queryResult
    } catch (err) {
      console.log('getVerifyDataError', err)
      return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] + err.message }
    } finally {
      if (isSet) connection.release()
    }
  }

  /*
    Fetch data for resent otp
  */
  static async getResentData(mobile, aggregator_order_id, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'SELECT otp,expiry,otp_template FROM ma_otp_master WHERE aggregator_order_id = "' + aggregator_order_id + '" AND mobile = ' + mobile + ' AND otp_type = "BBPS"  AND expiry > CURRENT_TIMESTAMP ORDER BY ma_otp_master_id DESC LIMIT 1'
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getResentData', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getResentData', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  /**
     * Creates a new  entry in otp master table
     */
  static async createEntry(_, fields) {
    console.log('create entry fields', fields)
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)
      fields.expiry = await this.getSQLExpiry(connection, fields.otp_type)
      const _result = await this.insert(connection, {
        data: fields
      })
      await mySQLWrapper.commit(connection)
      return _result
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      console.log('create entry error', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (connection._pool._freeConnections.indexOf(connection) === -1) connection.release()
    }
  }

  /**
     * Updates entry in otp master table
     */
  static async updateEntry(_, { aggregator_order_id, retry_count, flag }) {
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      await mySQLWrapper.beginTransaction(connection)
      const _result = await this.updateWhere(connection, {
        id: aggregator_order_id,
        where: 'aggregator_order_id',
        data: {
          retry_count,
          flag
        }
      })
      await mySQLWrapper.commit(connection)
      if (_result.affectedRows > 0) {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else {
        return { status: 400, respcode: 1023, message: errorMsg.responseCode[1023] }
      }
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      console.log('update entry error', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'updateEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Releases the connection
      if (connection._pool._freeConnections.indexOf(connection) === -1) connection.release()
    }
  }

  /**
     * Logic for sending otp to perform bbps transaction
     */
  static async sentOtp(_, fields) {
    const _user = await this.getUser(fields.mobile)
    const response = {}
    const generatedChecksum = checksum.checksum(fields.mobile + fields.aggregator_txn_id + fields.aggregator_order_id + fields.amount)
    if (generatedChecksum === fields.checksum) {
      if (_user.length > 0) {
        var params = {}
        params = {
          aggregator_order_id: fields.aggregator_order_id
        }
        const trans_data = await transaction.findMatching('', params)
        if (trans_data.length > 0) {
          if (trans_data[0].amount + trans_data[0].commission_amount == fields.amount) {
            const otp = util.isProduction() ? await this.generateOTP(6) : util.defaultOtp
            const expiry = await this.getExpiry('expiry')
            // const message = 'Dear Customer, your OTP to perform transaction is' + otp + '. This OTP will be valid for 5 minutes. Team Airpay!'
            var message = util.communication.BBPS
            message = message.replace('<Customer>', 'Customer')
            message = message.replace('<OTP>', otp)
            message = message.replace('<TIME>', util.expiryComm.BBPS)
            message = message.replace('<Salutation>', util.communication.Signature)
            const _data = await this.createEntry(_, {
              ma_user_id: _user[0].profileid,
              userid: _user[0].userid,
              otp: otp,
              mobile: fields.mobile,
              expiry: expiry,
              aggregator_order_id: fields.aggregator_order_id,
              retry_count: 0,
              flag: '1',
              otp_type: 'BBPS',
              otp_template: message
            })
            console.log('fields for create entry', _data)
            if (_data.affectedRows > 0) {
              // send otp and generate token
              // send otp and generate token

              await sms.sentSmsAsync(message, fields.mobile, util.templateid.BBPS, 'otp')
              const publickey = { authentication: _user[0].userid + fields.mobile }
              const token = await jwt.signTokenAuth(publickey)

              response.status = 200
              response.message = errorMsg.responseCode[1000]
              response.respcode = 1000
              response.token = token
              response.transaction = trans_data[0]
            } else {
              response.status = 400
              response.respcode = 1001
              response.message = errorMsg.responseCode[1001]
            }
          } else {
            response.status = 400
            response.respcode = 1015
            response.message = errorMsg.responseCode[1015]
          }
        } else {
          response.status = 400
          response.respcode = 1012
          response.message = errorMsg.responseCode[1012]
        }
      } else {
        response.status = 400
        response.respcode = 1003
        response.message = errorMsg.responseCode[1003]
      }
    } else {
      response.status = 400
      response.respcode = 1004
      response.message = errorMsg.responseCode[1004]
    }
    return response
  }

  /**
     * Logic to verify otp
     */
  static async verifyOtp(fields) {
    console.log('fields for verify', fields)
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const response = {}
      const generatedChecksum = checksum.checksum(fields.mobile + fields.otp + fields.transaction_id + fields.amount + fields.aggregator_order_id)
      if (generatedChecksum === fields.checksum) {
        const _user = await this.getUser(fields.mobile, connection)
        if (_user.length > 0) {
          var params = {}
          params = {
            aggregator_order_id: fields.aggregator_order_id
          }
          const _data = await transaction.findMatching('', params)

          const _verify = await this.getVerifyData(fields.aggregator_order_id, connection)
          var transaction_data = {}
          const actualamount = fields.amount - _data[0].commission_amount
          if (_verify.length > 0) {
            if (_verify[0].otp === fields.otp) {
              var data = {
                ma_user_id: _user[0].profileid,
                ma_status: 'ACTUAL',
                balance_flag: 'SUMMARY',
                connection
              }
              // const _balance = await balance.getPointsBalance('', data)
              await mySQLWrapper.beginTransaction(connection)
              const _balance = await balance.getWalletBalancesDirect('', data)
              // const comission_val = await comission.getCommission('', { ma_user_id: _user[0].profileid, ma_commission_type: '6', amount: fields.amount, ma_deduction_type: '2' })

              if (_balance.amount >= fields.amount) {
                // debit creidt api
                // update txn
                // debit creidt api
                // update txn

                const _creddeb = await pointsledger.sendMoney('', { distributorId: _user[0].profileid, retailerId: util.airpayUserId, amount: actualamount, orderid: fields.aggregator_order_id, commissionType: _data[0].transaction_type, userid: _user[0].userid, txnStatus: 'S', connection: connection })
                const incfields = {
                  commission_amount: _data[0].commission_amount,
                  amount: actualamount,
                  ma_user_id: _user[0].profileid,
                  orderid: fields.aggregator_order_id,
                  userid: _user[0].userid,
                  user_type: _user[0].user_type,
                  distributer_user_master_id: _user[0].distributer_user_master_id,
                  stateid: _user[0].state,
                  pan: _user[0].pan,
                  connection
                }
                var incentive_distribution
                if (_data[0].transaction_type == 17) {
                  incentive_distribution = await rehcargeIncentive.incentiveDistribution(incfields)
                } else {
                  incentive_distribution = await bbpsinecntive.incentiveDistribution(incfields)
                }
                if (_creddeb.status === 200 && incentive_distribution.status === 200) {
                  transaction_data = {
                    aggregator_order_id: fields.aggregator_order_id,
                    transaction_status: 'S',
                    amount: actualamount,
                    aggregator_txn_id: fields.transaction_id

                  }
                  response.status = '200'
                  response.message = errorMsg.responseCode[1000]
                  response.respcode = 1000
                  await mySQLWrapper.commit(connection)
                } else {
                  transaction_data = {
                    aggregator_order_id: fields.aggregator_order_id,
                    transaction_status: 'F',
                    amount: actualamount,
                    aggregator_txn_id: fields.transaction_id

                  }
                  response.status = 400
                  response.message = errorMsg.responseCode[1001]
                  response.respcode = 1001
                  await mySQLWrapper.rollback(connection)
                }

                // success
                // success
              } else {
                // update txn
                // insufficient funds
                // update txn
                // insufficient funds
                transaction_data = {
                  aggregator_order_id: fields.aggregator_order_id,
                  transaction_status: 'F',
                  amount: actualamount,
                  aggregator_txn_id: fields.transaction_id

                }
                response.status = 400
                response.message = errorMsg.responseCode[1005]
                response.respcode = 1005
              }
              await transaction.updateTransaction('', transaction_data)

              const trans_data = await transaction.findMatching('', params)
              await this.updateEntry('', { aggregator_order_id: fields.aggregator_order_id, retry_count: _verify[0].retry_count + 1, flag: '2' })
              response.transaction = trans_data[0]
            } else {
              if (_verify[0].retry_count >= 3) {
                const transaction_data = {
                  aggregator_order_id: fields.aggregator_order_id,
                  transaction_status: 'F',
                  amount: actualamount,
                  aggregator_txn_id: fields.transaction_id

                }
                await transaction.updateTransaction('', transaction_data)
                const trans_data = await transaction.findMatching('', params)

                response.status = 400
                response.message = errorMsg.responseCode[1006]
                response.respcode = 1006
                response.transaction = trans_data[0]
                // update txn to failed
                // update txn to failed
              } else {
                // update retry & status
                // update retry & status
                await this.updateEntry('', { aggregator_order_id: fields.aggregator_order_id, retry_count: _verify[0].retry_count + 1, flag: '3' })
                response.status = 400
                response.message = errorMsg.responseCode[1007]
                response.respcode = 1007
              }
            }
          } else {
            const transaction_data = {
              aggregator_order_id: fields.aggregator_order_id,
              transaction_status: 'F',
              amount: actualamount,
              aggregator_txn_id: fields.transaction_id

            }
            await transaction.updateTransaction('', transaction_data)
            const trans_data = await transaction.findMatching('', params)
            response.status = 400
            response.message = errorMsg.responseCode[1008]
            response.respcode = 1008
            response.transaction = trans_data[0]
          }
        } else {
          response.status = 400
          response.message = errorMsg.responseCode[1003]
          response.respcode = 1003
        }
      } else {
        response.status = 400
        response.respcode = 1004
        response.message = errorMsg.responseCode[1004]
      }
      return response
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      console.log('update entry error', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /**
     * Logic to resent otp
     */
  static async resentOtp(fields) {
    const response = {}
    const generatedChecksum = checksum.checksum(fields.mobile + fields.aggregator_order_id)
    if (generatedChecksum === fields.checksum) {
      const _user = await this.getUser(fields.mobile)
      if (_user.length > 0) {
        const _resent = await this.getResentData(fields.mobile, fields.aggregator_order_id)
        console.log(_resent)
        if (_resent.length > 0) {
          // const message = ' Dear Customer,The OTP to perform transaction is: ' + _resent[0].otp + '. This OTP will be valid for 5 minutes. Team Airpay!'
          await sms.sentSmsAsync(_resent[0].otp_template, fields.mobile, util.templateid.BBPS, 'otp')
          response.status = 200
          response.message = errorMsg.responseCode[1000]
          response.respcode = 1000
        } else {
          response.status = 400
          response.message = errorMsg.responseCode[1001]
          response.respcode = 1001
        }
        // } else {
        //   response.status = '401'
        //   response.message = 'invalid token'
        //   response.respcode = 1009
        // }
      } else {
        response.status = 400
        response.message = errorMsg.responseCode[1003]
        response.respcode = 1003
      }
    } else {
      response.status = 400
      response.respcode = 1004
      response.message = errorMsg.responseCode[1004]
    }
    return response
  }

  /**
     * Generate random number
     */
  static async generateOTP(length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  /**
     * Calculate date
     */
  static async getExpiry(type) {
    console.log(type)
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    if (type === 'expiry') {
      console.log('inside type')
      if (timezone !== -330) {
        today.setTime(today.getTime() + (util.expiry.BBPS * 60 * 1000) + (30 * 60 * 1000) + (5 * 60 * 60 * 1000)) // time converted to ist from utc for testing purpose on kubeless. On live remove the 5:30 from date calculation.
      } else {
        today.setTime(today.getTime() + (util.expiry.BBPS * 60 * 1000))
      }
      // today.setTime(today.getTime() + (5 * 60 * 1000) + (30 * 60 * 1000) + (5 * 60 * 60 * 1000)) // time converted to ist from utc for testing purpose on kubeless. On live remove the 5:30 from date calculation.
      const min = today.getMinutes()
      const sec = today.getSeconds()
      const hh = today.getHours()
      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${sec}`
    } else {
      return `${dd}${mm}${yyyy}`
    }
  }

  static async getOtp(headers, fields, connection = null) {
    var isSet = false
    var aggregatorOrderId = fields.aggregator_order_id
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const sql = 'SELECT otp FROM ma_otp_master WHERE aggregator_order_id = "' + aggregatorOrderId + '"  AND expiry > CURRENT_TIMESTAMP ORDER BY ma_otp_master_id DESC LIMIT 1'
      const queryResult = await this.rawQuery(sql, connection)

      if (queryResult.length > 0) {
        return { status: 200, message: queryResult[0].otp }
      } else {
        return { status: 400, respcode: 1023, message: errorMsg.responseCode[1023] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtp', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getOtp', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async getSQLExpiry(connection, otp_type = null) {
    const sqlExpiry = `SELECT TIMESTAMPADD(MINUTE,${util.expiry[otp_type]},CURRENT_TIMESTAMP()) as expiryTime`
    const resultExiry = await this.rawQuery(sqlExpiry, connection)
    console.log('resultExiry>>', resultExiry)
    return resultExiry[0].expiryTime
  }

  static async verifySecurePin(fields) {
    console.log('fields for verifySecurePin>>', fields)
    console.time('TIMER_verifySecurePin_getConnectionFromPool')
    const connection = await mySQLWrapper.getConnectionFromPool()
    console.timeEnd('TIMER_verifySecurePin_getConnectionFromPool')
    try {
      const response = {}
      const generatedChecksum = checksum.checksum(fields.mobile + fields.security_pin + fields.transaction_id + fields.amount + fields.aggregator_order_id)
      console.log('generatedChecksumString>>', fields.mobile + fields.security_pin + fields.transaction_id + fields.amount + fields.aggregator_order_id)
      console.log('generatedChecksum>>', generatedChecksum)
      
      if (generatedChecksum === fields.checksum || fields.testmode) {
        //  if (true) { // for testing
        //  if (true) { // for testing
        console.time('TIMER_verifySecurePin_getOrderUser')
        const _user = await this.getOrderUser(fields.aggregator_order_id, connection)
        console.timeEnd('TIMER_verifySecurePin_getOrderUser')
        if (_user.length > 0) {
          var params = {}
          params = {
            aggregator_order_id: fields.aggregator_order_id
          }
          console.time('TIMER_verifySecurePin_findMatching')
          const _data = await transaction.findMatching('', params)
          console.timeEnd('TIMER_verifySecurePin_findMatching')

          // const _verify = await this.getVerifyData(fields.aggregator_order_id, connection)

          /** ** Verify Security Pin Tempory commented don't delete */
          console.time('TIMER_verifySecurePin_verifySecurePin')
          const securePinCtrl = require('../securityPin/securityPinController')
          const securePinData = await securePinCtrl.verifySecurePin(null, {
            ma_user_id: _user[0].profileid,
            userid: _user[0].userid,
            security_pin: fields.security_pin,
            connection: connection
          })
          console.timeEnd('TIMER_verifySecurePin_verifySecurePin')
          console.log('securePinData>>', securePinData)
          /** ** Verify Security Pin End Tempory commented don't delete */
          var transaction_data = {}
          const actualamount = fields.amount - _data[0].commission_amount
          
          if (securePinData.status === 200) {
            // Check if integrated
            console.time('TIMER_verifySecurePin_integratedMer')
            const userSQL = `SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =${_user[0].userid}`
            const integratedMer = await this.rawQuery(userSQL, connection)
            console.log('Integrated Merchant OTP', integratedMer, _data)
            console.log("SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =" + _user[0].userid)
            console.log('Integrated Merchant OTP', integratedMer, _data)
            console.log("SELECT id,aggregator_user_id FROM users WHERE user_type='integrated' and id =" + _user[0].userid)
            if (integratedMer.length > 0 && (_data[0].transaction_type == '6' || _data[0].transaction_type == '17')) {
              var intData = {}
              intData.action = 'CREATETXN'
              intData.aggregator_user_id = integratedMer[0].aggregator_user_id
              intData.aggregator_order_id = fields.aggregator_order_id
              intData.amount = fields.amount + _data[0].commission_amount
              intData.ma_user_id = _data[0].ma_user_id
              intData.transaction_type = _data[0].transaction_type
              const resInt = await integrated.index(intData, connection)
              console.log('integrated returned this', resInt)
              if (resInt.status != 200) {
                return resInt
              }
            }
            console.timeEnd('TIMER_verifySecurePin_integratedMer')

            var data = {
              ma_user_id: _user[0].profileid,
              ma_status: 'ACTUAL',
              balance_flag: 'SUMMARY',
              connection
            }
            // const _balance = await balance.getPointsBalance('', data)

            console.time('TIMER_verifySecurePin_getWalletBalancesDirect')
            const _balance = await balance.getWalletBalancesDirect('', data)
            console.timeEnd('TIMER_verifySecurePin_getWalletBalancesDirect')
            // const comission_val = await comission.getCommission('', { ma_user_id: _user[0].profileid, ma_commission_type: '6', amount: fields.amount, ma_deduction_type: '2' })
            if (_balance.amount >= fields.amount) {
              // debit creidt api
              // update txn 
          
              console.time('TIMER_verifySecurePin_sendMoney')
              await mySQLWrapper.beginTransaction(connection)
              const _creddeb = await pointsledger.sendMoney('', { distributorId: _user[0].profileid, retailerId: util.airpayUserId, amount: actualamount, orderid: fields.aggregator_order_id, commissionType: _data[0].transaction_type, userid: _user[0].userid, txnStatus: 'S', connection: connection })
              console.timeEnd('TIMER_verifySecurePin_sendMoney')
              
              if (_data[0].transaction_type == '6' && _data[0].commission_amount > 0) {
                console.time('TIMER_verifySecurePin_getSystemCodesByVal')
                const common = require('../../util/common')
                const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '14' }, connection)
                const descType = descObj.code_desc ? descObj.code_desc : ''
                console.timeEnd('TIMER_verifySecurePin_getSystemCodesByVal')

                console.time('TIMER_verifySecurePin_BBPSSurcharge')
                const pointsLedgerAP = await pointsledger.createEntry('_', {
                  ma_user_id: _user[0].profileid,
                  amount: _data[0].commission_amount,
                  mode: 'dr',
                  transaction_type: 14, // BBPS surcharge type
                  // description: util.surchargePointsDebitDescription + 'BBPS',
                  description: 'Debit - ' + descType,
                  orderid: fields.aggregator_order_id,
                  userid: _user[0].userid,
                  ma_status: 'S',
                  corresponding_id: util.airpayCommissionId, // for noe apcomissionid
                  connection: connection
                })
                console.timeEnd('TIMER_verifySecurePin_BBPSSurcharge')

                console.log('pointsLedgerAP>>', pointsLedgerAP)
               
                if (pointsLedgerAP.status === 400) {
                  await mySQLWrapper.rollback(connection)
                  return pointsLedgerAP
                }
                console.time('TIMER_verifySecurePin_pointsDetails')
                var pointsDetailsEntries = {}
                // getWalletBalance replaced here
                pointsDetailsEntries = await balance.getWalletBalanceDirect('_', {
                  ma_user_id: _user[0].profileid,
                  amount: _data[0].commission_amount,
                  transactionType: '1',
                  connection: connection
                })
                console.log('2. wallet points balance', pointsDetailsEntries)
                if (pointsDetailsEntries.status === 400) {
                  // if (isSet) await mySQLWrapper.rollback(connection)
                  return pointsDetailsEntries
                }

                const pointsDetailsController = require('../creditDebit/pointsDetailsController')
                for (var i = 0; i < pointsDetailsEntries.details.length; i++) {
                  const entry = await pointsDetailsController.createEntry('_', {
                    ma_user_id: _user[0].profileid,
                    amount: pointsDetailsEntries.details[i].deductionAmount,
                    wallet_type: pointsDetailsEntries.details[i].wallet_type,
                    ma_points_ledger_master_id: pointsLedgerAP.id,
                    orderid: fields.aggregator_order_id,
                    ma_status: 'S',
                    connection: connection
                  })
                  if (entry.status === 400) {
                    // if (isSet) await mySQLWrapper.rollback(connection)
                    console.timeEnd('TIMER_verifySecurePin_pointsDetails')
                    return entry
                  }
                }
                console.timeEnd('TIMER_verifySecurePin_pointsDetails')
              } else {
                console.log('Commission Amount is zero or less than zero')
              }
              // write 
              let module = ''
              if (_data[0].transaction_type == '6') {
                module = 'bbps';
              }else if(_data[0].transaction_type == '17'){
                module= 'recharge'
              }

              const rawSql = `SELECT provider_name,utility_name FROM ma_transaction_master_details WHERE transaction_id = '${_data[0].transaction_id}' LIMIT 1;`
              const getProviderName = await this.rawQuery(rawSql, connection)
              const utility_name = getProviderName[0].utility_name
              const providerName = getProviderName[0].provider_name

              if (providerName === undefined || providerName === null) {
                return getProviderName;
              }
              const { charge, gst, afterDeduct, bankCharge, txnAmount, minAmount, maxAmount, type, bankName, isGstInclusive } = await this.calculatePaymodeCharges({ amount: _data[0].amount, type: module, ma_user_id: _data[0].ma_user_id, userid: _data[0].userid, commission_amount: _data[0].commission_amount, orderid: _data[0].aggregator_order_id, state_master_id: _user[0].state, connection: connection, provider_name: providerName, connection, utility_name: utility_name })
              
              const tempObj = { bankCharge, txnAmount, type, bankName, isGstInclusive }
              tempObj.platformFee = charge + gst
              log.logger({ pagename: require('path').basename(__filename), action: 'rechargeLedgerEntries', type: 'paymode charge process', fields: { charge: charge, GST: gst, otherDetails: tempObj } })

              fields.orderid = _data[0].aggregator_order_id
              fields.userid = _data[0].userid
              fields.ma_user_id = _data[0].ma_user_id
              fields.connection = connection
              fields.transaction_type = _data[0].transaction_type
              tempObj.provider_name = providerName
              tempObj.transaction_type = _data[0].transaction_type
              tempObj.utility_name = utility_name
          
              if (charge > 0) {
                if ((Number(fields.amount) >= Number(minAmount)) && (Number(fields.amount) <= Number(maxAmount))) {
                  console.log('<< slab range passed >>')

                  const chargesResponse = await this.chargesLedgerEntries(fields, charge, gst, tempObj)
                  log.logger({ pagename: require('path').basename(__filename), action: 'aepsLedgerEntries', type: 'chargesResponse', fields: chargesResponse })
                }
              }
              else{
                let res = await this.paymodeChargesRecord(fields, tempObj)
                log.logger({ pagename: require('path').basename(__filename), action: 'verifySecurePin', type: 'paymodeCharges', fields: res })
              }
              /*
              const incfields = {
                commission_amount: _data[0].commission_amount,
                amount: actualamount,
                ma_user_id: _user[0].profileid,
                orderid: fields.aggregator_order_id,
                userid: _user[0].userid,
                user_type: _user[0].user_type,
                distributer_user_master_id: _user[0].distributer_user_master_id,
                stateid: _user[0].state,
                pan: _user[0].pan,
                connection
              }
              var incentive_distribution
              if (_data[0].transaction_type == 17) {
                incentive_distribution = await rehcargeIncentive.incentiveDistribution(incfields)
              } else {
                incentive_distribution = await bbpsinecntive.incentiveDistribution(incfields)
              }
              */

              if (_creddeb.status === 200) {
                transaction_data = {
                  aggregator_order_id: fields.aggregator_order_id,
                  transaction_status: 'P',
                  amount: actualamount,
                  aggregator_txn_id: fields.transaction_id

                }
                response.status = '200'
                response.message = errorMsg.responseCode[1000]
                response.respcode = 1000
                await mySQLWrapper.commit(connection)
              } else {
                transaction_data = {
                  aggregator_order_id: fields.aggregator_order_id,
                  transaction_status: 'P',
                  amount: actualamount,
                  aggregator_txn_id: fields.transaction_id

                }
                response.status = 400
                response.message = errorMsg.responseCode[1001]
                response.respcode = 1001
                await mySQLWrapper.rollback(connection)
              }

              // success
            } else {
              // update txn
              // insufficient funds
              transaction_data = {
                aggregator_order_id: fields.aggregator_order_id,
                transaction_status: 'P',
                amount: actualamount,
                aggregator_txn_id: fields.transaction_id

              }
              response.status = 400
              response.message = errorMsg.responseCode[1005]
              response.respcode = 1005
            }
            console.time('TIMER_verifySecurePin_updateTransaction')
            await transaction.updateTransaction('', transaction_data)
            console.timeEnd('TIMER_verifySecurePin_updateTransaction')

            const trans_data = await transaction.findMatching('', params)

            response.transaction = trans_data[0]
          } else {
            const transaction_data = {
              aggregator_order_id: fields.aggregator_order_id,
              transaction_status: 'P',
              amount: actualamount,
              aggregator_txn_id: fields.transaction_id,
              remarks: securePinData.message
            }
            console.time('TIMER_verifySecurePin_updateTransaction')
            await transaction.updateTransaction('', transaction_data)
            console.timeEnd('TIMER_verifySecurePin_updateTransaction')
            const trans_data = await transaction.findMatching('', params)
            response.status = 400
            response.message = securePinData.message
            response.respcode = securePinData.respcode
            response.transaction = trans_data[0]
          }
        } else {
          response.status = 400
          response.message = errorMsg.responseCode[1003]
          response.respcode = 1003
        }
      } else {
        response.status = 400
        response.respcode = 1004
        response.message = errorMsg.responseCode[1004]
      }
      return response
    } catch (err) {
      await mySQLWrapper.rollback(connection)
      console.log('verifySecurePin update entry error', err)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifySecurePin', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'verifySecurePin', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  /*
    Fetch user details from mobile number
  */
  static async getOrderUser(aggregator_order_id, connection = null) {
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      var decryptionKey = util[env].secondaryEncrptionKey
      const sql = `select mum.profileid,mum.userid,mum.distributer_user_master_id,mum.user_type,mum.state,CAST(AES_DECRYPT(mum.pan,'${decryptionKey}') AS CHAR) as pan from ma_transaction_master as tm  
      JOIN ma_user_master as mum on tm.ma_user_id = mum.profileid AND tm.userid = mum.userid 
      where tm.aggregator_order_id='${aggregator_order_id}' AND mum.user_status = "Y"`
      const queryResult = await this.rawQuery(sql, connection)
      return queryResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getOrderUser', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getOrderUser', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async calculatePaymodeCharges(fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'request', fields: fields })
    try {
      let data = ''
      if (fields.type == 'recharge') {
        data = await this.getDistributionRecharge(fields)
      }
      else if (fields.type == 'bbps') {
        // data = await bbpsinecntive.incentiveDistribution(fields);
        data = await this.getDistribution(fields)
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'collectMoney getDistribution data', fields: data })

      let charge = 0
      let bank_charge = 0
      let gst = 0
      let afterDeduct = 0
      let finalData = {}
      let isGstInclusive = false

      if (data.status == 200 &&  data[0].merchant_charges > 0) {
        // percentage
        if (data[0].merchant_charges_applied_type == '2') {
          charge = fields.amount * (data[0].merchant_charges / 100)
          console.log('charge: ' + charge)
        } else {
          charge = data[0].merchant_charges // for merchant_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        if (data[0].bank_charges_applied_type == '2') {
          bank_charge = fields.amount * (data[0].bank_charges / 100)
          console.log('charge: ' + charge)
        } else {
          bank_charge = data[0].bank_charges // for bank_charges_applied_type = 1 (fixed amt)
          console.log('charge: ' + charge)
        }

        // gst calculation
        if (data[0].gst_applied_calculation_type == 'E') {
          gst = charge * (data[0].gst_percentage / 100)
        } else if (data[0].gst_applied_calculation_type == 'I') {
          isGstInclusive = true
          gst = charge - (charge * (100 / (100 + data[0].gst_percentage)))
          gst = gst * -1 // Negating for deduction
        }

        // round off gst to 2 decimals
        gst = Math.round(gst * 100) / 100
        console.log('gst: ' + Math.abs(gst))

        afterDeduct = Number(fields.amount) - (charge + gst)
        console.log('afterDeduct: ' + afterDeduct)

        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: data[0].min_amount,
          maxAmount: data[0].max_amount,
          type: data[0].type,
          bankName: data[0].bank_name,
          isGstInclusive: isGstInclusive
        }
      } else {
        finalData = {
          charge: charge,
          gst: gst,
          afterDeduct: afterDeduct,
          bankCharge: bank_charge,
          txnAmount: Number(fields.amount),
          minAmount: 0,
          maxAmount: 0,
          type: '',
          bankName: '',
          isGstInclusive: isGstInclusive
        }
      }
      
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'calc result', fields: finalData })
      return finalData
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'calculatePaymodeCharges', type: 'error', fields: err })
      return err
    }
  }

  static async chargesLedgerEntries(fields, charge, gst, tempObj) {
    log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'fields', fields: fields })
    log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'tempObj', fields: tempObj })
    const common = require('../../util/common')
    const BalanceController = require('../balance/balanceController')
    const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '70' }, fields.connection)
    const chargeDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '71' }, fields.connection)
    const gstDescObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '72' }, fields.connection)
    const pointsDetailsEntries = await BalanceController.getWalletBalanceDirect('_', {
      ma_user_id: fields.ma_user_id,
      amount: tempObj.isGstInclusive ? charge : charge + gst,
      transactionType: '1',
      connection: fields.connection
    })
    log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
    if (pointsDetailsEntries.status === 400) {
      return pointsDetailsEntries
    }
    //const pointsLedger = require('../creditDebit/pointsLedgerController')
    const chargePoints = await pointsledger.createEntry('_', {
      ma_user_id: fields.ma_user_id,
      amount: tempObj.isGstInclusive ? charge : charge + gst,
      mode: 'dr',
      transaction_type: '70',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: descObj.code_desc ? descObj.code_desc : 'Platform Fee',
      orderid: fields.orderid,
      userid: fields.userid,
      corresponding_id: util.airpayCommissionId,
      ma_status: 'S',
      connection: fields.connection
    }
    )
    if (chargePoints.status === 400) {
      return chargePoints
    }
    const PointsDetailsController = require('../creditDebit/pointsDetailsController')
    for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
      const entry = await PointsDetailsController.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: pointsDetailsEntries.details[i].deductionAmount,
        wallet_type: pointsDetailsEntries.details[i].wallet_type,
        ma_points_ledger_master_id: chargePoints.id,
        orderid: fields.aggregator_order_id,
        ma_status: 'S',
        connection: fields.connection
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'chargesLedgerEntries', type: 'PointsDetailsEntries', fields: entry })
      if (entry.status === 400) {
        return entry
      }
    }

    const customerCharge = await pointsledger.createEntry('_', {
      ma_user_id: util.airpayCommissionId,
      amount: tempObj.isGstInclusive ? charge + gst : charge,
      mode: 'cr',
      transaction_type: '71',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: chargeDescObj.code_desc ? chargeDescObj.code_desc : 'Net Platform Fee',
      orderid: fields.orderid,
      userid: fields.userid,
      corresponding_id: util.airpayUserId,
      ma_status: 'S',
      connection: fields.connection
    }
    )
    if (customerCharge.status === 400) {
      return customerCharge
    }

    const gstCharge = await pointsledger.createEntry('_', {
      ma_user_id: util.airpayCommissionId,
      amount: Math.abs(gst),
      mode: 'cr',
      transaction_type: '72',
      // description: util.AEPSCreditAirpay + ' for Collect Money',
      description: gstDescObj.code_desc ? gstDescObj.code_desc : 'GST on Platform Fee',
      orderid: fields.orderid,
      userid: fields.userid,
      corresponding_id: util.airpayUserId,
      ma_status: 'S',
      connection: fields.connection
    }
    )

    if (gstCharge.status === 400) {
      return gstCharge
    }

    return await this.paymodeChargesRecord(fields, tempObj)
  }
  static async getDistributionRecharge(fields) {
    const common_fns = require('../../util/common_fns')

    log.logger({ pagename: 'otpController.js', action: 'getDistributionRecharge', type: 'request', fields: fields})
    try {
      const checkOtherConfigurationFields = { ...fields }
      delete fields.distributerIdForGetDistribution
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var sql = `SELECT * FROM ma_slabwise_distribution_recharge where ${fields.amount} between min_amount and max_amount and record_status='Y' and provider_name='${fields.provider_name}'`


      // Check condition for integrated users
      // add user id condition, to add check for whether user is web or emitra
      const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.id = ${fields.userid} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id limit 1 `
      const resultint = await this.rawQuery(userintegrated, fields.connection)
      log.logger({ pagename: 'otpController.js', action: 'getDistributionRecharge', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        ma_user_id = util.integratedIncentive[resultint[0].integration_code]
        result = {}
        const retailerintegratedSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerintegratedSql, fields.connection)
        log.logger({ pagename: 'otpController.js', action: 'getDistributionRecharge', type: 'response - Integrated Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        } else {
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
        }
      }

      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerSql, fields.connection)
        log.logger({ pagename: 'otpController.js', action: 'getDistributionRecharge', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
      const commonFunction = require('../common/commonFunctionController')
      // Check other configuration (For now this is for distributor specific)
      result = {}
      console.log('sql>>>>>>>>>', sql)
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields: checkOtherConfigurationFields, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      // Reinitializing variables
      ma_user_id = 0
      result = {}

      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        // state_master_id = 2 // FOR TESTING ONLY
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: 'otpController.js', action: 'getDistributionRecharge', type: 'response - State Specific', fields: result })
        result = await this.rawQuery(stateSql, fields.connection)
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}

      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      result = await this.rawQuery(defaultSql, fields.connection)
      log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
  static async paymodeChargesRecord(fields, tempObj) {
    const common = require('../../util/common')
    

    const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: tempObj.transaction_type }, fields.connection)
    const descType = descObj.code_desc ? descObj.code_desc : 'Collect Money'
    const platformFeeAmt = Math.abs(tempObj.platformFee)
    const bankName = tempObj.bankName ? tempObj.bankName : ''

    var sub_transaction_type = '';
    if(fields.transaction_type == '17'){
      sub_transaction_type = tempObj.provider_name
    }
    else if(fields.transaction_type == '6'){
      sub_transaction_type = tempObj.utility_name
    }

    // insert bank_charges
    const bankChargesQuery = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges) VALUES (${fields.ma_user_id}, '${fields.orderid}', 'Bank charges', '${bankName}', '${descType}', '${sub_transaction_type}', ${tempObj.txnAmount}, ${tempObj.bankCharge})`
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'insert bank charges query', fields: bankChargesQuery })
    const bankChargesResult = await this.rawQuery(bankChargesQuery, fields.connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'bank charges query result', fields: bankChargesResult })

    // insert platform fee
    const platformFeeQuery = `INSERT INTO ma_bank_paymode_charges (ma_user_id, order_id, charges_type, bank_name, transaction_type, sub_transaction_type, transaction_amount, charges) VALUES (${fields.ma_user_id}, '${fields.orderid}', 'Platform Fee', '${bankName}', '${descType}', '${sub_transaction_type}', ${tempObj.txnAmount}, ${platformFeeAmt})`
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'insert bank charges query', fields: platformFeeQuery })
    const platformFeeResult = await this.rawQuery(platformFeeQuery, fields.connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'paymodeChargesRecord', type: 'bank charges query result', fields: platformFeeResult })

    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
  }

  static async getDistribution(fields) {
    const common_fns = require('../../util/common_fns')
 
    log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'request', fields: fields})
    try {
      const checkOtherConfigurationFields = { ...fields }
      delete fields.distributerIdForGetDistribution
      var state_master_id = 0
      var ma_user_id = 0
      var result = {}
      var sql = `SELECT * FROM ma_slabwise_distribution_bbps where ${fields.amount} between min_amount and max_amount and record_status='Y' `
 
 
      // Check condition for integrated users
      // add user id condition, to add check for whether user is web or emitra
      const userintegrated = `Select usr.id,ium.integration_code from users usr, ma_integration_user_master ium Where usr.profileid = ${fields.ma_user_id} AND usr.id = ${fields.userid} AND usr.user_type = 'integrated' AND usr.profileid = ium.ma_user_id limit 1 `
      const resultint = await this.rawQuery(userintegrated, fields.connection)
      log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Integrated user check', fields: resultint })
      if (resultint.length > 0) {
        ma_user_id = util.integratedIncentive[resultint[0].integration_code]
        result = {}
        const retailerintegratedSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerintegratedSql, fields.connection)
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Integrated Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        } else {
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
        }
      }
 
      // Check retailer specific configuration
      if (fields.ma_user_id !== null && fields.ma_user_id !== undefined && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id
        const retailerSql = sql + ` and ma_user_id=${ma_user_id}`
        result = await this.rawQuery(retailerSql, fields.connection)
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Retailer Specific', fields: result })
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
      const commonFunction = require('../common/commonFunctionController')
      // Check other configuration (For now this is for distributor specific)
      result = {}
      console.log('sql>>>>>>>>>', sql)
      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql, fields: checkOtherConfigurationFields, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'getDistributionAdditionalCondition response', fields: getDistributionAdditionalCondition })
      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      // Reinitializing variables
      ma_user_id = 0
      result = {}
 
      // Check state specific configuration
      if (fields.state_master_id !== null && fields.state_master_id !== undefined && fields.state_master_id > 0) {
        // state_master_id = 2 // FOR TESTING ONLY
        const stateSql = sql + ` and state_master_id=${fields.state_master_id} and ma_user_id=${ma_user_id}`
        log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - State Specific', fields: result })
        result = await this.rawQuery(stateSql, fields.connection)
        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }
 
      // Reinitializing variables
      ma_user_id = 0
      state_master_id = 0
      result = {}
 
      // Check default configuration
      const defaultSql = sql + ` and state_master_id=${state_master_id} and ma_user_id=${ma_user_id} and ma_dt_sdt_id = 0`
      result = await this.rawQuery(defaultSql, fields.connection)
      log.logger({ pagename: 'otpController.js', action: 'getDistribution', type: 'response - Default', fields: result })
      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }
      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] + ' For Configuration ' }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDistribution', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
  
}
module.exports = Otp
