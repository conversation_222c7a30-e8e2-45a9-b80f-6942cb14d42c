<table style="width:100%">
  <tr>
    <th>Records Fetched</th>
    <th>Records Processed</th>
    <th>Records Unprocessed</th>
  </tr>
  <tr>
    <td style="text-align:center"><%= recordsFetched %></td>
    <td style="text-align:center"><%= recordsProcessed %></td>
    <td style="text-align:center"><%= recordsFailed %></td>
  </tr>
</table>

<br/>
<hr/>

<table style="width:100%">
  <tr>
    <th style="text-align:center">Records Fetched</th>
  </tr>
  <hr/>
  <tr>
    <th style="text-align:center">Order id</th>
  </tr>
  <% recordsFetchedArr.forEach(function(fetched){ %>
  <tr>
      <td style="text-align:center"><%= fetched.orderid %></td>
  </tr>
  <% }) %>
</table>

<br/>
<hr/>
<hr/>

<table style="width:100%">
  <tr>
    <th style="text-align:center">Records Processed</th>
  </tr>
  
  <tr>
    <th style="text-align:center">Order id</th>
    <th style="text-align:center">Message</th>
    <th style="text-align:center">Updated Status</th>
  </tr>
  <% recordsProcessedArr.forEach(function(processed){ %>
  <tr>
      <td style="text-align:center"><%= processed.orderid %></td>
      <td style="text-align:center"><%= processed.message %></td>
      <td style="text-align:center"><%= processed.transaction_status %></td>
  </tr>
  <% }) %>
</table>

<br/>
<hr/>
<hr/>

<table style="width:100%">
  <tr>
    <th style="text-align:center">Records Unprocessed</th>
  </tr>
  <tr>
    <th style="text-align:center">Order id</th>
    <th style="text-align:center">Message</th>
    <th style="text-align:center">Updated Status</th>
  </tr>
  <% recordsFailedArr.forEach(function(failed){ %>
  <tr>
      <td style="text-align:center"><%= failed.orderid %></td>
      <td style="text-align:center"><%= failed.message %></td>
      <td style="text-align:center"><%= failed.transaction_status %></td>
  </tr>
  <% }) %>
</table>