const { createLogger, format, transports } = require('winston')
const moment = require('moment')
const LokiTransport = require('winston-loki')
// const store = require('../config/state-management/store')
const environment = process.env.NODE_ENV || 'development'
const logFileName = `app-log-${moment().format('YYYYMMDD-HHmm')}.log`
// const randomString = store.getState().log.log_id

// const addRandomString = format((info) => {
//   info.log_id = generateLogId() || 'unknown' // Add the random string to the log message
//   info.pagename = info.message.pagename || 'unknown'
//   info.action = info.message.action || 'unknown'
//   info.type = info.message.type || 'unknown'
//   info.fields = typeof info.message.fields === 'object' ? JSON.stringify(info.message.fields) : info.message.fields
//   info.timestamp = moment().format('YYYY-MM-DD HH:mm:ss')

//   delete info.message
//   return info
// })

const addRandomString = format((info) => {
  // console.log('info', info)
  info.labels = {
    pagename: info.message.pagename ? info.message.pagename : 'unknown',
    action: info.message.action ? info.message.action : 'unknown',
    type: info.message.type ? info.message.type : 'unknown',
    log_id: info.message.log_id ? info.message.log_id : 'unknown'
  }

  // info.fields = typeof info.message.fields === 'object' ? JSON.stringify(info.message.fields) : info.message.fields
  info.fields = info.message.fields
  info.timestamp = moment().format('YYYY-MM-DD HH:mm:ss')

  delete info.message
  return info
})

// Generate a unique log file name with a random string

const uniqueLogFileName = `logs/${logFileName}`
// console.log('ENV->', uniqueLogFileName, environment)
// console.log('LokiUrl->', process.env.LOKI_URL || 'http://*********:3100')

// Common logger configuration
const commonConfig = {
  format: format.combine(
    format.errors({ stack: true }),
    addRandomString(),
    format.json()
  ),
  exitOnError: false,
  transports: [
    new LokiTransport({
    //   host: process.env.LOKI_URL || 'http://**************:3100',
      host: process.env.LOKI_URL || 'http://************:3100',
      interval: 5, // Batch interval in seconds
      batching: true,
      labels: { app: 'vyaapaar-stag' },
      json: true,
      format: format.json(),
      replaceTimestamp: true,
      onConnectionError: (err) => console.error(err)
    }),
    new transports.Console()
  ]
}

const lokiLogger = createLogger({
  format: environment === 'development'
    ? format.combine(
      format.errors({ stack: true }),
      addRandomString(),
      format.timestamp(),
      format.prettyPrint(),
      format.colorize()
    )
    : commonConfig.format,
  exitOnError: false,
  transports: environment === 'development'
    ? [
      new transports.File({
        filename: uniqueLogFileName,
        level: 'info',
        handleExceptions: true,
        json: true,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        colorize: false,
        labels: { app: 'vyaapaar-stag' }
      })
    //   ...commonConfig.transports
    ]
    : commonConfig.transports

})

lokiLogger.on('error', (error) => {
  console.log('#######*LOG-ERROR*#########')
  console.error('Error in logger caught', error)
})

// function generateLogId () {
//   const now = new Date()
//   const date = now.toISOString().slice(0, 10).replace(/-/g, '')
//   const time = now.toTimeString().slice(0, 8).replace(/:/g, '')
//   return `log-${date}${time}`
// }

module.exports = { lokiLogger }
