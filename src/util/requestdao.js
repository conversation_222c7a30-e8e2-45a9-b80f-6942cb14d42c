const mySQLWrapper = require('../lib/mysqlWrapper')
const DAO = require('../lib/dao')

class RequestLogDAO extends DAO {
  /**
       * Overrides TABLE_NAME with this class' backing table at MySQL
       */
  get TABLE_NAME () {
    return 'ma_request_logs'
  }

  get PRIMARY_KEY () {
    return 'ma_request_log_id'
  }

  static insertData (insertObject) {
    this.TABLE_NAME = 'ma_request_logs'
    mySQLWrapper.getConnectionFromPool().then((connection) => {
      this.insert(connection, { data: insertObject }).then((data) => {}).catch((error) => {
        console.log('REQUEST_LOG_INSERT_ERROR', error)
      })
    }).catch((error) => {
      console.log('REQUEST_LOG_CATCH_INSERT_ERROR', error)
    })
  }
}

module.exports = RequestLogDAO
