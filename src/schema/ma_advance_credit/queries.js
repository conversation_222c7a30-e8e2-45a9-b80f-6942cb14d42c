const { GraphQLObjectType } = require('graphql')
const advanceCreditQueries = require('../../model/ma_advance_credit/queries')

module.exports = new GraphQLObjectType({
  name: 'RootQueryType',
  fields: {
    getAdvanceCreditForm: advanceCreditQueries.getAdvanceCreditForm,
    getMerchantDetails: advanceCreditQueries.getMerchantDetails,
    getRequestStatus: advanceCreditQueries.getRequestStatus,
    getCreditLimit: advanceCreditQueries.getCreditLimit
  }
})
