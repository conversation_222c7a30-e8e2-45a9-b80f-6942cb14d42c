# This is the SAM template that represents the architecture of your serverless application
# https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-template-basics.html

# The AWSTemplateFormatVersion identifies the capabilities of the template
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/format-version-structure.html
AWSTemplateFormatVersion: 2010-09-09
Description: >-
  Merchant app with SQS and DMT

# Transform section specifies one or more macros that AWS CloudFormation uses to process your template
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/transform-section-structure.html
Transform: AWS::Serverless-2016-10-31

# Shared configuration for all resources, more in
# https://github.com/awslabs/serverless-application-model/blob/master/docs/globals.rst
Globals:
  Function:
    # The PermissionsBoundary allows users to safely develop with their function's permissions constrained.
    # to their current application. All the functions and roles in this application have to include it and
    # it has to be manually updated when you add resources to your application.
    # More information in https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies_boundaries.html
    #PermissionsBoundary: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:policy/${AppId}-${AWS::Region}-PermissionsBoundary'
    
    CodeUri: ./
    Runtime: nodejs18.x
    MemorySize: 128
    Timeout: 30 #Chosen to be less than the default SQS Visibility Timeout of 30 seconds
    Environment:
      Variables:
        NSDL_PREFIX: S
        NODE_ENV: staging
        MYSQL_DB_USER: retailstage
        MYSQL_DB_NAME: merchantappuat
        MYSQL_DB_PASSWORD: s1rtwe12iGhu!
        MYSQL_DB_ADDRESS: ***********
        MYSQL_DB_POOL_SIZE: 50

        MYSQL_DB_USER_R: retailstage
        MYSQL_DB_NAME_R: merchantappuat
        MYSQL_DB_PASSWORD_R: s1rtwe12iGhu!
        MYSQL_DB_ADDRESS_R: ***********
        MYSQL_DB_POOL_SIZE_R: 50

        # Staging Mongo DB

        MONGODB_DB_USER : retail_admin
        MONGODB_DB_NAME : retail_admin
        MONGODB_DB_PASSWORD : GH2{QPS_#PihSf
        MONGODB_DB_PORT : 27017
        MONGODB_DB_POOL_SIZE : 100

        # BBPS Variables
        BBPS_MA_PAYMENTS_URL: https://payments.airpay.ninja/
        BBPS_REDIS_KEY: bbps:providers
        BBPS_MS_PAYMENTS_URL: https://partnerpay.airpay.ninja/
        BBPS_MA_CALLBACK_URL: https://hqvt6tbkl5.execute-api.ap-south-1.amazonaws.com/stag/bbpsnewbill
        BBPS_MA_RETURN_URL: https://hqvt6tbkl5.execute-api.ap-south-1.amazonaws.com/stag/bbpsregisterstatus
        SECONDARY_ENCRPTION_KEY: 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7'
        SMS_SIGNATURE: 'Team Airpay Vyaapaar.'

    VpcConfig:
      SecurityGroupIds:
        - sg-00aebcc3ec861cc91
      SubnetIds:
        - subnet-0d7d984ee55c48a33
        - subnet-042232aa58672d756

Parameters:
  AppId:
    Type: String
  ProjectName:
    Description: Merchant App
    Type: String    
    Default: stag-merchant-app
            
# Resources declares the AWS resources that you want to include in the stack
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/resources-section-structure.html
Resources:
  # Each Lambda function is defined by properties:
  # https://github.com/awslabs/serverless-application-model/blob:/master/versions/2016-10-31.md#awsserverlessfunction
  
  StagLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${AppId}-lambda-role
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaSQSQueueExecutionRole
        - arn:aws:iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          -
            Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
                - apigateway.amazonaws.com
            Action:
              - sts:AssumeRole
      Path: /
  
  # This is a Lambda function config associated with the source code: sqs-payload-logger.js
  #1
  sqsLoggerFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/sqs-payload-logger.sqsPayloadLoggerHandler
      Description: A Lambda function that logs the payload of messages sent to an associated SQS queue.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  #2
  authenticationFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: authentication.authentication
      Description: A Lambda function to fetch Account details.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  #3
  cashBalance:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cashbalance.cashbalance
      Description: A Lambda function to fetch Cash balance.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #4
  accountDetails:
    Type: AWS::Serverless::Function
    Properties:
      Handler: accountdetails.accountdetails
      Description: A Lambda function to fetch accountDetails.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #5
  cashLedgerHistory:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cashledgerhistory.cashledgerhistory
      Description: A Lambda function to fetch cash ledgerhistory.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #6
  cashWithdrawal:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cashwithdrawal.cashwithdrawal
      Description: A Lambda function to withdraw cash.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #7
  cashWithdrawalNeft:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cashwithdrawalneft.cashwithdrawalneft
      Description: A Lambda function cash withdra neft.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #8
  cashWithdrawalNeftUpdate:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cashwithdrawalneftupdate.cashwithdrawalneftupdate
      Description: A Lambda function neft update.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #9
  checkjwt:
    Type: AWS::Serverless::Function
    Properties:
      Handler: checkjwt.checkjwt
      Description: A Lambda function check jwt.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #10
  maCashAccount:
    Type: AWS::Serverless::Function
    Properties:
      Handler: macashaccount.macashaccount
      Description: A Lambda function cash account .
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #11
  maCashLedgerMaster:
    Type: AWS::Serverless::Function
    Properties:
      Handler: macashledgermaster.macashledgermaster
      Description: A Lambda function cash ledger master.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #12
  maCommissionMaster:
    Type: AWS::Serverless::Function
    Properties:
      Handler: macommissionmaster.macommissionmaster
      Description: A Lambda function ma commission master.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  #13      
  maGlobalPointsRate:
    Type: AWS::Serverless::Function
    Properties:
      Handler: maglobalpointsrate.maglobalpointsrate
      Description: A Lambda function global points rate.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  #14
  maneftdetails:
    Type: AWS::Serverless::Function
    Properties:
      Handler: maneftdetails.maneftdetails
      Description: A Lambda function Neft Details.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  #15
  maPointsAccount:
    Type: AWS::Serverless::Function
    Properties:
      Handler: mapointsaccount.mapointsaccount
      Description: A Lambda function points account .
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  #16
  maPointsLedgerMaster:
    Type: AWS::Serverless::Function
    Properties:
      Handler: mapointsledgermaster.mapointsledgermaster
      Description: A Lambda function point ledger master.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  #17
  maUserMaster:
    Type: AWS::Serverless::Function
    Properties:
      Handler: mausermaster.mausermaster
      Description: A Lambda function user master.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #18
  pointsLedgerHistory:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pointsledgerhistory.pointsledgerhistory
      Description: A Lambda function point ledger history.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #19
  pointsToCash:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pointstocash.pointstocash
      Description: A Lambda function points to cash.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #20
  recon:
    Type: AWS::Serverless::Function
    Properties:
      Handler: recon.recon
      Description: A Lambda function Recon.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #21
  refund:
    Type: AWS::Serverless::Function
    Properties:
      Handler: refund.refund
      Description: A Lambda function refund.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #22
  resentOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: resentotp.resentotp
      Description: A Lambda function user master.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #23
  sendMoney:
    Type: AWS::Serverless::Function
    Properties:
      Handler: sendmoney.sendmoney
      Description: A Lambda function send money.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #24
  sentOtpNew:
    Type: AWS::Serverless::Function
    Properties:
      Handler: sentotpnew.sentotpnew
      Description: A Lambda function send otp.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #25
  topup:
    Type: AWS::Serverless::Function
    Properties:
      Handler: topup.topup
      Description: A Lambda function send otp.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #26
  transaction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transaction.transaction
      Description: A Lambda function send otp.
      MemorySize: 256
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #27
  verifyOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: verifyotp.verifyotp
      Description: A Lambda function send otp.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  #28
  transfers:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transfers.transfers
      Description: A Lambda function For Transfers.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  #29
  beneficiary:
    Type: AWS::Serverless::Function
    Properties:
      Handler: beneficiary.beneficiary
      Description: A Lambda function TO add beneficiary.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  #30
  custlogin:
    Type: AWS::Serverless::Function
    Properties:
      Handler: custlogin.custlogin
      Description: A Lambda function for cutomer login.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #31
  bankdetails:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bankdetails.bankdetails
      Description: A Lambda function for banketails.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #32
  kyc:
    Type: AWS::Serverless::Function
    Properties:
      Handler: kyc.kyc
      Description: A Lambda function for kyc.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  #33
  migratenumber:
    Type: AWS::Serverless::Function
    Properties:
      Handler: migratenumber.migratenumber
      Description: A Lambda function for change mobile number.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

   #34
  tickets:
    Type: AWS::Serverless::Function
    Properties:
      Handler: tickets.tickets
      Description: A Lambda function send otp.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

 #35
  bankapimastertest:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bankapimastertest.bankapimastertest
      Description: A Lambda function send TEST.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn     
        
 #36
  customerlogintest:
    Type: AWS::Serverless::Function
    Properties:
      Handler: customerlogintest.customerlogintest
      Description: A Lambda function customer login TEST.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

 #37
  supportticket:
    Type: AWS::Serverless::Function
    Properties:
      Handler: supportticket.supportticket
      Description: A Lambda function customer login TEST.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#38
  logincron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: logincron.logincron
      Description: A Lambda function login cron.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#39
  transactioncron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transactioncron.transactioncron
      Description: A Lambda function transaction cron.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#40
  topupcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: topupcron.topupcron
      Description: A Lambda function topup cron.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#41
  login:
    Type: AWS::Serverless::Function
    Properties:
      Handler: login.login
      Description: A Lambda function for login.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

#42
  logout:
    Type: AWS::Serverless::Function
    Properties:
      Handler: logout.logout
      Description: A Lambda function for logout.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
#43
  transfercron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transfercron.transfercron
      Description: A Lambda function for transfercron.
      Timeout: 300
      Events:
        TransferCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

#43
  notice:
    Type: AWS::Serverless::Function
    Properties:
      Handler: notice.notice
      Description: A Lambda function for notice.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

#44
  leads:
    Type: AWS::Serverless::Function
    Properties:
      Handler: leads.leads
      Description: A Lambda function for leads.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn               

#44
  insurancepremiumdeduction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: insurancepremiumdeduction.insurancepremiumdeduction
      Description: A Lambda function for insurancepremiumdeduction.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn               

#45
  insurancepolicyconfirmation:
    Type: AWS::Serverless::Function
    Properties:
      Handler: insurancepolicyconfirmation.insurancepolicyconfirmation
      Description: A Lambda function for insurancepolicyconfirmation.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn               

#46
  insurance:
    Type: AWS::Serverless::Function
    Properties:
      Handler: insurance.insurance
      Description: A Lambda function for insurance.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn               
#47
  paymentverify:
    Type: AWS::Serverless::Function
    Properties:
      Handler: paymentverify.paymentverify
      Description: A Lambda function for paymentverify.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#48
  multiplecall:
    Type: AWS::Serverless::Function
    Properties:
      Handler: multiplecall.multiplecall
      Description: A Lambda function for multiplecall.js.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                       

 #49 
  orderconfirmcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: orderconfirmcron.orderconfirmcron
      Description: A Lambda function for transfercron.
      Timeout: 30
      Events:
        OrderConfirmationCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(1 hour)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

#50
  refundcredittempory:
    Type: AWS::Serverless::Function
    Properties:
      Handler: refundcredittempory.refundcredittempory
      Description: A Lambda function for refundcredittempory.js.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn    

#51
  smsproducer:
    Type: AWS::Serverless::Function
    Properties:
      Handler: smsproducer.smsproducer
      Description: A Lambda function for smsproducer.js.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn    

#52
  smsprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: smsprocessor.smsprocessor
      Description: A Lambda function for smsprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#53
  queuemanagerprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: queuemanagerprocessor.queuemanagerprocessor
      Description: A Lambda function for queuemanagerprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  riskprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: riskprocessor.riskprocessor
      Description: A Lambda function for riskprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  verifyprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: verifyprocessor.verifyprocessor
      Description: A Lambda function for verifyprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                 

#54
  evalue:
    Type: AWS::Serverless::Function
    Properties:
      Handler: evalue.evalue
      Description: A Lambda function for evalue.js.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#55
  incentivesprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: incentivesprocessor.incentivesprocessor
      Description: A Lambda function for incentivesprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 


#56
  ledgersprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ledgersprocessor.ledgersprocessor
      Description: A Lambda function for ledgersprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#57
  openendpoint:
    Type: AWS::Serverless::Function
    Properties:
      Handler: openEndpoint.openEndpoint
      Description: A Lambda function for openEndpoint.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  #58
  transferincentivecron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transferincentivecron.transferincentivecron
      Description: A Lambda function for transferincentivecron.
      Timeout: 300
      Events:
        TransIncCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(15 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  #59
  pmtmerchantcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pmtmerchantcron.pmtmerchantcron
      Description: A Lambda function for pmtmerchantcron.
      Timeout: 300
      Events:
        PmtMerCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(8 hours)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn           

#59
  amlthresholdprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: amlthresholdprocessor.amlthresholdprocessor
      Description: A Lambda function for amlthresholdprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#60
  updateamlsnapshot:
    Type: AWS::Serverless::Function
    Properties:
      Handler: updateAmlSnapshot.updateAmlSnapshot
      Description: A Lambda function for updateAmlSnapshot.js.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

#61
  # beneverificationcron:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     Handler: beneverificationcron.beneverificationcron
  #     Description: A Lambda function for beneverificationcron.
  #     Timeout: 300
  #     Events:
  #       beneverifyScheduledEvent:
  #         Type: Schedule
  #         Properties:
  #           Schedule: rate(10 minutes)
  #     Role:
  #       Fn::GetAtt:
  #       - StagLambdaRole
  #       - Arn

#62
  riactplcycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: riactplcycron.riactplcycron
      Description: A Lambda function for riactplcycron.
      Timeout: 300
      Events:
        riactplcyScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(30 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

#63
  rinotifycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: rinotifycron.rinotifycron
      Description: A Lambda function for rinotifycron.
      Timeout: 300
      Events:
        rinotifyScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        


#62
  verifypin:
    Type: AWS::Serverless::Function
    Properties:
      Handler: verifypin.verifypin
      Description: A Lambda function for verifypin.js.
      Timeout: 30
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn     

#63
  bbpsrequerycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpsRequeryCron.bbpsRequeryCron
      Description: A Lambda function for bbpsRequeryCron.js.
      Timeout: 300
      Events:
        bbpsrequeryScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn              

#64
  bbpspointbankcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpspointbankcron.bbpspointbankcron
      Description: A Lambda function for bbpspointbankcron.js.
      Timeout: 300
      Events:
        bbpbankScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn           

#65
  rechargestatus:
    Type: AWS::Serverless::Function
    Properties:
      Handler: rechargestatus.rechargestatus
      Description: A Lambda function for rechargestatus.js.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn     

#66
  decryption:
    Type: AWS::Serverless::Function
    Properties:
      Handler: decryption.decryption
      Description: A Lambda function for decryption.js.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

#67
  encryption:
    Type: AWS::Serverless::Function
    Properties:
      Handler: encryption.encryption
      Description: A Lambda function for encryption.js.
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

#68
  aepssettlementcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: aepssettlementcron.aepssettlementcron
      Description: A Lambda function for aepssettlementcron.js.
      Timeout: 300
      Events:
        aepssettcronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn         

#69
  aepsrequerycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: aepsrequerycron.aepsrequerycron
      Description: A Lambda function for aepsrequerycron.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

#70
  aepstransaction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: aepstransaction.aepstransaction
      Description: A Lambda function for aepstransaction.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

#71
  migratebenecron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: migratebenecron.migratebenecron
      Description: A Lambda function for migratebenecron.js.
      Timeout: 300
      Events:
        migratebeneScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                            


#72
  postreceiptcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: postreceiptcron.postreceiptcron
      Description: A Lambda function for postreceiptcron.js.
      Timeout: 300
      Events:
        postreceiptScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn         

#73
  cms:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cms.cms
      Description: A Lambda function for cms.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn         

#50
#  cmssettlementcron:
#    Type: AWS::Serverless::Function
#    Properties:
#      Handler: cmssettlementcron.cmssettlementcron
#      Description: A Lambda function for cmssettlementcron.js.
#      Timeout: 300
#      Events:
#        cmssettcronScheduledEvent:
#          Type: Schedule
#          Properties:
#            Schedule: rate(10 minutes)
#      Role:
#        Fn::GetAtt:
#        - StagLambdaRole
#        - Arn                       

  khatabooktransaction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: khatabooktransaction.khatabooktransaction
      Description: A Lambda function for khatabooktransaction.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 
        
  khatabookcustomer:
    Type: AWS::Serverless::Function
    Properties:
      Handler: khatabookcustomer.khatabookcustomer
      Description: A Lambda function for khatabookcustomer.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 
        
  khatabookaccount:
    Type: AWS::Serverless::Function
    Properties:
      Handler: khatabookaccount.khatabookaccount
      Description: A Lambda function for khatabookaccount.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
                
  upload:
    Type: AWS::Serverless::Function
    Properties:
      Handler: upload.upload
      Description: A Lambda function for upload.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn      

  khatabookreminder:
    Type: AWS::Serverless::Function
    Properties:
      Handler: khatabookreminder.khatabookreminder
      Description: A Lambda function for khatabookreminder.js.
      Timeout: 300
      Events:
        khataremindScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  cmsrequery:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cmsrequery.cmsrequery
      Description: A Lambda function for .
      Timeout: 300
      Events:
        cmsrequeryScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(30 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  transactionstatus:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transactionstatus.transactionstatus
      Description: A Lambda function for transactionstatus.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

  beneverify:
    Type: AWS::Serverless::Function
    Properties:
      Handler: beneverify.beneverify
      Description: A Lambda function for beneverify.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 
        
  remitterlogin:
    Type: AWS::Serverless::Function
    Properties:
      Handler: remitterlogin.remitterlogin
      Description: A Lambda function for remitterlogin.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  canceltransaction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: canceltransaction.canceltransaction
      Description: A Lambda function for remitterlogin.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  changepassword:
    Type: AWS::Serverless::Function
    Properties:
      Handler: changepassword.changepassword
      Description: A Lambda function for changepassword.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  confirmorder:
    Type: AWS::Serverless::Function
    Properties:
      Handler: confirmorder.confirmorder
      Description: A Lambda function for confirmorder.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  integratedauth:
    Type: AWS::Serverless::Function
    Properties:
      Handler: integratedauth.integratedauth
      Description: A Lambda function for integratedauth.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
          
  fasttagproduct:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fasttagproduct.fasttagproduct
      Description: A Lambda function for fasttagproduct.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  fasttagcustomer:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fasttagcustomer.fasttagcustomer
      Description: A Lambda function for fasttagcustomer.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  fastagissue:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fastagissue.fastagissue
      Description: A Lambda function for fastagissue.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  gold:
    Type: AWS::Serverless::Function
    Properties:
      Handler: gold.gold
      Description: A Lambda function for gold.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  goldagentbranch:
    Type: AWS::Serverless::Function
    Properties:
      Handler: goldagentbranch.goldagentbranch
      Description: A Lambda function for goldagentbranch.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn          
        
  fasttagstatemaster:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fasttagstatemaster.fasttagstatemaster
      Description: A Lambda function for fasttagstatemaster.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn       

  fasttagactivationcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fasttagactivationcron.fasttagactivationcron
      Description: A Lambda function for fasttagactivationcron.js.
      MemorySize: 256
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn     

  testdbtranshistory:
    Type: AWS::Serverless::Function
    Properties:
      Handler: testdbtranshistory.testdbtranshistory
      Description: A Lambda function for testdbtranshistory.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn     

  testdbtranshistorypool:
    Type: AWS::Serverless::Function
    Properties:
      Handler: testdbtranshistorypool.testdbtranshistorypool
      Description: A Lambda function for testdbtranshistorypool.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  refundbulk:
    Type: AWS::Serverless::Function
    Properties:
      Handler: refundbulk.refundbulk
      Description: A Lambda function for refundbulk.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                   

  refundprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: refundprocessor.refundprocessor
      Description: A Lambda function for refundprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  transferprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transferprocessor.transferprocessor
      Description: A Lambda function for transferprocessor.js.
      Timeout: 300
      Events:
        transferprocessorCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  transferrequeryprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transferrequeryprocessor.transferrequeryprocessor
      Description: A Lambda function for transferrequeryprocessor.js.
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn           

  inittransaction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: inittransaction.inittransaction
      Description: A Lambda function for inittransaction.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                
        
  bankcallback:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bankcallback.bankcallback
      Description: A Lambda function for bankcallback.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  testnsdllogin:
    Type: AWS::Serverless::Function
    Properties:
      Handler: testnsdllogin.testnsdllogin
      Description: A Lambda function for testnsdllogin.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  #43
  transfersadhoccron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transfersadhoccron.transfersadhoccron
      Description: A Lambda function for transfersadhoccron.
      Timeout: 300
      Events:
        transfersadhCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  beneadhoccron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: beneadhoccron.beneadhoccron
      Description: A Lambda function for beneadhoccron.
      Timeout: 300
      Events:
        beneadhocronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  profile:
    Type: AWS::Serverless::Function
    Properties:
      Handler: profile.profile
      Description: A Lambda function for profile.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        
        

  posactivation:
    Type: AWS::Serverless::Function
    Properties:
      Handler: posactivation.posactivation
      Description: A Lambda function for posactivation.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        
        
  adhoc:
    Type: AWS::Serverless::Function
    Properties:
      Handler: adhoc.adhoc
      Description: A Lambda function for adhoc.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        
        
  ekyc:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ekyc.ekyc
      Description: A Lambda function for ekyc.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        
        
  recharge:
    Type: AWS::Serverless::Function
    Properties:
      Handler: recharge.recharge
      Description: A Lambda function for recharge.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        
        
  bajajrevalidationcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bajajrevalidationcron.bajajrevalidationcron
      Description: A Lambda function for bajajrevalidationcron.js.
      Timeout: 300
      Events:
        bbpsrequeryScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(15 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

  shopping:
    Type: AWS::Serverless::Function
    Properties:
      Handler: shopping.shopping
      Description: A Lambda function for shopping.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  requestcredit:
    Type: AWS::Serverless::Function
    Properties:
      Handler: requestcredit.requestcredit
      Description: A Lambda function for requestcredit.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  useractionlog:
    Type: AWS::Serverless::Function
    Properties:
      Handler: useractionlog.useractionlog
      Description: A Lambda function for useractionlog.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  cashbackpromotion:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cashbackpromotion.cashbackpromotion
      Description: A Lambda function for cashbackpromotion.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  shoppingrequerycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: shoppingrequerycron.shoppingrequerycron
      Description: A Lambda function for shoppingrequerycron.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  testairtellogin:
    Type: AWS::Serverless::Function
    Properties:
      Handler: testairtellogin.testairtellogin
      Description: A Lambda function for testairtellogin.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  bbpsinsurance:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpsinsurance.bbpsinsurance
      Description: A Lambda function for bbpsinsurance.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  bbpsinsurancecron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpsinsurancecron.insurancecron
      Description: A Lambda function for bbpsinsurancecron.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
  
  electricity:
    Type: AWS::Serverless::Function
    Properties:
      Handler: electricity.electricity
      Description: A Lambda function for electricity.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  electricitycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: electricitycron.electricitycron
      Description: A Lambda function for electricitycron.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  qractivation:
    Type: AWS::Serverless::Function
    Properties:
      Handler: qractivation.qractivation
      Description: A Lambda function for qractivation.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  smsproviderswitch:
    Type: AWS::Serverless::Function
    Properties:
      Handler: smsproviderswitch.smsproviderswitch
      Description: A Lambda function for smsproviderswitch.js.
      Timeout: 120
      Events:
        SmsProviderSwitchScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  withdrawalhistory:
    Type: AWS::Serverless::Function
    Properties:
      Handler: withdrawalhistory.withdrawalhistory
      Description: A Lambda function for withdrawalhistory.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  getbankdetails:
    Type: AWS::Serverless::Function
    Properties:
      Handler: getbankdetails.getbankdetails
      Description: A Lambda function for getbankdetails.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  pennydrop:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pennydrop.pennydrop
      Description: A Lambda function for pennydrop.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  bbps:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbps.bbps
      Description: A Lambda function for bbps.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  bbpsnewbill:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpsnewbill.bbpsnewbill
      Description: A Lambda function for bbpsnewbill.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn


  bbpsregisterstatus:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpsregisterstatus.bbpsregisterstatus
      Description: A Lambda function for bbpsregisterstatus.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                        

  #bbpsproviderscron:
  #  Type: AWS::Serverless::Function
  #  Properties:
  #    Handler: bbpsproviderscron.bbpsproviderscron
  #    Description: A Lambda function for bbpsproviderscron.js.
  #    MemorySize: 512
  #    Timeout: 300
  #    Events:
  #      bbpsproviderscron:
  #        Type: Schedule
  #        Properties:
  #          Schedule: rate(1 hour)
  #    Role:
  #      Fn::GetAtt:
  #      - StagLambdaRole
  #      - Arn

  getbomqr:
    Type: AWS::Serverless::Function
    Properties:
      Handler: getbomqr.getbomqr
      Description: A Lambda function for getbomqr.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  merchantdashboard:
    Type: AWS::Serverless::Function
    Properties:
      Handler: merchantdashboard.merchantdashboard
      Description: A Lambda function for merchantdashboard.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn          

  upiorderverifycron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: upiorderverifycron.upiorderverifycron
      Description: A Lambda function for upiorderverifycron.js.
      Timeout: 120
      Events:
        upiorderverifycronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(30 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  kyccron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: kyccron.kyccron
      Description: A Lambda function for kyccron.js.
      Timeout: 120
      Events:
        kyccronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(2 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn
        
  aepsbkndwn:
    Type: AWS::Serverless::Function
    Properties:
      Handler: aepsbkndwn.aepsbkndwn
      Description: A Lambda function for aepsbkndwn.js.
      Timeout: 120
      Events:
        aepsbkndwnScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(15 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

  emitracron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: emitracron.emitracron
      Description: A Lambda function for emitracron.js.
      Timeout: 120
      Events:
        aepsbkndwnScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  ondccron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ondccron.ondccron
      Description: A Lambda function for ondccron.js.
      Timeout: 120
      Events:
        ondccronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(15 minutes)     

  autorefund:
    Type: AWS::Serverless::Function
    Properties:
      Handler: autorefund.autorefund
      Description: A Lambda function for autorefund.js.
      Timeout: 120
      Events:
        autorefundScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(10 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                                    

  intratransfers:
    Type: AWS::Serverless::Function
    Properties:
      Handler: intratransfers.intratransfers
      Description: A Lambda function for intratransfers.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn          

  nsdlkycstatus:
    Type: AWS::Serverless::Function
    Properties:
      Handler: nsdlkycstatus.nsdlkycstatus
      Description: A Lambda function for nsdlkycstatus.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  offers:
    Type: AWS::Serverless::Function
    Properties:
      Handler: offers.offers
      Description: A Lambda function for offers.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

      

  apirouter:
    Type: AWS::Serverless::Function
    Properties:
      Handler: apirouter.apirouter
      Description: A Lambda function for apirouter.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                        

  apirouterfileupload:
    Type: AWS::Serverless::Function
    Properties:
      Handler: apirouterfileupload.apirouterfileupload
      Description: A Lambda function for apirapirouterfileuploadouter.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  salepromotions:
    Type: AWS::Serverless::Function
    Properties:
      Handler: salepromotions.salepromotions
      Description: A Lambda function for apirsalepromotions.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn   

  #bbpsproviderspecific:
  #  Type: AWS::Serverless::Function
  #  Properties:
  #    Handler: bbpsproviderspecific.bbpsproviderspecific
  #    Description: A Lambda function for bbpsproviderspecific.js.
  #    Timeout: 120
  #    Role:
  #      Fn::GetAtt:
  #      - StagLambdaRole
  #      - Arn           

  ondc:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ondc.ondc
      Description: A Lambda function for ondc.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  ondcv2:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ondcv2.ondcv2
      Description: A Lambda function for ondcv2.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  newondc:
    Type: AWS::Serverless::Function
    Properties:
      Handler: newondc.newondc
      Description: A Lambda function for newondc.js.
      MemorySize: 256
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  ondcallback:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ondcallback.ondcallback
      Description: A Lambda function for ondcallback.js.
      MemorySize: 256
      Timeout: 300
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                  

  umang:
    Type: AWS::Serverless::Function
    Properties:
      Handler: umang.umang
      Description: A Lambda function for umang.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  loanlead:
    Type: AWS::Serverless::Function
    Properties:
      Handler: loanlead.loanlead
      Description: A Lambda function for loanlead.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  webformtoken:
    Type: AWS::Serverless::Function
    Properties:
      Handler: webformtoken.webformtoken
      Description: A Lambda function for webformtoken.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  aepsmerchantauth:
    Type: AWS::Serverless::Function
    Properties:
      Handler: aepsmerchantauth.aepsmerchantauth
      Description: A Lambda function for aepsmerchantauth.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn             

  riskcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: riskcron.riskcron
      Description: A Lambda function for riskcron.js.
      Timeout: 120
      Events:
        TransIncCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  webtokencron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: webtokencron.webtokencron
      Description: A Lambda function for webtokencron.js.
      Timeout: 120
      Events:
        WebTokenCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: cron(15 00 * * ? *)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  aepsauthdelcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: aepsauthdelcron.aepsauthdelcron
      Description: A Lambda function for aepsauthdelcron.js.
      Timeout: 120
      Events:
        AepsAuthDelCronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: cron(45 00 * * ? *)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  banklistcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: banklistcron.banklistcron
      Description: A Lambda function for banklistcron.js.
      Timeout: 300
      Events:
        banklistcronScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(1 hour)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  rekyc:
    Type: AWS::Serverless::Function
    Properties:
      Handler: rekyc.rekyc
      Description: A Lambda function for rekyc.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  fileupload:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fileupload.fileupload
      Description: A Lambda function for fileupload.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn              

## Foreign Money Transfer API

  fmt:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmt.fmt
      Description: A Lambda function for fmt.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  fmtbene:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmtbene.fmtbene
      Description: A Lambda function for fmtbene.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  fmtrefund:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmtrefund.fmtrefund
      Description: A Lambda function for fmtrefund.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  fmtremitter:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmtremitter.fmtremitter
      Description: A Lambda function for fmtremitter.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  fmttransfers:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmttransfers.fmttransfers
      Description: A Lambda function for fmttransfers.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  fmtrequery:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmtrequery.fmtrequery
      Description: A Lambda function for fmtrequery.js.
      Timeout: 120
      Events:
        fmtrequeryScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)      
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn 

  fmtrecon:
    Type: AWS::Serverless::Function
    Properties:
      Handler: fmtrecon.fmtrecon
      Description: A Lambda function for fmtrecon.
      Timeout: 120
      Events:
        fmtreconScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                  

  pmtstatic:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pmtstatic.pmtstatic
      Description: A Lambda function for pmtstatic.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  pmtstaticcron:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pmtstaticcron.pmtstaticcron
      Description: A Lambda function for pmtstaticcron.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  pendingprocessor:
    Type: AWS::Serverless::Function
    Properties:
      Handler: pendingprocessor.pendingprocessor
      Description: A Lambda function for pendingprocessor.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                             

## Merchant Locator API 

  nearbymerchanttoken:
    Type: AWS::Serverless::Function
    Properties:
      Handler: nearbymerchanttoken.nearbymerchanttoken
      Description: A Lambda function for nearbymerchanttoken.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  nearbymerchantlist:
    Type: AWS::Serverless::Function
    Properties:
      Handler: nearbymerchantlist.nearbymerchantlist
      Description: A Lambda function for nearbymerchantlist.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  braingymjr:
    Type: AWS::Serverless::Function
    Properties:
      Handler: braingymjr.braingymjr
      Description: A Lambda function for braingymjr.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  promotions:
    Type: AWS::Serverless::Function
    Properties:
      Handler: promotions.promotions
      Description: A Lambda function for promotions.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  soundbox:
    Type: AWS::Serverless::Function
    Properties:
      Handler: soundbox.soundbox
      Description: A Lambda function for soundbox.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

# Validate MID for DT and SDT

  validatemid:
    Type: AWS::Serverless::Function
    Properties:
      Handler: validatemid.validatemid
      Description: A Lambda function for validatemid.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn                

## External API 

  affiliatetxnstatus:
    Type: AWS::Serverless::Function
    Properties:
      Handler: affiliatetransactionstatus.affiliatetransactionstatus
      Description: A Lambda function for affiliatetransactionstatus.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  channellist:
    Type: AWS::Serverless::Function
    Properties:
      Handler: channellist.channellist
      Description: A Lambda function for channellist.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  getSessionToken:
    Type: AWS::Serverless::Function
    Properties:
      Handler: getSessionToken.getSessionToken
      Description: A Lambda function for getSessionToken.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  bankList:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bankList.bankList
      Description: A Lambda function for bankList.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  stateList:
    Type: AWS::Serverless::Function
    Properties:
      Handler: stateList.stateList
      Description: A Lambda function for stateList.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  cityList:
    Type: AWS::Serverless::Function
    Properties:
      Handler: cityList.cityList
      Description: A Lambda function for cityList.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  merchantOnboarding:
    Type: AWS::Serverless::Function
    Properties:
      Handler: merchantOnboarding.merchantOnboarding
      Description: A Lambda function for merchantOnboarding.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  merchants:
    Type: AWS::Serverless::Function
    Properties:
      Handler: merchants.merchants
      Description: A Lambda function for merchants.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  transactions:
    Type: AWS::Serverless::Function
    Properties:
      Handler: transactions.transactions
      Description: A Lambda function for transactions.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  walletBalance:
    Type: AWS::Serverless::Function
    Properties:
      Handler: walletBalance.walletBalance
      Description: A Lambda function for walletBalance.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  ledgers:
    Type: AWS::Serverless::Function
    Properties:
      Handler: ledgers.ledgers
      Description: A Lambda function for ledgers.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  getToken:
    Type: AWS::Serverless::Function
    Properties:
      Handler: getToken.getToken
      Description: A Lambda function for getToken.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtRemitterLogin:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRemitterLogin.dmtRemitterLogin
      Description: A Lambda function for dmtRemitterLogin.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtRemitterLimit:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRemitterLimit.dmtRemitterLimit
      Description: A Lambda function for dmtRemitterLimit.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtRemitterRegister:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRemitterRegister.dmtRemitterRegister
      Description: A Lambda function for dmtRemitterRegister.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtRemitterVerifyOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRemitterVerifyOtp.dmtRemitterVerifyOtp
      Description: A Lambda function for dmtRemitterVerifyOtp.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  resendOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: resendOtp.resendOtp
      Description: A Lambda function for resendOtp.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtAddBene:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtAddBeneficiary.dmtAddBeneficiary
      Description: A Lambda function for dmtAddBeneficiary.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtAddBeneVerifyOTP:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtAddBeneficiaryVerifyOTP.dmtAddBeneficiaryVerifyOTP
      Description: A Lambda function for dmtAddBeneficiaryVerifyOTP.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtAddBeneVerifyOTP:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtAddBeneficiaryVerifyOTP.dmtAddBeneficiaryVerifyOTP
      Description: A Lambda function for dmtAddBeneficiaryVerifyOTP.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtVerifyAddBene:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtVerifyAddBeneficiary.dmtVerifyAddBeneficiary
      Description: A Lambda function for dmtVerifyAddBeneficiary.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn  

  dmtSendDeleteBeneOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtSendDeleteBeneOtp.dmtSendDeleteBeneOtp
      Description: A Lambda function for dmtSendDeleteBeneOtp.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtVerifyDeleteBeneOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtVerifyDeleteBeneOtp.dmtVerifyDeleteBeneOtp
      Description: A Lambda function for dmtVerifyDeleteBeneOtp.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtBeneList:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtBeneficiaryList.dmtBeneficiaryList
      Description: A Lambda function for dmtBeneficiaryList.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtTransfers:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtTransfers.dmtTransfers
      Description: A Lambda function for dmtTransfers.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtRefundList:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRefundList.dmtRefundList
      Description: A Lambda function for dmtRefundList.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtRefundSendOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRefundSendOtp.dmtRefundSendOtp
      Description: A Lambda function for dmtRefundSendOtp.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        

  dmtRefundVerifyOtp:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtRefundVerifyOtp.dmtRefundVerifyOtp
      Description: A Lambda function for dmtRefundVerifyOtp.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  dmtTransferStatus:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dmtTransferStatus.dmtTransferStatus
      Description: A Lambda function for dmtTransferStatus.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  bbpsselectiveproviders:
    Type: AWS::Serverless::Function
    Properties:
      Handler: bbpsselectiveproviders.bbpsselectiveproviders
      Description: A Lambda function for bbpsselectiveproviders.js.
      Timeout: 120
      Events:
        bbpsselectiveproviders:
          Type: Schedule
          Properties:
            Schedule: rate(1 hour)
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn

  prepaidcard:
    Type: AWS::Serverless::Function
    Properties:
      Handler: prepaidcard.prepaidcard
      Description: A Lambda function for prepaidcard.js.
      Timeout: 120
      Role:
        Fn::GetAtt:
        - StagLambdaRole
        - Arn        


        
## External API 


  # khatabookAccountV2:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     Handler: khatabookAccountV2.khatabookAccountV2
  #     Description: A Lambda function for khatabookAccountV2.js.
  #     Timeout: 120
  #     Role:
  #       Fn::GetAtt:
  #       - StagLambdaRole
  #       - Arn

  # khatabookCustomerV2:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     Handler: khatabookCustomerV2.khatabookCustomerV2
  #     Description: A Lambda function for khatabookCustomerV2.js.
  #     Timeout: 120
  #     Role:
  #       Fn::GetAtt:
  #       - StagLambdaRole
  #       - Arn

  # khatabookTransactionV2:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     Handler: khatabookTransactionV2.khatabookTransactionV2
  #     Description: A Lambda function for khatabookTransactionV2.js.
  #     Timeout: 120
  #     Role:
  #       Fn::GetAtt:
  #       - StagLambdaRole
  #       - Arn
  
  # this is for shirgrapay insurance 
  # bbpsinsurance:
  #  Type: AWS::Serverless::Function
  #  Properties:
  #    Handler: bbpsinsurance.bbpsinsurance
  #    Description: A Lambda function for bbpsinsurance.js.
  #    Timeout: 120
  #    Role:
  #      Fn::GetAtt:
  #      - StagLambdaRole
  #      - Arn
