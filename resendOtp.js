const OtpRestHandler = require('./src/controller/externalApi/handlers/primaryServices/OtpRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.resendOtp = async (event, context, callback) => {
  const env = process.env.NODE_ENV || 'development'
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id', 'session_token'],
    isSessionTokenRequired: true,
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.RESENDOTP.name,
    request_url: util.externalServiceList.RESENDOTP.restEndpoint,
    callback: OtpRestHandler.resendOTP
  })
}
