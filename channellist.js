const primaryServicesRestHandler = require('./src/controller/externalApi/handlers/primaryServices/primaryServicesRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.channellist = async (event, context, callback) => {
  const env = process.env.NODE_ENV || 'development'
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id'],
    isTokenRequired: true,
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.SERVICE_LIST.name,
    request_url: util.externalServiceList.SERVICE_LIST.restEndpoint,
    callback: primaryServicesRestHandler.channelList
  })
}
