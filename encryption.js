'use strict'

const util = require('./src/util/util')
const common = require('./src/util/common')

const { serverlessErrorHandler } = require('./src/util/errorHandler')
const log = require('./src/util/log')

module.exports.encryption = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)
  }

  // middleware.logs(event)
  var payload = {}
  try {
    console.log(event.data.query)
    let response = await common.encryptEmitra(event.data.query, util.emitrapass)

    response = await serverlessErrorHandler(event, response)

    console.log('res1', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('ENCRYPTION ERROR', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
