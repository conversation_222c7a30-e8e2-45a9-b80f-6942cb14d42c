'use strict'

const { serverlessErrorHandler } = require('./src/util/errorHandler')
var cmsController = require('./src/controller/cms/cmsController.js')
const log = require('./src/util/log')

module.exports.cmsincentivefallback = async (event, context, callback) => {
  console.log('---RUNNING CMS INCENTIVE QUEUE FALL BACK CRON---')
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  var payload = {}
  try {
    var response = ''
    response = await cmsController.cmsIncentivesQueueFallBack()
    console.log(response)
    response = await serverlessErrorHandler(event, response)

    console.log('---CMS INCENTIVE QUEUE FALL BACK CRON FINAL RESPONSE---', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('---CMS INCENTIVE QUEUE FALL BACK CRON CATCH ERROR---', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
