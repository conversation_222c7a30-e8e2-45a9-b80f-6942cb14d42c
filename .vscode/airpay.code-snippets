{
  // Author: <PERSON>
  // Place your merchantapp workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  "Base Class Module": {
    "scope": "javascript",
    "prefix": [
      "elc",
      "controller"
    ],
    "body": [
      "const path = require('path')",
      "const DAO = require('../../lib/dao')",
      "const logs = require('../../util/log')",
      "const errorMsg = require('../../util/error')",
      "const mySQLWrapper = require('../../lib/mysqlWrapper')",
      "const errorEmail = require('../../util/errorHandler')",
      "const validator = require('../../util/validator')",
      "\nclass ${TM_FILENAME_BASE/(.{*)/${1:/upcase}/} extends DAO {",
      "\tget TABLE_NAME () {",
      "\t\treturn 'ma_${2:table_name}'",
      "\t}\n",
      "\tget PRIMARY_KEY () {",
      "\t\treturn 'ma_${2:table_name}_id'",
      "\t}\n",
      "\t/**",
      "\t * ${3:methodName} description - ${4:What's the method about?}",
      "\t * @param {null} _",
      "\t * @param {{ ma_user_id: number, userid: number$5 }} fields",
      "\t * @returns {Promise <{ status: number, message: string, respcode: number$6 }>}",
      "\t */",
      "\tstatic async ${3:methodName} (_, fields) {",
      "\t\tlogs.logger({ pagename: path.basename(__filename), action: '${3:methodName}', type: 'request', fields })",
      "\n\t\t// Validate All Required Fields - Add parameter name in array for fields validation",
      "\t\tconst validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'$7])",
      "\t\tif (validatorResponse.status != 200) return validatorResponse",
      "\n\t\tconst connection = await mySQLWrapper.getConnectionFromPool()",
      "\n\t\ttry {",
      "\t\t\t${0:// Happy Coding...}",
      "\t\t} catch (err) {",
      "\t\t\tlogs.logger({ pagename: path.basename(__filename), action: '${3:methodName}', type: 'err', fields: err })",
      "\t\t\terrorEmail.notifyCatchErrorEmail({",
      "\t\t\t\tfunction: '${3:methodName}',",
      "\t\t\t\tdata: { ...fields },",
      "\t\t\t\terror: err",
      "\t\t\t})",
      "\t\t\treturn { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }",
      "\t\t} finally {",
      "\t\t\tconnection.release()",
      "\t\t}",
      "\t}",
      "}\n",
      "module.exports = ${TM_FILENAME_BASE/(.{1})/${1:/upcase}/}\n"
    ],
    "description": "Creates a Dao extended Class and a basic class method"
  },
  "Masking Class Module": {
    "scope": "javascript",
    "prefix": [
      "elmc",
      "controllermasking"
    ],
    "body": [
      "const path = require('path')",
      "const DAO = require('../../lib/dao')",
      "const logs = require('../../util/log')",
      "const errorMsg = require('../../util/error')",
      "const mySQLWrapper = require('../../lib/mysqlWrapper')",
      "const errorEmail = require('../../util/errorHandler')",
      "const validator = require('../../util/validator')",
      "const common_fns = require('../../util/common_fns')",
      "\nclass ${TM_FILENAME_BASE/(.{1})/${1:/upcase}/} extends DAO {",
      "\tget TABLE_NAME () {",
      "\t\treturn 'ma_${2:table_name}'",
      "\t}\n",
      "\tget PRIMARY_KEY () {",
      "\t\treturn 'ma_${2:table_name}_id'",
      "\t}\n",
      "\t/**",
      "\t * ${3:methodName} description - ${4:What's the method about?}",
      "\t * @param {null} _",
      "\t * @param {{ ma_user_id: number, userid: number$5 }} fields",
      "\t * @returns {Promise <{ status: number, message: string, respcode: number$6 }>}",
      "\t */",
      "\tstatic async ${3:methodName} (_, fields) {",
      "\t\t// Masks or removes the specified elements from a json or graphql query string - maskValue(json|graphqlString, string|string[], removeElement, CompletelyMaskFullElement)",
      "\t\tlogs.logger({ pagename: path.basename(__filename), action: '${3:methodName}', type: 'request', fields: common_fns.maskValue(fields, ['pan', 'pancard', 'aadhar_number'$7], ${8|false,true|}, ${9|true,false|}) })",
      "\n\t\t// Validate All Required Fields - Add parameter name in array for fields validation",
      "\t\tconst validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'$10])",
      "\t\tif (validatorResponse.status != 200) return validatorResponse",
      "\n\t\tconst connection = await mySQLWrapper.getConnectionFromPool()",
      "\n\t\ttry {",
      "\t\t\t${0:// Happy Coding...}",
      "\t\t} catch (err) {",
      "\t\t\tlogs.logger({ pagename: path.basename(__filename), action: '${3:methodName}', type: 'err', fields: err })",
      "\t\t\terrorEmail.notifyCatchErrorEmail({",
      "\t\t\t\tfunction: '${3:methodName}',",
      "\t\t\t\tdata: { ...fields },",
      "\t\t\t\terror: err",
      "\t\t\t})",
      "\t\t\treturn { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }",
      "\t\t} finally {",
      "\t\t\tconnection.release()",
      "\t\t}",
      "\t}",
      "}\n",
      "module.exports = ${TM_FILENAME_BASE/(.{1})/${1:/upcase}/}\n"
    ],
    "description": "Creates a Dao extended Class and a masking class method"
  },
  "Basic Class Method": {
    "scope": "javascript",
    "prefix": [
      "elcm",
      "method"
    ],
    "body": [
      "/**",
      " * ${1:methodName} description - ${2:What's the method about?}",
      " * @param {null} _",
      " * @param {{ ma_user_id: number, userid: number$3 }} fields",
      " * @returns {Promise <{ status: number, message: string, respcode: number$4 }>}",
      " */",
      "static async ${1:methodName} (_, fields) {",
      "\tlogs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: 'request', fields })",
      "\n\t// Validate All Required Fields - Add parameter name in array for fields validation",
      "\tconst validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'$5])",
      "\tif (validatorResponse.status != 200) return validatorResponse",
      "\n\tconst connection = await mySQLWrapper.getConnectionFromPool()",
      "\n\ttry {",
      "\t\t${0:// Happy Coding...}",
      "\t} catch (err) {",
      "\t\tlogs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: 'err', fields: err })",
      "\t\terrorEmail.notifyCatchErrorEmail({",
      "\t\t\tfunction: '${1:methodName}',",
      "\t\t\tdata: { ...fields },",
      "\t\t\terror: err",
      "\t\t})",
      "\t\treturn { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }",
      "\t} finally {",
      "\t\tconnection.release()",
      "\t}",
      "}"
    ],
    "description": "Creates a basic class method"
  },
  "Basic Class Support Method": {
    "scope": "javascript",
    "prefix": [
      "elcsm",
      "methodsupport"
    ],
    "body": [
      "/**",
      " * @private",
      " * ${1:methodName} description - ${2:What's the method about?}",
      " * @param {{ ma_user_id: number, userid: number$3 }} fields",
      " * @param {any} con connection",
      " * @returns {Promise <{ status: number, message: string, respcode: number$4 }>}",
      " */",
      "static async ${1:methodName} (fields, con) {",
      "\tlogs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: 'request', fields })",
      "\n\t// Validate All Required Fields - Add parameter name in array for fields validation",
      "\tconst validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'$5])",
      "\tif (validatorResponse.status != 200) return validatorResponse",
      "\n\tconst tempConnection = !con",
      "\tconst connection = con || await mySQLWrapper.getConnectionFromPool()",
      "\n\ttry {",
      "\t\t${0:// Happy Coding...}",
      "\t} catch (err) {",
      "\t\tlogs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: 'err', fields: err })",
      "\t\terrorEmail.notifyCatchErrorEmail({",
      "\t\t\tfunction: '${1:methodName}',",
      "\t\t\tdata: { ...fields },",
      "\t\t\terror: err",
      "\t\t})",
      "\t\treturn { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }",
      "\t} finally {",
      "\t\tif (tempConnection) connection.release()",
      "\t}",
      "}"
    ],
    "description": "Creates a basic class method"
  },
  "Masking Class Method": {
    "scope": "javascript",
    "prefix": [
      "elmcm",
      "methodmasking"
    ],
    "body": [
      "/**",
      " * ${1:methodName} description - ${2:What's the method about?}",
      " * @param {null} _",
      " * @param {{ ma_user_id: number, userid: number$3 }} fields",
      " * @returns {Promise <{ status: number, message: string, respcode: number$4 }>}",
      " */",
      "static async ${1:methodName} (_, fields) {",
      "\t// Masks or removes the specified elements from a json or graphql query string - maskValue(json|graphqlString, string|string[], removeElement, CompletelyMaskFullElement)",
      "\tlogs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: 'request', fields: common_fns.maskValue(fields, ['pan', 'pancard', 'aadhar_number'$5], ${6|false,true|}, ${7|true,false|}) })",
      "\n\t// Validate All Required Fields - Add parameter name in array for fields validation",
      "\tconst validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid'$8])",
      "\tif (validatorResponse.status != 200) return validatorResponse",
      "\n\tconst connection = await mySQLWrapper.getConnectionFromPool()",
      "\n\ttry {",
      "\t\t${0:// Happy Coding...}",
      "\t} catch (err) {",
      "\t\tlogs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: 'err', fields: err })",
      "\t\terrorEmail.notifyCatchErrorEmail({",
      "\t\t\tfunction: '${1:methodName}',",
      "\t\t\tdata: { ...fields },",
      "\t\t\terror: err",
      "\t\t})",
      "\t\treturn { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }",
      "\t} finally {",
      "\t\tconnection.release()",
      "\t}",
      "}"
    ],
    "description": "Creates a masking class method"
  },
  "Return Statement": {
    "scope": "javascript",
    "prefix": [
      "elret"
    ],
    "body": [
      "return { status: ${1:200}, respcode: ${2:1000}, message: errorMsg.responseCode[${2:1000}]$3 }"
    ],
    "description": "Return statement"
  },
  "Graphql Model": {
    "scope": "javascript",
    "prefix": [
      "elgm",
      "model"
    ],
    "description": "Generates Graphql Model Code for mutations and queries",
    "body": [
      "const {",
      "\tGraphQLInt,",
      "\tGraphQLNonNull,",
      "\tGraphQLString",
      "} = require('graphql')",
      "const type = require('./type')",
      "const ${1:ControllerName} = require('../../controller/${2:ControllerFolerName}/${1:ControllerName}')",
      "\n// Defines the mutations",
      "module.exports = {",
      "\t${3:methodName}: {",
      "\t\ttype,",
      "\t\targs: {",
      "\t\t\tma_user_id: {",
      "\t\t\t\ttype: new GraphQLNonNull(GraphQLInt)",
      "\t\t\t},",
      "\t\t\tuserid: {",
      "\t\t\t\ttype: new GraphQLNonNull(GraphQLInt)",
      "\t\t\t}$0",
      "\t\t},",
      "\t\tresolve: ${1:ControllerName}.${3:methodName}.bind(${1:ControllerName})",
      "\t}",
      "}\n"
    ]
  },
  "Graphql Model Field": {
    "scope": "javascript",
    "prefix": [
      "elgmf"
    ],
    "description": "Generates Graphql fields in Model Code for mutations and queries",
    "body": [
      ",",
      "${1:methodName}: {",
      "\ttype,",
      "\targs: {",
      "\t\tma_user_id: {",
      "\t\t\ttype: new GraphQLNonNull(GraphQLInt)",
      "\t\t},",
      "\t\tuserid: {",
      "\t\t\ttype: new GraphQLNonNull(GraphQLInt)",
      "\t\t}$0",
      "\t},",
      "\tresolve: ${2:ControllerName}.${1:methodName}.bind(${2:ControllerName})",
      "}"
    ]
  },
  "Graphql Model Type": {
    "scope": "javascript",
    "prefix": [
      "elgmt"
    ],
    "description": "Generates Graphql Type code in Model",
    "body": [
      "const { GraphQLBoolean } = require('graphql')",
      "const {",
      "\tGraphQLString,",
      "\tGraphQLObjectType,",
      "\tGraphQLNonNull,",
      "\tGraphQLInt,",
      "\tGraphQLFloat,",
      "\tGraphQLList",
      "} = require('graphql')",
      "const enumType = require('../commonEnum')",
      "const scalarType = require('../commonScalar')",
      "\nconst ${1:yourTypeName}Type = new GraphQLObjectType({",
      "\tname: '${1:yourTypeName}',",
      "\tdescription: '${1:yourTypeName} type',",
      "\tfields: {",
      "\t\tstatus: {",
      "\t\t\ttype: GraphQLInt",
      "\t\t},",
      "\t\trespcode: {",
      "\t\t\ttype: GraphQLInt",
      "\t\t},",
      "\t\tmessage: {",
      "\t\t\ttype: GraphQLString",
      "\t\t}$0",
      "\t}",
      "})\n"
    ]
  },
  "Model Type Graphql List": {
    "scope": "javascript",
    "prefix": [
      "elgmtl"
    ],
    "description": "Generates GraphqlList Type code for type.js",
    "body": [
      ",",
      "${1:graphQlObjectTypeName}: {",
      "\ttype: GraphQLList(${1:graphQlObjectTypeName})",
      "}$0",
    ]
  },
  "Model Graphql Object Type": {
    "scope": "javascript",
    "prefix": [
      "elgmto"
    ],
    "description": "Generates GraphqlObjectType Type code for type.js",
    "body": [
      "const ${1:graphQlObjectTypeName} = new GraphQLObjectType({",
      "\tname: '${2:endpoint}_${1:graphQlObjectTypeName}',",
      "\tdescription: 'List of ${1:graphQlObjectTypeName} in ${2:endpoint} module',",
      "\tfields: {",
      "\t\t${3:field_name}: {",
      "\t\t\ttype: GraphQLInt",
      "\t\t}$0",
      "\t}",
      "})\n"
    ]
  },
  "Graphql Schema Index": {
    "scope": "javascript",
    "prefix": [
      "elgsi"
    ],
    "description": "Generates Graphql Schema Index Code",
    "body": [
      "const { GraphQLSchema } = require('graphql')",
      "const query = require('./queries')",
      "const mutation = require('./mutations')",
      "\nmodule.exports = new GraphQLSchema({",
      "\tquery,",
      "\tmutation",
      "})\n"
    ]
  },
  "Graphql Schema Field": {
    "scope": "javascript",
    "prefix": [
      "elgsf"
    ],
    "description": "Generates Graphql Schema Mutation/Query Code",
    "body": [
      "const { GraphQLObjectType } = require('graphql')",
      "const ${1:enpointName}${2|Mutations,Query|} = require('../../model/${3:model_folder_name}/${4|mutations,queries|}')",
      "\nmodule.exports = new GraphQLObjectType({",
      "\tname: 'Root${2|Mutations,Query|}Type',",
      "\tfields: {",
      "\t\t...${1:enpointName}${2|Mutations,Query|}$0",
      "\t}",
      "})\t"
    ]
  },
  "Graphql Schema Field API": {
    "scope": "javascript",
    "prefix": [
      "elgsfa"
    ],
    "description": "Adds another line of api",
    "body": [
      ",",
      "${1:apiname}: ${2:ModuleName}.${1:apiname}"
    ]
  },
  "Routes": {
    "scope": "javascript",
    "prefix": [
      "elr"
    ],
    "description": "Generate Code for Routes",
    "body": [
      "const depthLimit = require('graphql-depth-limit')",
      "const graphqlHTTP = require('express-graphql')",
      "const router = require('express').Router()",
      "const util = require('../util/util')",
      "const schema = require('../schema/${0:schema_folder_name}/index')",
      "\nrouter.post('/', graphqlHTTP({",
      "\tschema,",
      "\tgraphiql: false,",
      "\tvalidationRules: [depthLimit(5)]",
      "}))",
      "\nmodule.exports = router\n",
    ]
  },
  "Handler": {
    "scope": "javascript",
    "prefix": [
      "elh"
    ],
    "description": "Generate Code for Handler",
    "body": [
      "'use strict'",
      "\nconst { graphql } = require('graphql')",
      "const jwt = require('jsonwebtoken')",
      "const util = require('./src/util/util')",
      "const schema = require('./src/schema/${1:schema_folder_name}/index')",
      "const common = require('./src/util/common')",
      "const middleware = require('./src/util/middleware')",
      "\nconst { serverlessErrorHandler, notifyCatchErrorEmail } = require('./src/util/errorHandler')",
      "\nmodule.exports.${TM_FILENAME_BASE} = async (event, context, callback) => {",
      "\tconsole.log('event++++++++++++++++', event)",
      "\n\tif (process.env.IS_OFFLINE === 'true') {",
      "\t\tevent.data = JSON.parse(event.body)",
      "\t\tconsole.log('After Parse event.data', event.data)",
      "\n\t\t// Testing purpose",
      "\t\tconst isAuthorize = middleware.validateTokenSchema(event, {",
      "\t\t\tpublickey: 7556",
      "\t\t})",
      "\n\t\tconsole.log('isAuthorize))==>', isAuthorize)",
      "\t}",
      "\tconst downtimeresp = await common.downTime()",
      "\tif (downtimeresp[0] != 'undefined' && downtimeresp[0] != null) {",
      "\t\tif (downtimeresp[0].notice_flag == 'Y') {",
      "\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\treturn await common.encrypt(JSON.stringify({ status: 301, message: downtimeresp[0].message }))",
      "\t\t\t} else {",
      "\t\t\t\treturn JSON.stringify({ status: 301, message: downtimeresp[0].message })",
      "\t\t\t}",
      "\t\t}",
      "\t}",
      "\n\tlet payload = {}",
      "\tlet token = ''",
      "\ttry {",
      "\t\t// jwt verify",
      "\t\tif (util.isProduction() || util.isStaging()) {",
      "\t\t\tconsole.log('Production123')",
      "\t\t\ttoken = event.headers.Authorization",
      "\t\t\tpayload = jwt.verify(token, util.jwtKey)",
      "\t\t\tconsole.log('payload data++++++++', payload)",
      "\t\t}",
      "\t\t// jwt token db check",
      "\t\tif ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey)) {",
      "\t\t\tconsole.log('before decrypt data', event.data.query)",
      "\t\t\tif (util.isProduction() || util.isStaging()) {",
      "\t\t\t\tconst tokenresp = await common.tokenValidation(token, payload.publickey)",
      "\t\t\t\tconsole.log('tokenresp>>', tokenresp)",
      "\t\t\t\tif (tokenresp.status === 400) {",
      "\t\t\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\t\t\treturn await common.encrypt(JSON.stringify({ status: 401, message: tokenresp.message }))",
      "\t\t\t\t\t} else {",
      "\t\t\t\t\t\treturn JSON.stringify({ status: 401, message: tokenresp.message })",
      "\t\t\t\t\t}",
      "\t\t\t\t}",
      "\t\t\t}",
      "\t\t}",
      "\t\tconsole.log('before decrypt data', event.data.query)",
      "\t\tif (util.encryptionFlag) {",
      "\t\t\tevent.data.query = await common.decrypt(event.data.query, util.encryptpassword)",
      "\t\t\tconsole.log('after decrypt data', event.data.query)",
      "\t\t}",
      "\n\t\t// request size",
      "\t\tconst isSecuredResponse = await middleware.secureServerless(event, context, payload)",
      "\t\tif (isSecuredResponse.status === 500) {",
      "\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\treturn await common.encrypt(JSON.stringify(isSecuredResponse))",
      "\t\t\t} else {",
      "\t\t\t\treturn JSON.stringify(isSecuredResponse)",
      "\t\t\t}",
      "\t\t}",
      "\n\t\t// jwt detail  verify with grapql deails ma_user-id & profileid",
      "\t\tlet isAuthorize = {}",
      "\t\tif ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey)) {",
      "\t\t\tisAuthorize = middleware.validateTokenSchema(event, payload)",
      "\t\t\tconsole.log('isAuthorize))==>', isAuthorize)",
      "\t\t\tif (isAuthorize.status != 200) {",
      "\t\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\t\treturn await common.encrypt(JSON.stringify(isAuthorize))",
      "\t\t\t\t} else {",
      "\t\t\t\t\treturn JSON.stringify(isAuthorize)",
      "\t\t\t\t}",
      "\t\t\t}",
      "\t\t}",
      "\n\t\tlet response = await graphql(schema, event.data.query, (err, result) => {",
      "\t\t\tif (err) {",
      "\t\t\t\tconsole.log('Error in graphql', err)",
      "\t\t\t\tcallback(err)",
      "\t\t\t}",
      "\t\t\tconsole.log('handler result', result)",
      "\t\t\t// callback(null, { statusCode: 200, body: JSON.stringify(err) })",
      "\t\t})",
      "\n\t\tresponse = await serverlessErrorHandler(event, response)",
      "\n\t\tif (util.encryptionFlag) {",
      "\t\t\tconsole.log('res1', await common.encrypt(JSON.stringify(response), util.encryptpassword))",
      "\t\t\treturn await common.encrypt(JSON.stringify(response), util.encryptpassword)",
      "\t\t} else {",
      "\t\t\tconsole.log('res1', response)",
      "\t\t\tif (process.env.IS_OFFLINE === 'true') {",
      "\t\t\t\tcallback(null, { statusCode: 200, body: JSON.stringify(response) })",
      "\t\t\t}",
      "\t\t\treturn response",
      "\t\t}",
      "\t} catch (error) {",
      "\t\tconsole.log('JWT FAILED IN VERIFICATION', error)",
      "\t\t// return { status: 400, message: 'jwt failed'} // TokenExpiredError",
      "\t\tif (!(error instanceof jwt.JsonWebTokenError) && !(error instanceof jwt.TokenExpiredError)) {",
      "\t\t\tnotifyCatchErrorEmail({",
      "\t\t\t\tfunction: '${TM_FILENAME_BASE}Handler',",
      "\t\t\t\tdata: event.data,",
      "\t\t\t\terror: error",
      "\t\t\t})",
      "\t\t}",
      "\t\tlet errResp = {}",
      "\t\tif (error instanceof jwt.JsonWebTokenError) {",
      "\t\t\terrResp = { status: 401, message: error.message }",
      "\t\t} else {",
      "\t\t\terrResp = { status: 400, message: 'Request Failed' }",
      "\t\t}",
      "\t\tif (util.encryptionFlag) {",
      "\t\t\tconsole.log('err1', await common.encrypt(JSON.stringify(errResp), util.encryptpassword))",
      "\t\t\treturn await common.encrypt(JSON.stringify(errResp), util.encryptpassword)",
      "\t\t} else {",
      "\t\t\tconsole.log('res1', errResp)",
      "\t\t\treturn errResp",
      "\t\t}",
      "\t}",
      "}\n"
    ]
  },
  "Handler With Log Masking": {
    "scope": "javascript",
    "prefix": [
      "elhm"
    ],
    "description": "Generate Code for Handler with log masking",
    "body": [
      "'use strict'",
      "\nconst { graphql } = require('graphql')",
      "const jwt = require('jsonwebtoken')",
      "const util = require('./src/util/util')",
      "const schema = require('./src/schema/${1:schema_folder_name}/index')",
      "const common = require('./src/util/common')",
      "const middleware = require('./src/util/middleware')",
      "const common_fns = require('./src/util/common_fns')",
      "\nconst { serverlessErrorHandler, notifyCatchErrorEmail } = require('./src/util/errorHandler')",
      "\nmodule.exports.${TM_FILENAME_BASE} = async (event, context, callback) => {",
      "\tconsole.log('event++++++++++++++++', event)",
      "\n\tif (process.env.IS_OFFLINE === 'true') {",
      "\t\tevent.data = JSON.parse(event.body)",
      "\t\tconsole.log('After Parse event.data', event.data)",
      "\n\t\t// Testing purpose",
      "\t\tconst isAuthorize = middleware.validateTokenSchema(event, {",
      "\t\t\tpublickey: 7556",
      "\t\t})",
      "\n\t\tconsole.log('isAuthorize))==>', isAuthorize)",
      "\t}",
      "\tconst downtimeresp = await common.downTime()",
      "\tif (downtimeresp[0] != 'undefined' && downtimeresp[0] != null) {",
      "\t\tif (downtimeresp[0].notice_flag == 'Y') {",
      "\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\treturn await common.encrypt(JSON.stringify({ status: 301, message: downtimeresp[0].message }))",
      "\t\t\t} else {",
      "\t\t\t\treturn JSON.stringify({ status: 301, message: downtimeresp[0].message })",
      "\t\t\t}",
      "\t\t}",
      "\t}",
      "\n\tlet payload = {}",
      "\tlet token = ''",
      "\ttry {",
      "\t\t// jwt verify",
      "\t\tif (util.isProduction() || util.isStaging()) {",
      "\t\t\tconsole.log('Production123')",
      "\t\t\ttoken = event.headers.Authorization",
      "\t\t\tpayload = jwt.verify(token, util.jwtKey)",
      "\t\t\tconsole.log('payload data++++++++', payload)",
      "\t\t}",
      "\t\t// jwt token db check",
      "\t\tif ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey)) {",
      "\t\t\tconsole.log('before decrypt data', event.data.query)",
      "\t\t\tif (util.isProduction() || util.isStaging()) {",
      "\t\t\t\tconst tokenresp = await common.tokenValidation(token, payload.publickey)",
      "\t\t\t\tconsole.log('tokenresp>>', tokenresp)",
      "\t\t\t\tif (tokenresp.status === 400) {",
      "\t\t\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\t\t\treturn await common.encrypt(JSON.stringify({ status: 401, message: tokenresp.message }))",
      "\t\t\t\t\t} else {",
      "\t\t\t\t\t\treturn JSON.stringify({ status: 401, message: tokenresp.message })",
      "\t\t\t\t\t}",
      "\t\t\t\t}",
      "\t\t\t}",
      "\t\t}",
      "\t\tconsole.log('before decrypt data', event.data.query)",
      "\t\tif (util.encryptionFlag) {",
      "\t\t\tevent.data.query = await common.decrypt(event.data.query, util.encryptpassword)",
      "\t\t\t// Remove Value - maskValue Removes sensitive values from graphql query that are specified in the array",
      "\t\t\tconsole.log('after decrypt data', common_fns.maskValue(event.data.query, ['password', 'pan', 'aadhar_number', 'customer_aahaar'$0]))",
      "\t\t}",
      "\n\t\t// request size",
      "\t\tconst isSecuredResponse = await middleware.secureServerless(event, context, payload)",
      "\t\tif (isSecuredResponse.status === 500) {",
      "\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\treturn await common.encrypt(JSON.stringify(isSecuredResponse))",
      "\t\t\t} else {",
      "\t\t\t\treturn JSON.stringify(isSecuredResponse)",
      "\t\t\t}",
      "\t\t}",
      "\n\t\t// jwt detail  verify with grapql deails ma_user-id & profileid",
      "\t\tlet isAuthorize = {}",
      "\t\tif ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey)) {",
      "\t\t\tisAuthorize = middleware.validateTokenSchema(event, payload)",
      "\t\t\tconsole.log('isAuthorize))==>', isAuthorize)",
      "\t\t\tif (isAuthorize.status != 200) {",
      "\t\t\t\tif (util.encryptionFlag) {",
      "\t\t\t\t\treturn await common.encrypt(JSON.stringify(isAuthorize))",
      "\t\t\t\t} else {",
      "\t\t\t\t\treturn JSON.stringify(isAuthorize)",
      "\t\t\t\t}",
      "\t\t\t}",
      "\t\t}",
      "\n\t\tlet response = await graphql(schema, event.data.query, (err, result) => {",
      "\t\t\tif (err) {",
      "\t\t\t\tconsole.log('Error in graphql', err)",
      "\t\t\t\tcallback(err)",
      "\t\t\t}",
      "\t\t\tconsole.log('handler result', result)",
      "\t\t\t// callback(null, { statusCode: 200, body: JSON.stringify(err) })",
      "\t\t})",
      "\n\t\tresponse = await serverlessErrorHandler(event, response)",
      "\n\t\tif (util.encryptionFlag) {",
      "\t\t\tconsole.log('res1', await common.encrypt(JSON.stringify(response), util.encryptpassword))",
      "\t\t\treturn await common.encrypt(JSON.stringify(response), util.encryptpassword)",
      "\t\t} else {",
      "\t\t\tconsole.log('res1', response)",
      "\t\t\tif (process.env.IS_OFFLINE === 'true') {",
      "\t\t\t\tcallback(null, { statusCode: 200, body: JSON.stringify(response) })",
      "\t\t\t}",
      "\t\t\treturn response",
      "\t\t}",
      "\t} catch (error) {",
      "\t\tconsole.log('JWT FAILED IN VERIFICATION', error)",
      "\t\t// return { status: 400, message: 'jwt failed'} // TokenExpiredError",
      "\t\tif (!(error instanceof jwt.JsonWebTokenError) && !(error instanceof jwt.TokenExpiredError)) {",
      "\t\t\tnotifyCatchErrorEmail({",
      "\t\t\t\tfunction: '${TM_FILENAME_BASE}Handler',",
      "\t\t\t\tdata: event.data,",
      "\t\t\t\terror: error",
      "\t\t\t})",
      "\t\t}",
      "\t\tlet errResp = {}",
      "\t\tif (error instanceof jwt.JsonWebTokenError) {",
      "\t\t\terrResp = { status: 401, message: error.message }",
      "\t\t} else {",
      "\t\t\terrResp = { status: 400, message: 'Request Failed' }",
      "\t\t}",
      "\t\tif (util.encryptionFlag) {",
      "\t\t\tconsole.log('err1', await common.encrypt(JSON.stringify(errResp), util.encryptpassword))",
      "\t\t\treturn await common.encrypt(JSON.stringify(errResp), util.encryptpassword)",
      "\t\t} else {",
      "\t\t\tconsole.log('res1', errResp)",
      "\t\t\treturn errResp",
      "\t\t}",
      "\t}",
      "}\n"
    ]
  },
  "Handler For Cron": {
    "scope": "javascript",
    "prefix": [
      "elhc",
      "cronhandler"
    ],
    "description": "Generate Code for Cron Handler",
    "body": [
      "'use strict'",
      "\nconst { serverlessErrorHandler } = require('./src/util/errorHandler')",
      "const ${1:Controller} = require('./src/controller/${2:controller_folder_name}/${1:Controller}.js')",
      "\nmodule.exports.${TM_FILENAME_BASE} = async (event, context, callback) => {",
      "\tconsole.log('---RUNNING ${4:PURPOSE OF} CRON---')",
      "\n\tconst payload = {}",
      "\ttry {",
      "\t\tlet response = ''",
      "\t\tresponse = await ${1:Controller}.revalidateTransaction()",
      "\t\tconsole.log(response)",
      "\t\tresponse = await serverlessErrorHandler(event, response)",
      "\n\t\tconsole.log('---${4:PURPOSE OF} CRON FINAL RESPONSE---', response)",
      "\t\tif (process.env.IS_OFFLINE === 'true') {",
      "\t\t\tcallback(null, { statusCode: 200, body: JSON.stringify(response) })",
      "\t\t}",
      "\t\treturn response",
      "\t} catch (error) {",
      "\t\tconsole.log('---${4:PURPOSE OF} CRON CATCH ERROR---', error)",
      "\t\tlet errResp = {}",
      "\t\terrResp = { status: 400, message: 'Request Failed' }",
      "\t\tconsole.log('res1', errResp)",
      "\t\treturn errResp",
      "\t}",
      "}\n"
    ]
  },
  "SQL SELECT QUERY": {
    "scope": "javascript",
    "prefix": [
      "elsq"
    ],
    "description": "Generate Code for Cron Handler",
    "body": [
      "const ${1:name}Query = `SELECT ${2:*} FROM ma_${3:table_name} $4`",
      "const $1 = await this.rawQuery($1Query, connection)"
    ]
  },
  "LOGGER": {
    "scope": "javascript",
    "prefix": [
      "logs"
    ],
    "description": "Generate Logs syntax",
    "body": [
      "logs.logger({ pagename: path.basename(__filename), action: '${1:methodName}', type: '${2:type}', fields: ${3:fields} })"
    ]
  }
}