'use strict'
const AWS = require('aws-sdk')
const util = require('./src/util/util')
const axios = require('axios')
const mySQLWrapper = require('./src/lib/mysqlWrapper')
// Configure the region
AWS.config.update({ region: 'ap-south-1' })

// Create an SQS service object
const sqs = new AWS.SQS({ apiVersion: '2012-11-05' })
const log = require('./src/util/log')

module.exports.amlthresholdprocessor = async (event, context) => {
  console.log('Received event:', JSON.stringify(event))
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  let isSet = false; let connection = null
  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    event.Records = event.data
  }
  try {
    if (event.Records && event.Records.length > 0) {
      connection = await mySQLWrapper.getConnectionFromPool()
      isSet = true
      const messagesRes = []
      const region = context.invokedFunctionArn.split(':')[3]
      const accountId = context.invokedFunctionArn.split(':')[4]
      const queueName = util[process.env.NODE_ENV].queueurls.amlthreshold_messages
      // const queueUrl = `https://sqs.${region}.amazonaws.com/${accountId}/${queueName}`
      const queueUrl = util[process.env.NODE_ENV].queuebaseurl + queueName

      console.log('EVENTRecords>>', JSON.stringify(event.Records))
      const queueManagerCtrl = require('./src/controller/queuemanager/queueManagerController')
      const loadedCtrls = {}
      for (const { messageId, body, messageAttributes, receiptHandle } of event.Records) {
        const { queueId } = messageAttributes
        const requireToProcess = await queueManagerCtrl.requireToProcess(connection, queueId.stringValue)
        if (requireToProcess === false) {
          console.log('QueueSkipped>>', queueId)
          continue
        }
        try {
          const MessagePayload = JSON.parse(body)
          console.log('MessagePayload >>', MessagePayload)
          // process.exit();
          let workingCtrl = null
          if (('controller' in MessagePayload) && (MessagePayload.controller in loadedCtrls)) {
            workingCtrl = loadedCtrls[MessagePayload.controller]
          } else {
            switch (MessagePayload.controller) {
              case 'amlController':
                workingCtrl = require('./src/controller/aml/amlController')
                loadedCtrls[MessagePayload.controller] = workingCtrl
                break
              default:
                break
            }
          }
          if (workingCtrl !== null) {
            const dataUpdate = { queue_status: 'R' }
            const updateQueue = await queueManagerCtrl.updateWhereData(connection, { data: dataUpdate, id: queueId.stringValue, where: 'queue_id' })

            let apiResponse = {}
            switch (MessagePayload.controller + MessagePayload.functions) {
              case 'amlControllerinsertAmlSnapshot':
                apiResponse = await workingCtrl.insertAmlSnapshot({ ...MessagePayload.fields, connection })
                console.log('apiResponse', apiResponse)
                break
              default:
                break
            }
            const temObj = {}
            if (Object.keys(apiResponse).length > 0) {
              const dataUpdate2 = { queue_status: 'S', queue_process_response: JSON.stringify({ response: JSON.stringify(apiResponse) }) }
              const updateQueue2 = await queueManagerCtrl.updateWhereData(connection, { data: dataUpdate2, id: queueId.stringValue, where: 'queue_id' })
            }

            if (process.env.IS_OFFLINE === 'true') {

            } else {
              var deleteParams = {
                QueueUrl: queueUrl,
                ReceiptHandle: receiptHandle
              }
              const response = await sqs.deleteMessage(deleteParams).promise()
              if (('ResponseMetadata' in response) && ('RequestId' in response.ResponseMetadata)) {
                temObj.deletedId = response.ResponseMetadata.RequestId
              }
              console.log('DeleteMessageFromQueueResponse>>', response)
            }

            console.log('SQS message %s: %j processed', messageId, body)
          } else {
            console.log('Controller not found')
          }
        } catch (error) {
          console.log('amlthresholderror>>>', error)
          const dataUpdate2 = { queue_status: 'E', queue_process_response: JSON.stringify({ error: error.message }) }
          const updateQueue2 = await queueManagerCtrl.updateWhereData(connection, { data: dataUpdate2, id: queueId.stringValue, where: 'queue_id' })
        }
      }
      return JSON.stringify({
        message: `Successfully processed ${event.Records.length} messages.`,
        messageData: messagesRes
      })
    } else {
      return 'Message List is Empty.'
    }
  } catch (error) {
    console.log('error', error)
    return 'Something went wrong at sms' + error.message
  } finally {
    if (isSet) connection.release()
  }
}
