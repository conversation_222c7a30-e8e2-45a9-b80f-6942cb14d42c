const tokenRestHandler = require('./src/controller/externalApi/handlers/primaryServices/tokenRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.getToken = async (event, context, callback) => {
  const env = process.env.NODE_ENV || 'development'
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id'],
    isTokenRequired: false,
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.GENERATE_TOKEN.name,
    request_url: util.externalServiceList.GENERATE_TOKEN.restEndpoint,
    callback: tokenRestHandler.generateToken
  })
}
