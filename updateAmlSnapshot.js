'use strict'
const util = require('./src/util/util')
const mySQLWrapper = require('./src/lib/mysqlWrapper')
const dao = require('./src/lib/dao')
const log = require('./src/util/log')

module.exports.updateAmlSnapshot = async (event, context, callback) => {
  // console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)
  }

  let isSet = false
  const connection = await mySQLWrapper.getConnectionFromPool()
  isSet = true
  try {
    const query = event.data.query
    console.log('----------DATA.QUERY-------------')
    console.log(query)
    if (query && query.fromDate && query.toDate) {
      const pattern = /^([0-9]{4})-([0-9]{2})-([0-9]{2})$/
      if (pattern.test(query.fromDate) && pattern.test(query.toDate)) {
        const getTransfersSql = `SELECT 
                                  trf.ma_transfers_id,
                                  trf.uic,
                                  trf.ma_beneficiaries_id,
                                  trf.transfer_amount,
                                  bene.account_number,
                                  DATE_FORMAT(trf.addedon, '%Y-%m-%d') AS addedon
                                FROM ma_transfers AS trf
                                INNER JOIN ma_beneficiaries AS bene ON  trf.ma_beneficiaries_id = bene.ma_beneficiaries_id
                                WHERE trf.addedon BETWEEN '${query.fromDate} 00:00:00' AND '${query.toDate} 23:59:59'
                                AND trf.transfer_status = 'S'`
        console.log('---getTransfersSql---')
        console.log(getTransfersSql)
        const queryResult = await dao.rawQuery(getTransfersSql, connection)
        // console.log('---queryResult---')
        // console.log(queryResult)
        if (queryResult.length > 0) {
          await mySQLWrapper.beginTransaction(connection)

          /* UPDATE ENTRY FROM SNAPSHOT TABLE FOR THE DATE TO 0 */
          const resetSql1 = `UPDATE ma_aml_threshold_snapshot SET amount = 0 WHERE snapshot_date BETWEEN '${query.fromDate}' AND '${query.toDate}'`
          const resetSql1Result = await dao.rawQuery(resetSql1, connection)

          const resetSql2 = `UPDATE ma_aml_threshold_snapshot_details SET amount = 0 WHERE snapshot_date BETWEEN '${query.fromDate}' AND '${query.toDate}'`
          const resetSql2Result = await dao.rawQuery(resetSql2, connection)

          for (const transfer of queryResult) {
            // Insert/update transfer amount in snapshot table
            const snapShotQuery = `INSERT INTO ma_aml_threshold_snapshot
                                    (snapshot_date, type, type_value, amount)
                                  VALUES
                                    ('${transfer.addedon}', '1', '${transfer.uic}', ${transfer.transfer_amount}),
                                    ('${transfer.addedon}', '2', '${transfer.account_number}', ${transfer.transfer_amount})
                                  ON DUPLICATE KEY 
                                  UPDATE
                                    amount = amount + ${transfer.transfer_amount}`
            const snapShotQueryResult = await dao.rawQuery(snapShotQuery, connection)
            // console.log('snapShotQuery')
            // console.log(snapShotQuery)

            // Insert/update transfer amount in snapshot details table
            const snapShotDetailsQuery = `INSERT INTO ma_aml_threshold_snapshot_details
                              (snapshot_date, uic, beneficiary_id, account_number, amount)
                            VALUES
                              ('${transfer.addedon}', '${transfer.uic}', '${transfer.ma_beneficiaries_id}', '${transfer.account_number}', ${transfer.transfer_amount})
                            ON DUPLICATE KEY 
                            UPDATE
                              amount = amount + ${transfer.transfer_amount}`
            const snapShotDetailsQueryResult = await dao.rawQuery(snapShotDetailsQuery, connection)
            // console.log('snapShotDetailsQuery')
            // console.log(snapShotDetailsQuery)
          }
          await mySQLWrapper.commit(connection)
          if (process.env.IS_OFFLINE === 'true') {
            callback(null, { statusCode: 200, body: JSON.stringify({ status: 200, message: 'Records successfully updated' }) })
          }
          return JSON.stringify({ status: 200, message: 'Records successfully updated' })
        } else {
          if (process.env.IS_OFFLINE === 'true') {
            callback(null, { statusCode: 200, body: JSON.stringify({ status: 200, message: 'No records found' }) })
          }
          return JSON.stringify({ status: 200, message: 'No records found' })
        }
      } else {
        if (process.env.IS_OFFLINE === 'true') {
          callback(null, { statusCode: 200, body: JSON.stringify({ status: 400, message: 'Date format should be YYYY-MM-DD' }) })
        }
        return JSON.stringify({ status: 400, message: 'Date format should be YYYY-MM-DD' })
      }
    } else {
      if (process.env.IS_OFFLINE === 'true') {
        callback(null, { statusCode: 200, body: JSON.stringify({ status: 400, message: 'From date and To date is required' }) })
      }
      return JSON.stringify({ status: 400, message: 'From date and To date is required' })
    }
  } catch (error) {
    console.log('error', error)
    await mySQLWrapper.rollback(connection)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify({ status: 400, message: error.message }) })
    }
    return JSON.stringify({ status: 400, message: error.message })
  } finally {
    if (isSet) connection.release()
  }
}
