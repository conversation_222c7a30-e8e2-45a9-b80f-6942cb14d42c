'use strict'

const { graphql } = require('graphql')
const jwt = require('jsonwebtoken')
const util = require('./src/util/util')
const schema = require('./src/schema/externalApiHandlers/index')
const common = require('./src/util/common')
const middleware = require('./src/util/middleware')
const { serverlessErrorHandler } = require('./src/util/errorHandler')
const log = require('./src/util/log')

module.exports.apirouter = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)
  }
  const downtimeresp = await common.downTime()
  if (downtimeresp[0] != 'undefined' && downtimeresp[0] != null) {
    if (downtimeresp[0].notice_flag == 'Y') {
      if (util.encryptionFlag) {
        return await common.encrypt(JSON.stringify({ status: 301, message: downtimeresp[0].message }))
      } else {
        return JSON.stringify({ status: 301, message: downtimeresp[0].message })
      }
    }
  }
  var payload = {}
  var token = ''
  try {
    // util.isProduction() = true
    if (util.isProduction()) {
      console.log('Production123')
      token = event.headers.Authorization
      payload = jwt.verify(token, util.jwtKey)
      console.log('payload data++++++++', payload)
    }
    if (!util.isProduction() || (util.isProduction() && payload.publickey)) {
      console.log('before decrypt data', event.data.query)
      if (util.isProduction()) {
        const tokenresp = await common.tokenValidation(token, payload.publickey)
        if (tokenresp.status === 400) {
          if (util.encryptionFlag) {
            return await common.encrypt(JSON.stringify({ status: 401, message: tokenresp.message }))
          } else {
            return JSON.stringify({ status: 401, message: tokenresp.message })
          }
        }
      }
      if (util.encryptionFlag) {
        event.data.query = await common.decrypt(event.data.query, util.encryptpassword)
        console.log('after decrypt data', event.data.query)
      }

      const isSecuredResponse = await middleware.secureServerless(event)
      if (isSecuredResponse.status === 500) {
        if (util.encryptionFlag) {
          return await common.encrypt(JSON.stringify(isSecuredResponse))
        } else {
          return JSON.stringify(isSecuredResponse)
        }
      }

      let response = await graphql(schema, event.data.query, (err, result) => {
        if (err) {
          console.log('Error in graphql', err)
          callback(err)
        }
        console.log('handler result', result)
        // callback(null, { statusCode: 200, body: JSON.stringify(err) })
      })

      response = await serverlessErrorHandler(event.data, response)

      if (util.encryptionFlag) {
        console.log('res1', await common.encrypt(JSON.stringify(response), util.encryptpassword))
        return await common.encrypt(JSON.stringify(response), util.encryptpassword)
      } else {
        console.log('res1', response)
        if (process.env.IS_OFFLINE === 'true') {
          callback(null, { statusCode: 200, body: JSON.stringify(response) })
        }
        return response
      }
      // console.log('=========Graphql Res==========',res)
    } else {
      console.log('jwt failed')
    }
  } catch (error) {
    console.log('JWT FAILED IN VERIFICATION', error)
    // return { status: 400, message: 'jwt failed'}
    var errResp = {}
    if (error instanceof jwt.JsonWebTokenError) {
      errResp = { status: 401, message: error.message }
    } else {
      errResp = { status: 400, message: 'Request Failed' }
    }
    if (util.encryptionFlag) {
      console.log('err1', await common.encrypt(JSON.stringify(errResp), util.encryptpassword))
      return await common.encrypt(JSON.stringify(errResp), util.encryptpassword)
    } else {
      console.log('res1', errResp)
      return errResp
    }
  }
}
