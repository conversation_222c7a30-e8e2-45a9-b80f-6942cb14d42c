const tokenRestHandler = require('./src/controller/externalApi/handlers/primaryServices/tokenRestHandler')
const util = require('./src/util/util')
const RestApiAwsLamdaHandler = require('./src/controller/externalApi/handlers/restApiAwsLambdaHandler')
const log = require('./src/util/log')

module.exports.getSessionToken = async (event, context, callback) => {
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }
  const env = process.env.NODE_ENV || 'development'
  return await RestApiAwsLamdaHandler.handler({
    event,
    headerParams: ['affiliate_id', 'token'],
    isTokenRequired: true,
    isIpWhiteListRequired: util.externalIPWhiteListCheckRequired.includes(env),
    isEncryptionRequired: util.externalEncryptionRequired.includes(env),
    request_type: util.externalServiceList.GENERATE_SESSION.name,
    request_url: util.externalServiceList.GENERATE_SESSION.restEndpoint,
    callback: tokenRestHandler.generateSession
  })
}
