-- get category list query

SELECT
	spm.ma_shopping_product_master_id AS category_id,
	spm.name,
	spm.title,
	spm.sub_title,
	spm.logo_url,
	spm.bg_url,
	spm.description,
	MIN(CASE WHEN ISNULL(spd.discounted_price) THEN spd.price ELSE spd.discounted_price END) AS min_price,
	spd.price,
	spd.discounted_price
FROM
	ma_shopping_product_master spm
INNER JOIN
	ma_shopping_product_details spd
ON
	spd.product_status = 'A' AND
	!ISNULL(spd.quantity) AND
	spd.quantity > 0 AND
	spm.ma_shopping_product_master_id = spd.ma_shopping_product_master_id
WHERE
	spm.category_status = 'A'
GROUP BY spd.ma_shopping_product_master_id


-- get product list query

SELECT
	spd.ma_shopping_product_details_id AS product_id,
	spd.name AS title,
	spd.description AS sub_title,
	spd.virtual,
	spm.description,
	spd.price,
	spd.discounted_price,
	spd.min_quantity,
	spd.max_quantity
FROM
	ma_shopping_product_details spd
INNER JOIN
	ma_shopping_product_master spm
ON
	spm.category_status = 'A' AND
	spm.ma_shopping_product_master_id = spd.ma_shopping_product_master_id
WHERE
	spd.ma_shopping_product_master_id = 1 AND
	spd.product_status = 'A' AND
	!ISNULL(spd.quantity) AND
	spd.quantity > 0


-- get address list query for a merchant

SELECT
	ma_shopping_address_id AS address_id,
	NAME,
	email,
	phone,
	address,
	pincode
FROM
	ma_shopping_address
WHERE
	ma_user_id = 28641