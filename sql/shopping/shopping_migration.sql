-- Shopping Address

CREATE TABLE `ma_shopping_address` (
  `ma_shopping_address_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(10) DEFAULT NULL,
  `address` text,
  `pincode` varchar(6) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `state` int(11) DEFAULT NULL,
  `address_status` enum('A','I') DEFAULT 'A' COMMENT 'A - Active, I - Inactive',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_address_id`),
  KEY `shopping_address_index` (`ma_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- Shopping Order Master

CREATE TABLE `ma_shopping_order_master` (
  `ma_shopping_order_master_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(11) DEFAULT NULL,
  `orderid` varchar(100) DEFAULT NULL,
  `ma_transaction_master_id` int(11) DEFAULT NULL,
  `ip_address` varchar(100) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL COMMENT 'ma_shopping_product_master_id',
  `product_id` int(11) DEFAULT NULL COMMENT 'ma_shopping_product_details_id',
  `quantity` int(11) DEFAULT NULL,
  `amount` decimal(12,4) DEFAULT NULL,
  `delivery_charges` decimal(12,4) DEFAULT NULL,
  `current_status` enum('I','P','S','REV','F','A','SP') DEFAULT NULL COMMENT 'I - Initiated, P - Pending, S - Success, REV - Reverse, F - Failed,A-Fastag Assigned,SP-Shipped',
  `virtual` enum('Y','N') DEFAULT NULL COMMENT 'Y - Yes, N - No',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_order_master_id`),
  KEY `shopping_order_index` (`ma_transaction_master_id`,`ma_shopping_order_master_id`,`ma_user_id`,`orderid`,`category_id`,`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=latin1


-- Shopping Product Master

CREATE TABLE `ma_shopping_product_master` (
  `ma_shopping_product_master_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `sub_title` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `bg_url` varchar(255) DEFAULT NULL,
  `logo_url` varchar(255) DEFAULT NULL,
  `category_status` enum('A','I') DEFAULT 'A' COMMENT 'A - Active, I - Inactive',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_product_master_id`),
  KEY `shopping_product_master_index` (`ma_shopping_product_master_id`,`category_status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- Shopping Product Details

CREATE TABLE `ma_shopping_product_details` (
  `ma_shopping_product_details_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_shopping_product_master_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `price` decimal(12,4) DEFAULT NULL,
  `discounted_price` decimal(12,4) DEFAULT NULL,
  `delivery_charges` decimal(12,4) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `product_status` enum('A','I') DEFAULT 'A' COMMENT 'A - Active, I - Inactive',
  `virtual` enum('Y','N') DEFAULT 'Y' COMMENT 'Y - Yes, N - No',
  `min_quantity` int(11) DEFAULT NULL,
  `max_quantity` int(11) DEFAULT NULL,
  `user_type_allowed` varchar(100) DEFAULT 'RT',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_product_details_id`),
  KEY `shopping_product_details_index` (`ma_shopping_product_details_id`,`ma_shopping_product_master_id`,`product_status`,`user_type_allowed`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1


-- Shopping Slabwise Distribution

CREATE TABLE `ma_slabwise_distribution_shopping` (
  `ma_slabwise_distribution_shopping_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL DEFAULT '0',
  `inclusive_flag` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Y - Yes, N - No',
  `state_master_id` int(11) NOT NULL DEFAULT '0',
  `ma_user_id` int(11) NOT NULL DEFAULT '0',
  `customer_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `customer_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `rt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `rt_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `rt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
  `dt_share` decimal(12,4) DEFAULT '0.0000',
  `dt_share_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
  `dt_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
  `sd_share` decimal(12,4) DEFAULT '0.0000',
  `sd_share_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed , 2 => percent',
  `sd_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
  `min_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `max_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `settings_flag` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> GENERAL, 2=> GLOBAL',
  PRIMARY KEY (`ma_slabwise_distribution_shopping_id`),
  KEY `shopping_slabwise_index` (`state_master_id`,`ma_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- Shopping Coupon Master

CREATE TABLE `ma_shopping_coupon_master` (
  `ma_shopping_coupon_master_id` int(11) NOT NULL AUTO_INCREMENT,
  `coupon_code` varchar(100) NOT NULL,
  `ma_shopping_product_master_id` int(11) NOT NULL,
  `added_by` int(11) DEFAULT NULL COMMENT 'admin user id',
  `status` enum('U','A') DEFAULT 'U' COMMENT 'U - Unassigned, A - Assigned',
  `orderid` varchar(100) DEFAULT NULL,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_coupon_master_id`),
  KEY `shopping_coupon_index` (`status`,`orderid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- Shopping Shipping Master

CREATE TABLE `ma_shopping_shipping_master` (
  `ma_shopping_shipping_master_id` int(11) NOT NULL AUTO_INCREMENT,
  `orderid` varchar(100) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `pincode` varchar(10) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(255) DEFAULT NULL,
  `shipping_status` enum('I','C','CAN','D','F') DEFAULT 'I' COMMENT 'I - Initated, C - Confirmed, CAN - Cancelled, D - Delivered',
  `virtual` enum('Y','N') DEFAULT 'N' COMMENT 'Y - Yes, N - No',
  `ma_shopping_courier_companies_id` int(11) DEFAULT NULL,
  `shipped_on` timestamp NULL DEFAULT NULL,
  `delivered_to` varchar(99) DEFAULT NULL,
  `awb_no` varchar(98) DEFAULT NULL,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `shipped_upd_by` int(11) DEFAULT NULL,
  `shipped_upd_on` timestamp NULL DEFAULT NULL,
  `delivered_upd_by` int(11) DEFAULT NULL,
  `delivered_upd_on` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ma_shopping_shipping_master_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- Shopping API Logs

CREATE TABLE `ma_shopping_api_log` (
  `ma_shopping_api_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(100) DEFAULT NULL,
  `party_name` varchar(100) DEFAULT NULL,
  `request` text,
  `response` text,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_api_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- Shopping courier companies

CREATE TABLE `ma_shopping_courier_companies` (
  `ma_shopping_courier_companies_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(98) NOT NULL COMMENT 'A-Active,I-Inactive',
  `status` enum('A','I') NOT NULL DEFAULT 'A',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_shopping_courier_companies_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1


-- transaction_type & wallet, insert & update migration queries
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`) VALUES ('200001', 'transaction_type', '34', 'Shopping Cart');
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`) VALUES ('200001', 'transaction_type', '35', 'Shopping Cart Incentive');
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`) VALUES ('200001', 'transaction_type', '36', 'Shopping Cart Delivery');
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`) VALUES ('200003', 'wallet_type', '10', 'wallet_10::200017');
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`) VALUES ('200017', 'wallet_10_transaction_type', '34,35,36', 'Transaction types in wallet 10');
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`) VALUES ('200009', 'allow_flag', '10', 'N');
ALTER TABLE `ma_allow_withdrawal` CHANGE `wallet_type` `wallet_type` ENUM('1','2','3','4','5','6','7','8','9','10') CHARSET latin1 COLLATE latin1_swedish_ci NULL COMMENT '1- TopUp wallet, 2- Collect Money wallet, 3- AEPS wallet, 4- BBPS wallet, 5-Insurance wallet, 6 - incentives wallet, 7 - CMS Wallet, 8 - Gold Wallet, 9 - Fastag Wallet, 10 - Shopping Wallet';
INSERT INTO ma_allow_withdrawal (ma_user_id,priority,transaction_type,wallet_type,allow_flag) SELECT profileid,'10','34,35,36','10','N' FROM ma_user_master WHERE user_type  != 'SD' AND mer_user = 'mer' AND profileid NOT IN (18999) AND addedon < '2021-07-19 23:59:59';
INSERT INTO `ma_channels_master` (`state_master_id`, `ma_user_id`, `channel_name`, `record_status`, `ma_flag`, `settings_flag`) VALUES ('0', '0', 'SHOPPING', 'Y', 'N', '2');
ALTER TABLE `ma_points_ledger_master` CHANGE `transaction_type` `transaction_type` ENUM('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive, 23 - Micro ATM,24 - CMS ,25 - MATM Incentive, 26 - CMS Incentive,27 - Adhoc, 28 - GOLD, 29 - GOLD Incentive, 30 - Withdrawal Charges, 31 - Fasttag issuance, 32 - Fasttag Recharge, 33 - Fasttag Incentives, 34 - Shopping transaction, 35 - Shopping Incentive, 36 - Shopping delivery charges, 37- Cashback Promotion';
ALTER TABLE `ma_cash_ledger_master` CHANGE `transaction_type` `transaction_type` ENUM('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive, 23 - Micro ATM,24 - CMS ,25 - MATM Incentive, 26 - CMS Incentive,27 - Adhoc, 28 - GOLD, 29 - GOLD Incentive, 30 - Withdrawal Charges, 31 - Fasttag issuance, 32 - Fasttag Recharge, 33 - Fasttag Incentives, 34 - Shopping transaction, 35 - Shopping Incentive, 36 - Shopping delivery charges, 37- Cashback Promotion'; 
ALTER TABLE `ma_points_ledger_details` CHANGE `wallet_type` `wallet_type` ENUM('1','2','3','4','5','6','7','8','9','10', '11') CHARSET latin1 COLLATE latin1_swedish_ci NULL COMMENT '1- TopUp wallet, 2- Collect Money wallet, 3- AEPS wallet, 4- BBPS wallet, 5-Insurance wallet, 6 - MATM, 7- CMS Wallet, 8 - GOLD wallet, 9 - Fasttag wallet, 10 - Shopping Wallet, 11 - Cashback Promotion Wallet';
ALTER TABLE `ma_orderwise_taxes` CHANGE `transaction_type` `transaction_type` ENUM('4','8','9','16','18','19','22','25','26','29','33','35') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '35 - Shopping Incentives';
ALTER TABLE `ma_commission_master` CHANGE `ma_commission_type` `ma_commission_type` ENUM('1','2','3','4','5','6','7','8','9','10','18','15','19','22','23','25','26','37','34','35','36') CHARSET latin1 COLLATE latin1_swedish_ci NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive, 23 - Micro ATM,24 - CMS ,25 - MATM Incentive, 26 - CMS Incentive,27 - Adhoc, 28 - GOLD, 29 - GOLD Incentive, 30 - Withdrawal Charges, 31 - Fasttag issuance, 32 - Fasttag Recharge, 33 - Fasttag Incentives, 34 - Shopping transaction, 35 - Shopping Incentive, 36 - Shopping delivery charges, 37- Cashback Promotion'; 
ALTER TABLE `ma_transaction_master` CHANGE `transaction_type` `transaction_type` ENUM('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49','50','51') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive, 23 - Micro ATM,24 - CMS ,25 - MATM Incentive, 26 - CMS Incentive,27 - Adhoc, 28 - GOLD, 29 - GOLD Incentive, 30 - Withdrawal Charges, 31 - Fasttag issuance, 32 - Fasttag Recharge, 33 - Fasttag Incentives, 34 - Shopping transaction, 35 - Shopping Incentive, 36 - Shopping delivery charges, 37- Cashback Promotion'; 

UPDATE `ma_allow_withdrawal` SET transaction_type = '34,35,36' WHERE wallet_type = 10


-- irrelevant alter queries, all create table queries is up-to-date
ALTER TABLE `ma_shopping_order_master` DROP COLUMN `ma_shopping_address_id`, ADD COLUMN `virtual` ENUM('Y','N') NULL COMMENT 'Y - Yes, N - No' AFTER `current_status`;
ALTER TABLE `ma_shopping_order_master` CHANGE `current_status` `current_status` ENUM('I','P','S','REV','F') CHARSET latin1 COLLATE latin1_swedish_ci NULL COMMENT 'I - Initiated, P - Pending, S - Success, REV - Reverse, F - Failed';
ALTER TABLE `ma_shopping_product_details` DROP COLUMN `delivery_charges_per_quantity`, DROP COLUMN `gst`;
ALTER TABLE `ma_shopping_address` ADD COLUMN `city` VARCHAR(255) NULL AFTER `pincode`, ADD COLUMN `state` INT NULL AFTER `city`;
ALTER TABLE `ma_shopping_shipping_master` CHANGE `shipping_status` `shipping_status` ENUM('I','C','CAN','D','F') CHARSET latin1 COLLATE latin1_swedish_ci DEFAULT 'I' NULL COMMENT 'I - Initated, C - Confirmed, CAN - Cancelled, D - Delivered';
ALTER TABLE `ma_shopping_shipping_master` ADD COLUMN `city` VARCHAR(100) NULL AFTER `pincode`, ADD COLUMN `state` VARCHAR(255) NULL AFTER `city`;
ALTER TABLE `ma_shopping_order_master` ADD INDEX `orderid` (`orderid`), ADD INDEX `ma_user_id` (`ma_user_id`), ADD INDEX `product_id` (`product_id`), ADD INDEX `category_id` (`category_id`);
ALTER TABLE `ma_shopping_order_master` ADD COLUMN `ma_transaction_master_id` INT AFTER `orderid`;