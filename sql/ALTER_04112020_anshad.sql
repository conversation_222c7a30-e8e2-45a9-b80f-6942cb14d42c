/* ----------- CREATE BACKUP TABLE -------------- */
CREATE TABLE pan_backup SELECT ma_user_master_id,pan FROM ma_user_master;

CREATE TABLE votar_id_backup SELECT ma_user_master_id,votar_id FROM ma_user_master;

CREATE TABLE retailer_aadhaar_backup SELECT ma_user_master_id,aadhar_number FROM ma_user_master;

CREATE TABLE customer_aadhaar_backup SELECT ma_transaction_master_details_id,customer_aadhaar FROM ma_transaction_master_details;

/* -------------- ALTER AADHAAR COLUMN ------------ */
ALTER TABLE `ma_transaction_master_details` 
CHANGE `customer_aadhaar` `customer_aadhaar` varbinary(400) NULL AFTER `customer_name`;

/* -------------- ENCRYPT AADHAR COLUMN ---------- */
/* FOR DEVELOPMENT */
UPDATE ma_transaction_master_details
SET customer_aadhaar = AES_ENCRYPT(customer_aadhaar, 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7');

/* FOR STAGING */
UPDATE ma_transaction_master_details
SET customer_aadhaar = AES_ENCRYPT(customer_aadhaar, 'c024f2066cdd0a6ac397adcf5bbaad');

/* FOR PRODUCTION */
UPDATE ma_transaction_master_details
SET customer_aadhaar = AES_ENCRYPT(customer_aadhaar, '1784faf7afb6f1e6f855e5a9cd1c0a');



/* -------------- ALTER PAN,AADHAR_NUMBER and VOTAR_ID COLUMNS ---------- */
ALTER TABLE `ma_user_master` 
CHANGE `pan` `pan` varbinary(400) NULL , 
CHANGE `aadhar_number` `aadhar_number` varbinary(400) NULL , 
CHANGE `votar_id` `votar_id` varbinary(400) NULL ;

/* -------------- ENCRYPT PAN, AADHAR_NUMBER and VOTAR_ID COLUMNS ---------- */
/* FOR DEVELOPMENT */
UPDATE ma_user_master
SET pan = AES_ENCRYPT(pan, 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7'), 
aadhar_number = AES_ENCRYPT(aadhar_number, 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7'), 
votar_id = AES_ENCRYPT(votar_id, 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7');

/* FOR STAGING */
UPDATE ma_user_master
SET pan = AES_ENCRYPT(pan, 'c024f2066cdd0a6ac397adcf5bbaad'),
aadhar_number = AES_ENCRYPT(aadhar_number, 'c024f2066cdd0a6ac397adcf5bbaad'),
votar_id = AES_ENCRYPT(votar_id, 'c024f2066cdd0a6ac397adcf5bbaad');

/* FOR PRODUCTION */
UPDATE ma_user_master
SET pan = AES_ENCRYPT(pan, '1784faf7afb6f1e6f855e5a9cd1c0a'),
aadhar_number = AES_ENCRYPT(aadhar_number, '1784faf7afb6f1e6f855e5a9cd1c0a'),
votar_id = AES_ENCRYPT(votar_id, '1784faf7afb6f1e6f855e5a9cd1c0a');

/*-----------------------REVERT BACK PROCEDURE-----------------------------*/
ALTER TABLE `ma_user_master` 
CHANGE `pan` `pan` varbinary(100) NULL;

UPDATE ma_user_master
JOIN pan_backup ON ma_user_master.ma_user_master_id = pan_backup.ma_user_master_id
SET ma_user_master.pan = pan_backup.pan
