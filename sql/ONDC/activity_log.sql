CREATE TABLE ma_ondc_activity_status_log (
	ma_ondc_activity_status_log_id INT UNSIGNED auto_increment NOT NULL,
	ondc_order_id varchar(100) NOT NULL,
    ma_ondc_seller_details_id varchar(100) NOT NULL,
	order_status varchar(50) NULL COMMENT 'PENDING / ACCEPTED / COMPLETED',
	fulfillment_status varchar(50) NULL COMMENT 'SERVICABLE / PENDING / PACKED ',
	source varchar(70) NULL COMMENT 'Confirm API / Update API Retail / Update API Admin - Function Name',
	call_type varchar(20) NULL COMMENT 'Manual, Auto, Callback',
	addedon datetime DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (`ma_ondc_activity_status_log_id`),
	<PERSON><PERSON>Y `order_idx` (`ondc_order_id`),
	KEY `order_statusx` (`order_status`),
	KEY `fulfillment_statusx` (`fulfillment_status`)
)ENGINE=InnoDB DEFAULT CHARSET=latin1;