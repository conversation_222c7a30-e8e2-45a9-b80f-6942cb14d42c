-- 23-090-2022
ALTER TABLE ma_ondc_customer_details DROP COLUMN address1;
ALTER TABLE ma_ondc_customer_details DROP COLUMN address2;
ALTER TABLE ma_ondc_customer_details ADD door varchar(100) NULL after `email_id`;
ALTER TABLE ma_ondc_customer_details ADD building varchar(100) NULL after `door`;
ALTER TABLE ma_ondc_customer_details ADD street varchar(150) NULL after `building`;
ALTER TABLE ma_ondc_customer_details ADD locality varchar(100) NULL after `street`;
ALTER TABLE ma_ondc_customer_details ADD ward varchar(100) after `locality`;
ALTER TABLE ma_ondc_customer_details ADD country varchar(100) after `state`;
ALTER TABLE ma_ondc_customer_details ADD address_status enum('I','A') after `pincode`;

-- 23-09-2022 Added ondc to otp master
ALTER TABLE ma_otp_master MODIFY COLUMN otp_type enum('CO','BBPS','BE','BT','MN','KYC','REF','CW','BA','SP','DR','IU','CMS','GOLD','DBE','POS','RC','CR', 'ONDC') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration, IU - Integration User, CMS - Cash Management System, GOLD - Dvara Smart Gold';

-- 23-090-2022 renamed page_no to bpp_index_order
ALTER TABLE ma_ondc_search_results CHANGE page_no bpp_index_order int(11) NULL;

-- 26-09-2022 userid added
ALTER TABLE ma_ondc_search_results ADD userid int after `ma_user_id`;

-- 30-09-2022
ALTER TABLE ma_ondc_customer_details CHANGE door address1 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_customer_details CHANGE building address2 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_customer_details DROP COLUMN street;
ALTER TABLE ma_ondc_customer_details DROP COLUMN ward;

-- 07-11-2022
CREATE TABLE `ma_ondc_cancelled_products` (
  `ma_ondc_cancelled_products_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(100) NOT NULL,
  `parent_id` varchar(100) NOT NULL,
  `product_id` varchar(255) NOT NULL,
  `quantity` int(11) NOT NULL,
  `cancellation_reason_id` varchar(10) NOT NULL,
  `price` decimal(12,4) NOT NULL,
  `total_price` decimal(12,4) NOT NULL,
  `ma_ondc_product_details_id` int(11) NOT NULL,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_ondc_cancelled_products_id`),
  KEY `ma_ondc_cancelled_products_order_id_IDX` (`order_id`) USING BTREE,
  KEY `ma_ondc_cancelled_products_parent_id_IDX` (`parent_id`) USING BTREE,
  KEY `ma_ondc_cancelled_products_ma_ondc_product_details_id_IDX` (`ma_ondc_product_details_id`) USING BTREE,
  KEY `ma_ondc_cancelled_products_product_id_IDX` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1

CREATE TABLE `ma_ondc_cancellation_reasons` (
  `ma_ondc_cancellation_reasons` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) NOT NULL,
  `reason` varchar(150) NOT NULL,
  `record_status` enum('A','I') NOT NULL DEFAULT 'A' COMMENT 'A- Active, I - Inactive',
  `addedon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_ondc_cancellation_reasons`),
  KEY `idx_ondc_cancellation_reasons_status` (`record_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=latin1

-- 10-11-2022 cancelled_quantity and returned_quantity added.
ALTER TABLE ma_ondc_product_details ADD cancelled_quantity int DEFAULT 0 NOT NULL;
ALTER TABLE ma_ondc_product_details CHANGE cancelled_quantity cancelled_quantity int DEFAULT 0 NOT NULL AFTER product_id;
ALTER TABLE ma_ondc_product_details ADD returned_quantity int DEFAULT 0 NOT NULL;
ALTER TABLE ma_ondc_product_details CHANGE returned_quantity returned_quantity int DEFAULT 0 NOT NULL AFTER product_quantity;
ALTER TABLE ma_ondc_cancellation_reasons CHANGE ma_ondc_cancellation_reasons ma_ondc_cancellation_reasons_id int(11) auto_increment NOT NULL;
ALTER TABLE ma_ondc_cancelled_products ADD cancelled_by varchar(100) NOT NULL;
ALTER TABLE ma_ondc_cancelled_products CHANGE cancelled_by cancelled_by varchar(100) NOT NULL AFTER ma_user_id;
ALTER TABLE ma_ondc_cancelled_products ADD ma_user_id int NULL;
ALTER TABLE ma_ondc_cancelled_products CHANGE ma_user_id ma_user_id int NULL AFTER ma_ondc_cancelled_products_id;
ALTER TABLE ma_otp_master MODIFY COLUMN otp_type enum('CO','BBPS','BE','BT','MN','KYC','REF','CW','BA','SP','DR','IU','CMS','GOLD','DBE','POS','RC','CR','ONDC','ONDCC') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration, IU - Integration User, CMS - Cash Management System, GOLD - Dvara Smart Gold, ONDC - Open Network for Digital Commerce';

-- 13-11-2022
RENAME TABLE ma_ondc_cancelled_products TO ma_ondc_refunded_products;
ALTER TABLE ma_ondc_refunded_products CHANGE ma_ondc_cancelled_products_id ma_ondc_refunded_products_id int(11) auto_increment NOT NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE cancelled_by refunded_by varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE cancellation_reason_id reason_id varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL;
ALTER TABLE ma_ondc_refunded_products ADD userid int(11) NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE userid userid int(11) NULL AFTER ma_ondc_refunded_products_id;
ALTER TABLE ma_ondc_refunded_products ADD refund_type varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE refund_type refund_type varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL AFTER image1;
ALTER TABLE ma_ondc_refunded_products ADD image1 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE image1 image1 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image2;
ALTER TABLE ma_ondc_refunded_products ADD image2 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE image2 image2 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image3;
ALTER TABLE ma_ondc_refunded_products ADD image3 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE image3 image3 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image4;
ALTER TABLE ma_ondc_refunded_products ADD image4 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE image4 image4 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image5;
ALTER TABLE ma_ondc_refunded_products ADD image5 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE image5 image5 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER ma_ondc_product_details_id;
ALTER TABLE ma_ondc_refunded_products CHANGE addedon addedon datetime DEFAULT CURRENT_TIMESTAMP NULL AFTER updatedon;
ALTER TABLE ma_ondc_refunded_products CHANGE updatedon updatedon datetime DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NULL FIRST;
ALTER TABLE ma_ondc_refunded_products CHANGE image1 image1 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER refund_type;
ALTER TABLE ma_ondc_refunded_products CHANGE image5 image5 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image4;
ALTER TABLE ma_ondc_refunded_products CHANGE refund_type refund_type varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL AFTER ma_ondc_product_details_id;
ALTER TABLE ma_ondc_refunded_products CHANGE image2 image2 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image1;
ALTER TABLE ma_ondc_refunded_products CHANGE image3 image3 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image2;
ALTER TABLE ma_ondc_refunded_products CHANGE image4 image4 varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL AFTER image3;
ALTER TABLE ma_ondc_refunded_products CHANGE updatedon updatedon datetime DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NULL AFTER addedon;
ALTER TABLE ma_ondc_refund_reasons ADD refund_type varchar(100) NOT NULL;
ALTER TABLE ma_ondc_refund_reasons CHANGE refund_type refund_type varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL AFTER record_status;

-- 17-11-2022
ALTER TABLE ma_ondc_product_details ADD refunded_amount varchar(100) DEFAULT 0 NOT NULL;
ALTER TABLE ma_ondc_product_details CHANGE refunded_amount refunded_amount varchar(100) DEFAULT 0 NOT NULL AFTER cancellable;
ALTER TABLE ma_ondc_refunded_products CHANGE order_id aggregator_order_id varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL;
ALTER TABLE ma_ondc_refunded_products ADD refunded_amount varchar(100) DEFAULT 0 NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE refunded_amount refunded_amount varchar(100) DEFAULT 0 NULL AFTER total_price;


-- 18-01-2022
ALTER TABLE ma_ondc_refunded_products ADD refund_status varchar(100) DEFAULT 'PENDING' NULL;
ALTER TABLE ma_ondc_refunded_products CHANGE refund_status refund_status varchar(100) DEFAULT 'PENDING' NULL AFTER ma_ondc_product_details_id;


-- 23-01-2023
ALTER TABLE ma_ondc_seller_details ADD seller_support_information varchar(100) NULL;
