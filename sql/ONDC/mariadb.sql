CREATE TABLE `ma_ondc_search_results` (
  `sessionid` varchar(100) DEFAULT NULL,
  `addedon` timestamp,
  `search_req_id` varchar(255) DEFAULT NULL,
  `search_txn_id` varchar(100) DEFAULT NULL,
  `search_msg_id` varchar(100) DEFAULT NULL,
  `search_bpp_id` varchar(100) DEFAULT NULL,
  `search_unq_id` varchar(100) DEFAULT NULL,
  `search_subs_id` varchar(100) DEFAULT NULL,
  `prov_id` varchar(100) DEFAULT NULL,
  `prov_name` varchar(200) DEFAULT NULL,
  `prov_symbol` varchar(500) DEFAULT NULL,
  `prov_short_desc` text DEFAULT NULL,
  `prov_long_desc` text DEFAULT NULL,
  `prov_image` text DEFAULT NULL,
  `prov_ondc_fssai_license_no` varchar(100) DEFAULT NULL,
  `prov_ttl` varchar(50) DEFAULT NULL,
  `prov_loc_id` varchar(100) DEFAULT NULL,
  `prov_loc_gps` varchar(100) DEFAULT NULL,
  `prov_loc_addr_door` varchar(20) DEFAULT NULL,
  `prov_loc_addr_name` varchar(200) DEFAULT NULL,
  `prov_loc_addr_building` varchar(200) DEFAULT NULL,
  `prov_loc_addr_street` varchar(200) DEFAULT NULL,
  `prov_loc_addr_locality` varchar(100) DEFAULT NULL,
  `prov_loc_addr_ward` varchar(100) DEFAULT NULL,
  `prov_loc_addr_city` varchar(100) DEFAULT NULL,
  `prov_loc_addr_state` varchar(100) DEFAULT NULL,
  `prov_loc_addr_country` varchar(100) DEFAULT NULL,
  `prov_loc_addr_area_code` int(11) DEFAULT NULL,
  `prov_expiry` datetime DEFAULT NULL,
  `prov_rateable` tinyint(4) DEFAULT NULL,
  `prov_items_id` varchar(100) DEFAULT NULL,
  `prov_items_desc_name` varchar(1000) DEFAULT NULL,
  `prov_items_desc_symbol` varchar(500) DEFAULT NULL,
  `prov_items_desc_short_desc` text DEFAULT NULL,
  `prov_items_desc_long_desc` text DEFAULT NULL,
  `prov_items_desc_images` longtext,
  `prov_items_price_curr` varchar(10) DEFAULT NULL,
  `prov_items_price_value` decimal(10,4) DEFAULT NULL,
  `store_distance` decimal(10,2) DEFAULT NULL,
  `prov_items_price_est_value` decimal(10,4) DEFAULT NULL,
  `prov_items_price_comp_val` decimal(10,4) DEFAULT NULL,
  `prov_items_price_list_val` decimal(10,4) DEFAULT NULL,
  `prov_items_price_off_val` decimal(10,4) DEFAULT NULL,
  `prov_items_price_min_val` decimal(10,4) DEFAULT NULL,
  `prov_items_price_max_val` decimal(10,4) DEFAULT NULL,
  `prov_items_cat_id` varchar(100) DEFAULT NULL,
  `prov_items_ful_id` varchar(100) DEFAULT NULL,
  `prov_items_loc_id` varchar(100) DEFAULT NULL,
  `prov_items_matched` tinyint(4) DEFAULT NULL,
  `prov_items_related` tinyint(4) DEFAULT NULL,
  `prov_items_rateable` tinyint(4) DEFAULT NULL,
  `prov_items_recomm` tinyint(4) DEFAULT NULL,
  `prov_items_ondc_return` tinyint(4) DEFAULT NULL,
  `prov_items_ondc_cancel` tinyint(4) DEFAULT NULL,
  `prov_items_ondc_return_window` varchar(20) DEFAULT NULL,
  `prov_items_ondc_min_value` varchar(20) DEFAULT NULL,
  `prov_items_ondc_max_value` varchar(20) DEFAULT NULL,
  `prov_items_ondc_sell_pick_return` tinyint(4) DEFAULT NULL,
  `prov_items_ondc_time_to_ship` varchar(10) DEFAULT NULL,
  `prov_items_ondc_cod` tinyint(4) DEFAULT NULL,
  `prov_items_ondc_customer_care` varchar(500) DEFAULT NULL,
  `prov_items_ondc_statutory_req_desc` text DEFAULT NULL,
  `prov_items_ondc_statutory_req_maf_name` varchar(100) DEFAULT NULL,
  `prov_items_ondc_statutory_req_addr` TEXT,
  `prov_items_ondc_statutory_req_generic_name` varchar(1000) DEFAULT NULL,
  `prov_items_ondc_statutory_req_net_qua` varchar(50) DEFAULT NULL,
  `prov_items_ondc_statutory_req_maf_year` varchar(20) DEFAULT NULL,
  `prov_items_ondc_statutory_req_origin_country` varchar(45) DEFAULT NULL,
  `prov_items_ondc_statutory_req_nutri_info` varchar(1000) DEFAULT NULL,
  `prov_items_ondc_statutory_req_add_info` varchar(1000) DEFAULT NULL,
  `prov_items_ondc_statutory_req_net_quantity_display` varchar(10) DEFAULT NULL,
  `prov_items_ondc_statutory_req_tag_veg` varchar(500) DEFAULT NULL,
  `prov_items_ondc_statutory_req_tag_non_veg` varchar(500) DEFAULT NULL,
  `prov_items_ondc_statutory_req_brand_own_fssai_lic_no` varchar(100) DEFAULT NULL,
  `prov_items_ondc_statutory_req_other_fssai_lic_no` varchar(100) DEFAULT NULL,
  `prov_items_ondc_statutory_req_importer_fssai_lic_no` varchar(100) DEFAULT NULL,
  `category_names` varchar(500) DEFAULT NULL,
  `fulfilment_id` varchar(100) DEFAULT NULL,
  `fulfilment_type` text DEFAULT NULL,
  `fulfilment_tracking` tinyint(4) DEFAULT NULL,
  `fulfilment_start_loc_id` varchar(100) DEFAULT NULL,
  `fulfilment_start_loc_desc_name` varchar(100) DEFAULT NULL,
  `fulfilment_start_loc_gps` varchar(100) DEFAULT NULL,
  `fulfilment_start_contact_phone` varchar(100) DEFAULT NULL,
  `fulfilment_start_contact_email` varchar(250) DEFAULT NULL,
  `ma_user_id` int(11) DEFAULT NULL,
  `userid` int(11) DEFAULT NULL,
  `bpp_index_order` int(11) DEFAULT NULL
);

ALTER TABLE ma_ondc_search_results ADD FULLTEXT(prov_items_desc_name);
ALTER TABLE ma_ondc_search_results ADD FULLTEXT(prov_name);
ALTER TABLE ma_ondc_search_results ADD FULLTEXT(prov_items_cat_id);