CREATE TABLE `ma_aml_threshold_snapshot` (
  `snapshot_id` int(11) NOT NULL AUTO_INCREMENT,
  `snapshot_date` date NOT NULL,
  `type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 - Customer, 2 - Beneficiary ',
  `type_value` varchar(50) NOT NULL COMMENT 'Customer uic/Beneficiary id',
  `amount` double(12,2) NOT NULL,
  `addedon` timestamp NULL DEFAULT current_timestamp(),
  `updatedon` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`snapshot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `ma_aml_threshold_snapshot_details` (
  `snapshot_details_id` int(11) NOT NULL AUTO_INCREMENT,
  `snapshot_date` date NOT NULL,
  `uic` varchar(50) NOT NULL,
  `beneficiary_id` int(11) NOT NULL,
  `amount` double(12,2) NOT NULL,
  `addedon` timestamp NULL DEFAULT current_timestamp(),
  `updatedon` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`snapshot_details_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `ma_aml_highlighted_bene` (
  `ma_aml_highlighted_bene_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_user_id` int(11) NOT NULL,
  `uic` varchar(15) NOT NULL,
  `beneficiary_name` varchar(50) NULL,
  `account_number` varchar(25) NULL,
  `mobile_number` varchar(15) NULL,
  `ifsc_code` varchar(15) NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);