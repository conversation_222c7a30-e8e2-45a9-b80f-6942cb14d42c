CREATE TABLE `webform_token_log` (
  `webform_token_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(1000) DEFAULT NULL,
  `session_counter` varchar(10) DEFAULT NULL,
  `form_type` varchar(10) DEFAULT NULL,
  `expire_time` varchar(20) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`webform_token_log_id`),
  KEY `expire_time_idx` (`expire_time`),
  KEY `form_type_idx` (`form_type`),
  KEY `ma_user_id_idx` (`ma_user_id`),
  KEY `token_idx` (`token`),
  KEY `user_id_idx` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='This table is used to maintain the session ids for all form types'