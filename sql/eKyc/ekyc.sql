-- ma_ekyc_bank_master;


CREATE TABLE `ma_ekyc_bank_master` (
`ma_ekyc_bank_master_id` INT NOT NULL AUTO_INCREMENT,
`bank_name` VARCHAR(255),
`bank_logo` VARCHAR(255),
`added_by` INT,
`bank_status` ENUM('A','I') COMMENT 'A - Active, I - Inactive',
`biometric_kyc` ENUM('Y','N') DEFAULT 'N' COMMENT 'Y - Yes, N - No',
`updated_by` INT,
`addedon` DATETIME DEFAULT CURRENT_TIMESTAMP(),
`updatedon` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (`ma_ekyc_bank_master_id`)
);

ALTER TABLE `ma_ekyc_bank_master` ADD COLUMN `biometric_kyc` ENUM('Y','N') DEFAULT 'N' NULL COMMENT 'Y - Yes, N - No' AFTER `bank_status`

--@Block ma_ekyc_registraition


CREATE TABLE `ma_ekyc_registraition` (
  `ma_ekyc_registraition_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_ekyc_bank_master_id` int(11) DEFAULT NULL,
  `ma_user_id` int(11) DEFAULT NULL,
  `ip_added_from` varchar(15) DEFAULT NULL,
  `orderid` varchar(50) DEFAULT NULL,
  `ekyc_status` enum('I','P','S','F') DEFAULT NULL COMMENT 'I - Initiated, P - Pending, S - Success, F - Failure',
  `biometric_status` ENUM('I','P','S','F') DEFAULT NULL COMMENT 'I - Initiated, P - Pending, S - Success, F - Failure',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_ekyc_registraition_id`)
);




ALTER TABLE `ma_ekyc_registraition` CHANGE `ma_ekyc_bank_master` `ma_ekyc_bank_master_id` INT(11) NULL; 
ALTER TABLE `ma_ekyc_registraition` ADD COLUMN `orderid` VARCHAR(50) NULL AFTER `ip_added_from`, ADD COLUMN `ekyc_status` ENUM('I','P','S','F') NULL COMMENT 'I - Initiated, P - Pending, S - Success, F - Failure' AFTER `orderid`;
ALTER TABLE `ma_ekyc_registraition` ADD COLUMN `biometric_status` ENUM('I','P','S','F') NULL COMMENT 'I - Initiated, P - Pending, S - Success, F - Failure' AFTER `ekyc_status`;

--@BLOCK ma_ekyc_api_log;


CREATE TABLE `ma_ekyc_api_log` (
  `ma_ekyc_api_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(100) DEFAULT NULL,
  `party_name` varchar(100) DEFAULT NULL,
  `request` text,
  `response` text,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_ekyc_api_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1

--@Block ekyc status query
SELECT bank_name, bank_logo, bank_status FROM ma_ekyc_bank_master WHERE bank_status = 'A' AND ma_ekyc_bank_master_id NOT IN (SELECT ma_ekyc_bank_master_id FROM ma_ekyc_registraition WHERE ma_user_id = 28479)

--@Block ekyc form data query
SELECT aadhar_number, CAST(AES_DECRYPT(pan,'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7') AS CHAR) AS pan, mobile_id AS phone FROM ma_user_master WHERE profileid = 28479

--@Block ekyc bank details for send otp and resend otp
Select bank_name, bank_status from ma_ekyc_bank_master where ma_ekyc_bank_master_id = 1 and bank_status = 'A'

--@BLOCK ekyc update existing initiated from same ma_user_id
UPDATE
  ma_ekyc_registraition
SET
  ekyc_status = 'F'
WHERE
  ma_ekyc_bank_master_id = 1 AND
  ma_user_id = 28479 AND
  ekyc_status = 'I'

--@BLOCK ekyc update initiated to pending
UPDATE
  ma_ekyc_registraition
SET
  ekyc_status = 'P'
WHERE
  ma_ekyc_bank_master_id = 1 AND
  ma_user_id = 28479 AND
  orderid = 'MAEKYC95421621944804'

--@BLOCK ekyc update pending to success
UPDATE
  ma_ekyc_registraition
SET
  ekyc_status = 'P'
WHERE
  ma_ekyc_bank_master_id = 1 AND
  ma_user_id = 28479 AND
  orderid = 'MAEKYC95421621944804'

--@BLOCK ekyc registeration status

SELECT
  ma_user_id,
  orderid,
  ekyc_status
FROM
  ma_ekyc_registraition
WHERE
  ma_ekyc_bank_master_id = 1 AND
  ma_user_id = 28479 AND
  orderid = 'MAEKYC95421621944804'