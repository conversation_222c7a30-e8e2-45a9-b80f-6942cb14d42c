CREATE TABLE `ma_ipn_pending_request` (
  `ma_ipn_pending_request_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ma_ipn_request_id` int(10) NOT NULL,
  `request` varchar(1000) DEFAULT NULL,
  `aggregator_order_id` varchar(30) DEFAULT NULL,
  `response` varchar(70) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `airpayid` int(10) DEFAULT NULL,
  `transaction_type_flag` enum('1','2','3') DEFAULT NULL COMMENT '1 => RetailApp Transactions, 2 => User Generated Transactions, 3 => POS Transactions',
  `pending_status` enum('0','1') NOT NULL DEFAULT '0' COMMENT '0 => Pending, 1 => Completed',
  PRIMARY KEY (`ma_ipn_pending_request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


ALTER TABLE `ma_ipn_pending_request`
ADD INDEX `ma_ipn_request_id` (`ma_ipn_request_id`),
ADD INDEX `aggregator_order_id` (`aggregator_order_id`);


ALTER TABLE `ma_ipn_pending_request`
ADD `amount` decimal(12,4) NULL,
ADD `terminalid` int(15) NULL COMMENT 'Terminal Id for POS' AFTER `amount`,
ADD `ma_user_id` int(10) NULL AFTER `terminalid`;


ALTER TABLE `ma_ipn_request`
ADD `amount` decimal(12,4) NULL,
ADD `terminalid` int(15) NULL COMMENT 'Terminal Id for POS' AFTER `amount`,
ADD `ma_user_id` int(10) NULL AFTER `terminalid`;