-- Insert data into credit score range master table
INSERT INTO `ma_credit_score_range_master` (`min_score`, `max_score`, `max_limit`) VALUES
(0, 649, 0),
(650, 650, 100000),
(651, 660, 125000),
(661, 670, 150000),
(671, 680, 175000),
(681, 690, 200000),
(691, 700, 225000),
(701, 710, 250000),
(711, 720, 275000),
(721, 730, 300000),
(731, 740, 325000),
(741, 750, 350000),
(751, 760, 375000),
(761, 770, 400000),
(771, 780, 425000),
(781, 790, 450000),
(791, 800, 475000),
(801, 900, 500000);
 
 