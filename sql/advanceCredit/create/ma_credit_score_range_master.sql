-- Credit score range master table

CREATE TABLE `ma_credit_score_range_master` (
  `ma_credit_score_band_id` int NOT NULL AUTO_INCREMENT,
  `min_score` int NOT NULL,
  `max_score` int NOT NULL,
  `max_limit` decimal(12,2) NOT NULL,
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
  `added_by` int NULL,
  `updated_by` int NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_credit_score_band_id`)
  KEY `idx_score_range` (`min_score`,`max_score`),
  KEY `idx_record_status` (`record_status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Credit Score Range Master';