ALTER TABLE ma_geo_location ADD device_name varchar(100) NULL COMMENT 'aeps device name';
ALTER TABLE ma_geo_location ADD device_model_no varchar(100) NULL COMMENT 'aeps device model no';
ALTER TABLE ma_geo_location ADD device_serial_no varchar(100) NULL COMMENT 'aeps device serail no';
ALTER TABLE ma_geo_location ADD bank_name varchar(100) NULL COMMENT 'aeps bank name sent for transaction';

CREATE INDEX ma_geo_location_device_name_IDX USING BTREE ON ma_geo_location (device_name);
CREATE INDEX ma_geo_location_bank_name_IDX USING BTREE ON ma_geo_location (bank_name);
