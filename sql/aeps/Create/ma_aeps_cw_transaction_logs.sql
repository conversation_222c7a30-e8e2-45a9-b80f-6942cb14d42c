CREATE TABLE `ma_aeps_cw_transaction_logs` (
  `ma_aeps_cw_transaction_logs_id` int unsigned NOT NULL AUTO_INCREMENT,
  `ma_user_id` int unsigned NOT NULL COMMENT 'merchant profile id',
  `order_id` varchar(50) DEFAULT NULL COMMENT 'Order id',
  `merchant_txn_id` varchar(75) DEFAULT NULL,
  `sent_bank_id` varchar(50) DEFAULT NULL,
  `received_bank_id` varchar(50) DEFAULT NULL,
  `request_type` varchar(50) DEFAULT NULL COMMENT 'CW',
  `two_factor_auth_status` varchar(10) DEFAULT NULL COMMENT 'S - Success, F - Fail',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_aeps_cw_transaction_logs_id`),
  KEY `inx_ma_user_id` (`ma_user_id`),
  <PERSON>EY `inx_two_factor_auth_status` (`two_factor_auth_status`),
  <PERSON><PERSON>Y `inx_order_id` (`order_id`),
  <PERSON>EY `inx_addedon` (`addedon`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='This table is used to store 2FA AEPS Transaction order id ';