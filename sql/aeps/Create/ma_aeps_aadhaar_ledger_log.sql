CREATE TABLE `ma_aeps_aadhaar_ledger_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `ma_user_id` int unsigned DEFAULT NULL,
  `userid` int unsigned DEFAULT NULL,
  `order_id` varchar(50) DEFAULT NULL,
  `amount` decimal(12,4) DEFAULT NULL,
  `ledger_status` varchar(10) NOT NULL DEFAULT 'P' COMMENT 'P - Pending, S - Success, F - Fail Transaction, NA - Transaction Not found',
  `incentive_status` varchar(10) NOT NULL DEFAULT 'P' COMMENT 'P - Pending, S - Success, F - Fail, NA - Not Found ',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remarks` varchar(200) DEFAULT NULL,
  `no_of_attempts` int unsigned DEFAULT '0',
  `cron_status` varchar(10) NOT NULL DEFAULT 'P' COMMENT 'P - Pending, V - Verified',
  `transaction_type` varchar(30) DEFAULT NULL,
  `request_type` varchar(30) NOT NULL DEFAULT 'orderverify',
  PRIMARY KEY (`id`),
  KEY `idx_added_on` (`addedon`),
  KEY `idx_ledger_status` (`ledger_status`),
  KEY `idx_incentive_status` (`incentive_status`),
  KEY `ma_ipn_order_verify_log_cron_status_IDX` (`cron_status`) USING BTREE,
  KEY `ma_ipn_order_verify_log_transaction_type_IDX` (`transaction_type`) USING BTREE,
  KEY `idx_remarks` (`remarks`),
) ENGINE=InnoDB DEFAULT CHARSET=latin1