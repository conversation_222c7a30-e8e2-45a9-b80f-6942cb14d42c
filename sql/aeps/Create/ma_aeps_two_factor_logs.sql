CREATE TABLE `ma_aeps_two_factor_logs` (
  `ma_aeps_two_factor_logs_id` int unsigned NOT NULL AUTO_INCREMENT,
  `ma_user_id` int unsigned NOT NULL COMMENT 'merchant profile id',
  `api_response` varchar(100) DEFAULT NULL COMMENT 'MA Api response',
  `request_type` varchar(50) DEFAULT NULL COMMENT 'Registration, Authorisation',
  `bank_code` varchar(50) DEFAULT NULL COMMENT 'priority bank - response we get from MA API',
  `auth_status` varchar(50) DEFAULT NULL COMMENT 'Success, Fail',
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_aeps_two_factor_logs_id`),
  KEY `inx_ma_user_id` (`ma_user_id`),
  KEY `inx_request_type` (`request_type`),
  KEY `inx_addedon` (`addedon`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='This table is used to store 2FA Authorisation & Registration of Merchants for AEPS';