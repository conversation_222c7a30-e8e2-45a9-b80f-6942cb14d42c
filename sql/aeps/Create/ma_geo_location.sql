CREATE TABLE `ma_geo_location` (
  `ma_geo_location_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(10) unsigned NOT NULL COMMENT 'merchant profile id',
  `user_id` int(10) unsigned DEFAULT NULL COMMENT 'merchant user id',
  `order_id` varchar(100) NOT NULL COMMENT 'aggregator order id, reference no',
  `request_type` varchar(20) NOT NULL COMMENT 'aeps, dmt, withdrawal, cashin',
  `amount` decimal(12,4) DEFAULT NULL COMMENT 'amount - optional field',
  `latitude` varchar(50) DEFAULT NULL COMMENT 'latitude of merchant',
  `longtitude` varchar(50) DEFAULT NULL COMMENT 'longitude of merchant',
  `radius` varchar(50) DEFAULT NULL COMMENT 'distance  lat, long in kilometer',
  `login_ip` varchar(50) DEFAULT NULL COMMENT 'login ip of merchant',
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_geo_location_id`),
  KEY `inx_ma_user_id` (`ma_user_id`),
  KEY `inx_request_type` (`request_type`),
  KEY `inx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='This table is used to store latitude & longitude for aeps, dmt, cashin & withdrawal modules'