CREATE TABLE `ma_aeps_merchant_auth_token` (
  `ma_aeps_merchant_auth_token_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'This is a primary key of the table',
  `ma_user_id` int NOT NULL ,
  `expiry_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `authtoken` varchar(255) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_aeps_merchant_auth_token_id`),
  <PERSON><PERSON><PERSON> `user_id_idx` (`ma_user_id`),
  KEY `expiry_time_idx` (`expiry_time`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='This table is used to store Aeps Merchant auth token';