ALTER TABLE `ma_bank_on_boarding_details` CHANGE `entity_type` `entity_type` ENUM('<PERSON><PERSON><PERSON>','BENEFICIARY','AGENT','REFUND','PREVALIDATE','DEL_BENEFICIARY','CHECK_BENE_EXISTS','PRE_CUSTLOGIN','AUTO_ADD_BENE','IS_BENEMOBILE_MANDATORY','AUTO_ADD_REMITTER','IS_KYC_AVAILABLE','SYNC_BENEFICIARY', 'REMITTER_NAME_VALIDATION','AUTO_SYNC_DELETE_BENEFICIARIES') COLLATE 'latin1_general_ci' NOT NULL AFTER `ma_bank_on_boarding_id`;