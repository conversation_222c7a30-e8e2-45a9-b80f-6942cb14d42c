ALTER TABLE `ma_transaction_master_details`
CHANGE `customer_aadhaar` `customer_aadhaar` varchar(50) NULL ;

/* -------------- ALTER AADHAAR COLUMN ------------ */
ALTER TABLE `ma_transaction_master_details` 
CHANGE `customer_aadhaar` `customer_aadhaar` varbinary(400) NULL AFTER `customer_name`;

/* -------------- DECRYPT AADHAR COLUMN ---------- */
/* FOR DEVELOPMENT */
UPDATE ma_transaction_master_details
SET customer_aadhaar = AES_DECRYPT(customer_aadhaar, 'ShVmYq3t6w9z$C&FJ@NcRfTjWnZr4u7');

/* FOR STAGING */
UPDATE ma_transaction_master_details
SET customer_aadhaar = AES_DECRYPT(customer_aadhaar, 'c024f2066cdd0a6ac397adcf5bbaad');

/* FOR PRODUCTION */
UPDATE ma_transaction_master_details
SET customer_aadhaar = AES_DECRYPT(customer_aadhaar, '1784faf7afb6f1e6f855e5a9cd1c0a');