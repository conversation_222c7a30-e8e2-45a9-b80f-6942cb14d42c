ALTER TABLE `ma_billpay_transaction_master`
ADD `payment_status` enum('I','P','S','F') NOT NULL COMMENT 'direct index payment statuss' AFTER `transaction_status`,
ADD `payment_response` json NOT NULL COMMENT 'payment response json' AFTER `payment_status`,
ADD `bank_response` json NOT NULL COMMENT 'third party api response' AFTER `payment_response`,
ADD `makepayment_request_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'makepayment request reference table' AFTER `invoice_id`;

ALTER TABLE `ma_billpay_transaction_master`
ADD INDEX `makepayment_request_id` (`makepayment_request_id`);