CREATE TABLE `ma_refund_transfers`(
    `ma_refund_transfer_id` INT NOT NULL AUTO_INCREMENT,
    `request_number` VARCHAR(50) NOT NULL,
    `cron_status` VARCHAR(10) NOT NULL DEFAULT 'I',
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(`ma_refund_transfer_id`),
    INDEX `idx_request_number`(`request_number`)
) ENGINE = INNODB;