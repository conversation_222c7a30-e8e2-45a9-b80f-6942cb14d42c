CREATE TABLE `ma_remitter_name_verification` (
    `id` INT(10) NOT NULL AUTO_INCREMENT,
    `ma_user_id` INT(10) NULL DEFAULT NULL,
    `userid` INT(10) NULL DEFAULT NULL,
    `first_name` VA<PERSON>HA<PERSON>(50) NULL DEFAULT NULL,
    `last_name` VARCHAR(50) NULL DEFAULT NULL,
    `verified_flag` BOOLEAN NOT NULL DEFAULT FALSE,
    `ma_bank_on_boarding_id` INT(10) NULL DEFAULT NULL,
    `bank_request` LONGTEXT NULL DEFAULT NULL,
    `bank_response` LONGTEXT NULL DEFAULT NULL,
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;