INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES
(NULL,	11,	'SENDER',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'BENEFICIARY',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'REFUND',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'DEL_BENEFICIARY',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'PREVALIDATE',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'CHECK_BENE_EXISTS',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'AUTO_ADD_BENE',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'IS_BENEMOBILE_MANDATORY',	'NO',	'{}',	now(),	now()),
(NULL,	11,	'AUTO_ADD_REMITTER',	'NO',	'{}',	now(),	now()),
(NULL,  11, 'PRE_CUSTLOGIN',    'YES',    '[{\"field\": \"firstName\",\"label\": \"First Name\", \"rules\": {\"max\": 100, \"min\": 2, \"regex\": \"^[a-zA-Z. \']{1,100}$\", \"required\": true}, \"inputype\": \"text\",\"value\": \"\"}, {\"field\": \"lastName\", \"label\": \"Last Name\", \"rules\": {\"max\": 100, \"min\": 2, \"regex\": \"^[a-zA-Z. \']{1,100}$\", \"required\": true}, \"inputype\": \"text\",\"value\": \"\"}]',    now(),    now()),
(NULL,  11, 'AUTO_SYNC_DELETE_BENEFICIARIES',    'NO',    '{}',    now(),    now()),
(NULL,  11, 'IS_KYC_AVAILABLE',    'NO',    '{}',    now(),    now()),
(NULL,  11, 'SYNC_BENEFICIARY',    'NO',    '{}',    now(),    now()),
(NULL,  11, 'REMITTER_NAME_VALIDATION',    'NO',    '{}',    now(),    now()),
(NULL,  11, 'REMITTER_NAME_CORRECTION',    'YES',    '{}',    now(),    now());


INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES 
(NULL,  5, 'INTERBANK_TRANSACTION',    'YES',    '{}',    now(),    now()),
(NULL,  9, 'INTERBANK_TRANSACTION',    'YES',    '{}',    now(),    now()),
(NULL,  10, 'INTERBANK_TRANSACTION',    'YES',    '{}',    now(),    now()),
(NULL,  11, 'INTERBANK_TRANSACTION',    'NO',    '{}',    now(),    now());
