CREATE TABLE `ma_chargeback_request` (
  `ma_chargeback_request_id` int unsigned NOT NULL AUTO_INCREMENT,
  `ma_user_id` int DEFAULT NULL,
  `airpayid` int DEFAULT NULL,
  `aggregator_order_id` varchar(30) DEFAULT NULL,
  `transaction_type_flag` varchar(10) DEFAULT NULL COMMENT '10 => UPI Transactions, 20 => POSTransactions, 23 => HATM Transactions',
  `ipn_transaction_type` varchar(20) DEFAULT NULL COMMENT 'CHAREGEBACK, REVERSAL, REFUNDED',
  `chargeback_status` varchar(10) DEFAULT NULL COMMENT 'P - Pending, S - Success, N - Transaction Not Found, LNA - Ledger not available',
  `amount` decimal(12,4) DEFAULT NULL,
  `request_type` varchar(10) DEFAULT NULL COMMENT 'IPN - ipn request, RECON - recon request',
  `terminalid` int DEFAULT NULL COMMENT 'Terminal Id for POS',
  `request` varchar(1000) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `response` varchar(255) DEFAULT NULL,
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY `aggregator_order_idx` (`aggregator_order_id`),
  KEY `airpayidx` (`airpayid`),
  KEY `ma_user_idx` (`ma_user_id`),
  KEY `transaction_type_flagx` (`transaction_type_flag`),
  KEY `ipn_transaction_typex` (`ipn_transaction_type`),
  KEY `chargeback_statusx` (`chargeback_status`),
  KEY `request_typex` (`request_type`),
  PRIMARY KEY (`ma_chargeback_request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;