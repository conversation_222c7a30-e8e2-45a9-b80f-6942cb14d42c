UPDATE ma_bank_on_boarding_details SET `fields_required_json` = '[
    {
        "kycType" : "BIO",
        "fields" : [],
        "type" : "modal",
        "modalMessage" : ""
    },
    {
        "kycType" : "PAN",
        "fields" : [{
            "field": "pannumber",
            "label": "PAN Number",
            "rules": {
                "max": 10,
                "min": 10, 
                "regex": "^([A-Z]){5}([0-9]){4}([A-Z]){1}$",
                "required": true
            },
            "inputype": "text"
        }]
    }
]' WHERE `ma_bank_on_boarding_id` =  5 AND `entity_type` = 'IS_KYC_AVAILABLE';