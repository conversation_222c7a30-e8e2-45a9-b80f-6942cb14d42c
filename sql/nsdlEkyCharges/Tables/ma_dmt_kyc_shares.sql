CREATE TABLE `ma_dmt_kyc_shares` (
  `ma_dmt_kyc_shares_id` int NOT NULL AUTO_INCREMENT,
  `state_master_id` int NOT NULL DEFAULT '0',
  `ma_user_id` int NOT NULL DEFAULT '0',
  `ma_dt_sdt_id` int NOT NULL DEFAULT '0',
  `customer_charges` decimal(12,4) DEFAULT '0.0000',
  `bank_charges` decimal(12,4) DEFAULT '0.0000',
  `ma_bank_on_boarding_id` int unsigned DEFAULT '0',
  `rt_share` decimal(12,4) DEFAULT '0.0000',
  `rt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
  `dt_share` decimal(12,4) DEFAULT '0.0000',
  `dt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
  `sd_share` decimal(12,4) DEFAULT '0.0000',
  `sd_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed , 2 => percent',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Y=> Active, N=> Inactive',
  `settings_flag` enum('1','2') DEFAULT '2' COMMENT '1=>General, 2=> Global',
  PRIMARY KEY (`ma_bene_shares_id`),
  KEY `ma_bank_on_boarding_id` (`ma_bank_on_boarding_id`),
  FOREIGN KEY (`ma_bank_on_boarding_id`) REFERENCES `ma_bank_on_boarding` (`ma_bank_on_boarding_id`))