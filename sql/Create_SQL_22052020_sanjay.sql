DROP TABLE IF EXISTS ma_billpay_transaction_master;
CREATE TABLE ma_billpay_transaction_master (
  ma_billpay_transactionid int(10) unsigned NOT NULL AUTO_INCREMENT,
  order_id varchar(50) NOT NULL,
  ma_transaction_master_id int(11) NOT NULL,
  ma_user_id int(11) NOT NULL,
  userid int(11) NOT NULL,
  amount decimal(12,4) DEFAULT NULL,
  utility_name varchar(100) DEFAULT NULL,
  utility_id int(11) DEFAULT NULL,
  provider_id int(11) DEFAULT NULL,
  provider_name varchar(50) DEFAULT NULL,
  action_type varchar(50) DEFAULT NULL,
  transaction_status enum('I','P','S','F') NOT NULL,
  customer_mobile_number varchar(12) NOT NULL,
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ma_billpay_transactionid),
  KEY ma_transaction_master_id (ma_transaction_master_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;