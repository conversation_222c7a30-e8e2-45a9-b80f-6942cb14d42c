CREATE TABLE `ma_qr_master` (
  `ma_qr_master_id` int(9) unsigned NOT NULL AUTO_INCREMENT,
  `vpa` varchar(45) NOT NULL,
  `bank` varchar(45) NOT NULL,
  `qr_status` varchar(10) DEFAULT 'N' COMMENT 'N- New,A-active, D-disable',
  `added_by` varchar(45) NOT NULL,
  `added_on` datetime DEFAULT CURRENT_TIMESTAMP,
  `ma_user_id` int(9) DEFAULT NULL,
  `mapped_by` varchar(45) DEFAULT NULL,
  `mapped_on` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_by` varchar(45) DEFAULT NULL,
  `updated_on` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_qr_master_id`),
  UNIQUE KEY `mult_col_idx_ban_vpa` (`bank`,`vpa`),
  <PERSON><PERSON><PERSON> `mult_col_idx_status_addedon` (`qr_status`,`added_on`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1