/*
* Update GST for GST user
*/
UPDATE ma_orderwise_taxes as new
JOIN 
(SELECT o.ma_orderwise_taxes_id,(amount-(amount/1.18)) as gst_amount
FROM ma_orderwise_taxes as o
JOIN ma_user_master as u on o.ma_user_id = u.profileid
WHERE u.gst_number IS NOT NULL AND u.gst_number!='' AND o.amount !=0 AND o.gst_amount = 0) as map ON
new.ma_orderwise_taxes_id = map.ma_orderwise_taxes_id 
SET new.gst_amount = map.gst_amount;

/*
* Update GST % for GST user for gst master table
*/
UPDATE ma_gst_master as new
JOIN (
SELECT o.ma_gst_master_id,"18.00" as percentage
FROM ma_gst_master as o
JOIN ma_user_master as u on o.ma_user_id = u.profileid
WHERE u.gst_number IS NOT NULL AND u.gst_number!='' AND o.percentage!=18
) as map ON
new.ma_gst_master_id= map.ma_gst_master_id
SET new.percentage= map.percentage;

/*
* Update Bene Verification Entries Too
*/
ALTER TABLE ma_orderwise_taxes
CHANGE transaction_type transaction_type enum('4','8','9','16','18','19','22') COLLATE 'latin1_swedish_ci' NOT NULL AFTER ma_orderwise_taxes_id;

ALTER TABLE ma_orderwise_taxes
ADD UNIQUE orderid_ma_user_id_transaction_type (orderid, ma_user_id, transaction_type);


DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS pointLedgerMigrate $$
CREATE PROCEDURE pointLedgerMigrate()
BEGIN
 BLOCK1: begin
DECLARE done INT DEFAULT FALSE;
 
DECLARE ma_user_id  INT(11);        
DECLARE orderid  varchar(50);
DECLARE transaction_type VARCHAR(10);
DECLARE ma_status VARCHAR(10);
DECLARE amount decimal(12,4);


 
DECLARE cur1 CURSOR FOR SELECT l.amount,l.orderid,l.transaction_type,l.ma_user_id,l.ma_status
FROM ma_points_ledger_master as l
WHERE l.transaction_type IN ('4','8','9','16','18','19','22')
AND l.ma_status IN('S','R','REV')
AND l.ma_user_id NOT IN(99999,99998,18999,30057) ORDER BY ma_points_ledger_master_id DESC ;
 
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
 OPEN cur1;  
 read_loop: LOOP
 FETCH cur1 INTO amount,orderid,transaction_type,ma_user_id,ma_status;
 IF done THEN
  LEAVE read_loop;
 END IF;
        IF (SELECT count(*) as c FROM ma_orderwise_taxes as t WHERE t.transaction_type = transaction_type and t.orderid = orderid and t.ma_user_id = ma_user_id ) = 0 THEN
            BEGIN
 
            /*
            * Check If Pan Card Percentage
            */
                -- SELECT amount,orderid,transaction_type,ma_user_id,ma_status;    
                SET @pancentage = 20;
                
                IF (SELECT count(*) as c FROM ma_user_master as u WHERE u.profileid = ma_user_id AND u.pan IS NOT NULL AND u.pan!='' ) > 0 THEN
                BEGIN
                /*
                * Check If GST Calculation Required
                */
                SET @pancentage = 3.75;
                END;
                END IF;
 
                SET @actualamount = (amount*100/(100-@pancentage));
                SET @tdsamout = @actualamount * (@pancentage/100);
                /*
                * Check If GST Calculation Required
                */
                SET @gstamount = 0;
                IF (SELECT count(*) as c FROM ma_user_master as u WHERE u.profileid = ma_user_id AND u.gst_number IS NOT NULL AND u.gst_number!='' ) > 0 THEN
                BEGIN
                /*
                * Check If GST Calculation Required
                */
                SET @gstamount = (@actualamount - (@actualamount / 1.18));
                END;
                END IF; 

                SELECT amount,@pancentage,@gstamount,orderid,transaction_type,ma_user_id,ma_status; 
 
                INSERT INTO ma_orderwise_taxes (transaction_type, ma_transfers_id, orderid, ma_user_id, amount, gst_amount, tds_amount, ma_status, addedon, updatedon)
                SELECT * FROM (SELECT transaction_type,NULL AS ma_transfers_id,orderid,ma_user_id,@actualamount as amount,@gstamount as gst_amount,@tdsamout as tds_amount,IF(ma_status = 'REV','R',ma_status) as ma_status,CURRENT_TIMESTAMP as addedon,CURRENT_TIMESTAMP As updatedon) AS tmp;
                -- WHERE NOT EXISTS (
                --     SELECT t.orderid as orderid FROM ma_orderwise_taxes as t WHERE t.transaction_type = transaction_type and t.orderid = orderid and t.ma_user_id = ma_user_id
                -- ) LIMIT 1;            
            END;
        END IF;        
 END LOOP read_loop;
 CLOSE cur1;
 END BLOCK1;
END
$$


-- Call Procedure for migration
call pointLedgerMigrate();
