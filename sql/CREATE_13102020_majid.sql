/* ----------------------- CMS ONBOARDING TABLE CREATION --------------------------*/

CREATE TABLE `ma_cms_merchant_on_boarding` (
  `ma_cms_merchant_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `merchant_name` varchar(255) NOT NULL,
  `merchant_legal_name` varchar(255) NOT NULL COMMENT 'Company Name',
  `company_reg_no` varchar(100) NOT NULL COMMENT 'Merchant Unique Id',
  `pan` varchar(10) NULL,
  `gst_number` varchar(50) NULL,
  `gst_state_id` smallint(5) NULL,
  `api_url` varchar(100) NOT NULL,
  `return_url` varchar(255) NULL,
  `api_credentials` varchar(255) NOT NULL,
  `setup_charges` decimal(12,2) NOT NULL,
  `ipn_url` varchar(100) NOT NULL,
  `contact_details` varchar(255) NULL,
  `mobile` varchar(15) NULL,
  `email` varchar(100) NULL,
  `merchant_status` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Y - Enable, N - Disable',
  `addedon` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE `ma_cms_merchant_bank` (
  `ma_cms_merchant_bank_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_cms_merchant_id` int(11) NOT NULL,
  `bank_name` varchar(255) NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `account_no` varchar(25) NOT NULL,
  `account_type` enum('S','C') NOT NULL DEFAULT 'S' COMMENT 'S - Savings, C - Current',
  `ifsc_code` varchar(15) NOT NULL,
  `bank_status` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Y -Enable, N - Disable',
  `addedon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE `ma_cms_merchant_on_boarding`
ADD `udf1` varchar(100) COLLATE 'latin1_swedish_ci' NULL AFTER `email`,
ADD `udf2` varchar(100) COLLATE 'latin1_swedish_ci' NULL AFTER `udf1`,
ADD `udf3` varchar(100) COLLATE 'latin1_swedish_ci' NULL AFTER `udf2`,
ADD `udf4` varchar(100) COLLATE 'latin1_swedish_ci' NULL AFTER `udf3`,
ADD `udf5` varchar(100) COLLATE 'latin1_swedish_ci' NULL AFTER `udf4`;

CREATE TABLE `ma_slabwise_distribution_cms` (
 `ma_slabwise_distribution_cms_id` int(11) NOT NULL AUTO_INCREMENT,
 `state_master_id` int(11) NOT NULL DEFAULT '0',
 `ma_user_id` int(11) NOT NULL DEFAULT '0',
 `customer_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
 `customer_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
 `rt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
 `rt_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
 `rt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
 `dt_share` decimal(12,4) DEFAULT '0.0000',
 `dt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
 `dt_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
 `sd_share` decimal(12,4) DEFAULT '0.0000',
 `sd_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed , 2 => percent',
 `sd_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
 `min_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
 `max_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
 `record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
 `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 `settings_flag` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> GENERAL, 2=> GLOBAL',
 PRIMARY KEY (`ma_slabwise_distribution_cms_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=latin1

ALTER TABLE `ma_cms_merchant_on_boarding` CHANGE `ma_cms_merchant_id` `ma_cms_merchant_on_boarding_id` INT(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `ma_cms_merchant_on_boarding` CHANGE `contact_details` `contact_name` varchar(255);

CREATE TABLE `ma_cms_merchant_api_details` (
  `ma_cms_merchant_api_details_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_cms_merchant_on_boarding_id` int(11) NOT NULL,
  `api_url` varchar(100) NOT NULL,
  `api_type` enum('GET','POST') NULL DEFAULT 'POST',
  `api_params` varchar(255) NULL,
  `validation_rules` varchar(255) NULL,
  `secret_key` varchar(255) NULL,
  `token` varchar(255) NULL,
  `username` varchar(100) NULL,
  `password` varchar(100) NULL,
  `udf1` varchar(100) NULL,
  `udf2` varchar(100) NULL,
  `udf3` varchar(100) NULL,
  `udf4` varchar(100) NULL,
  `addedon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE `ma_cms_merchant_on_boarding`
ADD `ma_user_id` int(11) NOT NULL COMMENT 'Profile Id' AFTER `company_reg_no`;

ALTER TABLE `ma_cms_merchant_on_boarding` CHANGE `setup_charges` `setup_charges` ENUM('1','2','3') NOT NULL COMMENT ' 1 => fixed , 2 => percent, 3 => both';

ALTER TABLE `ma_slabwise_distribution_cms` CHANGE `dt_applied_type` `dt_share_applied_type` enum('1','2') NOT NULL COMMENT '1 => fixed, 2 => percent';
ALTER TABLE `ma_slabwise_distribution_cms` CHANGE `sd_applied_type` `sd_share_applied_type` enum('1','2') NOT NULL COMMENT '1 => fixed, 2 => percent';

ALTER TABLE `ma_cms_merchant_on_boarding`
DROP `api_url`,
DROP `api_credentials`;

/*------------------------------------------------------------------------------------------------------------*/


/*--------------------------------SYSTEM CODES TABLE INSERT---------------------------------------------------*/

INSERT INTO `system_codes` (`serial_no`, `code_id`, `code_name`, `code_val`, `code_desc`) VALUES (NULL, '200001', 'transaction_type', '24', 'CMS');

INSERT INTO `system_codes` (`serial_no`, `code_id`, `code_name`, `code_val`, `code_desc`) VALUES (NULL, '200001', 'transaction_type', '26', 'CMS Incentive');


/*--------------------------------TRANSACTION TABLE ALTER---------------------------------------------------*/

* ALTER TABLE `ma_transaction_master`
CHANGE `transaction_type` `transaction_type` enum('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49','50','51') COLLATE 'latin1_swedish_ci' NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney 15- Insurance 16 - topup incentive,17 -Recharge,18-Recharge Incentive ,19 - Insurance Incentive,20 - POS,21 - Beneficiary validation,22 -Beneficiary validation Incentive,23 - Micro ATM, 24 - CMS, 25 - MATM Incentive, 26 - CMS Incentive' AFTER `transaction_status`;

* ALTER TABLE `ma_transaction_master_details`
ADD `cms_unique_id` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `utility_name`,
ADD `cms_ma_user_id` int NULL AFTER `cms_unique_id`;

/*--------------------------------POINTS LEDGER TABLE ALTER---------------------------------------------------*/
* ALTER TABLE `ma_points_ledger_master`
CHANGE `transaction_type` `transaction_type` enum('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26') COLLATE 'latin1_swedish_ci' NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive, 23 - Micro ATM,24 - CMS ,25 - MATM Incentive, 26 - CMS Incentive' AFTER `mode`;

/*--------------------------------CASH LEDGER TABLE ALTER---------------------------------------------------*/

* ALTER TABLE `ma_cash_ledger_master`
CHANGE `transaction_type` `transaction_type` enum('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','20','21','22','23','24','25','26') COLLATE 'latin1_swedish_ci' NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive, 20 - POS,21-BeneValidation Charges,22-BeneValidation Incentive, 23 -Micro ATM, 24- CMS,25 - MATM Incentive, 26 - CMS Incentive' AFTER `mode`;

/*--------------------------------ORDERWISE TABLE ALTER---------------------------------------------------*/

* ALTER TABLE `ma_orderwise_taxes`
CHANGE `transaction_type` `transaction_type` enum('4','8','9','16','18','19','22','25','26') COLLATE 'latin1_swedish_ci' NOT NULL AFTER `ma_orderwise_taxes_id`;

/*--------------------------------COMMISSION MASTER TABLE ALTER---------------------------------------------------*/

* ALTER TABLE  `ma_commission_master` CHANGE `ma_commission_type` `ma_commission_type` enum('1','2','3','4','5','6','7','8','9','10','18','15','19','22','23','25','26') DEFAULT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 15-Insurance,18-Recharge Incentive ,19 - Insurance Incentive,22-BeneValidation Incentive,23 - Micro ATM, 25 - MATM Incentive, 26 => cms incentive';

/*--------------------------------CHANNEL MASTER TABLE ALTER---------------------------------------------------*/

* ALTER TABLE `ma_channels_master`
CHANGE `channel_name` `channel_name` enum('AEPS','DMT','COLLECTMONEY','BBPS','INSURANCE','WITHDRAWAL','CASHINONLINE','CASHINEVALUE','CMS') COLLATE 'latin1_swedish_ci' NULL AFTER `ma_user_id`;


/*--------------------------------WALLET QUERIES---------------------------------------------------*/
INSERT INTO `ma_commission_master` (`ma_user_id`,`state_master_id`,`ma_commission_type`, `ma_commission_percentage`, `ma_deduction_type`, `applied_type`,`commission_status`) VALUES ('0','0','26', '20.00', '1', '2','Y');
INSERT INTO `ma_commission_master` (`ma_user_id`,`state_master_id`,`ma_commission_type`, `ma_commission_percentage`, `ma_deduction_type`, `applied_type`,`commission_status`) VALUES ('0','0','26', '3.75', '3', '2','Y');

* ALTER TABLE ma_allow_withdrawal CHANGE wallet_type `wallet_type` enum('1','2','3','4','5','6','7') DEFAULT NULL COMMENT '1- TopUp wallet, 2- Collect Money wallet, 3- AEPS wallet, 4- BBPS wallet, 5-Insurance wallet, 6-Micro Atm, 7-CMS wallet';

insert into system_codes (code_id,code_name,code_val,code_desc) VALUES
('200003','wallet_type','6','wallet_6::200012'),
('200012','wallet_6_transaction_type','23,25','Transaction types in wallet 6 '),
('200009','allow_flag','6','Y'),
('200003','wallet_type','7','wallet_7::200013'),
('200013','wallet_7_transaction_type','24,26','Transaction types in wallet 7 '),
('200009','allow_flag','7','N')

* ALTER TABLE `ma_points_ledger_details`
CHANGE `wallet_type` `wallet_type` enum('1','2','3','4','5','6','7') COLLATE 'latin1_swedish_ci' NULL COMMENT '1- TopUp wallet, 2- Collect Money wallet, 3- AEPS wallet, 4- BBPS wallet, 5-Insurance wallet, 6 - MATM, 7- CMS Wallet' AFTER `amount`;




 /* INSERT INTO `ma_channels_master` (`state_master_id`, `ma_user_id`, `channel_name`, `record_status`, `ma_flag`, `settings_flag`)
VALUES ('0', '0', 'CMS', 'N', 'N', '1');

INSERT INTO `ma_channels_master` (`state_master_id`, `ma_user_id`, `channel_name`, `record_status`, `ma_flag`, `settings_flag`)
VALUES ('0', '29419', 'CMS', 'Y', 'N', '1');
INSERT INTO `ma_channels_master` (`state_master_id`, `ma_user_id`, `channel_name`, `record_status`, `ma_flag`, `settings_flag`)
VALUES ('0', '29431', 'CMS', 'Y', 'N', '1');

INSERT INTO  `ma_commission_master` (`ma_commission_type`, `ma_commission_percentage`, `ma_deduction_type`, `applied_type`) VALUES ('26', '20.00', '1', '2'); 
INSERT INTO  `ma_commission_master` (`ma_commission_type`, `ma_commission_percentage`, `ma_deduction_type`, `applied_type`) VALUES ('26', '3.75', '3', '2'); 

*/





/* 
INSERT INTO ma_allow_withdrawal (ma_user_id,priority,transaction_type,wallet_type,allow_flag) VALUES ('29431','7','24,26','7','N');
INSERT INTO ma_allow_withdrawal (ma_user_id,priority,transaction_type,wallet_type,allow_flag) VALUES ('29419','7','24,26','7','N');
 */

