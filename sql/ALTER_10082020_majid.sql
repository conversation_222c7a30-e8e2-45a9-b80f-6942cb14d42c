ALTER TABLE `ma_aml_highlighted`
ADD `period` int(4) NULL AFTER `type`,
ADD `remarks` varchar(250) COLLATE 'latin1_swedish_ci' NULL AFTER `period`;

ALTER TABLE `ma_aml_highlighted`
ADD `threshold_id` int(11) NULL AFTER `remarks`,
ADD `exceeded_by` decimal(12,4) NULL AFTER `threshold_id`;

ALTER TABLE `ma_aml_threshold`
ADD `block_transaction` enum('Y','N') COLLATE 'latin1_swedish_ci' NOT NULL DEFAULT 'N' COMMENT 'Flag to block transaction' AFTER `threshold_status`;

ALTER TABLE `ma_transfers_threshold`
ADD `block_transaction` enum('Y','N') COLLATE 'latin1_swedish_ci' NOT NULL DEFAULT 'N' COMMENT 'Flag to block transaction' AFTER `threshold_period`;

ALTER TABLE `ma_transfers_threshold`
ADD `threshold_status` enum('A','D') COLLATE 'latin1_swedish_ci' NOT NULL DEFAULT 'A' COMMENT 'A - Added, D - Deleted' AFTER `block_transaction`;

ALTER TABLE `ma_transfers_threshold`
ADD `beneficiary_threshold` int(4) NULL COMMENT 'Threshold amount for bene addition' AFTER `block_transaction`,
ADD `block_beneficiary` enum('Y','N') COLLATE 'latin1_swedish_ci' NOT NULL DEFAULT 'N' COMMENT 'Flag to block bene addition' AFTER `beneficiary_threshold`;

ALTER TABLE `ma_aml_highlighted_bene`
ADD `bank_master_id` int(10) NULL AFTER `ifsc_code`;

ALTER TABLE `ma_aml_threshold_snapshot_details`
ADD `account_number` varchar(25) NOT NULL AFTER `beneficiary_id`;


/*  INDEXES */

ALTER TABLE `ma_aml_threshold_snapshot`
ADD INDEX `type` (`type`),
ADD INDEX `type_value` (`type_value`),
ADD INDEX `snapshot_date` (`snapshot_date`);

ALTER TABLE `ma_aml_threshold_snapshot_details`
ADD INDEX `uic` (`uic`),
ADD INDEX `account_number` (`account_number`),
ADD INDEX `snapshot_date` (`snapshot_date`);

ALTER TABLE `ma_aml_threshold_snapshot`
ADD UNIQUE `snapshot_date_type_type_value` (`snapshot_date`, `type`, `type_value`);

ALTER TABLE `ma_aml_threshold_snapshot_details`
ADD UNIQUE `snapshot_date_uic_account_number` (`snapshot_date`, `uic`, `account_number`);
