CREATE TABLE `ma_risk_management_api_log`(
    `ma_risk_management_api_log_id` INT NOT NULL AUTO_INCREMENT,
    `ma_user_id` INT NULL DEFAULT NULL,
    `userid` INT NULL DEFAULT NULL,
    `aggregator_order_id` VARCHAR(50) NOT NULL,
    `transaction_status` ENUM('I', 'P', 'F', 'S') NOT NULL DEFAULT 'I' COMMENT '\'I\'- initiated,\'P\' - Pending,\'S\' - Success, \'F\' - Fail',
    `transaction_type` VARCHAR(5) NULL DEFAULT NULL,
    `retry_count` INT NULL DEFAULT '0',
    `api_request` TEXT NULL DEFAULT NULL,
    `api_response` TEXT NULL DEFAULT NULL,
    `api_status` VARCHAR(20) NULL DEFAULT NULL,
    `cron_status` VARCHAR(10) NOT NULL DEFAULT 'I' COMMENT '\'I\'- initiated,\'P\' - In Progress,\'C\' - Compeleted',
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(`ma_risk_management_api_log_id`),
    UNIQUE `idx_aggregator_order_id_transaction_status` (`aggregator_order_id`,`transaction_status`),
    KEY `idx_cron_status`(`cron_status`),
    KEY `idx_retry_count`(`retry_count`)
);