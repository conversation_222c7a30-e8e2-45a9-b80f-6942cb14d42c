INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`)
VALUES ('1100099', 'max_risk_api_retry_count', '2', 'Risk Analysis Retry Count', now(), now());

INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`)
VALUES ('1100036', 'risk_api_batch_size', '300', 'Risk API - set batch size', now(), now());

INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`)
VALUES ('1100037', 'is_risk_analysis_active', 'N', 'Risk API -  check flag. Y - active, N - in-active', now(), now());

/* NEW CHANGES */
INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`)
VALUES ('1100038', 'risk_block_transaction', 'N', 'Risk API Block Transaction -  check flag. Y - active, N - in-active', now(), now());

INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`)
VALUES ('1100039', 'risk_score_threshold', '50', 'Risk API Threshold Value  check flag.', now(), now());