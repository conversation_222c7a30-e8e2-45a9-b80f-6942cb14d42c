INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('40', 'AePS mini statement', 'AEPS_STMT', 'AePS mini statement', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('41', 'AePS mini statement incentive', 'AEPS_STMT_INCENTIVE', 'AePS mini statement incentive', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('40', 'AePS balance enquiry', 'AEPS_BAL_ENQ', 'AePS balance enquiry', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('41', 'AePS balance enquiry incentive', 'AEPS_BAL_INCENTIVE', 'AePS balance enquiry incentive', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());