ALTER TABLE `merchantapp_qc`.`ma_points_ledger_master` CHANGE `transaction_type` `transaction_type` ENUM('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - Collect<PERSON><PERSON>, 11 - TD<PERSON>, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive, 23 - Micro ATM,24 - CMS ,25 - MATM Incentive, 26 - CMS Incentive, 37 - Cashback Promotion, 38-DMT KYC Charges,39-DMT KYC Charges Incentive,40 - AEPS Mini Statement, 41 - AEPS Mini Statement Incentive, 42 - AEPS Balance Enquiry, 43 - AEPS Balance Enquiry Incentive, 44 - ONDC ,45 - ONDC Incentive';