ALTER TABLE `ma_slabwise_distribution_transfers`
ADD `dt_share` decimal(12,4) DEFAULT 0.0000 AFTER `rt_operative_amount`,
ADD `dt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent' AFTER `dt_share`,
ADD `dt_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges' AFTER `dt_applied_type`,
ADD `sd_share` decimal(12,4) DEFAULT 0.0000 AFTER `dt_operative_amount`,
ADD `sd_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed , 2 => percent' AFTER `sd_share`,
ADD `sd_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges' AFTER `sd_applied_type`,
ADD `customer_type` enum('KYC','NONKYC') DEFAULT 'NONKYC' COMMENT 'KYC => KYC Customer, NONKYC => Non Kyc Customer' AFTER `sd_operative_amount`;