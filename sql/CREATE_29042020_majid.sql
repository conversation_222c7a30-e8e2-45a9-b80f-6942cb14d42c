DROP TABLE IF EXISTS `ma_cities_master`;
CREATE TABLE `ma_cities_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) NOT NULL,
  `state_id` int(11) NOT NULL,
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

DROP TABLE IF EXISTS `ma_states_master`;
CREATE TABLE `ma_states_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) NOT NULL,
  `zone_id` int(11) NOT NULL DEFAULT '1',
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

DROP TABLE IF EXISTS `ma_support_ticket_docs`;
CREATE TABLE `ma_support_ticket_docs` (
  `ma_support_ticket_doc_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `support_ticket_id` int(11) NOT NULL,
  `document_path` varchar(250) COLLATE 'latin1_swedish_ci' NOT NULL,
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updateon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;