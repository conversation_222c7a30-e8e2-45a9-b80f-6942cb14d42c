ALTER TABLE `ma_transaction_master_details`
CHANGE `customer_name` `customer_name` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `transaction_id`,
CHANGE `customer_aadhaar` `customer_aadhaar` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `customer_name`,
ADD `customer_mobile` varchar(15) COLLATE 'latin1_swedish_ci' NULL AFTER `customer_aadhaar`,
ADD `customer_email` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `custome_mobile`,
CHANGE `bank_name` `bank_name` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `customer_email`,
CHANGE `customer_virtualid` `customer_virtualid` varchar(100) COLLATE 'latin1_swedish_ci' NULL AFTER `bank_balance`;

ALTER TABLE `ma_transaction_master_details`
ADD `provider_name` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `customer_virtualid`,
ADD `utility_name` varchar(50) COLLATE 'latin1_swedish_ci' NULL AFTER `provider_name`;


ALTER TABLE `ma_transaction_master_details`
CHANGE `bank_name` `bank_name` varchar(250) COLLATE 'latin1_swedish_ci' NULL AFTER `customer_email`;