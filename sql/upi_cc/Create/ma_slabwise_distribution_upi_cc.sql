CREATE TABLE `ma_slabwise_distribution_upi_cc` (
  `ma_slabwise_distribution_upi_cc_id` int NOT NULL AUTO_INCREMENT,
  `state_master_id` int NOT NULL DEFAULT '0',
  `ma_user_id` int NOT NULL DEFAULT '0',
  `customer_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `customer_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `gst_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `gst_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `customer_type` enum('KYC','NONKYC') DEFAULT 'NONKYC' COMMENT 'KYC => KYC Customer, NONKYC => Non Kyc Customer',
  `min_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `max_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `settings_flag` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> GENERAL, 2=> GLOBAL',
  `added_by` varchar(45) DEFAULT NULL,
  `updated_by` varchar(45) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`ma_slabwise_distribution_upi_cc_id`),
  KEY `idx_ma_user_id` (`ma_user_id`),
  KEY `idx_state_master_id` (`state_master_id`),
  KEY `idx_settings_flag` (`settings_flag`),
  KEY `idx_record_status` (`record_status`)
)