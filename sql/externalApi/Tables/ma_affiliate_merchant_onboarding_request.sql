CREATE TABLE `ma_affiliate_merchant_onboarding_request`(
    `ma_affiliate_merchant_onboarding_request_id` INT NOT NULL AUTO_INCREMENT,
    `affiliate_id` INT NULL COMMENT 'Affiliate Merhcant ID',
    `ms_api_request_body` TEXT(100) NULL COMMENT 'MS API Request',
    `ms_api_response_body` TEXT(100) NULL COMMENT 'MS API Response',
    `ms_api_status` CHAR(2) NULL COMMENT 'MS API Status',
    `lead_api_request_body` TEXT(100) NULL COMMENT 'Lead API Request',
    `lead_api_response_body` TEXT(100) NULL COMMENT 'Lead API Response',
    `lead_api_status` CHAR(2) NULL COMMENT 'Lead API Status',
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(`ma_affiliate_merchant_onboarding_request_id`),
    KEY `affiliate_id` (`affiliate_id`)
);