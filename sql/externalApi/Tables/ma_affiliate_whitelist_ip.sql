CREATE TABLE `ma_affiliate_whitelist_ip` (
  `ma_affiliate_whitelist_ip_id` int NOT NULL AUTO_INCREMENT,
  `affiliate_ma_user_id` int NOT NULL COMMENT 'ma_affiliate_mgmt_id of ma_affiliate_mgmt table',
  `ip_address` varchar(50) NOT NULL COMMENT 'ip for whitelisting',
  `url` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'staging or production url',
  `ip_status` varchar(5) DEFAULT NULL COMMENT 'A- active , I - inactive',
  `added_by` int NOT NULL COMMENT 'who added ',
  `added_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'added on date ',
  `updated_by` int DEFAULT NULL COMMENT 'who updated ',
  `updated_on` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'updated date ',
  `phase` varchar(5) DEFAULT NULL COMMENT 'D- developemnt , P- production',
  PRIMARY KEY (`ma_affiliate_whitelist_ip_id`),
  KEY `idx_affiliate_id` (`affiliate_ma_user_id`),
  KEY `idx_whitelist_ip` (`ip_address`),
  KEY `idx_staging_url` (`url`),
  KEY `idx_added_on` (`added_on`)
) ENGINE = INNODB;