CREATE TABLE `ma_external_api_logs`(
    `ma_external_api_log_id` INT NOT NULL AUTO_INCREMENT,
    `affiliate_id` INT(50) NULL DEFAULT NULL,
    `merchant_id` INT(50) NULL DEFAULT NULL,
    `request_type` VARCHAR(10) NULL DEFAULT NULL,
    `request_url` VARCHAR(10) NULL DEFAULT NULL,
    `request_datetime` DATETIME NULL DEFAULT NULL,
    `request_json` TEXT NULL DEFAULT NULL,
    `request_params` TEXT NULL DEFAULT NULL,
    `response_json` TEXT NULL DEFAULT NULL,
    `response_datetime` DATETIME NULL DEFAULT NULL,
    `response_time` VARCHAR(10) NULL DEFAULT NULL,
    `added_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_on` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMAR<PERSON> KEY(`ma_external_api_log_id`)
);