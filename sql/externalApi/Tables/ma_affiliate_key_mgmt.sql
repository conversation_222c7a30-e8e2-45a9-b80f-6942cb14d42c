CREATE TABLE `ma_affiliate_key_mgmt` (
  `ma_affiliate_key_mgmt_id` int NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `affiliate_id` int NOT NULL COMMENT 'ma_affiliate_mgmt_id of ma_affiliate_mgmt table ',
  `api_key` varchar(50) DEFAULT NULL COMMENT 'API Key',
  `request_key` varchar(50) DEFAULT NULL COMMENT 'Request encryption and decryption AES256 Key',
  `response_key` varchar(50) DEFAULT NULL COMMENT 'Response encryption and decryption AES256 Key',
  `staging_key` varchar(100) DEFAULT NULL COMMENT 'staging key',
  `production_key` varchar(100) DEFAULT NULL COMMENT 'production key',
  `affiliate_key_status` varchar(5) NOT NULL DEFAULT 'I' COMMENT 'I -inactive, A-active',
  `added_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'added on date',
  `updated_on` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'updated on date',
  PRIMARY KEY (`ma_affiliate_key_mgmt_id`),
  KEY `idx_affiliate_id` (`affiliate_id`),
  KEY `idx_staging_key` (`staging_key`),
  KEY `idx_production_key` (`production_key`),
  KEY `idx_added_on` (`added_on`)
);