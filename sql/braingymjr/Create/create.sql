-- braingym customer details table

CREATE TABLE `ma_braingymjr_customer_details` (
  `braingymjr_customer_details_id` bigint(50) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT 'Parent Name',
  `email_id` varchar(1000) NOT NULL,
  `mobile_number` varchar(10) NOT NULL,
  `customer_child_name` varchar(100) DEFAULT NULL COMMENT 'customer child name',
  `customer_child_dob` date DEFAULT NULL COMMENT 'customer child dob',
  `password` varchar(10) DEFAULT NULL COMMENT 'Default password will be 123456',
  `added_on` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Added on',
  `updated_on` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
  `existing_user` varchar(10) DEFAULT NULL COMMENT ' if user  already exist then Y else N',
  PRIMARY KEY (`braingymjr_customer_details_id`),
  KEY `ma_braingymjr_customer_details_mobile_number_IDX` (`mobile_number`) USING BTREE,
  <PERSON><PERSON>Y `ma_braingymjr_customer_details_existing_user_IDX` (`existing_user`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=latin1


-- braingym customer plan and transaction details table

CREATE TABLE `ma_braingymjr_cust_plan_details` (
  `braingymjr_transaction_details_id` bigint(50) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(11) NOT NULL,
  `userid` int(11) NOT NULL,
  `braingymjr_customer_id` bigint(50) NOT NULL,
  `orderid` varchar(100) NOT NULL COMMENT 'transaction id',
  `amount` decimal(12,4) NOT NULL COMMENT 'plan amount',
  `email_id` varchar(1000) NOT NULL,
  `mobile_number` varchar(10) NOT NULL,
  `plan_details` varchar(100) NOT NULL COMMENT 'customer existing plan',
  `transaction_status` varchar(20) DEFAULT NULL,
  `added_on` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Added on',
  `updated_on` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
  `otp_verify` varchar(10) DEFAULT 'N' COMMENT ' if otp is verified then  Y else N',
  PRIMARY KEY (`braingymjr_transaction_details_id`),
  KEY `ma_braingymjr_cust_plan_details_mobile_number_IDX` (`mobile_number`) USING BTREE,
  KEY `ma_braingymjr_cust_plan_details_orderid_IDX` (`orderid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=latin1


