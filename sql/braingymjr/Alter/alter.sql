

-- altert table otp master


ALTER TABLE `ma_otp_master`
CHANGE `otp_type` 
`otp_type` enum('CO','BBPS','BE','BT','MN','KYC','REF','CW','BA','SP','DR','IU','CMS','GOLD','DBE','POS','RC','CR','ONDC','ONDCC','ONDCORDERHISTORYOTP','ONDCORDERRETURNOTP','BAAZAARHISTORYOTP','BGJR') CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration, IU - Integration User, CMS - Cash Management System, GOLD - <PERSON><PERSON><PERSON>, ONDC - Open Network for Digital Commerce, ONDCC - Open Network for Digital Commerce Cancellation OTP',