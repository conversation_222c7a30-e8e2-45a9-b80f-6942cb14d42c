-- ma_billpay_electricity_provider_master

CREATE TABLE `ma_billpay_electricity_provider_master` (
  `ma_billpay_electricity_provider_master_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) DEFAULT NULL,
  `provider_name` varchar(50) DEFAULT NULL,
  `logo_url` varchar(100) DEFAULT NULL,
  `provider_status` enum('A','I') NOT NULL,
  `api_params` longtext,
  `states` varchar(255) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_billpay_electricity_provider_master_id`),
  KEY `idx_providers_master` (`provider_id`,`provider_name`,`provider_status`)
);

-- ma_slabwise_distribution_electricity

CREATE TABLE `ma_slabwise_distribution_electricity` (
  `ma_slabwise_distribution_electricity_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(11) NOT NULL DEFAULT '0',
  `ma_dt_sdt_id` int(11) NOT NULL DEFAULT '0',
  `state_master_id` int(11) NOT NULL DEFAULT '0',
  `provider_id` int(11) NOT NULL,
  `provider_name` varchar(50) NOT NULL,
  `customer_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `customer_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `rt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `rt_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `rt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
  `dt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `dt_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `dt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
  `sd_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `sd_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `sd_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
  `min_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `max_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
  `settings_flag` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => General , 2 => Global',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_slabwise_distribution_electricity_id`),
  KEY `idx_electricity_slabwise` (`ma_user_id`,`state_master_id`,`provider_id`,`provider_name`,`min_amount`,`max_amount`)
);

-- ma_billpay_insurance_provider_master

CREATE TABLE `ma_billpay_insurance_provider_master` (
  `ma_billpay_insurance_provider_master_id` int(10) NOT NULL AUTO_INCREMENT,
  `provider_id` int(20) NOT NULL DEFAULT '0',
  `provider_name` varchar(50) NOT NULL,
  `logo_url` varchar(255) NOT NULL,
  `api_params` longtext,
  `provider_status` enum('A','I') NOT NULL DEFAULT 'A' COMMENT 'A=> Active, I=> Inactive',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_billpay_insurance_provider_master_id`),
  KEY `idx_insurance_master` (`provider_id`,`provider_name`)
);

-- ma_insurance_details_transaction

CREATE Table `ma_insurance_details_transaction` (
`ma_insurance_details_transaction_id` int(20) NOT NULL AUTO_INCREMENT,
`ma_user_id` int(11) NOT NULL DEFAULT '0',
`userid` int(11) NOT NULL DEFAULT '0',
`order_id` varchar(50) NOT NULL,
`amount` decimal(12,4) DEFAULT NULL,
`policy_no` varchar(25) NOT NULL,
`transaction_status` enum('I','P','S','F','R','REV','PS','V') DEFAULT NULL COMMENT '''I''- initiated,''P'' - Pending,''S'' - Success, ''F'' - Fail,''R''- Refund,''REV-Reverse,PS-partial success,V-void',
`addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
`updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (`ma_insurance_details_transaction_id`)
);

-- ma_slabwise_distribution_insurance

CREATE TABLE `ma_slabwise_distribution_bbps_insurance` (
`ma_slabwise_distribution_bbps_insurance_id` int(11) NOT NULL AUTO_INCREMENT,
`ma_user_id` int(11) NOT NULL DEFAULT '0',
`ma_dt_sdt_id` int(11) NOT NULL DEFAULT '0',
`state_master_id` int(11) NOT NULL DEFAULT '0',
`provider_id` int(11) NOT NULL,
`provider_name` varchar(50) NOT NULL,
`customer_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
`customer_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
`rt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
`rt_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
`rt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
`dt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
`dt_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
`dt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
`sd_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
`sd_share_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
`sd_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> ''txn_amount'', 2 => ''customer_charges''',
`min_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
`max_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
`record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
`addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
`updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
`settings_flag` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> GENERAL, 2=> GLOBAL',
PRIMARY KEY (`ma_slabwise_distribution_bbps_insurance_id`),
KEY `idx_slab_dist_insurance` (`ma_user_id`,`state_master_id`,`provider_id`,`provider_name`,`min_amount`,`max_amount`)
);
