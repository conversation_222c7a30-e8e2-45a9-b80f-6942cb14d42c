INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'Category', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "General", "a:Value": "General"}, {"a:Label": "OBC", "a:Value": "OBC"}, {"a:Label": "ST", "a:Value": "ST"}, {"a:Label": "SC", "a:Value": "SC"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'PhysicallyHandicapped', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Handicapped", "a:Value": "Handicapped"}, {"a:Label": "Not Handicapped", "a:Value": "Not Handicapped"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'AlternateOccupationType', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Government", "a:Value": "Government"}, {"a:Label": "Self Employed", "a:Value": "Self Employed"},{"a:Label": "Public Sector", "a:Value": "Public Sector"},{"a:Label": "Private", "a:Value": "Private"},{"a:Label": "Other", "a:Value": "Other"},{"a:Label": "None", "a:Value": "None"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'HighestEducationQualification', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Under 10th", "a:Value": "Under 10th"}, {"a:Label": "10th", "a:Value": "10th"},{"a:Label": "12th", "a:Value": "12th"},{"a:Label": "Graduate", "a:Value": "Graduate"},{"a:Label": "Post Graduate", "a:Value": "Post Graduate"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'Course', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "IIBF Advance", "a:Value": "IIBF Advance"}, {"a:Label": "IIBF Basic", "a:Value": "IIBF Basic"},{"a:Label": "Certified by Bank", "a:Value": "Certified by Bank"},{"a:Label": "None", "a:Value": "None"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'DeviceName', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Laptop", "a:Value": "Laptop"}, {"a:Label": "Handheld", "a:Value": "Handheld"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'ConnectivityType', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Landline", "a:Value": "Landline"}, {"a:Label": "Mobile", "a:Value": "Mobile"},{"a:Label": "VSAT", "a:Value": "VSAT"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'Provider', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Airtel", "a:Value": "Airtel"}, {"a:Label": "Jio", "a:Value": "Jio"},{"a:Label": "BSNL", "a:Value": "BSNL"},{"a:Label": "MTNL", "a:Value": "MTNL"},{"a:Label": "Vodafone Ideal Ltd", "a:Value": "Vodafone Ideal Ltd"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'EntityType', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label": "Sole Proprietor", "a:Value": "Sole Proprietor"}, {"a:Label": "Individual", "a:Value": "Individual"}]}}', now(), now());

INSERT INTO `ma_pmt_static_data` (`ma_pmt_static_data_id`, `static_data_type`, `static_data_request`, `static_data_response`, `addedon`, `updatedon`) VALUES (NULL, 'WeeklyOff', '{}', '{"a:Code": "000", "a:Message": "Success", "a:DataList": {"a:Data": [{"a:Label":"Monday","a:Value":"Monday"},{"a:Label":"Tuesday","a:Value":"Tuesday"},{"a:Label":"Wednesday","a:Value":"Wednesday"},{"a:Label":"Thursday","a:Value":"Thursday"},{"a:Label":"Friday","a:Value":"Friday"},{"a:Label":"Saturday","a:Value":"Saturday"},{"a:Label":"Sunday","a:Value":"Sunday"},{"a:Label":"None","a:Value":"None"}]}}', now(), now());