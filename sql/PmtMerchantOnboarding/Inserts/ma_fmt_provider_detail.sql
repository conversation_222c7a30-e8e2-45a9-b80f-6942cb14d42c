INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{\"title\":\"Merchant Register\",\"formid\":\"1\",\"child_id\":\"0\",\"postKey\":\"merchantRegister\",\"fields\":[{\"field\":\"GSTIN\",\"label\":\"GSTIN (Optional)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":15,\"min\":15,\"regex\":\"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$\"},\"type\":\"text\",\"postKey\":\"GSTIN\"},{\"field\":\"DOB\",\"label\":\"DOB\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":3,\"regex\":\"^[0-9]{2}/[0-9]{2}/[0-9]{4}$\",\"format\":\"MM/DD/YYYY\",\"to_range\":-18,\"from_range\":-100},\"type\":\"date\",\"postKey\":\"Dob\"},{\"field\":\"Gender\",\"label\":\"Gender\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Gender\",\"ddvalue\":[]},{\"field\":\"Category\",\"label\":\"Category\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Category\",\"ddvalue\":[]},{\"field\":\"Father Name Or Spouse Name\",\"label\":\"Father Name Or Spouse Name\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z,.\'-\\\\\\\\\\\\\\\\s]+$\"},\"type\":\"text\",\"postKey\":\"Fathernameorspousename\"},{\"field\":\"Are your physically handicapped?\",\"label\":\"Are your physically handicapped?\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Physicallyhandicapped\",\"ddvalue\":[]},{\"field\":\"Alternate Occupation Type\",\"label\":\"Alternate Occupation Type\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Alternateoccupationtype\",\"ddvalue\":[],\"action_rule\":{\"action\":\"hide_field\",\"parent_key\":[\"None\"],\"post_keys\":[\"Alternateoccupationdescription\"]}},{\"field\":\"Alternate Occupation Description\",\"label\":\"Alternate Occupation Description\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":50,\"min\":10,\"regex\":\"^[A-Za-z,.\'-\\\\\\\\\\\\\\\\s]+$\"},\"type\":\"text\",\"postKey\":\"Alternateoccupationdescription\"},{\"field\":\"Highest Education Qualification\",\"label\":\"Highest Education Qualification\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Highesteducationqualification\",\"ddvalue\":[]},{\"field\":\"Corporate Individual BC\",\"label\":\"Corporate Individual BC\",\"isEditable\":false,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z0-9 ,.\'-]+$\"},\"type\":\"text\",\"value\":\"Individual\",\"postKey\":\"CorporateindividualBC\"},{\"field\":\"Operating Hours From\",\"label\":\"Operating Hours From\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z0-9 ,.\'-:]+$\"},\"type\":\"timepicker\",\"value\":\"09:00 AM\",\"postKey\":\"Operatinghoursfrom\"},{\"field\":\"Operating Hours To\",\"label\":\"Operating Hours To\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z0-9 ,.\'-:]+$\"},\"type\":\"timepicker\",\"value\":\"10:00 PM\",\"postKey\":\"Operatinghoursto\"},{\"field\":\"Course\",\"label\":\"Course (BC Certification)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Course\",\"ddvalue\":[],\"action_rule\":{\"action\":\"hide_field\",\"parent_key\":[\"None\"],\"post_keys\":[\"Dateofpassing\",\"Institutename\"]}},{\"field\":\"Date Of Passing\",\"label\":\"Date Of Passing (BC Certification)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":10,\"min\":3,\"regex\":\"^[0-9]{2}/[0-9]{2}/[0-9]{4}$\",\"format\":\"MM/DD/YYYY\",\"to_range\":0,\"from_range\":-100},\"type\":\"date\",\"postKey\":\"Dateofpassing\"},{\"field\":\"Institute Name\",\"label\":\"Institute Name (BC Certification)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":20,\"min\":5,\"regex\":\"^[A-Za-z,.\'-\\\\\\\\\\\\\\\\s]+$\"},\"type\":\"text\",\"postKey\":\"Institutename\"},{\"field\":\"Device Name\",\"label\":\"Device Name\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Devicename\",\"ddvalue\":[]},{\"field\":\"Connectivity Type\",\"label\":\"Connectivity Type\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Connectivitytype\",\"ddvalue\":[]},{\"field\":\"Provider\",\"label\":\"Provider\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Provider\",\"ddvalue\":[]},{\"field\":\"Entity Type\",\"label\":\"Entity Type\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Entitytype\",\"ddvalue\":[]},{\"field\":\"Weekly Off\",\"label\":\"Weekly Off\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Weeklyoff\",\"ddvalue\":[]},{\"field\":\"Expected Annual Turnover\",\"label\":\"Expected Annual Turnover (in Lakhs)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":6,\"regex\":\"^[0-9]+$\"},\"type\":\"text\",\"postKey\":\"Expectedannualturnover\"},{\"field\":\"Expected Annual Income\",\"label\":\"Expected Annual Income (in Lakhs)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":6,\"regex\":\"^[0-9]+$\"},\"type\":\"text\",\"postKey\":\"Expectedannualincome\"}]}', 'MERCHANT_REGISTRATION', 'Y', 'Y', 'PROVIDER');

INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{}', 'IS_MERCHANT_KYC_MANDATORY', 'Y', 'N', 'AIRPAY');

INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{\"title\":\"Merchant Register\",\"formid\":\"1\",\"child_id\":\"0\",\"postKey\":\"merchantRegister\",\"fields\":[{\"field\":\"GSTIN\",\"label\":\"GSTIN (Optional)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":15,\"min\":15,\"regex\":\"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$\"},\"type\":\"text\",\"postKey\":\"GSTIN\"},{\"field\":\"DOB\",\"label\":\"DOB\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":3,\"regex\":\"^[0-9]{2}/[0-9]{2}/[0-9]{4}$\",\"format\":\"MM/DD/YYYY\",\"to_range\":-18,\"from_range\":-100},\"type\":\"date\",\"postKey\":\"Dob\"},{\"field\":\"Gender\",\"label\":\"Gender\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Gender\",\"ddvalue\":[]},{\"field\":\"Category\",\"label\":\"Category\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Category\",\"ddvalue\":[]},{\"field\":\"Father Name Or Spouse Name\",\"label\":\"Father Name Or Spouse Name\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z,.\'-\\\\\\\\\\\\\\\\s]+$\"},\"type\":\"text\",\"postKey\":\"Fathernameorspousename\"},{\"field\":\"Are your physically handicapped?\",\"label\":\"Are your physically handicapped?\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Physicallyhandicapped\",\"ddvalue\":[]},{\"field\":\"Alternate Occupation Type\",\"label\":\"Alternate Occupation Type\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Alternateoccupationtype\",\"ddvalue\":[],\"action_rule\":{\"action\":\"hide_field\",\"parent_key\":[\"None\"],\"post_keys\":[\"Alternateoccupationdescription\"]}},{\"field\":\"Alternate Occupation Description\",\"label\":\"Alternate Occupation Description\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":50,\"min\":10,\"regex\":\"^[A-Za-z,.\'-\\\\\\\\\\\\\\\\s]+$\"},\"type\":\"text\",\"postKey\":\"Alternateoccupationdescription\"},{\"field\":\"Highest Education Qualification\",\"label\":\"Highest Education Qualification\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Highesteducationqualification\",\"ddvalue\":[]},{\"field\":\"Corporate Individual BC\",\"label\":\"Corporate Individual BC\",\"isEditable\":false,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z0-9 ,.\'-]+$\"},\"type\":\"text\",\"value\":\"Individual\",\"postKey\":\"CorporateindividualBC\"},{\"field\":\"Operating Hours From\",\"label\":\"Operating Hours From\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z0-9 ,.\'-:]+$\"},\"type\":\"timepicker\",\"value\":\"09:00 AM\",\"postKey\":\"Operatinghoursfrom\"},{\"field\":\"Operating Hours To\",\"label\":\"Operating Hours To\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":2,\"regex\":\"^[A-Za-z0-9 ,.\'-:]+$\"},\"type\":\"timepicker\",\"value\":\"10:00 PM\",\"postKey\":\"Operatinghoursto\"},{\"field\":\"Course\",\"label\":\"Course (BC Certification)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Course\",\"ddvalue\":[],\"action_rule\":{\"action\":\"hide_field\",\"parent_key\":[\"None\"],\"post_keys\":[\"Dateofpassing\",\"Institutename\"]}},{\"field\":\"Date Of Passing\",\"label\":\"Date Of Passing (BC Certification)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":10,\"min\":3,\"regex\":\"^[0-9]{2}/[0-9]{2}/[0-9]{4}$\",\"format\":\"MM/DD/YYYY\",\"to_range\":0,\"from_range\":-100},\"type\":\"date\",\"postKey\":\"Dateofpassing\"},{\"field\":\"Institute Name\",\"label\":\"Institute Name (BC Certification)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":false,\"validation\":{\"max\":20,\"min\":5,\"regex\":\"^[A-Za-z,.\'-\\\\\\\\\\\\\\\\s]+$\"},\"type\":\"text\",\"postKey\":\"Institutename\"},{\"field\":\"Device Name\",\"label\":\"Device Name\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Devicename\",\"ddvalue\":[]},{\"field\":\"Connectivity Type\",\"label\":\"Connectivity Type\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Connectivitytype\",\"ddvalue\":[]},{\"field\":\"Provider\",\"label\":\"Provider\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Provider\",\"ddvalue\":[]},{\"field\":\"Entity Type\",\"label\":\"Entity Type\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Entitytype\",\"ddvalue\":[]},{\"field\":\"Weekly Off\",\"label\":\"Weekly Off\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":10,\"min\":4,\"regex\":\"^[a-zA-Z ]+$\"},\"type\":\"DD\",\"postKey\":\"Weeklyoff\",\"ddvalue\":[]},{\"field\":\"Expected Annual Turnover\",\"label\":\"Expected Annual Turnover (in Lakhs)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":6,\"regex\":\"^[0-9]+$\"},\"type\":\"text\",\"postKey\":\"Expectedannualturnover\"},{\"field\":\"Expected Annual Income\",\"label\":\"Expected Annual Income (in Lakhs)\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":50,\"min\":6,\"regex\":\"^[0-9]+$\"},\"type\":\"text\",\"postKey\":\"Expectedannualincome\"}]}', 'MERCHANT_ON_BOARDING', 'Y', 'N', 'AIRPAY');

INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{}', 'IS_MERCHANT_ONBOARDING_MANDATORY', 'Y', 'N', 'AIRPAY');

INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{}', 'IS_MERCHANT_PAN_VALIDATION_MANDATORY', 'Y', 'N', 'AIRPAY');

INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{}', 'IS_MERCHANT_OTP_CONSENT_MANDATORY', 'Y', 'N', 'AIRPAY');


INSERT INTO ma_fmt_provider_detail (ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type)
VALUES(1, '{}', 'MERCHANT_PRE_VALIDATE', 'Y', 'N', 'AIRPAY');



INSERT INTO `ma_fmt_provider_detail` (`ma_fmt_provider_detail_id`, `ma_fmt_provider_master_id`, `fields_required_json`, `entity_type`, `entity_status`, `is_otp_required`, `otp_type`, `addedon`, `updatedon`) VALUES (NULL, '1', '{\"title\":\"Merchant Local Address\",\"formid\":\"1\",\"child_id\":\"0\",\"postKey\":\"merchantLocalAddress\",\"fields\":[{\"field\":\"Localaddress\",\"label\":\"Localaddress\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"Localaddress\"},{\"field\":\"Localarea\",\"label\":\"Localarea\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"Localarea\"},{\"field\":\"Localcity\",\"label\":\"Localcity\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"Localcity\"},{\"field\":\"Localdistrict\",\"label\":\"Localdistrict\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"Localdistrict\"},{\"field\":\"Localstate\",\"label\":\"Localstate\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"Localstate\"},{\"field\":\"Localpincode\",\"label\":\"Localpincode\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"Localpincode\"}]}', 'MERCHANT_LOCAL_ADDRESS', 'Y', 'N', '', now(),now());


INSERT INTO `ma_fmt_provider_detail` (`ma_fmt_provider_detail_id`, `ma_fmt_provider_master_id`, `fields_required_json`, `entity_type`, `entity_status`, `is_otp_required`, `otp_type`, `addedon`, `updatedon`) VALUES (NULL, '1', '{\"title\":\"Merchant Shop Address\",\"formid\":\"1\",\"child_id\":\"0\",\"postKey\":\"merchantShopAddress\",\"fields\":[{\"field\":\"shopaddress\",\"label\":\"shopaddress\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"shopaddress\"},{\"field\":\"shoparea\",\"label\":\"shoparea\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"shoparea\"},{\"field\":\"shopcity\",\"label\":\"shopcity\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"shopcity\"},{\"field\":\"shopdistrict\",\"label\":\"shopdistrict\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"shopdistrict\"},{\"field\":\"shopstate\",\"label\":\"shopstate\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"shopstate\"},{\"field\":\"shoppincode\",\"label\":\"shoppincode\",\"isEditable\":true,\"isVisible\":true,\"isRequired\":true,\"validation\":{\"max\":5,\"min\":50,\"regex\":\"^[a-zA-Z0-9]+$\"},\"type\":\"text\",\"postKey\":\"shoppincode\"}]}', 'MERCHANT_SHOP_ADDRESS', 'Y', 'N', '', now(),now());