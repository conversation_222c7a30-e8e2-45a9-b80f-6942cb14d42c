ALTER TABLE `ma_otp_master` CHANGE `otp_type` `otp_type` ENUM('CO','BB<PERSON>','BE','BT','MN','KYC','REF','CW','BA','SP','DR','IU','CMS','GOLD','DBE','POS','RC','CR','ONDC','ONDCC','ONDCORDERHISTORYOTP','ONDCORDERRETURNOTP','BAAZAARHISTORYOTP','BGJR','FMT','FMTCO','FMTBEN','FMTREFUND','PREPAIDOTP','FMTME') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration, IU - Integration User, CMS - Cash Management System, GOLD - Dvara Smart Gold, DBE - Delete beneficiary, POS - POS Activation, RC - Request Credit, FMT - FMT Transaction,FMTCO - FMT Customer,FMTBEN - FMT BENEFICIARY,FMTREFUND - FMT REFUND,PREPAIDOTP - Prepaid Card Otp,FMTME- FMT MERCHANT ONBOARDING';