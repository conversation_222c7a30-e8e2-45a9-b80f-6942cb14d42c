CREATE TABLE `ma_fmt_merchant_detail_provider_mapping` (
  `ma_fmt_merchant_detail_provider_mapping_id` int NOT NULL AUTO_INCREMENT,
  `ma_fmt_merchant_detail_id` int NOT NULL COMMENT 'Refer ma_fmt_customer_detail table',
  `ma_fmt_provider_master_id` int NOT NULL COMMENT 'Refer ma_fmt_provider_master table',
  `ma_user_id` int NOT NULL,
  `provider_branch_code` int NOT NULL COMMENT 'Refer ma_fmt_provider_master table',
  `registration_status` char(5) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive',
  `kyc_status` char(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive',
  `onboarding_status` char(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive',
  `pan_validation_status` char(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive',
  `otp_consent_status` char(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive',
  `merchant_active_status` varchar(30) DEFAULT NULL,
  `Category` varchar(50) DEFAULT NULL,
  `FatherNameOrSpouseName` varchar(50) DEFAULT NULL,
  `PhysicallyHandicapped` varchar(50) DEFAULT NULL,
  `AlternateOccupationType` varchar(50) DEFAULT NULL,
  `HighestEducationQualification` varchar(50) DEFAULT NULL,
  `OperatingHoursFrom` varchar(50) DEFAULT NULL,
  `OperatingHoursTo` varchar(50) DEFAULT NULL,
  `Course` varchar(50) DEFAULT NULL,
  `DateOfPassing` varchar(50) DEFAULT NULL,
  `InstituteName` varchar(50) DEFAULT NULL,
  `DeviceName` varchar(50) DEFAULT NULL,
  `ConnectivityType` varchar(50) DEFAULT NULL,
  `Provider` varchar(50) DEFAULT NULL,
  `EntityType` varchar(50) DEFAULT NULL,
  `ExpectedAnnualTurnover` varchar(50) DEFAULT NULL,
  `ExpectedAnnualIncome` varchar(50) DEFAULT NULL,
  `WeeklyOff` varchar(20) DEFAULT NULL,
  `Shopaddress` varchar(100) DEFAULT NULL,
  `Shoparea` varchar(50) DEFAULT NULL,
  `Shopcity` varchar(50) DEFAULT NULL,
  `Shopdistrict` varchar(50) DEFAULT NULL,
  `Shopstate` varchar(50) DEFAULT NULL,
  `Shoppincode` varchar(50) DEFAULT NULL,
  `merchant_data_json` text NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_fmt_merchant_detail_provider_mapping_id`),
  KEY `idx_ma_fmt_merchant_detail_id` (`ma_fmt_merchant_detail_id`),
  KEY `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`),
  KEY `idx_registration_status` (`registration_status`),
  KEY `idx_kyc_status` (`kyc_status`),
  KEY `idx_onboarding_status` (`onboarding_status`),
  KEY `idx_pan_validation_status` (`pan_validation_status`),
  KEY `idx_otp_consent_status` (`otp_consent_status`),
  KEY `idx_merchant_active_status` (`merchant_active_status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Foreign Money Transfer Partner Provider Merchant provider Mapping ';
