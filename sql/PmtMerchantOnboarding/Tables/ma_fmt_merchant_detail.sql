CREATE TABLE `ma_fmt_merchant_detail` (
  `ma_fmt_merchant_detail_id` int NOT NULL AUTO_INCREMENT,
  `ma_fmt_country_master_id` int NOT NULL COMMENT 'Refer ma_fmt_country_master Table',
  `ma_user_id` int NOT NULL,
  `userid` int NOT NULL,
  `merchant_status` char(5) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive',
  `mobile_number` varchar(15) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_fmt_merchant_detail_id`),
  KEY `idx_ma_fmt_country_master_id` (`ma_fmt_country_master_id`),
  KEY `idx_mobile_number` (`mobile_number`),
  KEY `idx_merchant_status` (`merchant_status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Foreign Money Transfer Partner Provider Merchant Detail';