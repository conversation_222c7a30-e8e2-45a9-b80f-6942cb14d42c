INSERT INTO `ma_slabwise_withdrawal_bank_charges` (
    `entity_type`,
    `state_master_id`,
    `ma_user_id`,
    `ma_dt_sdt_id`,
    `min_range`,
    `max_range`,
    `merchant_charges`,
    `gst_charges_applied_type`,
    `gst_charges`,
    `record_status`,
    `paymode_type`,
    `settings_flag`,
    `added_by`,
    `updated_by`,
    `addedon`,
    `updatedon`,
    `deleted_at`
) VALUES (
    'MERCHANT',           
    0,                    
    0,                  
    0,              
    '20',               
    'Above',               
    10,             
    '1',                   
    1,               
    'Y',                  
    'NEFT',                
    '1',                   
    'admin',               
    'admin',               
    CURRENT_TIMESTAMP,     
    CURRENT_TIMESTAMP,    
    NULL                   
);

INSERT INTO `ma_slabwise_withdrawal_bank_charges` (
    `entity_type`,
    `state_master_id`,
    `ma_user_id`,
    `ma_dt_sdt_id`,
    `min_range`,
    `max_range`,
    `merchant_charges`,
    `gst_charges_applied_type`,
    `gst_charges`,
    `record_status`,
    `paymode_type`,
    `settings_flag`,
    `added_by`,
    `updated_by`,
    `addedon`,
    `updatedon`,
    `deleted_at`
) VALUES (
    'Distributor',           
    0,                    
    0,                  
    0,              
    '20',               
    'Above',               
    10,             
    '1',                   
    1,               
    'Y',                  
    'NEFT',                
    '1',                   
    'admin',               
    'admin',               
    CURRENT_TIMESTAMP,     
    CURRENT_TIMESTAMP,    
    NULL                   
);

INSERT INTO `ma_slabwise_withdrawal_bank_charges` (
    `entity_type`,
    `state_master_id`,
    `ma_user_id`,
    `ma_dt_sdt_id`,
    `min_range`,
    `max_range`,
    `merchant_charges`,
    `gst_charges_applied_type`,
    `gst_charges`,
    `record_status`,
    `paymode_type`,
    `settings_flag`,
    `added_by`,
    `updated_by`,
    `addedon`,
    `updatedon`,
    `deleted_at`
) VALUES (
    'State',           
    0,                    
    0,                  
    0,              
    '20',               
    'Above',               
    10,             
    '1',                   
    1,               
    'Y',                  
    'NEFT',                
    '1',                   
    'admin',               
    'admin',               
    CURRENT_TIMESTAMP,     
    CURRENT_TIMESTAMP,    
    NULL                   
);

INSERT INTO `ma_slabwise_withdrawal_bank_charges` (
    `entity_type`,
    `state_master_id`,
    `ma_user_id`,
    `ma_dt_sdt_id`,
    `min_range`,
    `max_range`,
    `merchant_charges`,
    `gst_charges_applied_type`,
    `gst_charges`,
    `record_status`,
    `paymode_type`,
    `settings_flag`,
    `added_by`,
    `updated_by`,
    `addedon`,
    `updatedon`,
    `deleted_at`
) VALUES (
    'Global',           
    0,                    
    0,                  
    0,              
    '20',               
    'Above',               
    10,             
    '1',                   
    1,               
    'Y',                  
    'NEFT',                
    '1',                   
    'admin',               
    'admin',               
    CURRENT_TIMESTAMP,     
    CURRENT_TIMESTAMP,    
    NULL                   
);