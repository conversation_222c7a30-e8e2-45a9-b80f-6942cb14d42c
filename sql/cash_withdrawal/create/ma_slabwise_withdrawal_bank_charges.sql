CREATE TABLE `ma_slabwise_withdrawal_bank_charges` ( `ma_slabwise_withdrawal_bank_charges_id` int NOT NULL AUTO_INCREMENT,entity_type varchar(100),`state_master_id` int NOT NULL DEFAULT '0', `ma_user_id` int NOT NULL DEFAULT '0', `ma_dt_sdt_id` int NOT NULL DEFAULT '0', `min_range` varchar(50) NOT NULL DEFAULT '0',`max_range` varchar(50) NOT NULL DEFAULT '0', `merchant_charges` decimal(12,4) NOT NULL DEFAULT '0.0000', `gst_charges_applied_type` enum('1','2') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1' COMMENT '1 => Inclusive, 2 => Exclusive', `gst_charges` decimal(12,4) NOT NULL DEFAULT '0.0000', `record_status` enum('Y','N') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'Y', `paymode_type` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL, `settings_flag` enum('1','2') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '1' COMMENT '1=> GENERAL, 2=> GLOBAL', `added_by` varchar(45) DEFAULT NULL, `updated_by` varchar(45) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL, `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP, `deleted_at` datetime DEFAULT NULL, PRIMARY KEY (`ma_slabwise_withdrawal_bank_charges_id`), KEY `idx_ma_user_id` (`ma_user_id`), KEY `idx_ma_dt_sdt_id` (`ma_dt_sdt_id`), KEY `idx_state_master_id` (`state_master_id`) ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1