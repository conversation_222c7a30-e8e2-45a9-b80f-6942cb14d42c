ALTER TABLE `ma_ipn_order_verify_log` ADD `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `addedon`, ADD `remarks` varchar(200) NULL AFTER `updatedon`,  ADD `no_of_attempts` INT UNSIGNED DEFAULT '0' AFTER `remarks`, ADD `cron_status` VARCHAR(10) NOT NULL DEFAULT 'P' COMMENT 'P - Pending, V - Verified' AFTER `no_of_attempts`;

CREATE INDEX ma_ipn_order_verify_log_cron_status_IDX USING BTREE ON ma_ipn_order_verify_log (cron_status,addedon);