CREATE IF NOT EXISTS TABLE `ma_security_questions_master` (
  `question_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `question_title` varchar(255) COLLATE 'utf8mb4_general_ci' NOT NULL,
  `question_status` enum('1','2') NOT NULL DEFAULT '2' COMMENT '1-Active,2-Inactive',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE='InnoDB' COLLATE 'utf8mb4_general_ci';

CREATE NOT EXISTS TABLE `ma_user_security_answer_mapping` (
  `answer_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `question_id` int(11) NOT NULL,
  `ma_user_id` int(11) NOT NULL,
  `userid` int(11) NOT NULL,
  `secret_answer` varchar(255) NOT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE='InnoDB';


ALTER TABLE `ma_user_security_answer_mapping`
ADD INDEX `question_id` (`question_id`),
ADD INDEX `ma_user_id` (`ma_user_id`),
ADD INDEX `userid` (`userid`);