DROP TABLE IF EXISTS `ma_transaction_master_details`;
CREATE TABLE `ma_transaction_master_details` (
  `ma_transaction_master_details_id` int(10) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_transaction_master_id` int(10) NOT NULL,
  `ma_user_id` int(10) NOT NULL,
  `userid` int(10) NOT NULL,
  `transaction_id` varchar(50) COLLATE 'latin1_swedish_ci' NULL,
  `customer_name` varchar(50) NULL COMMENT 'AEPS customer name',
  `customer_aadhaar` varchar(50) NULL COMMENT 'AEPS customer aadhar',
  `bank_name` varchar(50) NULL COMMENT 'AEPS Bank name',
  `terminalid` int(15) NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


INSERT INTO ma_transaction_master_details 
 (
   ma_transaction_master_id,
   ma_user_id,
   userid,
   transaction_id,
   customer_name,
   customer_aadhaar
 )
SELECT 
   ma_transaction_master_id,
   ma_user_id,
   userid,
   transaction_id,
   customer_name,
   customer_aadhaar
FROM 
   ma_transaction_master
WHERE
   customer_aadhaar IS NOT NULL;