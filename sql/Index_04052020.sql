ALTER TABLE ma_billpay_gateway_master
ADD INDEX ma_billpay_appid (ma_billpay_appid);

ALTER TABLE ma_billpay_provider_master
ADD INDEX ma_billpay_gateway_id (ma_billpay_gateway_id),
ADD INDEX ma_billpay_appid (ma_billpay_appid);

ALTER TABLE ma_billpay_transaction_master
ADD UNIQUE billpay_transaction_id (billpay_transaction_id),
ADD INDEX ma_billpay_gateway_id (ma_billpay_gateway_id),
ADD INDEX ma_billpay_provider_id (ma_billpay_provider_id),
ADD INDEX ma_billpay_appid (ma_billpay_appid);