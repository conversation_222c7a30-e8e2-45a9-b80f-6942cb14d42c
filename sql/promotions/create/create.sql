CREATE TABLE ma_promotions (
  id bigint(50) NOT NULL AUTO_INCREMENT,
  ma_user_id int(11) NOT NULL,
  userid int(11) NOT NULL,
  mobile_number varchar(10) NOT NULL,
  campaign_name  varchar(100),
  added_on datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Added on',
  updated_on datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
  PRIMARY KEY (id),
  KEY `ma_user_id_idx` (`ma_user_id`),
  KEY `mobile_number_idx` (`mobile_number`),
  KEY `campaign_name_idx` (`campaign_name`)
  
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='This table is used to store promotions data like bajajemicard.'