CREATE TABLE `ma_tds_merchant_master` (
`ma_tds_merchant_master_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
`ma_user_id` int(11) unsigned NOT NULL,
`applied_type` tinyint(3) NOT NULL COMMENT '1-Fix Tds, 2-Percentage Tds',
`tds_value` decimal(12,4) NOT NULL,
`tds_status` enum('active','inactive') NOT NULL,
`added_by` varchar(50) NOT NULL,
`addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
`updated_by` varchar(50) NOT NULL,
`updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
PRIMARY KEY (`ma_tds_merchant_master_id`),
KEY `ma_user_id` (`ma_user_id`),
KEY `tds_status` (`tds_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;