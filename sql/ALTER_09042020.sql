ALTER TABLE `ma_bank_on_boarding_details` CHANGE `fields_required_json` `fields_required_json` text COLLATE 'utf8mb4_general_ci' NULL AFTER `otp_required`;
SELECT `fields_required_json`, JSON_VALID(`fields_required_json`) FROM ma_bank_on_boarding_details;
UPDATE `ma_bank_on_boarding_details` SET fields_required_json='{}' WHERE fields_required_json='';
SELECT `fields_required_json`, JSON_VALID(`fields_required_json`) FROM ma_bank_on_boarding_details;
ALTER TABLE `ma_bank_on_boarding_details` CHANGE `fields_required_json` `fields_required_json` json NOT NULL AFTER `otp_required`;
SELECT `fields_required_json`, JSON_VALID(`fields_required_json`) FROM ma_bank_on_boarding_details;