CREATE TABLE `ma_gold_customer_on_boarding` (
 `ma_gold_customer_id` int(11) NOT NULL AUTO_INCREMENT,
 `customer_name` varchar(255) NOT NULL,
 `ext_customer_id` varchar(100) DEFAULT NULL,
 `dob` date NOT NULL,
 `gender` enum('m','f','o') NOT NULL COMMENT 'M - Male, F - Female, O - Other',
 `mobile` varchar(15) NOT NULL,
 `email` varchar(100) NOT NULL,
 `address_line_one` varchar(255) NOT NULL,
 `address_line_two` varchar(255) DEFAULT NULL,
 `address_line_three` varchar(100) DEFAULT NULL,
 `bank_name` varchar(255) NOT NULL,
 `account_name` varchar(255) DEFAULT NULL,
 `account_no` varchar(25) DEFAULT NULL,
 `ifsc_code` varchar(15) DEFAULT NULL,
 `branch_name` varchar(100) DEFAULT NULL,
 `upi_address` varchar(100) DEFAULT NULL,
 `nominee_name` varchar(255) DEFAULT NULL,
 `nominee_mobile` varchar(15) DEFAULT NULL,
 `nominee_relation` varchar(50) DEFAULT NULL,
 `guardian_name` varchar(255) DEFAULT NULL,
 `guardian_mobile` varchar(15) DEFAULT NULL,
 `guardian_relation` varchar(50) DEFAULT NULL,
 `branch_id` varchar(100) DEFAULT NULL,
 `agent_id` varchar(100) DEFAULT NULL,
 `document_type` enum('maskedAadhaar','pan','voterCard','drivingLicense','ngeraCard','passport') DEFAULT NULL,
 `document_no` varchar(100) DEFAULT NULL,
 `document_filepath` varchar(100) DEFAULT NULL,
 `document_uploadid` varchar(100) DEFAULT NULL,
 `document_fetchurl` varchar(100) DEFAULT NULL,
 `consentdoc_no` varchar(100) DEFAULT NULL,
 `consentdoc_filepath` varchar(100) DEFAULT NULL,
 `consentdoc_aproved` enum('Y','N') DEFAULT NULL,
 `consentdoc_uploadid` varchar(100) DEFAULT NULL,
 `consentdoc_fetchurl` varchar(100) DEFAULT NULL,
 `createdon` timestamp NOT NULL DEFAULT current_timestamp(),
 `updatedon` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
 PRIMARY KEY (`ma_gold_customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;