CREATE TABLE `ma_bene_shares` (
  `ma_bene_shares_id` int(3) NOT NULL AUTO_INCREMENT,
  `state_master_id` int(11) NOT NULL DEFAULT '0',
  `ma_user_id` int(11) NOT NULL DEFAULT '0',
  `customer_charges` decimal(12,4) DEFAULT '0.0000',
  `ma_bank_on_boarding_id` int(10) unsigned DEFAULT '0',
  `rt_share` decimal(12,4) DEFAULT '0.0000',
  `rt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
  `dt_share` decimal(12,4) DEFAULT '0.0000',
  `dt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
  `sd_share` decimal(12,4) DEFAULT '0.0000',
  `sd_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed , 2 => percent',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Y=> Active, N=> Inactive',
  `settings_flag` enum('1','2') DEFAULT '2' COMMENT '1=>General, 2=> Global',
  PRIMARY KEY (`ma_bene_shares_id`),
  KEY `ma_bank_on_boarding_id` (`ma_bank_on_boarding_id`),
  CONSTRAINT `ma_bene_shares_ibfk_1` FOREIGN KEY (`ma_bank_on_boarding_id`) REFERENCES `ma_bank_on_boarding` (`ma_bank_on_boarding_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE `ma_bene_verification` (
  `ma_bene_verification_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ma_transaction_master_id` int(10) NOT NULL,
  `ma_user_id` int(10) unsigned DEFAULT NULL,
  `userid` int(10) unsigned DEFAULT NULL,
  `uic` varchar(15) DEFAULT NULL,
  `bene_mobile_number` varchar(15) DEFAULT NULL,
  `account_number` varchar(25) NOT NULL,
  `ifsc_code` varchar(25) DEFAULT NULL,
  `ma_bank_on_boarding` int(10) unsigned NOT NULL,
  `validation_amount` decimal(12,4) DEFAULT NULL,
  `bene_verify_status` enum('I','S','F','P') DEFAULT NULL COMMENT 'I-Initiated, S-Success, F-Failed, P-pending',
  `utr_number` varchar(100) DEFAULT '0' COMMENT 'Bank transaction id',
  `request_number` varchar(100) DEFAULT NULL COMMENT 'Request Number sent to Bank',
  `bank_request` json DEFAULT NULL COMMENT 'Bank Request parameter',
  `bank_response` json DEFAULT NULL COMMENT 'Bank API Response',
  `bank_service_charges` decimal(12,4) DEFAULT '0.0000' COMMENT 'Bank Service Charges',
  `bank_benename` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'Bank side bene name',
  `bank_gross_amount` decimal(12,4) DEFAULT '0.0000' COMMENT 'Bank gross amount',
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_bene_verification_id`),
  KEY `request_number` (`request_number`),
  KEY `inx_ma_transaction_master_id` (`ma_transaction_master_id`),
  KEY `inx_ma_user_id` (`ma_user_id`),
  KEY `inx_uic` (`uic`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Transactions for Beneficary verifications';

