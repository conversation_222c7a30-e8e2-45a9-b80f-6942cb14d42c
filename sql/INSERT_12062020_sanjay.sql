### Insert Configuration 
/* 
Define Customer Charges on bank on boarding
*/
UPDATE `ma_bank_on_boarding` SET `bank_charges` = '2.5' WHERE `ma_bank_on_boarding_id` = '5';

/*
Activate bene_verification & merchant
*/
UPDATE `ma_bank_on_boarding` SET `bene_verification` = 'Y' WHERE `ma_bank_on_boarding_id` = '5';
UPDATE `ma_user_on_boarding_bank_mapping` SET `bene_verification` = 'Y' WHERE `ma_user_id` = '28479' AND `ma_bank_on_boarding_id` = '5'

/*
Define Customer Charges Global Setting
*/
INSERT INTO `ma_bene_shares` (`ma_bene_shares_id`, `state_master_id`, `ma_user_id`, `customer_charges`, `ma_bank_on_boarding_id`, `rt_share`, `rt_applied_type`, `dt_share`, `dt_applied_type`, `sd_share`, `sd_applied_type`, `addedon`, `updatedon`, `record_status`, `settings_flag`) VALUES
(3,	0,	0,	5.0000,	5,	0.0000,	'2',	0.0000,	'2',	0.0000,	'2',	'2020-06-12 12:20:42',	'2020-06-12 16:39:29',	'Y',	'2');


