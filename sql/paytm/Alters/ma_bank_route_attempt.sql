ALTER TABLE `ma_bank_route_attempt` ADD `mobile_number` VARCHAR(15) NULL AFTER `ma_bank_route_attempt_id`, ADD INDEX `mobile_number` (`mobile_number`);

ALTER TABLE `ma_bank_route_attempt` CHANGE `transfer_mode` `transfer_mode` ENUM('AD<PERSON><PERSON><PERSON>FICIARY','TRANSF<PERSON>','RETRY','AUTORETR<PERSON>','ADDCUSTOMER') CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT 'Transfer mode BENEFICIARY / TRANSFER / RETRY / AUTORETRY / ADDCUSTOMER';