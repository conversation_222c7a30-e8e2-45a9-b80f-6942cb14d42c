ALTER TABLE `ma_beneficiary_bank_mapping`
<PERSON><PERSON><PERSON> `ma_bank_on_boarding_id` `ma_bank_on_boarding_id` int(10) unsigned NULL,
ADD `ma_user_id` int(10) unsigned NULL AFTER `ma_bank_on_boarding_id`,
ADD `userid` int(10) unsigned NULL AFTER `ma_user_id`,
ADD `receiver_id` varchar(50) NULL AFTER `userid`,
CHANGE `ma_beneficiaries_id` `ma_beneficiaries_id` int(10) unsigned NULL AFTER `receiver_id`,
ADD `uic` VARCHAR(15) NULL AFTER `ma_beneficiaries_id`,
ADD `bank_status` enum('S','F','I','P','E','D') NULL COMMENT 'S- Success, F - Failure, I -Initiated P- Pending E-Error, D - Deleted' AFTER `uic`;


ALTER TABLE `ma_beneficiary_bank_mapping`
ADD UNIQUE `ma_beneficiaries_id_ma_bank_on_boarding_id` (`ma_beneficiaries_id`, `ma_bank_on_boarding_id`);

ALTER TABLE `ma_beneficiary_bank_mapping`
ADD `uic` `uic` VARCHAR(15) NULL AFTER `ma_beneficiaries_id`;