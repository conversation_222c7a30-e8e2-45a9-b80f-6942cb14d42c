DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS addOldRemitterMapping $$
CREATE PROCEDURE addOldRemitterMapping()
BEGIN
 BLOCK1: begin
DECLARE done INT DEFAULT FALSE;

DECLARE ima_customer_details_id  INT(11);         
DECLARE ima_user_id  INT(11);   
DECLARE iuserid  INT(11);      
DECLARE iuic  varchar(50);
DECLARE ima_bank_on_boarding_id  INT(11);      
DECLARE imobile_number VARCHAR(15);
DECLARE iremitter_id INT(11);


 
DECLARE cur1 CURSOR FOR SELECT 
ma_customer_details.ma_customer_details_id,
ma_customer_details.ma_user_id,
ma_customer_details.userid,
ma_customer_details.uic,
'5' as ma_bank_on_boarding_id,
ma_customer_details.mobile_number,
ma_customer_details.remitter_id
FROM ma_customer_details
WHERE customer_status = 'Y' 
AND remitter_id > 0 
AND CONCAT_WS('_',ma_customer_details_id,uic,'5') NOT IN (SELECT CONCAT_WS('_',ma_customer_details_id,uic,ma_bank_on_boarding_id) as uniqcol
FROM ma_customer_details_bank_mapping);
 
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
 OPEN cur1;  
 read_loop: LOOP
 FETCH cur1 INTO ima_customer_details_id,ima_user_id,iuserid,iuic,ima_bank_on_boarding_id,imobile_number,iremitter_id;
 IF done THEN
  LEAVE read_loop;
 END IF;
        IF (SELECT count(*) as c FROM ma_customer_details_bank_mapping as t WHERE t.ma_customer_details_id = ima_customer_details_id and t.uic = iuic and t.ma_bank_on_boarding_id = ima_bank_on_boarding_id ) = 0 THEN
            BEGIN
 
        INSERT INTO `ma_customer_details_bank_mapping` (`ma_customer_details_id`, `ma_user_id`, `userid`, `uic`, `ma_bank_on_boarding_id`, `mobile_number`, `remitter_id`, `bank_status`, `addedon`, `updatedon`) VALUES
(ima_customer_details_id,	ima_user_id,	iuserid,	iuic,	ima_bank_on_boarding_id,	imobile_number,	iremitter_id,	'S',	CURRENT_TIMESTAMP,	CURRENT_TIMESTAMP);
                       
            END;
        END IF;        
 END LOOP read_loop;
 CLOSE cur1;
 END BLOCK1;
END
$$

-- call addOldRemitterMapping();