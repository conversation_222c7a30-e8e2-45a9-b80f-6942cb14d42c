Backup 

 

CREATE TABLE ma_user_on_boarding_bank_mapping_bak_12052021 LIKE ma_user_on_boarding_bank_mapping; 
INSERT INTO ma_user_on_boarding_bank_mapping_bak_12052021 SELECT * FROM ma_user_on_boarding_bank_mapping;

 

CREATE TABLE ma_slabwise_distribution_bbps_bak_12052021 LIKE ma_slabwise_distribution_bbps; 
INSERT INTO ma_slabwise_distribution_bbps_bak_12052021 SELECT * FROM ma_slabwise_distribution_bbps;

 

CREATE TABLE ma_slabwise_distribution_aeps_bak_12052021 LIKE ma_slabwise_distribution_aeps; 
INSERT INTO ma_slabwise_distribution_aeps_bak_12052021 SELECT * FROM ma_slabwise_distribution_aeps;

 

Truncate

 

truncate table ma_user_on_boarding_bank_mapping
truncate table ma_slabwise_distribution_bbps
truncate table ma_slabwise_distribution_aeps

 


ALTER

 

ALTER TABLE ma_user_on_boarding_bank_mapping ADD PRIMARY KEY(ma_user_on_boarding_bank_mapping_id);
ALTER TABLE  ma_user_on_boarding_bank_mapping DROP INDEX ma_user_on_boarding_bank_mapping_id;

 

ALTER TABLE  ma_slabwise_distribution_bbps modify column  ma_slabwise_distribution_bbps_id int(11) NOT NULL AUTO_INCREMENT  primary key FIRST;

 

ALTER TABLE ma_slabwise_distribution_aeps modify column ma_slabwise_distribution_aeps_id int(11) NOT NULL AUTO_INCREMENT  primary key FIRST;