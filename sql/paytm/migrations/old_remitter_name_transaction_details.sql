DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS UpdateRemitterNameTransactionDetails $$
CREATE PROCEDURE UpdateRemitterNameTransactionDetails()
BEGIN
 BLOCK1: begin
DECLARE done INT DEFAULT FALSE;

DECLARE ima_transaction_master_id INT(11);
DECLARE icustomer_name  varchar(50);
 
DECLARE cur1 CURSOR FOR SELECT
tm.ma_transaction_master_id,
cd.remitter_name
FROM `ma_transaction_master` as tm
JOIN `ma_transaction_master_details` as tmd on tmd.ma_transaction_master_id = tm.ma_transaction_master_id
JOIN  `ma_customer_details` as cd on cd.mobile_number = tm.mobile_number AND cd.customer_status = 'Y'
WHERE tm.transaction_type = 2;
 
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
 OPEN cur1;  
 read_loop: LOOP
 FETCH cur1 INTO ima_transaction_master_id,icustomer_name;
 IF done THEN
  LEAVE read_loop;
 END IF;
        BEGIN
             START TRANSACTION;
               UPDATE ma_transaction_master_details
               SET
               customer_name = icustomer_name
               WHERE  ma_transaction_master_id = ima_transaction_master_id ;    
             COMMIT; 
        END;         
 END LOOP read_loop;
 CLOSE cur1;
 END BLOCK1;
END
$$

-- call UpdateRemitterNameTransactionDetails();