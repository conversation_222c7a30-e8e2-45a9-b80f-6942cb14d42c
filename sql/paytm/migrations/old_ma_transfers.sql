DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS UpdateBankInfoTransfer $$
CREATE PROCEDURE UpdateBankInfoTransfer()
BEGIN
 BLOCK1: begin
DECLARE done INT DEFAULT FALSE;

DECLARE ima_transfers_id INT(11);
DECLARE iifsc_code  varchar(50);
DECLARE ima_bank_master_id  INT(11);      
DECLARE ibank_name VARCHAR(300);
 
DECLARE cur1 CURSOR FOR SELECT
t.ma_transfers_id, 
b.ifsc_code,
b.ma_bank_master_id,
bm.bank_name
FROM `ma_transfers` as t
JOIN `ma_beneficiaries` as b on b.ma_beneficiaries_id = t.ma_beneficiaries_id
JOIN `ma_bank_master` as bm on bm.ma_bank_master_id = b.ma_bank_master_id
WHERE t.`ifsc_code` IS NULL;
 
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
 OPEN cur1;  
 read_loop: LOOP
 FETCH cur1 INTO ima_transfers_id,iifsc_code,ima_bank_master_id,ibank_name;
 IF done THEN
  LEAVE read_loop;
 END IF;
        BEGIN
             UPDATE ma_transfers
             SET
             ifsc_code = iifsc_code,
             ma_bank_master_id = ima_bank_master_id,
             bank_name = ibank_name
             WHERE  ma_transfers_id = ima_transfers_id ;     
        END;         
 END LOOP read_loop;
 CLOSE cur1;
 END BLOCK1;
END
$$

-- call UpdateBankInfoTransfer();