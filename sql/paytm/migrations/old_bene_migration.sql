DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS UpdateOldBeneMapping $$
CREATE PROCEDURE UpdateOldBeneMapping()
BEGIN
 BLOCK1: begin
DECLARE done INT DEFAULT FALSE;

DECLARE ima_beneficiary_bank_mapping_id  INT(11);         
DECLARE ima_customer_details_bank_id  INT(11);         
DECLARE ima_bank_on_boarding_id  INT(11);         
DECLARE ima_user_id  INT(11);   
DECLARE iuserid  INT(11);      
DECLARE ireceiver_id  varchar(50);
DECLARE ima_beneficiaries_id  INT(11);      
DECLARE ibank_status VARCHAR(15);
DECLARE iaddedon  varchar(50);
DECLARE iupdatedon  varchar(50);

 
DECLARE cur1 CURSOR FOR SELECT
'5' as ma_bank_on_boarding_id,
b.ma_user_id,
b.userid,
b.receiver_id,
b.ma_beneficiaries_id,
'S' as bank_status,
b.addedon,
b.updatedon
FROM ma_beneficiaries as b
WHERE b.beneficiary_status = 'Y' AND b.receiver_id > 0;
 
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
 OPEN cur1;  
 read_loop: LOOP
 FETCH cur1 INTO ima_bank_on_boarding_id,ima_user_id,iuserid,ireceiver_id,ima_beneficiaries_id,ibank_status,iaddedon,iupdatedon;
 IF done THEN
  LEAVE read_loop;
 END IF;
        IF (SELECT count(*) as c FROM ma_beneficiary_bank_mapping as t WHERE t.ma_beneficiaries_id = ima_beneficiaries_id and t.ma_bank_on_boarding_id = ima_bank_on_boarding_id ) = 0 THEN
            BEGIN
 
             INSERT INTO `ma_beneficiary_bank_mapping` (`ma_bank_on_boarding_id`, `ma_user_id`, `userid`, `receiver_id`, `ma_beneficiaries_id`, `bank_status`, `addedon`, `updatedon`)
            VALUES (ima_bank_on_boarding_id, ima_user_id, iuserid, ireceiver_id, ima_beneficiaries_id, ibank_status, iaddedon, iupdatedon);
                       
            END; 
        END IF;  
 END LOOP read_loop;
 CLOSE cur1;
 END BLOCK1;
END
$$

-- call UpdateOldBeneMapping();