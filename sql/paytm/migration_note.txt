A) Downtime :: 3 hrs After 10 pm
   > Update ma_notices set notice_flag = 'Y' where displayType = '3'   
B) Take Back Up Of Tables :: Refer : sql/paytm folders from Source Code
   1) create Procedures createTableBackUp with file 
      > sql/paytm/backup/backup.sql
   2) Run Procedures for following table
    >>
    call createTableBackUp('ma_bank_on_boarding_details','backup');
    call createTableBackUp('ma_bank_route_attempt','backup');
    call createTableBackUp('ma_bank_type','backup');
    call createTableBackUp('ma_beneficiary_bank_mapping','backup');
    call createTableBackUp('ma_customer_details','backup');
    call createTableBackUp('ma_otp_master','backup');
    call createTableBackUp('ma_slabwise_distribution_transfers','backup');
    call createTableBackUp('ma_transaction_master_details','backup');
    call createTableBackUp('ma_transfers','backup');
    call createTableBackUp('ma_transfers_shares','backup');
    call createTableBackUp('ma_bene_shares','backup');
    

C) DB Migrations :: Refer : > sql/paytm/migrations folders from Source Code

1) Create ALL tables from > sql/paytm/tables folders
  
2) Alters ALL Tables from sql/paytm/Alters folders

3) Old Remitter Migration Procedures // Migrate before production
  This will migrate old nsdl customer entries to ma_customer_details_bank_mapping table
  as per new structure 
  - sql/paytm/migrations/old_remitter_migration.sql
    ==> call addOldRemitterMapping(); 
    
4) TAKE backup & TRUNCATE ma_beneficiary_bank_mapping; // this data will be add from Procedures
5) Following SQL Only Need to Run Once Only !!!
 - sql/paytm/migrations/ma_transfers_shares.sql
 - sql/paytm/migrations/ma_slabwise_distribution_transfers.sql
 - sql/paytm/migrations/ma_bene_shares.sql
6) Migrate Beneficiary
 - sql/paytm/migrations/old_bene_migration.sql
   call UpdateOldBeneMapping();
7) Migrate transfers entries regarding bank details
 - sql/paytm/migrations/old_ma_transfers.sql 
   call UpdateBankInfoTransfer();
8) Migrate remitter and bene name in transaction details
 - sql/paytm/migrations/old_remitter_name_transaction_details.sql
   call UpdateRemitterNameTransactionDetails();   
9) DB Configurations plz pdf
   > update  system_codes set code_val = '34221' where code_id = 200010 // need to confirm code_val from production, it should be emitra default retailer

D) Optional -- In case of Rollback Scenario
   1) create Procedures restoreTableBackUp with file 
      > sql/paytm/restore/restore.sql
   2) Run Procedures for following table
    >>
    call restoreTableBackUp('ma_bank_on_boarding_details','backup');
    call restoreTableBackUp('ma_bank_route_attempt','backup');
    call restoreTableBackUp('ma_bank_type','backup');
    call restoreTableBackUp('ma_beneficiary_bank_mapping','backup');
    call restoreTableBackUp('ma_customer_details','backup');
    call restoreTableBackUp('ma_otp_master','backup');
    call restoreTableBackUp('ma_slabwise_distribution_transfers','backup');
    call restoreTableBackUp('ma_transaction_master_details','backup');
    call restoreTableBackUp('ma_transfers','backup');
    call restoreTableBackUp('ma_transfers_shares','backup');
    call restoreTableBackUp('ma_bene_shares','backup');

E) Configuration List ::    
  1) Add app_version header in api gateway
  2) Create SQS FIFO Queues
    1) Create Remitter Queue
    Staging - createremittermessages.fifo
    Production - prodcreateremittermessages.fifo
    
    Handler for Queue ==> remitterprocessor.js
    
    2) reQuery transfer
    Staging - requerymessage.fifo
    Production - prodrequerymessage.fifo
    
    Handler for Queue ==> transferrequeryprocessor.js
    
    3) Transfer Queue
    Staging - transfermessage.fifo
    Production -prodtransfermessage.fifo
    
    Handler for Queue ==> transferprocessor.js