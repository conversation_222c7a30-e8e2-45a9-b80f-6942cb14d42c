INSERT INTO `system_codes` (`serial_no`, `code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`, `deleted_at`) VALUES
(NULL,	1100001,	'sqsenable',	'true',	'sqs queue enable',	CURRENT_TIMESTAMP,	CURRENT_TIMESTAMP,	NULL);


INSERT INTO `system_codes` (`serial_no`, `code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`, `deleted_at`) VALUES
(NULL,	1100002,	'appversion',	'1.45:::true',	'current or minimum app version required',	CURRENT_TIMESTAMP,	CURRENT_TIMESTAMP,	NULL),
(NULL,	1100002,	'appversioncheck',	'false',	'enable app version check',	CURRENT_TIMESTAMP,	CURRENT_TIMESTAMP,	NULL);


INSERT INTO `system_codes` (`serial_no`, `code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`, `deleted_at`) VALUES (NULL, '1100003', 'default_bank_for_aeps_kiosk', '5', 'This is default on boarding bank id which us used for aeps and kiosk registration ', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, NULL);
