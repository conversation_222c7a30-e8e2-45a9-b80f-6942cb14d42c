INSERT INTO ma_bank_on_boarding_details (ma_bank_master_details_id, ma_bank_on_boarding_id, entity_type, otp_required, fields_required_json, addedon, updatedon) VALUES
(NULL, 9, 'SENDER', 'YES', '{}', '2021-01-12 09:43:04', '2021-01-12 09:43:04');

INSERT INTO ma_bank_on_boarding_details (ma_bank_master_details_id, ma_bank_on_boarding_id, entity_type, otp_required, fields_required_json, addedon, updatedon) VALUES (NULL, '9', 'PREVALIDATE', 'YES', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


INSERT INTO ma_bank_on_boarding_details (ma_bank_master_details_id, ma_bank_on_boarding_id, entity_type, otp_required, fields_required_json, addedon, updatedon) VALUES (NULL, '9', 'DEL_BENEFICIARY', 'YES', '{}', '2021-01-14 10:53:45', '2021-01-14 10:53:45')

INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES (NULL, '5', 'DEL_BENEFICIARY', 'YES', '{}', '2021-01-14 10:53:45', '2021-01-14 10:53:45');


INSERT INTO ma_bank_on_boarding_details (ma_bank_master_details_id, ma_bank_on_boarding_id, entity_type, otp_required, fields_required_json, addedon, updatedon) VALUES (NULL, '5', 'PREVALIDATE', 'NO', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO ma_bank_on_boarding_details (ma_bank_master_details_id, ma_bank_on_boarding_id, entity_type, otp_required, fields_required_json, addedon, updatedon) VALUES (NULL, '9', 'BENEFICIARY', 'YES', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES
(NULL,	9,	'REFUND',	'NO',	'{}',	'2020-03-16 11:36:36',	'2020-03-16 11:36:36');