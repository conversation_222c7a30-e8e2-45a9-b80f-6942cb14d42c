CREATE TABLE IF NOT EXISTS `dmt_session_logs` (
  `sessiond_log_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `session_hash` varchar(50) NOT NULL,
  `session_action` varchar(50) NOT NULL,
  `ma_bank_on_boarding_id` int(11) UNSIGNED NOT NULL DEFAULT '0',
  `session_params` json DEFAULT NULL,
  `order_id` varchar(50) NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`sessiond_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;