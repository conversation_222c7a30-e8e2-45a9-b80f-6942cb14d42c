CREATE TABLE `ma_bank_callback_logs` (
  `ma_bank_callback_logs_id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_name` varchar(100) DEFAULT NULL,
  `ma_bank_on_boarding_id` int(11) DEFAULT NULL,
  `bank_request` json DEFAULT NULL,
  `ma_transaction_master_id` varchar(50) DEFAULT NULL,
  `parent_id` varchar(50) DEFAULT NULL,
  `transaction_id` int(11) DEFAULT NULL,
  `status` enum('S','F','P') DEFAULT NULL,
  `addededon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_bank_callback_logs_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1