CREATE TABLE `dmt_session` (
  `sessiond_id` int(11) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `session_hash` varchar(50) NOT NULL,
  `sessionexpiry` timestamp NULL,
  `ma_bank_on_boarding_id` int(11) unsigned NULL,
  `bank_priority_json` JSON NULL DEFAULT NULL ,
  `ma_user_id` int(11) unsigned NOT NULL,
  `userid` int(11) NOT NULL,
  `uic` varchar(15) NULL,
  `mobile_number` varchar(15) NOT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  PRIMARY KEY (`sessiond_id`),
  UNIQUE KEY `session_hash` (`session_hash`),
  KEY `ma_bank_on_boarding` (`ma_bank_on_boarding_id`)
) ENGINE='InnoDB';