DROP TABLE IF EXISTS `ma_customer_details_bank_mapping`;
CREATE TABLE `ma_customer_details_bank_mapping` (
  `ma_customer_details_bank_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ma_customer_details_id` int(11) unsigned NOT NULL,
  `ma_user_id` int(11) unsigned NOT NULL,
  `userid` int(11) unsigned NOT NULL,
  `uic` varchar(15) NOT NULL,
  `ma_bank_on_boarding_id` int(11) unsigned NOT NULL,
  `mobile_number` varchar(15) NOT NULL,
  `remitter_id` varchar(50) DEFAULT NULL COMMENT 'This field is bank side remitter reference id used to transfer',
  `bank_status` enum('S','F','I','P','E','D') NOT NULL COMMENT 'S- Success, F - Failure, I -Initiated P- Pending E-Error, D - Deleted',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_customer_details_bank_id`),
  UNIQUE KEY `ma_customer_details_id_uic_ma_bank_on_boarding_id` (`ma_customer_details_id`,`uic`,`ma_bank_on_boarding_id`),
  KEY `ma_customer_details_id` (`ma_customer_details_id`),
  KEY `ma_user_id` (`ma_user_id`),
  KEY `userid` (`userid`),
  KEY `uic` (`uic`),
  KEY `ma_bank_on_boarding_id` (`ma_bank_on_boarding_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;