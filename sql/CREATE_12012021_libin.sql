DROP TABLE IF EXISTS `ma_user_devices`;
CREATE TABLE `ma_user_devices` (
`ma_user_device_id` int(10) NOT NULL AUTO_INCREMENT,
`ma_user_id` int(10) DEFAULT NULL,
`userid` int(10) DEFAULT NULL,
`device_type` enum('M','D') DEFAULT NULL COMMENT 'mobile/desktop',
`imei` text,
`latitude` decimal(10,8) DEFAULT NULL,
`longitude` decimal(10,8) DEFAULT NULL,
`verify_flag` enum('V','NV') DEFAULT NULL COMMENT 'Verified/NotVerified',
`addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
`updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (`ma_user_device_id`),
KEY `inx_ma_user_id` (`ma_user_id`),
<PERSON>EY `inx_userid` (`userid`)
) ENGINE=InnoDB AUTO_INCREMENT=12041 DEFAULT CHARSET=latin1 COMMENT='Device details capture during login'

