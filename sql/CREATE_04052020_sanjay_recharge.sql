SET NAMES utf8mb4;

DROP TABLE IF EXISTS ma_billpay_app_master;
CREATE TABLE ma_billpay_app_master (
  ma_billpay_appid int(11) NOT NULL AUTO_INCREMENT,
  app_name varchar(30) NOT NULL,
  app_status enum('Y','N') NOT NULL DEFAULT 'N',
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ma_billpay_appid)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO ma_billpay_app_master (ma_billpay_appid, app_name, app_status, addedon, updatedon) VALUES
(1,	'Shighrapay',	'Y',	'2020-05-04 08:07:30',	'2020-05-04 08:07:30');

DROP TABLE IF EXISTS ma_billpay_gateway_master;
CREATE TABLE ma_billpay_gateway_master (
  ma_billpay_gateway_id int(10) unsigned NOT NULL AUTO_INCREMENT,
  gateway_name varchar(50) NOT NULL,
  priority tinyint(4) NOT NULL,
  gateway_status enum('Y','N') NOT NULL DEFAULT 'N',
  shortname varchar(5) NOT NULL,
  ma_billpay_appid int(10) unsigned NOT NULL,
  api_token varchar(100) NOT NULL,
  gateway_user_id varchar(50) NOT NULL,
  gateway_client_id varchar(50) NOT NULL,
  gateway_type enum('Prefunded','Settlement') NOT NULL DEFAULT 'Settlement',
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ma_billpay_gateway_id),
  KEY ma_billpay_appid (ma_billpay_appid)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO ma_billpay_gateway_master (ma_billpay_gateway_id, gateway_name, priority, gateway_status, shortname, ma_billpay_appid, api_token, gateway_user_id, gateway_client_id, gateway_type, addedon, updatedon) VALUES
(1,	'shrikhpay',	1,	'Y',	'sp',	1,	'pFjLAZNMFCXDnCxnxD8usPOyl8fIA56KIYatlGtlZonkaaD2x1VHQZNqicVJ',	'310',	'**********',	'Prefunded',	'2020-05-04 11:26:02',	'2020-05-08 11:24:25');

DROP TABLE IF EXISTS ma_billpay_provider_master;
CREATE TABLE ma_billpay_provider_master (
  ma_billpay_provider_id int(10) unsigned NOT NULL AUTO_INCREMENT,
  operator_code varchar(20) NOT NULL,
  operator_name varchar(32) NOT NULL,
  operator_category enum('1','2') NOT NULL COMMENT '1-Prepaid,2-Postpaid',
  ma_billpay_gateway_id int(10) unsigned NOT NULL,
  ma_billpay_appid int(10) unsigned NOT NULL,
  provider_status enum('Y','N') NOT NULL DEFAULT 'N',
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ma_billpay_provider_id),
  KEY ma_billpay_gateway_id (ma_billpay_gateway_id),
  KEY ma_billpay_appid (ma_billpay_appid)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4;

INSERT INTO ma_billpay_provider_master (ma_billpay_provider_id, operator_code, operator_name, operator_category, ma_billpay_gateway_id, ma_billpay_appid, provider_status, addedon, updatedon) VALUES
(1,	'1',	'AIRTEL',	'1',	1,	1,	'Y',	'2020-05-04 12:11:42',	'2020-05-07 05:15:36'),
(2,	'2',	'VODAFONE',	'1',	1,	1,	'Y',	'2020-05-04 12:11:59',	'2020-05-07 05:15:36'),
(3,	'3',	'IDEA',	'1',	1,	1,	'Y',	'2020-05-04 12:12:19',	'2020-05-07 05:15:36'),
(4,	'4',	'TATA INDICOM',	'1',	1,	1,	'N',	'2020-05-04 12:12:37',	'2020-05-07 05:15:12'),
(5,	'5',	'TATA DOCOMO',	'1',	1,	1,	'N',	'2020-05-04 12:13:00',	'2020-05-07 05:15:12'),
(6,	'6',	'TELENOR',	'1',	1,	1,	'N',	'2020-05-04 12:13:17',	'2020-05-07 05:15:12'),
(7,	'7',	'MTNL',	'1',	1,	1,	'Y',	'2020-05-04 12:13:33',	'2020-05-07 05:15:36'),
(8,	'8',	'BSNL',	'1',	1,	1,	'Y',	'2020-05-04 12:13:49',	'2020-05-07 05:15:36'),
(9,	'9',	'AIRCEL',	'1',	1,	1,	'N',	'2020-05-04 12:14:08',	'2020-05-07 05:15:12'),
(10,	'10',	'VIDEOCON',	'1',	1,	1,	'N',	'2020-05-04 12:14:26',	'2020-05-07 05:15:12'),
(11,	'11',	'MTS',	'1',	1,	1,	'N',	'2020-05-04 12:14:44',	'2020-05-07 05:15:12'),
(12,	'23',	'AIRTEL POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:15:03',	'2020-05-07 05:15:12'),
(13,	'24',	'IDEA POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:15:42',	'2020-05-07 05:15:12'),
(14,	'25',	'VODAFONE POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:15:58',	'2020-05-07 05:15:12'),
(15,	'26',	'RELIANCE GSM POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:16:14',	'2020-05-07 05:15:12'),
(16,	'27',	'RELIANCE CDMA POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:16:34',	'2020-05-07 05:15:12'),
(17,	'28',	'TATA DOCOMO  POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:16:50',	'2020-05-07 05:15:12'),
(18,	'29',	'AIRCEL  POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:17:06',	'2020-05-07 05:15:12'),
(19,	'30',	'MTS  POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:17:25',	'2020-05-07 05:15:12'),
(20,	'31',	'MTNL TELEPHONE',	'1',	1,	1,	'N',	'2020-05-04 12:17:45',	'2020-05-07 05:15:12'),
(21,	'32',	'BSNL TELEPHONE',	'1',	1,	1,	'N',	'2020-05-04 12:18:23',	'2020-05-07 05:15:12'),
(22,	'33',	'AIRTEL TELEPHONE',	'1',	1,	1,	'N',	'2020-05-04 12:18:53',	'2020-05-07 05:15:12'),
(23,	'39',	'RELIANCE GSM',	'1',	1,	1,	'N',	'2020-05-04 12:19:19',	'2020-05-07 05:15:12'),
(24,	'40',	'RELIANCE CDMA',	'1',	1,	1,	'N',	'2020-05-04 12:19:36',	'2020-05-07 05:15:12'),
(25,	'42',	'BSNL STV',	'1',	1,	1,	'Y',	'2020-05-04 12:20:07',	'2020-05-07 05:15:36'),
(26,	'43',	'TATA DOCOMO STV',	'1',	1,	1,	'N',	'2020-05-04 12:20:33',	'2020-05-07 05:15:12'),
(27,	'44',	'TELENOR STV',	'1',	1,	1,	'N',	'2020-05-04 12:21:08',	'2020-05-07 05:15:12'),
(28,	'47',	'MTNL STV',	'1',	1,	1,	'Y',	'2020-05-04 12:21:40',	'2020-05-07 05:15:36'),
(29,	'58',	'TIKONA POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:22:05',	'2020-05-07 05:15:12'),
(30,	'73',	'BSNL POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:22:56',	'2020-05-07 05:15:12'),
(31,	'84',	'TATA TELE SERVICES POSTPAID',	'2',	1,	1,	'N',	'2020-05-04 12:23:14',	'2020-05-07 05:15:12'),
(32,	'112',	'RELIANCE JIO',	'1',	1,	1,	'Y',	'2020-05-04 12:23:53',	'2020-05-07 05:15:36'),
(33,	'12',	'DISH TV',	'1',	1,	1,	'Y',	'2020-05-07 05:16:06',	'2020-05-07 05:16:06'),
(34,	'13',	'TATA SKY',	'1',	1,	1,	'Y',	'2020-05-07 05:16:18',	'2020-05-07 05:16:18'),
(35,	'14',	'SUN TV',	'1',	1,	1,	'Y',	'2020-05-07 05:16:32',	'2020-05-07 05:16:32'),
(36,	'15',	'VIDEOCON DTH',	'1',	1,	1,	'Y',	'2020-05-07 05:16:47',	'2020-05-07 05:16:47'),
(37,	'17',	'AIRTEL DEGITAL TV',	'1',	1,	1,	'Y',	'2020-05-07 05:17:02',	'2020-05-07 05:17:02');

DROP TABLE IF EXISTS ma_billpay_transaction_master;
CREATE TABLE ma_billpay_transaction_master (
  ma_billpay_transactionid int(10) unsigned NOT NULL AUTO_INCREMENT,
  order_id varchar(50) NOT NULL,
  ma_transaction_master_id int(11) NOT NULL,
  ma_user_id int(11) NOT NULL,
  userid int(11) NOT NULL,
  billpay_transaction_id varchar(50) NOT NULL,
  amount decimal(10,2) NOT NULL,
  transaction_status enum('I','P','S','F') NOT NULL,
  ma_billpay_gateway_id int(10) unsigned NOT NULL,
  ma_billpay_provider_id int(10) unsigned NOT NULL,
  ma_billpay_appid int(10) unsigned NOT NULL,
  customer_mobile_number varchar(12) NOT NULL,
  billpay_ref_id varchar(100) DEFAULT NULL,
  biller_input varchar(1024) DEFAULT NULL,
  biller_response varchar(1024) DEFAULT NULL,
  cron_settlement tinyint(4) NOT NULL DEFAULT '0' COMMENT '1-Settlement By Cron ',
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ma_billpay_transactionid),
  UNIQUE KEY billpay_transaction_id (billpay_transaction_id),
  KEY ma_billpay_gateway_id (ma_billpay_gateway_id),
  KEY ma_billpay_provider_id (ma_billpay_provider_id),
  KEY ma_billpay_appid (ma_billpay_appid),
  KEY ma_transaction_master_id (ma_transaction_master_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- 2020-05-12 06:08:24
