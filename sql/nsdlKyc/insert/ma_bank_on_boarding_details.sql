INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES
(NULL,	5,	'IS_KYC_AVAILABLE',	'YES',	'[
    {
        "kycType" : "PAN",
        "fields" : [{
            "field": "pannumber",
            "label": "PAN Number",
            "rules": {
                "max": 10,
                "min": 10, 
                "regex": "^([A-Z]){5}([0-9]){4}([A-Z]){1}$",
                "required": true
            },
            "inputype": "text"
        }]
    },
    {
        "kycType" : "BIO",
        "fields" : []
    }
]',	now(),	now());


UPDATE `ma_bank_on_boarding_details` SET `fields_required_json` = '[
    {
        "kycType" : "PAN",
        "fields" : [{
            "field": "pannumber",
            "label": "PAN Number",
            "rules": {
                "max": 10,
                "min": 10, 
                "regex": "^[A-Z]{5}[0-9]{4}[A-Z]{1}$",
                "required": true
            },
            "inputype": "text"
        }]
    },
    {
        "kycType" : "BIO",
        "fields" : []
    }
]'  WHERE ma_bank_on_boarding_id = 5 AND entity_type = 'IS_KYC_AVAILABLE';



