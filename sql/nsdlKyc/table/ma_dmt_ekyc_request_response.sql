CREATE TABLE `ma_dmt_ekyc_request_response` (
  `ma_dmt_ekyc_request_response_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(11)  NULL,
  `userid` int(11)  NULL,
  `mobile_number` varchar(15)  NULL,
  `uic` varchar(15)  NULL,
  `ma_bank_on_boarding_id` int(11)  NULL,
  `request` text  NULL,
  `response` text  NULL,
  `requestId` varchar(40) DEFAULT NULL,
  `kyc_status` ENUM('I','S','F')  NULL DEFAULT 'I' COMMENT '''I''- initiated,''S'' - Success, ''F'' - Fail',
  `addedon` timestamp  NULL DEFAULT current_timestamp(),
  `updatedon` timestamp  NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`), INDEX `requestId` (`requestId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;