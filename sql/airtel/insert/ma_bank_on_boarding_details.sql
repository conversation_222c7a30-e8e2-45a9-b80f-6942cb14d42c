INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES
(NULL,	10,	'SENDER',	'YES',	'{}',	now(),	now()),
(NULL,	10,	'BENEFICIARY',	'YES',	'{}',	now(),	now()),
(NULL,	10,	'REFUND',	'NO',	'{}',	now(),	now()),
(NULL,	10,	'DEL_BENEFICIARY',	'NO',	'{}',	now(),	now()),
(NULL,	10,	'PREVALIDATE',	'YES',	'{}',	now(),	now()),
(NULL,	10,	'AUTO_ADD_BENE',	'YES',	'{}',	now(),	now()),
(NULL,	10,	'REFUND',	'NO',	'{}',	now(),	now()),
(NULL,	10,	'IS_BENEMOBILE_MANDATORY',	'YES',	'{}',	now(),	now()),
(NULL,	10,	'AUTO_ADD_REMITTER',	'NO',	'{}',	now(),	now()),
(NULL,    10,    'PRE_CUSTLOGIN',    'YES',    '[{\"field\": \"firstName\",\"label\": \"First Name\", \"rules\": {\"max\": 100, \"min\": 5, \"regex\": \"/^[a-zA-Z. \']{1,100}$/\", \"required\": true}, \"inputype\": \"text\"}, {\"field\": \"lastName\", \"label\": \"Last Name\", \"rules\": {\"max\": 100, \"min\": 5, \"regex\": \"/^[a-zA-Z. \']{1,100}$/\", \"required\": true}, \"inputype\": \"text\"}]',    now(),    now());


INSERT INTO `ma_bank_on_boarding_details` (`ma_bank_master_details_id`, `ma_bank_on_boarding_id`, `entity_type`, `otp_required`, `fields_required_json`, `addedon`, `updatedon`) VALUES
(NULL,	5,	'PRE_CUSTLOGIN',	'NO',	'{}',	now(),	now()),
(NULL,	9,	'PRE_CUSTLOGIN',	'NO',	'{}',	now(),	now()),
(NULL,	5,	'AUTO_ADD_BENE',	'NO',	'{}',	now(),	now()),
(NULL,	9,	'AUTO_ADD_BENE',	'NO',	'{}',	now(),	now());
(NULL,	5,	'IS_BENEMOBILE_MANDATORY',	'NO',	'{}',	now(),	now()),
(NULL,	9,	'IS_BENEMOBILE_MANDATORY',	'NO',	'{}',	now(),	now()),
(NULL,	5,	'AUTO_ADD_REMITTER',	'YES',	'{}',	now(),	now()),
(NULL,	9,	'AUTO_ADD_REMITTER',	'NO',	'{}',	now(),	now());
