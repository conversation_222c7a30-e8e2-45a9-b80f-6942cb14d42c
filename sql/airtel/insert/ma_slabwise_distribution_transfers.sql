INSERT INTO ma_slabwise_distribution_transfers (ma_slabwise_distribution_transfers_id,state_master_id,ma_user_id,ma_bank_on_boarding_id,customer_charges,customer_charges_applied_type,rt_share,rt_share_applied_type,rt_operative_amount,min_amount,max_amount,record_status,addedon,updatedon,settings_flag)
SELECT NULL,state_master_id,ma_user_id,10,customer_charges,customer_charges_applied_type,rt_share,rt_share_applied_type,rt_operative_amount,min_amount,max_amount,record_status,CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP(),settings_flag
FROM ma_slabwise_distribution_transfers as t2
WHERE t2.ma_bank_on_boarding_id = 5;