:: AIRTEL DMT MIGRATION NOTE ::
A) Downtime :: 3 hrs After 10 pm : Make 
   > Update ma_notices set notice_flag = 'Y' where displayType = '3'   
    

B) Take Back Up Of Tables 
    call createTableBackUp('ma_bank_on_boarding_details','backup');
    call createTableBackUp('ma_bank_type','backup');
    call createTableBackUp('ma_beneficiary_bank_mapping','backup');
    call createTableBackUp('ma_customer_details','backup');
    call createTableBackUp('ma_otp_master','backup');
    call createTableBackUp('ma_slabwise_distribution_transfers','backup');
    call createTableBackUp('ma_transaction_master_details','backup');
    call createTableBackUp('ma_transfers','backup');
    call createTableBackUp('ma_transfers_shares','backup');
    call createTableBackUp('ma_bene_shares','backup');


C) DB Migrations :: Refer : > sql/airtel/ folders from Source Code


1) Alters ALL Tables from sql/airtel/Alters folders

sql\airtel\Alter\dmt_session.sql
sql\airtel\Alter\ma_bank_on_boarding_details.sql
sql\airtel\Alter\ma_bank_on_boarding.sql
sql\airtel\Alter\ma_otp_master.sql

2) Insert entries from > sql/airtel/insert folder Need to Run Once Only !!!
sql\airtel\insert\ma_bank_on_boarding_details.sql
sql\airtel\insert\ma_bank_on_boarding.sql
sql\airtel\insert\ma_bank_type.sql
sql\airtel\insert\ma_bene_shares.sql
sql\airtel\insert\ma_kyc_limits.sql
sql\airtel\insert\ma_slabwise_distribution_transfers.sql
sql\airtel\insert\ma_transfers_shares.sql
sql\airtel\insert\ma_user_on_boarding_bank_mapping.sql

D) Optional -- In case of Rollback Scenario

    call restoreTableBackUp('ma_bank_on_boarding_details','backup');
    call restoreTableBackUp('ma_bank_type','backup');
    call restoreTableBackUp('ma_beneficiary_bank_mapping','backup');
    call restoreTableBackUp('ma_customer_details','backup');
    call restoreTableBackUp('ma_otp_master','backup');
    call restoreTableBackUp('ma_slabwise_distribution_transfers','backup');
    call restoreTableBackUp('ma_transaction_master_details','backup');
    call restoreTableBackUp('ma_transfers','backup');
    call restoreTableBackUp('ma_transfers_shares','backup');
    call restoreTableBackUp('ma_bene_shares','backup');

E) Npm Install 
    npm install match-all@1.2.6 