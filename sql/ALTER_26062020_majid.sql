ALTER TABLE `ma_transaction_master`
ADD `parent_id` varchar(50) NOT NULL DEFAULT '' COMMENT 'Parent order id for refund',
ADD `is_refund` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Refunded flag' AFTER `parent_id`;

ALTER TABLE `ma_points_ledger_master`
ADD `parent_id` varchar(50) NOT NULL DEFAULT '';

ALTER TABLE `ma_cash_ledger_master`
ADD `parent_id` varchar(50) NOT NULL DEFAULT '';

ALTER TABLE `ma_points_account`
ADD `parent_id` varchar(50) NOT NULL DEFAULT '';

ALTER TABLE `ma_cash_account`
ADD `parent_id` varchar(50) NOT NULL DEFAULT '';

ALTER TABLE `ma_transaction_master`
ADD `parent_transaction_master_id` int(10) NOT NULL DEFAULT '0' COMMENT 'Parent transaction id for refund';

ALTER TABLE `ma_cash_ledger_master`
ADD `parent_transaction_master_id` int(10) NOT NULL DEFAULT '0';

ALTER TABLE `ma_points_ledger_master`
ADD `parent_transaction_master_id` int(10) NOT NULL DEFAULT '0';

ALTER TABLE `ma_cash_account`
ADD `parent_transaction_master_id` int(10) NOT NULL DEFAULT '0';

ALTER TABLE `ma_points_account`
ADD `parent_transaction_master_id` int(10) NOT NULL DEFAULT '0';

ALTER TABLE `ma_orderwise_taxes`
ADD `parent_id` varchar(50) NOT NULL DEFAULT '',
ADD `parent_transaction_master_id` int(10) NOT NULL DEFAULT '0' AFTER `parent_id`;


/*----------------------------ma_transaction_master-------------------------*/
-- ma_transaction_master parent_id update
UPDATE ma_transaction_master
SET parent_id = COALESCE(
  IF(
    SUBSTRING( aggregator_order_id, ( ( LENGTH(aggregator_order_id) - LOCATE( '-',REVERSE( aggregator_order_id ) ) ) + 2 ) ) = '',
    aggregator_order_id,
    SUBSTRING( aggregator_order_id, ( ( LENGTH(aggregator_order_id) - LOCATE( '-',REVERSE( aggregator_order_id ) ) ) + 2 ) )
  ),
  ''
);

-- ma_transaction_master is_refund update
UPDATE ma_transaction_master AS parent
JOIN ma_transaction_master AS child ON  parent.aggregator_order_id = child.parent_id
SET parent.is_refund = '1'
WHERE child.transaction_status = 'R';

-- ma_transaction_master parent_transaction_master_id update
UPDATE ma_transaction_master AS parent
JOIN ma_transaction_master AS child ON  parent.aggregator_order_id = child.parent_id
SET child.parent_transaction_master_id = parent.ma_transaction_master_id
WHERE child.transaction_status IN ('R','REV');


/*----------------------------ma_points_ledger_master-------------------------*/

-- Point ledger parent_id update
UPDATE ma_points_ledger_master
SET parent_id = COALESCE(
  IF(
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) ) = '',
    orderid,
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) )
  ),
  ''
);

-- Point ledger parent_transaction_master_id update
UPDATE ma_points_ledger_master AS a
JOIN ma_transaction_master AS b ON a.parent_id = b.aggregator_order_id 
SET a.parent_transaction_master_id = b.ma_transaction_master_id


/*----------------------------ma_points_account-------------------------*/

-- Points account parent_id update
UPDATE ma_points_account
SET parent_id = COALESCE(
  IF(
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) ) = '',
    orderid,
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) )
  ),
  ''
);

-- Points account parent_transaction_master_id update
UPDATE ma_points_account AS a
JOIN ma_transaction_master AS b ON a.parent_id = b.aggregator_order_id 
SET a.parent_transaction_master_id = b.ma_transaction_master_id


/*----------------------------ma_cash_ledger_master-------------------------*/

-- Cash ledger parent_id update
UPDATE ma_cash_ledger_master
SET parent_id = COALESCE(
  IF(
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) ) = '',
    orderid,
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) )
  ),
  ''
);

-- Cash ledger parent_transaction_master_id update
UPDATE ma_cash_ledger_master AS a
JOIN ma_transaction_master AS b ON a.parent_id = b.aggregator_order_id 
SET a.parent_transaction_master_id = b.ma_transaction_master_id


/*----------------------------ma_cash_account-------------------------*/

-- Cash account parent_id update
UPDATE ma_cash_account
SET parent_id = COALESCE(
  IF(
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) ) = '',
    orderid,
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) )
  ),
  ''
);

-- Cash account parent_transaction_master_id update
UPDATE ma_cash_account AS a
JOIN ma_transaction_master AS b ON a.parent_id = b.aggregator_order_id 
SET a.parent_transaction_master_id = b.ma_transaction_master_id


/*----------------------------ma_orderwise_taxes-------------------------*/

-- Orderwise Taxes parent_id update
UPDATE ma_orderwise_taxes
SET parent_id = COALESCE(
  IF(
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) ) = '',
    orderid,
    SUBSTRING( orderid, ( ( LENGTH(orderid) - LOCATE( '-',REVERSE( orderid ) ) ) + 2 ) )
  ),
  ''
);

-- Orderwise Taxes parent_transaction_master_id update
UPDATE ma_orderwise_taxes AS a
JOIN ma_transaction_master AS b ON a.parent_id = b.aggregator_order_id 
SET a.parent_transaction_master_id = b.ma_transaction_master_id


/*----------------------------AEPS LTS-------------------------*/

-- SET LATEST AEPS FOR LTS
/* UPDATE ma_transaction_master
SET addedon = CURRENT_TIMESTAMP,
transaction_status = 'P'
WHERE transaction_type = 5 
AND ma_user_id = '28552'
ORDER BY ma_transaction_master_id DESC 
LIMIT 1 */


