CREATE TABLE `ma_pos_transaction_logs` (
  `ma_pos_transaction_logs_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(10) DEFAULT NULL,
  `airpay_id` varchar(50) DEFAULT NULL,
  `order_id` varchar(50) DEFAULT NULL,
  `pos_type` enum('SALE','BE') DEFAULT NULL,
  `amount` decimal(12,4) DEFAULT NULL,
  `receipt_data` text,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_pos_transaction_logs_id`),
  UNIQUE KEY `airpay_id` (`airpay_id`),
  KEY `pos_key_idx` (`ma_user_id`,`airpay_id`,`order_id`,`pos_type`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=latin1