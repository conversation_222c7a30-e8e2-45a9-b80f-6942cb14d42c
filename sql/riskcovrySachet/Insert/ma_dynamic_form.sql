INSERT INTO ma_dynamic_forms
(channel_name, form_type, api_params, isActive)
VALUES('Riskcovry Sachet', 'insured_form', '[{"title":"Insured Details","formid":"2","child_id":"0","fields":[{"label":"Insured Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"insured_title","isRequired":"true"},{"label":"Insured First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"insured_first_name","isRequired":"true"},{"label":"Insured Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"insured_last_name","isRequired":"true"},{"label":"Insured Phone Number","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":10,"min":10,"regex":"^[6-9]{1}[0-9]{9}$"},"postKey":"insured_mobile","isRequired":"true"},{"label":"Insured email","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":10,"regex":"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.]+[.][a-zA-Z]{2,6}$"},"postKey":"insured_email","isRequired":"true"},{"label":"Insured Aadhar Card","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":12,"min":12,"regex":"^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$"},"postKey":"insured_aadhar","isRequired":"false"},{"label":"Insured DOB","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":55,"min":18,"from_range":-55,"to_range":-18,"regex":"^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"insured_dob","isRequired":"true"},{"label":"Insured Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"Other"}],"postKey":"insured_gender","isRequired":"true"},{"label":"Insured Address Line 1","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":1,"regex":"^[A-Za-z0-9-() ,''/]+$"},"postKey":"insured_address_line_one","isRequired":"true"},{"label":"Insured Address Line 2","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":1,"regex":"^[A-Za-z0-9-() ,''/]+$"},"postKey":"insured_address_line_two","isRequired":"true"},{"label":"Insured City","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":30,"min":1,"regex":"^[A-Za-z ,''-()/]+$"},"postKey":"insured_city","isRequired":"true"},{"label":"Insured State","value":"","type":"DD","ddvalue":[{"key":"Andaman and Nicobar Islands","value":"Andaman and Nicobar Islands"},{"key":"Arunachal Pradesh","value":"Arunachal Pradesh"},{"key":"Andhra Pradesh","value":"Andhra Pradesh"},{"key":"Assam","value":"Assam"},{"key":"Bihar","value":"Bihar"},{"key":"Chandigarh","value":"Chandigarh"},{"key":"Chhattisgarh","value":"Chhattisgarh"},{"key":"Dadra and Nagar Haveli","value":"Dadra and Nagar Haveli"},{"key":"Daman and Diu","value":"Daman and Diu"},{"key":"Delhi","value":"Delhi"},{"key":"Goa","value":"Goa"},{"key":"Gujarat","value":"Gujarat"},{"key":"Haryana","value":"Haryana"},{"key":"Himachal Pradesh","value":"Himachal Pradesh"},{"key":"Jammu and Kashmir","value":"Jammu and Kashmir"},{"key":"Jharkhand","value":"Jharkhand"},{"key":"Karnataka","value":"Karnataka"},{"key":"Kerala","value":"Kerala"},{"key":"Lakshadweep","value":"Lakshadweep"},{"key":"Madhya Pradesh","value":"Madhya Pradesh"},{"key":"Maharashtra","value":"Maharashtra"},{"key":"Manipur","value":"Manipur"},{"key":"Meghalaya","value":"Meghalaya"},{"key":"Mizoram","value":"Mizoram"},{"key":"Nagaland","value":"Nagaland"},{"key":"Odisha","value":"Odisha"},{"key":"Pondicherry","value":"Pondicherry"},{"key":"Punjab","value":"Punjab"},{"key":"Rajasthan","value":"Rajasthan"},{"key":"Sikkim","value":"Sikkim"},{"key":"Tamil Nadu","value":"Tamil Nadu"},{"key":"Telangana","value":"Telangana"},{"key":"Tripura","value":"Tripura"},{"key":"Uttar Pradesh","value":"Uttar Pradesh"},{"key":"Uttarakhand","value":"Uttarakhand"},{"key":"West Bengal","value":"West Bengal"}],"isEditable":true,"isVisible":true,"validation":{"max":30,"min":1,"regex":"^[A-Za-z ]+$"},"postKey":"insured_state","isRequired":"true"},{"label":"Insured Zipcode","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":6,"min":6,"regex":"^[1-9][0-9]{5}$"},"postKey":"insured_pincode","isRequired":"true"}]},{"title":"Nominee Details","formid":"3","child_id":"2","fields":[{"label":"Nominee Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":5,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"nominee_title","isRequired":"false"},{"label":"Nominee First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"nominee_firstname","isRequired":"false"},{"label":"Nominee Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"nominee_lastname","isRequired":"false"},{"label":"Nominee Phone Number","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":10,"min":10,"regex":"^[6-9]{1}[0-9]{9}$"},"postKey":"nominee_mobile","isRequired":"false"},{"label":"Nominee email","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":10,"regex":"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.]+[.][a-zA-Z]{2,6}$"},"postKey":"nominee_email","isRequired":"false"},{"label":"Nominee Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"Other"}],"postKey":"nominee_gender","isRequired":"false"},{"label":"Nominee Relation","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":8,"min":3,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"mother","value":"Mother"},{"key":"father","value":"Father"},{"key":"sister","value":"Sister"},{"key":"brother","value":"Brother"},{"key":"husband","value":"Husband"},{"key":"wife","value":"Wife"},{"key":"son","value":"Son"},{"key":"daughter","value":"Daughter"}],"postKey":"nominee_relation","isRequired":"false"},{"label":"Nominee DOB","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":100,"min":18,"from_range":-100,"to_range":-18,"regex":"^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"nominee_dob","isRequired":"false"}]}]', 'Y');

INSERT INTO ma_dynamic_forms
(channel_name, form_type, api_params, isActive)
VALUES('Riskcovry Sachet', 'customer form', '[{"title":"Proposer Details","formid":"1","child_id":"0","fields":[{"label":"Proposer Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":5,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"proposer_title","isRequired":"true"},{"label":"Proposer First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"proposer_first_name","isRequired":"true"},{"label":"Proposer Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"proposer_last_name","isRequired":"true"},{"label":"Proposer Phone Number","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":10,"min":10,"regex":"^[6-9]{1}[0-9]{9}$"},"postKey":"proposer_mobile","isRequired":"true"},{"label":"Proposer email","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":10,"regex":"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.]+[.][a-zA-Z]{2,6}$"},"postKey":"proposer_email","isRequired":"true"},{"label":"Proposer Aadhar Card","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":12,"min":12,"regex":"^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$"},"postKey":"proposer_aadhar","isRequired":"true"},{"label":"Proposer DOB","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":55,"min":18,"regex":"^^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"proposer_dob","isRequired":"true"},{"label":"Proposer Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"other"}],"postKey":"proposer_gender","isRequired":"true"}]},{"title":"Insured Details","formid":"2","child_id":"0","fields":[{"label":"Insured details same as proposer","value":"false","type":"check_box","isEditable":true,"isVisible":true,"validation":null,"postKey":"check_box","isRequired":"true","action_rule":{"action":"copy","where":"form_id","form_id":"1"}},{"label":"Insured Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"insured_title","parentPostKey":"proposer_title","isRequired":"true"},{"label":"Insured First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"insured_first_name","parentPostKey":"proposer_first_name","isRequired":"true"},{"label":"Insured Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"insured_last_name","parentPostKey":"proposer_last_name","isRequired":"true"},{"label":"Insured Phone Number","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":10,"min":10,"regex":"^[6-9]{1}[0-9]{9}$"},"postKey":"insured_mobile","parentPostKey":"proposer_mobile","isRequired":"true"},{"label":"Insured email","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":10,"regex":"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.]+[.][a-zA-Z]{2,6}$"},"postKey":"insured_email","parentPostKey":"proposer_email","isRequired":"true"},{"label":"Insured Aadhar Card","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":12,"min":12,"regex":"^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$"},"postKey":"insured_aadhar","parentPostKey":"proposer_aadhar","isRequired":"false"},{"label":"Insured DOB","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":55,"min":18,"from_range":-55,"to_range":-18,"regex":"^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"insured_dob","parentPostKey":"proposer_dob","isRequired":"true"},{"label":"Insured Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"other"}],"postKey":"insured_gender","parentPostKey":"proposer_gender","isRequired":"true"},{"label":"Insured Address Line 1","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":1,"regex":"^[A-Za-z0-9-() ,''/]+$"},"postKey":"insured_address_line_one","parentPostKey":"","isRequired":"true"},{"label":"Insured Address Line 2","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":1,"regex":"^[A-Za-z0-9-() ,''/]+$"},"postKey":"insured_address_line_two","parentPostKey":"","isRequired":"true"},{"label":"Insured City","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":30,"min":1,"regex":"^[A-Za-z ,''-()/]+$"},"postKey":"insured_city","parentPostKey":"","isRequired":"true"},{"label":"Insured State","value":"","type":"DD","ddvalue":[{"key":"Andaman and Nicobar Islands","value":"Andaman and Nicobar Islands"},{"key":"Arunachal Pradesh","value":"Arunachal Pradesh"},{"key":"Andhra Pradesh","value":"Andhra Pradesh"},{"key":"Assam","value":"Assam"},{"key":"Bihar","value":"Bihar"},{"key":"Chandigarh","value":"Chandigarh"},{"key":"Chhattisgarh","value":"Chhattisgarh"},{"key":"Dadra and Nagar Haveli","value":"Dadra and Nagar Haveli"},{"key":"Daman and Diu","value":"Daman and Diu"},{"key":"Delhi","value":"Delhi"},{"key":"Goa","value":"Goa"},{"key":"Gujarat","value":"Gujarat"},{"key":"Haryana","value":"Haryana"},{"key":"Himachal Pradesh","value":"Himachal Pradesh"},{"key":"Jammu and Kashmir","value":"Jammu and Kashmir"},{"key":"Jharkhand","value":"Jharkhand"},{"key":"Karnataka","value":"Karnataka"},{"key":"Kerala","value":"Kerala"},{"key":"Lakshadweep","value":"Lakshadweep"},{"key":"Madhya Pradesh","value":"Madhya Pradesh"},{"key":"Maharashtra","value":"Maharashtra"},{"key":"Manipur","value":"Manipur"},{"key":"Meghalaya","value":"Meghalaya"},{"key":"Mizoram","value":"Mizoram"},{"key":"Nagaland","value":"Nagaland"},{"key":"Odisha","value":"Odisha"},{"key":"Pondicherry","value":"Pondicherry"},{"key":"Punjab","value":"Punjab"},{"key":"Rajasthan","value":"Rajasthan"},{"key":"Sikkim","value":"Sikkim"},{"key":"Tamil Nadu","value":"Tamil Nadu"},{"key":"Telangana","value":"Telangana"},{"key":"Tripura","value":"Tripura"},{"key":"Uttar Pradesh","value":"Uttar Pradesh"},{"key":"Uttarakhand","value":"Uttarakhand"},{"key":"West Bengal","value":"West Bengal"}],"isEditable":true,"isVisible":true,"validation":{"max":30,"min":1,"regex":"^[A-Za-z ]+$"},"postKey":"insured_state","parentPostKey":"","isRequired":"true"},{"label":"Insured Zipcode","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":6,"min":6,"regex":"^[1-9][0-9]{5}$"},"postKey":"insured_pincode","parentPostKey":"","isRequired":"true"},{"label":"Relation with Proposer","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":8,"min":3,"regex":"^(self|mother|father|sister|brother|husband|wife|son|daughter|other)$"},"ddvalue":[{"key":"self","value":"self"},{"key":"mother","value":"mother"},{"key":"father","value":"father"},{"key":"sister","value":"sister"},{"key":"brother","value":"brother"},{"key":"husband","value":"husband"},{"key":"wife","value":"wife"},{"key":"son","value":"son"},{"key":"daughter","value":"daughter"},{"key":"other","value":"other"}],"postKey":"relation_with_proposer","isRequired":"true"}]},{"title":"Nominee Details","formid":"3","child_id":"2","fields":[{"label":"Nominee Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":5,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"nominee_title","isRequired":"true"},{"label":"Nominee First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"nominee_firstname","isRequired":"false"},{"label":"Nominee Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"nominee_lastname","isRequired":"false"},{"label":"Nominee Phone Number","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":10,"min":10,"regex":"^[6-9]{1}[0-9]{9}$"},"postKey":"nominee_mobile","isRequired":"false"},{"label":"Nominee email","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":10,"regex":"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.]+[.][a-zA-Z]{2,6}$"},"postKey":"nominee_email","isRequired":"false"},{"label":"Nominee Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"Other"}],"postKey":"nominee_gender","isRequired":"false"},{"label":"Nominee Relation","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":8,"min":3,"regex":"^(mother|father|sister|brother|husband|wife|son|daughter|other)$"},"ddvalue":[{"key":"mother","value":"mother"},{"key":"father","value":"father"},{"key":"sister","value":"sister"},{"key":"brother","value":"brother"},{"key":"husband","value":"husband"},{"key":"wife","value":"wife"},{"key":"son","value":"son"},{"key":"daughter","value":"daughter"},{"key":"other","value":"Other"}],"postKey":"nominee_relation","isRequired":"false"},{"label":"Nominee DOB","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":100,"min":18,"from_range":-100,"to_range":-18,"regex":"^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"nominee_dob","isRequired":"false"}]}]', 'Y');

