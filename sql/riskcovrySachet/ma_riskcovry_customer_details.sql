CREATE TABLE `ma_riskcovry_customer_details` (
  `ma_riskcovry_customer_id` int NOT NULL AUTO_INCREMENT,
  `ma_user_id` int NOT NULL,
  `userid` int NOT NULL,
  `orderid` varchar(25) NOT NULL,
  `proposer_title` enum('Mr','Mrs','Ms') NOT NULL,
  `proposer_first_name` varchar(255) NOT NULL,
  `proposer_last_name` varchar(255) NOT NULL,
  `proposer_dob` varchar(15) DEFAULT NULL,
  `proposer_gender` enum('male','female','other') DEFAULT NULL,
  `proposer_mobile` varchar(15) NOT NULL,
  `proposer_email` varchar(255) NOT NULL,
  `proposer_aadhar` varchar(25) DEFAULT NULL,
  `insured_title` enum('Mr','Mrs','Ms') NOT NULL,
  `insured_first_name` varchar(255) NOT NULL,
  `insured_last_name` varchar(255) NOT NULL,
  `insured_dob` varchar(15) DEFAULT NULL,
  `insured_gender` enum('male','female','other') DEFAULT NULL,
  `insured_mobile` varchar(15) NOT NULL,
  `insured_email` varchar(100) NOT NULL,
  `insured_aadhar` varchar(25) DEFAULT NULL,
  `insured_address_line_one` varchar(255) NOT NULL,
  `insured_address_line_two` varchar(255) DEFAULT NULL,
  `insured_city` varchar(50) DEFAULT NULL,
  `insured_state` varchar(50) DEFAULT NULL,
  `insured_pincode` int DEFAULT NULL,
  `relation_with_proposer` varchar(50) DEFAULT NULL,
  `nominee_title` enum('Mr','Mrs','Ms') DEFAULT NULL,
  `nominee_firstname` varchar(255) DEFAULT NULL,
  `nominee_lastname` varchar(255) DEFAULT NULL,
  `nominee_dob` varchar(15) DEFAULT NULL,
  `nominee_gender` enum('male','female','other') DEFAULT NULL,
  `nominee_mobile` varchar(15) DEFAULT NULL,
  `nominee_email` varchar(100) DEFAULT NULL,
  `nominee_relation` varchar(50) DEFAULT NULL,
  `policy_number` varchar(25) DEFAULT NULL,
  `policy_name` varchar(50) DEFAULT NULL,
  `policy_category_code` varchar(25) DEFAULT NULL,
  `amount` decimal(12,4) DEFAULT NULL,
  `expiry` date DEFAULT NULL,
  `coi` text,
  `source` varchar(25) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_riskcovry_customer_id`),
  KEY `idx_ma_user_id_userid` (`ma_user_id`,`userid`),
  KEY `idx_insured_mobile_policy_number` (`insured_mobile`,`policy_number`),
  KEY `idx_orderid` (`orderid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1