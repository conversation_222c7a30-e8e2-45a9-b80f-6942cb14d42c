CREATE TABLE `ma_dynamic_forms` (
  `ma_dynamic_forms_id` int unsigned NOT NULL AUTO_INCREMENT,
  `channel_name` varchar(25) DEFAULT NULL,
  `form_type` varchar(25) DEFAULT NULL,
  `api_params` longtext,
  `isActive` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Y-Yes,N-No',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_dynamic_forms_id`),
  <PERSON><PERSON>Y `addedon` (`addedon`),
  KEY `idx_channel_form_id` (`ma_dynamic_forms_id`,`channel_name`),
  KEY `idx_channel_form_type` (`channel_name`,`form_type`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1