ALTER TABLE `ma_insurance_category_master`
ADD COLUMN `riskcovry_sachet` enum('true','false') DEFAULT 'false' COMMENT 'true- riskcovry sachet product, false - not a riskcovry sachet product',
ADD INDEX `riskcovry_sachet_idx` (`riskcovry_sachet`, `ma_insurance_category_master_id`);

ALTER TABLE `ma_insurance_category_master`
ADD COLUMN `amount ` int(11) DEFAULT 0 COMMENT 'Premium amount of a particular policy plan',
ADD INDEX `amount_idx` (`amount`, `ma_insurance_category_master_id`);

ALTER TABLE `ma_insurance_category_master`
ADD COLUMN `expiry_period ` varchar(25) DEFAULT NULL COMMENT 'Plan validity of a particular policy plan',
ADD INDEX `expiry_idx` (`expiry_period`, `ma_insurance_category_master_id`);

alter table `ma_insurance_category_master`
add column `policy_wordings` text COMMENT 'Policy_wordings document URL of a particular policy plan';

ALTER table `ma_insurance_category_master`
add column `is_proposer_details_required` enum('true', 'false') DEFAULT 'false' COMMENT 'true - Proposer details form is required, false - Proposer details form is not required, only insured details form is enough';

ALTER TABLE `ma_insurance_category_master`
ADD COLUMN `additional_info` text COMMENT 'Will contain additional info such as Plan Features, Terms & conditions about a particular policy plan ';

alter table `ma_insurance_category_master`
add column `policy_name` varchar (100) DEFAULT NULL COMMENT 'Will contain the Policy Name to be dispalyed of a policy plan';