CREATE TABLE IF NOT EXISTS `ma_country` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `iso` char(5) NOT NULL,
  `name` varchar(100) NOT NULL,
  `nicename` varchar(100) NOT NULL,
  `iso3` char(10) DEFAULT NULL,
  `numcode` smallint(10) DEFAULT NULL,
  `phonecode` int(10) NOT NULL,
  `country_status` varchar(10) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active, N-Inactive',
  `priority` smallint(10) NOT NULL DEFAULT 1 COMMENT '1-Highest, 0-Lowest',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `country_key_namex` (`name`),
  KEY `country_key_phonecodex` (`phonecode`),
  KEY `country_key_country_statusx` (`country_status`),
  KEY `country_key_priorityx` (`priority`)
) ENGINE=InnoDB  DEFAULT CHARSET=latin1;