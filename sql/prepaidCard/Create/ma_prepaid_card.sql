CREATE TABLE IF NOT EXISTS `ma_prepaid_card` (
  `ma_prepaid_card_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(10) DEFAULT NULL,
  `userid` int(10) DEFAULT NULL,
  `program_type_id` int(10) NULL,
  `title` varchar(10) DEFAULT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `mobile_code` varchar(10) DEFAULT NULL,
  `mobile_no` varchar(20) DEFAULT NULL,
  `address` varchar(500) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state_id` int(10) DEFAULT NULL,
  `country_id` int(10) DEFAULT NULL,
  `pincode` varchar(10) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,  
  `customer_type` varchar(50) DEFAULT NULL COMMENT 'RESIDENT, TOURIST', 
  `additional_info` longtext DEFAULT NULL,
  `passport_kyc_verify` varchar(5)  DEFAULT 'N' COMMENT 'Y - Yes, N - No',
  `passport_expiry_date` date DEFAULT NULL,
  `passport_no` varchar(50) DEFAULT NULL,
  `passport_front_link` varchar(100) DEFAULT NULL,
  `passport_back_link` varchar(100) DEFAULT NULL,
  `visa_no` varchar(50) DEFAULT NULL,
  `visa_expiry_date` date DEFAULT NULL,
  `visa_front_link` varchar(100) DEFAULT NULL,
  `visa_back_link` varchar(100) DEFAULT NULL,
  `visa_kyc_verify` varchar(5)  DEFAULT 'N' COMMENT 'Y - Yes, N - No',
  `live_photo_link` varchar(100) DEFAULT NULL,  
  `caf_link` varchar(100) DEFAULT NULL,  
  `customer_id` varchar(50) DEFAULT NULL,
  `card_kit_assign` varchar(5) DEFAULT 'F' COMMENT 'P - PASS, F - FAIL',
  `card_kit_fail_reason` varchar(255) DEFAULT NULL,
  `card_kit_no` varchar(100) DEFAULT NULL,
  `ms_customer_id` varchar(75) DEFAULT NULL,
  `customer_card_no` varchar(50) DEFAULT NULL,
  `card_expiry_date` varchar(25) DEFAULT NULL,
  `card_status` varchar(5) DEFAULT 'A' COMMENT 'A - Active, I - Inactive , D - Disable, E - Expired',
  `customer_opt_verify` varchar(5) DEFAULT 'N' COMMENT 'Y - YES, N - NO',
  `agent_opt_verify` varchar(5) DEFAULT 'N' COMMENT 'Y - YES, N - NO',
  `customer_registration` varchar(5) DEFAULT 'F' COMMENT 'P - PASS, F - FAIL',
  `customer_registration_fail_reason` varchar(255) DEFAULT NULL,
  `card_top_up_amount` varchar(100) DEFAULT NULL,
  `mpin_verify` varchar(5) DEFAULT 'F' COMMENT 'P - PASS, F - FAIL',
  `transaction_id` varchar(50) DEFAULT NULL,
  `device_id` varchar(100) DEFAULT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `validity_date` varchar(30) DEFAULT NULL,
  `card_design` varchar(50) DEFAULT NULL,
  `requestor_name` varchar(50) DEFAULT NULL,
  `reference_id` varchar(100) DEFAULT NULL,
  `kyc_type` varchar(10) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_prepaid_card_id`),
  KEY `ma_user_idx` (`ma_user_id`),
  KEY `first_namex` (`first_name`),
  KEY `last_namex` (`last_name`),
  KEY `mobile_nox` (`mobile_no`),
  KEY `emailx` (`email`),
  KEY `customer_typex` (`customer_type`),
  KEY `customer_idx` (`customer_id`),
  KEY `card_statusx` (`card_status`),
  KEY `ma_prepaid_card_customer_card_no_IDX` (`customer_card_no`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=latin1;
