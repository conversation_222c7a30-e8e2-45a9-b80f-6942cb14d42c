CREATE TABLE IF NOT EXISTS `ma_prepaid_card_program_master` (
  `ma_prepaid_card_program_master_id` int(11) NOT NULL AUTO_INCREMENT,
  `program_name` varchar(100) NOT NULL,
  `program_status` varchar(10) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active, N-Inactive',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_prepaid_card_program_master_id`),
  KEY `program_key_namex` (`program_name`),
  <PERSON><PERSON>Y `program_key_statuscodex` (`program_status`)
) ENGINE=InnoDB  DEFAULT CHARSET=latin1;

INSERT INTO merchantapp_qc.ma_prepaid_card_program_master (program_name)
	VALUES ('Goa Government');
INSERT INTO merchantapp_qc.ma_prepaid_card_program_master (program_name)
	VALUES ('Seva Money');
