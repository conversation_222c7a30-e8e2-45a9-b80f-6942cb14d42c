INSERT INTO ma_dynamic_forms (channel_name,form_type,api_params,isActive) VALUES ('Prepaid Card','prepaid_card_form','[{"label":"Select Program","isEditable":true,"isVisible":true,"isRequired":true,"value":"","validation":{"max":20,"min":2,"regex":"^[a-zA-Z ]+$"},"type":"DD","postKey":"program_type","ddvalue":[]},{"label":"Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":5,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"title","isRequired":"true"},{"label":"First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"first_name","isRequired":"true"},{"label":"Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"last_name","isRequired":"true"},{"label":"Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"other"}],"postKey":"gender","isRequired":"true"},{"label":"DOB","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":55,"min":18,"from_range": -55,"to_range": -18,"regex":"^^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"dob","isRequired":"true"},{"label":"Mobile Number","isEditable":true,"isVisible":true,"isRequired":true,"code":"+91","value":"","validation":{"max":20,"min":2,"regex":"^\\d{5,15}$"},"type":"DD_mobile","postKey":"mobile_code","ddvalue":[]},{"label":"Residing Country","isEditable":true,"isVisible":true,"isRequired":true,"ddvalue":[],"validation":{"max":35,"min":2,"regex":"^[A-Za-z ]+$"},"type":"DD","postKey":"country"},{"label":"Email","isEditable":"true","value": "","isVisible":true,"postKey":"email","isRequired":"true","type":"text","validation":{"max":"50","min":"10","regex":"^[a-z0-9.-_]+[@][a-z0-9-_]+[.][a-z0-9.]{2,5}$"}}]','Y');


INSERT INTO ma_dynamic_forms (channel_name,form_type,api_params,isActive,addedon,updatedon) VALUES
	 ('Resident Card','resident_card_form','[{"label":"Select Program","isEditable":true,"isVisible":true,"isRequired":true,"value":"","validation":{"max":20,"min":2,"regex":"^[a-zA-Z ]+$"},"type":"DD","postKey":"program_type","ddvalue":[]},{"label":"Title","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":5,"min":2,"regex":"^(Mrs|Mr|Ms)$"},"ddvalue":[{"key":"Mrs","value":"Mrs"},{"key":"Mr","value":"Mr"},{"key":"Ms","value":"Ms"}],"postKey":"title","isRequired":"true"},{"label":"First Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"first_name","isRequired":"true"},{"label":"Last Name","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":15,"min":1,"regex":"^[A-Za-z ,.''-]+$"},"postKey":"last_name","isRequired":"true"},{"label":"Gender","value":"","type":"DD","isEditable":true,"isVisible":true,"validation":{"max":6,"min":4,"regex":"^[a-zA-Z ]+$"},"ddvalue":[{"key":"male","value":"Male"},{"key":"female","value":"Female"},{"key":"other","value":"other"}],"postKey":"gender","isRequired":"true"},{"label":"Date of Birth","value":"","type":"date","isEditable":true,"isVisible":true,"validation":{"max":55,"min":18,"from_range": -55,"to_range": -18,"regex":"^^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$"},"postKey":"dob","isRequired":"true"},{"label":"Mobile Number","isEditable":true,"isVisible":true,"isRequired":true,"value":"","code":"+91","validation":{"max":20,"min":2,"regex":"^\\d{5,15}$"},"type":"DD_mobile","postKey":"mobile_code","ddvalue":[]},{"label":"Email ID","isEditable":"true","value": "","isVisible":true,"postKey":"email","isRequired":"true","type":"text","validation":{"max":"50","min":"10","regex":"^[a-z0-9.-_]+[@][a-z0-9-_]+[.][a-z0-9.]{2,5}$"}},{"label":"Address","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":50,"min":1,"regex":"^(?! )[A-Za-z ,''-]*(?<! )$"},"postKey":"address","isRequired":"true"},{"label":"City","value":"","type":"text","isEditable":true,"isVisible":true,"validation":{"max":30,"min":1,"regex":"^(?! )[A-Za-z ,''-]*(?<! )$"},"postKey":"city","isRequired":"true"},{"label":"State","value":"","type":"DD","ddvalue":[{"key":"Andaman and Nicobar Islands","value":"Andaman and Nicobar Islands"},{"key":"Arunachal Pradesh","value":"Arunachal Pradesh"},{"key":"Andhra Pradesh","value":"Andhra Pradesh"},{"key":"Assam","value":"Assam"},{"key":"Bihar","value":"Bihar"},{"key":"Chandigarh","value":"Chandigarh"},{"key":"Chhattisgarh","value":"Chhattisgarh"},{"key":"Dadra and Nagar Haveli","value":"Dadra and Nagar Haveli"},{"key":"Daman and Diu","value":"Daman and Diu"},{"key":"Delhi","value":"Delhi"},{"key":"Goa","value":"Goa"},{"key":"Gujarat","value":"Gujarat"},{"key":"Haryana","value":"Haryana"},{"key":"Himachal Pradesh","value":"Himachal Pradesh"},{"key":"Jammu and Kashmir","value":"Jammu and Kashmir"},{"key":"Jharkhand","value":"Jharkhand"},{"key":"Karnataka","value":"Karnataka"},{"key":"Kerala","value":"Kerala"},{"key":"Lakshadweep","value":"Lakshadweep"},{"key":"Madhya Pradesh","value":"Madhya Pradesh"},{"key":"Maharashtra","value":"Maharashtra"},{"key":"Manipur","value":"Manipur"},{"key":"Meghalaya","value":"Meghalaya"},{"key":"Mizoram","value":"Mizoram"},{"key":"Nagaland","value":"Nagaland"},{"key":"Odisha","value":"Odisha"},{"key":"Pondicherry","value":"Pondicherry"},{"key":"Punjab","value":"Punjab"},{"key":"Rajasthan","value":"Rajasthan"},{"key":"Sikkim","value":"Sikkim"},{"key":"Tamil Nadu","value":"Tamil Nadu"},{"key":"Telangana","value":"Telangana"},{"key":"Tripura","value":"Tripura"},{"key":"Uttar Pradesh","value":"Uttar Pradesh"},{"key":"Uttarakhand","value":"Uttarakhand"},{"key":"West Bengal","value":"West Bengal"}],"isEditable":true,"isVisible":true,"validation":{"max":30,"min":1,"regex":"^[A-Za-z ]+$"},"postKey":"state","isRequired":"true"},{"label":"Pincode","value":"","type":"Number","isEditable":true,"isVisible":true,"validation":{"max":6,"min":6,"regex":"^[1-9][0-9]{5}$"},"postKey":"pincode","isRequired":"true"}]','Y','2024-02-19 20:47:28','2024-03-18 13:24:27');