ALTER TABLE merchantapp_qc.ma_transaction_type_master MODIFY COLUMN transaction_type_description varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('59', 'Prepaid Card Transaction', 'PREPAID_CARD', 'PREPAID CARD TRANSACTION', 'YES', 'TRANSACTION_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('60', 'Prepaid Card Incentive', '<PERSON>EPAID_CARD_INCENTIVE', 'PREPAID CARD INCENTIVE', 'YES', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('61', 'Prepaid Card Issuance FEES', 'PREPAID_ISSUANCE_FEES', 'PREPAID CARD ISSUANCE FEES', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('62', 'Prepaid Card GST Charges', 'PREPAID_CARD_GST_CHARGES', 'PREPAID CARD GST CHARGES', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('63', 'Prepaid Card Transaction Charges', 'PREPAID_CARD_TRANSACTION_CHARGES', 'PREPAID CARD TRANSACTION CHARGES', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());