CREATE TABLE `ma_prepaid_card_topup` (
  `ma_prepaid_card_topup_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(10) DEFAULT NULL,
  `userid` int(10) DEFAULT NULL,
  `customer_id` varchar(50) DEFAULT NULL,
  `card_top_up_amount` varchar(100) DEFAULT NULL,
  `mpin_verify` varchar(5) DEFAULT NULL COMMENT 'P - PASS, F - FAIL',
  `aggregator_order_id` varchar(50) DEFAULT NULL,
  `transaction_status` varchar(5) DEFAULT NULL COMMENT 'S-SUCCESS, P-PENDING, F-FAIL, I-INITIATDE',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_prepaid_card_topup_id`),
  <PERSON>EY `ma_user_idx` (`ma_user_id`),
  KEY `order_idx` (`aggregator_order_id`),
  <PERSON><PERSON>Y `customer_idx` (`customer_id`),
  KEY `txn_status` (`transaction_status`)
) ENGINE=InnoDB  DEFAULT CHARSET=latin1;