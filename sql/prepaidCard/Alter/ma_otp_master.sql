ALTER TABLE `ma_otp_master`
CHANGE `otp_type` 
`otp_type` enum('CO','BBPS','BE','BT','MN','KYC','REF','CW','BA','SP','DR','IU','CMS','GOLD','DBE','POS','RC','CR','ONDC','ONDCC','ONDCORDERHISTORYOTP','ONDCORDERRETURNOTP','BAAZAARHISTORYOTP','BGJR','FMT','FMTCO','FMTBEN','FMTREFUND','PCCUSTREGOTP','PCMERREGOTP') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration, IU - Integration User, CMS - Cash Management System, GOLD - Dvara Smart Gold, DBE - Delete beneficiary, POS - POS Activation, RC - Request Credit,ONDC - Open Network for Digital Commerce, ONDCC - Open Network for Digital Commerce Cancellation OTP,BGJR - BrainGym Jr, FMT - FMT Transaction,FMTCO - FMT Customer,FMTBEN - FMT BENEFICIARY,FMTREFUND - FMT REFUND, PCCUSTREGOTP - Prepaid Card Customer Register Otp, PCMERREGOTP - Prepaid Card Merchant Register Otp';

ALTER TABLE merchantapp_qc.ma_otp_master MODIFY COLUMN otp_type enum('CO','BBPS','BE','BT','MN','KYC','REF','CW','BA','SP','DR','IU','CMS','GOLD','DBE','POS','RC','CR','ONDC','ONDCC','ONDCORDERHISTORYOTP','ONDCORDERRETURNOTP','BAAZAARHISTORYOTP','BGJR','FMT','FMTCO','FMTBEN','FMTREFUND','PCCUSTREGOTP','PCMERREGOTP','PCEMAILOTP') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration, IU - Integration User, CMS - Cash Management System, GOLD - Dvara Smart Gold, DBE - Delete beneficiary, POS - POS Activation, RC - Request Credit,ONDC - Open Network for Digital Commerce, ONDCC - Open Network for Digital Commerce Cancellation OTP,BGJR - BrainGym Jr, FMT - FMT Transaction,FMTCO - FMT Customer,FMTBEN - FMT BENEFICIARY,FMTREFUND - FMT REFUND, PCCUSTREGOTP - Prepaid Card Customer Register Otp, PCMERREGOTP - Prepaid Card Merchant Register Otp, PCEMAILOTP - Prepaid Card Customer Email Otp';
