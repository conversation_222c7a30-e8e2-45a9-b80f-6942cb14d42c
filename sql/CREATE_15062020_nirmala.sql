CREATE TABLE ma_orderwise_taxes (
  ma_orderwise_taxes_id int(11) NOT NULL AUTO_INCREMENT,
  transaction_type enum('4','8','9','16','18','19') NOT NULL,
  ma_transfers_id int(11) DEFAULT NULL,
  orderid varchar(50) DEFAULT NULL,
  ma_user_id int(11) NOT NULL,
  amount decimal(12,4) DEFAULT '0.0000',
  gst_amount decimal(12,4) DEFAULT '0.0000',
  tds_amount decimal(12,4) DEFAULT '0.0000',
  ma_status enum('S','R') DEFAULT NULL,
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ma_orderwise_taxes_id)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;