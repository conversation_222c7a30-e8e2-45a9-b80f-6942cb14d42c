/*Table structure for table `ma_billpayment_utility_providers` */
DROP TABLE IF EXISTS `ma_billpayment_utility_providers`;

CREATE TABLE `ma_billpayment_utility_providers` (
  `ma_billpayment_utility_provider_id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_name` varchar(100) DEFAULT NULL,
  `provider_key` varbinary(10) DEFAULT NULL,
  `web_icon` varchar(200) DEFAULT NULL,
  `app_icon` varchar(200) DEFAULT NULL,
  `createdon` timestamp NOT NULL DEFAULT current_timestamp(),
  `updatedon` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`ma_billpayment_utility_provider_id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4;

/*Table structure for table `ma_channels` */
DROP TABLE IF EXISTS `ma_channels`;

CREATE TABLE `ma_channels` (
  `ma_channels_id` int(11) NOT NULL AUTO_INCREMENT,
  `channel_name` enum('AEPS','DMT','COLLECTMONEY','BBPS','INSURANCE','WITHDRAWAL','CASHINONLINE','CASHINEVALUE','FASTAG','GOLD','UPI','UPIQR','CMS','KHATA') NOT NULL,
  `web_icon` varchar(200) DEFAULT NULL,
  `app_icon` varchar(200) DEFAULT NULL,
  `display_name` varchar(100) DEFAULT NULL,
  `priority` int(10) DEFAULT NULL,
  `status` enum('1','0') DEFAULT '1',
  `addedon` timestamp NOT NULL DEFAULT current_timestamp(),
  `updatedon` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`ma_channels_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;