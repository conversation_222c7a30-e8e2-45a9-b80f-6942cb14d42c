ALTER TABLE `ma_transaction_master`
CHANGE `transaction_type` `transaction_type` enum('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49','50','51') COLLATE 'latin1_swedish_ci' NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - <PERSON>lectMoney 15- Insurance 16 - topup incentive,17 -Recharge,18-Recharge Incentive ,19 - Insurance Incentive,20 - POS,21 - Beneficiary validation,22 -Beneficiary validation Incentive' AFTER `transaction_status`;

INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`, `deleted_at`)
VALUES ('200001', 'transaction_type', '20', 'Beneficiary Validation', now(), now(), NULL);

INSERT INTO `system_codes` (`code_id`, `code_name`, `code_val`, `code_desc`, `created_at`, `updated_at`, `deleted_at`)
VALUES ('200001', 'transaction_type', '22', 'Beneficiary Validation Incentive', now(), now(), NULL);


ALTER TABLE `ma_bank_on_boarding`
ADD `bene_charges` decimal(12,4) NULL AFTER `bank_charges`; // 2.5
/*// ma_transaction_master added karna bank_charges added to keep to note
*/ma_transaction_master =>  bank_charges

ALTER TABLE `ma_bank_on_boarding`
ADD `bene_verification` enum('Y','N') NULL DEFAULT 'N' AFTER `bene_charges`;

ALTER TABLE `ma_user_on_boarding_bank_mapping`
ADD `bene_verification` enum('Y','N') COLLATE 'utf8_general_ci' NOT NULL DEFAULT 'N' COMMENT 'Beneficiary verification Required' AFTER `updatedon`;

ALTER TABLE `ma_beneficiaries`
ADD `ma_bene_verification_id` int(11) NULL DEFAULT '0' COMMENT 'Beneficiary verification' AFTER `receiver_id`;


ALTER TABLE `ma_commission_master` CHANGE `ma_commission_type` `ma_commission_type` ENUM('1','2','3','4','5','6','7','8','9','10','18','15','19','22') CHARSET latin1 COLLATE latin1_swedish_ci NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 15-Insurance,18-Recharge Incentive ,19 - Insurance Incentive,22-BeneValidation Incentive'; 

 

INSERT INTO `ma_commission_master` (`ma_commission_type`, `ma_commission_percentage`, `ma_deduction_type`, `applied_type`) VALUES ('22', '20', '1','2'); 
INSERT INTO `ma_commission_master` (`ma_commission_type`, `ma_commission_percentage`, `ma_deduction_type`, `applied_type`) VALUES ('22', '3.75', '3', '2'); 

 

ALTER TABLE `ma_points_ledger_master` CHANGE `transaction_type` `transaction_type` ENUM('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive,17 -Recharge, 18- Recharge Incentives, 19- insurance Incentives, 20 - POS, 21- Benevalidation, 22- Benevalidation incentive'; 


UPDATE ma_allow_withdrawal SET transaction_type='1,3,2,4,12,13,14,11,16,21,22' WHERE wallet_type='1';



ALTER TABLE `ma_cash_ledger_master` CHANGE `transaction_type` `transaction_type` ENUM('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','20','21','22') CHARSET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '1 - topup, 2 - transfers, 3- sendMoney, 4- Commissionearned, 5- AEPS, 6- BBPS, 7- Points to Cash, 8- AEPS Incentive 9- BBPS Incentive 10 - CollectMoney, 11 - TDS, 12 - GST,13 - TRF charges,14 - BBPS Surcharge,15 - Insurance,16 - Topup Incentive, 20 - POS,21-BeneValidation Charges,22-BeneValidation Incentive';