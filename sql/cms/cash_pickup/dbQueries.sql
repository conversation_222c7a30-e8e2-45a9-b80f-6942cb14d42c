ALTER TABLE ma_cms_sub_merchants ADD type_flag varchar(15) DEFAULT NULL COMMENT 'CP-Cash Pickup, CD-Cash Drop';


CREATE TABLE `ma_cms_cashpickup_request_master` (
  `ma_cms_cashpickup_request_master_id` int NOT NULL AUTO_INCREMENT,
  `request_id` varchar(100) DEFAULT NULL,
  `partner_id` varchar(100) DEFAULT NULL,
  `company_id` varchar(100) DEFAULT NULL,
  `company_name` varchar(100) DEFAULT NULL,
  `client_code` varchar(100) DEFAULT NULL,
  `client_name` varchar(100) DEFAULT NULL,
  `client_mobile` varchar(100) DEFAULT NULL,
  `client_address` varchar(500) DEFAULT NULL,
  `request_status` varchar(15) DEFAULT NULL COMMENT 'P-Pending, A-Accepted',
  `attended_status` varchar(100) DEFAULT 'N' COMMENT 'Y-Yes, N-No',
  `request_date` varchar(100) DEFAULT NULL,
  `accepted_date` varchar(100) DEFAULT NULL,
  `ma_user_id` int DEFAULT NULL,
  `userid` int DEFAULT NULL,
  `call_no` varchar(100) DEFAULT NULL,
  `crn_no` varchar(100) DEFAULT NULL,
  `child_crn_no` varchar(100) DEFAULT NULL,
  `sl_no` varchar(100) DEFAULT NULL,
  `updated_client_name` varchar(100) DEFAULT NULL,
  `updated_client_mobile` varchar(100) DEFAULT NULL,
  `freeze_flag` varchar(15) DEFAULT NULL COMMENT 'Y-Yes, N-No',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `beat_account_details` text,
  `decrypted_response` text,
  PRIMARY KEY (`ma_cms_cashpickup_request_master_id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_client_code` (`client_code`),
  KEY `idx_request_status` (`request_status`),
  KEY `idx_ma_user_id` (`ma_user_id`),
  KEY `idx_userid` (`userid`)
) COMMENT='Used to store cash pickup main request';


CREATE TABLE `ma_cms_cashpickup_assign_list` (
  `ma_cms_cashpickup_assign_list_id` int NOT NULL AUTO_INCREMENT,
  `request_id` varchar(100) DEFAULT NULL,
  `request_date` varchar(100) DEFAULT NULL,
  `partner_id` varchar(100) DEFAULT NULL,
  `request_status` varchar(15) DEFAULT NULL COMMENT 'P-Pending, A-Accepted, R-Rejected, C-Completed, F-Failed',
  `ma_user_id` int DEFAULT NULL,
  `userid` int DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_cms_cashpickup_assign_list_id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_request_status` (`request_status`),
  KEY `idx_ma_user_id` (`ma_user_id`),
  KEY `idx_userid` (`userid`)
) COMMENT='Used to store assigned cash pickup request to different merchants';


CREATE TABLE `ma_cms_cashpickup_transaction_master` (
  `ma_cms_cashpickup_transaction_master_id` int NOT NULL AUTO_INCREMENT,
  `request_id` varchar(100) DEFAULT NULL,
  `ma_user_id` int DEFAULT NULL,
  `userid` int DEFAULT NULL,
  `client_code` varchar(100) DEFAULT NULL,
  `client_name` varchar(100) DEFAULT NULL,
  `client_mobile` varchar(100) DEFAULT NULL,
  `client_address` varchar(500) DEFAULT NULL,
  `partner_id` varchar(100) DEFAULT NULL,
  `company_name` varchar(100) DEFAULT NULL,
  `order_id` varchar(100) DEFAULT NULL,
  `amount` int DEFAULT NULL,
  `transaction_status` varchar(15) DEFAULT NULL COMMENT 'S-Success, F-Failed',
  `reason` varchar(100) DEFAULT NULL,
  `latitude` varchar(100) DEFAULT NULL,
  `longitude` varchar(100) DEFAULT NULL,
  `ip` varchar(100) DEFAULT NULL,
  `nil_cash_flag` varchar(15) DEFAULT NULL COMMENT 'Y-Yes, N-No',
  `retry_count` int(11) DEFAULT 0,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_cms_cashpickup_transaction_master_id`),
  KEY `idx_ma_user_id` (`ma_user_id`),
  KEY `idx_userid` (`userid`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='Used to store cash pickup request transaction record';


CREATE TABLE `ma_cms_cashpickup_reason_list` (
  `ma_cms_cashpickup_reason_list_id` int NOT NULL AUTO_INCREMENT,
  `reason` varchar(100) DEFAULT NULL,
  `status` varchar(15) DEFAULT NULL COMMENT 'A-Active, I-Inactive',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_cms_cashpickup_reason_list_id`)
)  COMMENT='List of reasons to cancel cash pickup request';

INSERT INTO ma_cms_cashpickup_reason_list (reason, status) VALUES('Client Denied', 'A');
INSERT INTO ma_cms_cashpickup_reason_list (reason, status) VALUES('Shop Closed', 'A');
INSERT INTO ma_cms_cashpickup_reason_list (reason, status) VALUES('Customer Unavailable', 'A');
INSERT INTO ma_cms_cashpickup_reason_list (reason, status) VALUES('Skipped', 'A');


CREATE TABLE `ma_cms_cashpickup_denomination` (
  `ma_cms_cashpickup_denomination_id` int NOT NULL AUTO_INCREMENT,
  `request_id` varchar(100) DEFAULT NULL,
  `order_id` varchar(100) DEFAULT NULL,
  `cash_type` varchar(100) DEFAULT NULL,
  `currency` int DEFAULT NULL,
  `unit` int DEFAULT '0',
  `amount` int DEFAULT '0',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_cms_cashpickup_denomination_id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='Used to store cash pickup amount denomination details on each transaction';