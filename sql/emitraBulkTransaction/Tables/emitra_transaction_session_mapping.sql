CREATE TABLE `emitra_transaction_session_mapping`(
    `emitra_transaction_session_mapping_id` INT(11) NOT NULL AUTO_INCREMENT,
    `session_id` VARCHAR(50) NOT NULL,
    `ma_transaction_master_id` INT(11) NOT NULL,
    `aggregator_order_id` VARCHAR(50) NULL,
    `transaction_status` ENUM('I','P','S','F','R') NULL,
     `response` TEXT NOT NULL,
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(), 
    `updatedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(), 
    PRIMARY KEY(`emitra_transaction_session_mapping_id`), KEY `session_id`(`session_id`));