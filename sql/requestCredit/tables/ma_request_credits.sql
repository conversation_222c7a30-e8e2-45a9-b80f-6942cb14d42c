CREATE TABLE `ma_request_credits` (
 `ma_request_credit_id` int(11) NOT NULL AUTO_INCREMENT,
 `ma_user_id` int(11) NOT NULL,
 `amount_requested` decimal(12,4) DEFAULT NULL,
 `amount_approved` decimal(12,4) DEFAULT NULL,
 `mapped_reference_id` varchar(25) DEFAULT NULL,
 `user_type` enum('rt','dt') DEFAULT NULL,
 `request_to` int(11) DEFAULT NULL,
 `requesting_ip` varchar(40) DEFAULT NULL,
 `approved_ip` varchar(40) DEFAULT NULL,
 `request_status` enum('P','A','R','E') DEFAULT NULL COMMENT 'P-Pending, A-Approved, R-Rejected, E-Expired',
 `addedon` timestamp NOT NULL DEFAULT current_timestamp(),
 `updatedon` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
 PRIMARY KEY (`ma_request_credit_id`),
 KEY `request_credits_index` (`ma_user_id`,`mapped_reference_id`,`user_type`,`request_to`,`request_status`,`addedon`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4