CREATE TABLE `ma_fmt_customer_detail`(
    `ma_fmt_customer_detail_id` INT NOT NULL AUTO_INCREMENT,
    `ma_fmt_country_master_id` INT NOT NULL COMMENT 'Refer ma_fmt_country_master Table',
    `ma_user_id` INT NOT NULL,
    `userid` INT NOT NULL,
    `customer_name` VARCHAR(50) NULL DEFAULT NULL,
    `customer_nationality` VARCHAR(50) NULL DEFAULT NULL,
    `customer_status` CHAR(5) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive',
    `mobile_number` VARCHAR(15) NULL DEFAULT NULL,
    `customer_address` TEXT NOT NULL,
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(`ma_fmt_customer_detail_id`),
    INDEX `idx_ma_fmt_country_master_id`(`ma_fmt_country_master_id`),
    INDEX `idx_mobile_number`(`mobile_number`),
    INDEX `idx_customer_status`(`customer_status`)
)COMMENT = 'Foreign Money Transfer Partner provider Remitter Detail';