CREATE TABLE `ma_fmt_country_master` ( 
    `ma_fmt_country_master_id` INT NOT NULL AUTO_INCREMENT , 
    `country_name` VARCHAR(50) NULL DEFAULT NULL , 
    `country_code` VARCHAR(20) NULL DEFAULT NULL , 
    `currency_code` VARCHAR(20) NULL DEFAULT NULL , 
    `country_status` CHAR(10) NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `conversion_inr_rate` DECIMAL(12,4) NULL DEFAULT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `added_by` VARCHAR(45) NULL DEFAULT NULL,
    `updated_by` VARCHAR(45) NULL DEFAULT NULL,  
    PRIMARY KEY (`ma_fmt_country_master_id`), 
    INDEX `idx_country_code` (`country_code`), 
    INDEX `idx_country_status` (`country_status`))
    COMMENT = 'Foreign Money Transfer countries master ';