CREATE TABLE `ma_fmt_ekyc_request_response` ( 
  `ma_fmt_ekyc_request_response_id` INT(50) NOT NULL AUTO_INCREMENT , 
  `ma_user_id` INT(50) NOT NULL , 
  `userid` INT(50) NOT NULL , 
  `mobile_number` VARCHAR(20) NOT NULL , 
  `ma_fmt_provider_master_id` INT(50) NOT NULL COMMENT 'Refer Table ma_fmt_provider_master' , 
  `request` TEXT NOT NULL , 
  `response` TEXT NOT NULL , 
  `requestId` VARCHAR(50) NOT NULL , 
  `aggregator_order_id` VARCHAR(50) NOT NULL , 
  `kyc_status` CHAR(5) NOT NULL DEFAULT 'N' COMMENT 'I-INITIATE,AV-AADHAR CARD VERIFIED,V-VERIFIED' , 
  `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
  `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
  <PERSON><PERSON><PERSON><PERSON>EY (`ma_fmt_ekyc_request_response_id`), 
  INDEX `idx_kyc_status` (`kyc_status`), 
  INDEX `idx_requestId` (`requestId`), 
  INDEX `idx_aggregator_order_id` (`aggregator_order_id`), 
  INDEX `idx_mobile_number` (`mobile_number`), 
  INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`))
  COMMENT = 'Foreign Money Transfer Remitter KYC Request Response';