CREATE TABLE `ma_fmt_provider_detail` ( 
    `ma_fmt_provider_detail_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_fmt_provider_master_id` INT NOT NULL , 
    `fields_required_json` TEXT NULL DEFAULT NULL , 
    `entity_type` VARCHAR(50) NOT NULL , 
    `entity_status` CHAR(5) NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `is_otp_required` CHAR(5) NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `otp_type` VARCHAR(30) NOT NULL COMMENT 'AIRPAY,provider', 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `added_by` VARCHAR(45) NULL DEFAULT NULL,
    `updated_by` VARCHAR(45) NULL DEFAULT NULL,  
    PRIMARY KEY (`ma_fmt_provider_detail_id`), 
    INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`))
    COMMENT = 'Foreign Money Transfer Partner provider Setting';