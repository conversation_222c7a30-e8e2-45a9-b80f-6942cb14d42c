CREATE TABLE `ma_fmt_form_data` ( 
    `ma_fmt_form_data_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_user_id` INT(50) NULL DEFAULT NULL , 
    `userid` INT(50) NULL DEFAULT NULL , 
    `mobile_number` VARCHAR(50) NULL , 
    `session_hash` VARCHAR(50) NULL , 
    `form_data_type` VARCHAR(50) NULL , 
    `form_data` TEXT NULL DEFAULT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    PRIMARY KEY (`ma_fmt_form_data_id`), 
    INDEX `idx_mobile_number` (`mobile_number`), 
    INDEX `idx_session_hash` (`session_hash`), 
    INDEX `idx_form_data_type` (`form_data_type`), 
    INDEX `idx_userid` (`userid`), 
    INDEX `idx_ma_user_id` (`ma_user_id`)) 
    ENGINE = InnoDB COMMENT = 'Foreign Money Form Data';