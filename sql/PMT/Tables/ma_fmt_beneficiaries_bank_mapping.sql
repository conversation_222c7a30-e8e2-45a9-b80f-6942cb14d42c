CREATE TABLE `ma_fmt_beneficiaries_provider_mapping` ( 
    `ma_fmt_beneficiaries_provider_mapping_id` INT NOT NULL AUTO_INCREMENT,  
    `ma_fmt_beneficiaries_id` INT NOT NULL COMMENT 'Refer ma_fmt_beneficiaries table' ,  
    `ma_fmt_provider_payment_mode_id` INT(10) NOT NULL COMMENT 'Refer ma_fmt_provider_mode table' ,  
    `ma_fmt_provider_master_id` INT(10) NOT NULL COMMENT 'Refer ma_fmt_provider_master table' ,  
    `bene_provider_status` CHAR(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive',  
    `bene_provider_reference_id` VARCHAR(10) NOT NULL ,  
    `provider_request` TEXT NULL DEFAULT NULL ,  
    `provider_response` TEXT NULL DEFAULT NULL ,
    `bene_mapping_status` CHAR(5) NOT NULL DEFAULT 'N' COMMENT 'Y-ACTIVE, N-INACTIVE'
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,  
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ma_fmt_beneficiaries_provider_mapping_id`),
    KEY `idx_fmt_beneficiaries_id` (`ma_fmt_beneficiaries_id`),
    KEY `idx_ma_fmt_provider_payment_mode_id` (`ma_fmt_provider_payment_mode_id`),
    KEY `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`),
    KEY `idx_bene_provider_status` (`bene_provider_status`),
    KEY `idx_bene_mapping_status` (`bene_mapping_status`)
    )
    COMMENT = 'Foreign Money Transfer Partner provider Beneficiaries provider Mapping ';