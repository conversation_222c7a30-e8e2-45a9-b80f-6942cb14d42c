CREATE TABLE `ma_fmt_session` ( 
    `ma_fmt_session_id` INT(10) NOT NULL AUTO_INCREMENT , 
    `ma_fmt_provider_master_id` INT(10) NOT NULL COMMENT 'Priority provider ID of ma_fmt_provider_master' , 
    `session_hash` VARCHAR(20) NOT NULL , 
    `session_status` CHAR(5) NOT NULL DEFAULT 'Y' COMMENT 'Y - Yes, N - No' , 
    `ma_user_id` INT(10) NULL DEFAULT NULL , 
    `userid` INT(10) NULL DEFAULT NULL , 
    `provider_priority_json` TEXT NULL DEFAULT NULL , 
    `mobile_number` VARCHAR(10) NULL DEFAULT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    PRIMARY KEY (`ma_fmt_session_id`), 
    INDEX `idx_session_hash` (`session_hash`), 
    INDEX `idx_mobile_number` (`mobile_number`),
    INDEX `idx_ma_user_id` (`ma_user_id`),
    INDEX `idx_userid` (`userid`),
    INDEX `idx_session_status` (`session_status`)
    )
    COMMENT = 'Foreign Money Transfer Merchant Session';