CREATE TABLE `ma_fmt_provider_limit` ( 
    `ma_fmt_provider_limit_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_fmt_provider_master_id` INT NOT NULL COMMENT 'Refer ma_fmt_provider_master table' , 
    `remitter_type` VARCHAR(10) NOT NULL COMMENT 'KYC | NONKYC', 
    `limit_type` VARCHAR(20) NULL DEFAULT NULL , 
    `limit_code` VARCHAR(20) NULL DEFAULT NULL , 
    `limit_status` CHAR(5) NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `per_day_limit` DECIMAL(12,4) NULL DEFAULT NULL , 
    `per_month_limit` DECIMAL(12,4) NULL DEFAULT NULL , 
    `per_yearly_limit` DECIMAL(12,4) NULL DEFAULT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `added_by` VARCHAR(45) NULL DEFAULT NULL,
    `updated_by` VARCHAR(45) NULL DEFAULT NULL,
    PRIMARY KEY (`ma_fmt_provider_limit_id`), 
    INDEX `idx_limit_status` (`limit_status`), 
    INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`))
    COMMENT = 'Foreign Money Transfer Partner provider Mode Limit';