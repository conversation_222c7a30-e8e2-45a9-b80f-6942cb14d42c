CREATE TABLE `ma_fmt_provider_user_onboarding_mapping` ( 
    `ma_fmt_provider_user_onboarding_mapping_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_fmt_provider_master_id` INT NOT NULL COMMENT 'Refer ma_fmt_provider_master table' , 
    `ma_fmt_country_master_id` INT NOT NULL COMMENT 'Refer ma_fmt_country_master Table' , 
    `ma_user_id` INT NULL DEFAULT NULL , 
    `userid` INT NULL DEFAULT NULL , 
    `provider_credential` TEXT NULL DEFAULT NULL , 
    `partner_provider_status` CHAR(5) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `added_by` VARCHAR(45) NULL DEFAULT NULL,
    `updated_by` VARCHAR(45) NULL DEFAULT NULL, 
    PRIMARY KEY (`ma_fmt_provider_user_onboarding_mapping_id`), 
    INDEX `idx_ma_user_id` (`ma_user_id`), 
    INDEX `idx_partner_provider_status` (`partner_provider_status`), 
    INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`), 
    INDEX `idx_ma_fmt_country_master_id` (`ma_fmt_country_master_id`))
    COMMENT = 'Foreign Money Transfer Partner provider Merchant Mapping ';