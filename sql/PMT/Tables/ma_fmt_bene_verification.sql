CREATE TABLE `ma_fmt_bene_verification` (
  `ma_fmt_bene_verification_id` int unsigned NOT NULL AUTO_INCREMENT,
  `ma_transaction_master_id` int NOT NULL COMMENT 'Refer ma_transaction_master table',
  `ma_fmt_provider_master_id` INT(10) NOT NULL COMMENT 'Refer ma_fmt_provider_master table',
  `ma_user_id` int unsigned DEFAULT NULL,
  `userid` int unsigned DEFAULT NULL,
  `mobile_number` VARCHAR(50) NULL DEFAULT NULL,
  `bene_mobile_number` varchar(15) DEFAULT NULL,
  `account_number` varchar(25) NOT NULL,
  `ifsc_code` varchar(25) DEFAULT NULL,
  `validation_amount` decimal(12,4) DEFAULT NULL,
  `bene_verify_status` enum('I','S','F','P') DEFAULT NULL COMMENT 'I-Initiated, S-Success, F-Failed, P-pending',
  `utr_number` varchar(100) DEFAULT '0' COMMENT 'Provider transaction id',
  `request_number` varchar(100) DEFAULT NULL COMMENT 'Request Number sent to Provider',
  `provider_request` json DEFAULT NULL COMMENT 'Provider Request parameter',
  `provider_response` json DEFAULT NULL COMMENT 'Provider API Response',
  `provider_service_charges` decimal(12,4) DEFAULT '0.0000' COMMENT 'provider Service Charges',
  `provider_rrn` varchar(60) DEFAULT '' COMMENT 'provider Transaction Id',
  `provider_benename` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'provider side bene name',
  `provider_gross_amount` decimal(12,4) DEFAULT '0.0000' COMMENT 'provider gross amount',
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_fmt_bene_verification_id`),
  KEY `request_number` (`request_number`),
  KEY `idx_ma_transaction_master_id` (`ma_transaction_master_id`),
  KEY `idx_ma_user_id` (`ma_user_id`),
  KEY `idx_uic` (`uic`),
  KEY `idx_account_number` (`account_number`),
  KEY `idx_ifsc_code` (`ifsc_code`)
);