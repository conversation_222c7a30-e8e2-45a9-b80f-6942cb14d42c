CREATE TABLE `ma_fmt_api_logs` (
  `ma_fmt_api_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `ma_user_id` int(50) DEFAULT NULL,
  `userid` int(50) DEFAULT NULL,
  `mobile_number` varchar(50) DEFAULT NULL,
  `session_hash` varchar(50) DEFAULT NULL,
  `request_type` varchar(50) DEFAULT NULL,
  `request` text,
  `response` text,
  `api_status` int(50) DEFAULT NULL,
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `url` text,
  PRIMARY KEY (`ma_fmt_api_log_id`),
  KEY `idx_mobile_number` (`mobile_number`),
  KEY `idx_session_hash` (`session_hash`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_ma_user_id` (`ma_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Foreign Money Transfer API log'