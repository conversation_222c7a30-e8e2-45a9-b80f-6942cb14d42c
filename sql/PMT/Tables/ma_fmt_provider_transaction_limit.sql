CREATE TABLE `ma_fmt_provider_transaction_limit`(
    `ma_fmt_provider_transaction_limit_id` INT(10) NOT NULL AUTO_INCREMENT,
    `ma_fmt_provider_master_id` INT(10) NOT NULL COMMENT 'refer ma_fmt_provider_master table',
    `ma_fmt_provider_payment_mode_id` INT NOT NULL COMMENT 'refer ma_fmt_provider_payment_modes table',
    `remitter_type` VARCHAR(10) NOT NULL COMMENT 'KYC | NONKYC',
    `transaction_limit_status` CHAR(5) NOT NULL DEFAULT 'Y',
    `min_amount` DECIMAL(12, 4) NULL,
    `max_amount` DECIMAL(12, 4) NULL,
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(`ma_fmt_provider_transaction_limit_id`),
    INDEX `idx_ma_fmt_provider_master_id`(`ma_fmt_provider_master_id`)
) COMMENT = 'Foreign Money Transfer Provider Transaction Limit';