CREATE TABLE `ma_fmt_txn_activity_log` ( 
    `ma_fmt_txn_activity_log_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_fmt_transfers_id` INT NULL, 
    `aggregator_order_id` VARCHAR(30) NULL DEFAULT NULL , 
    `transfer_status` TEXT NULL DEFAULT NULL COMMENT 'I - initiate,H - Hold,A - Approved,PH - Payment Hold,UP - Unpaid,PT - Post,PD - Paid,C - Cancelled,UCD -Unclaimed,R - Refund,S - Success,F - Fail,REV - Reverse,P - Pending', 
    `transaction_status` TEXT NULL DEFAULT NULL COMMENT 'I- initiated,P - Pending,S - Success, F - Fail,R- Refund,REV-Reverse,PS-Partial Success',
    `addedon` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    PRIMARY KEY (`ma_fmt_txn_activity_log_id`), 
    INDEX `idx_ma_fmt_transfers_id` (`ma_fmt_transfers_id`),
    INDEX `idx_aggregator_order_id` (`aggregator_order_id`)
    ) COMMENT = 'FMT Transfer Status History';