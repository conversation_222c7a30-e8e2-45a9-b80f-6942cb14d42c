CREATE TABLE `ma_fmt_provider_payment_modes` ( 
    `ma_fmt_provider_payment_mode_id` INT(10) NOT NULL AUTO_INCREMENT , 
    `ma_fmt_provider_master_id` INT(10) NOT NULL COMMENT 'Refer ma_fmt_provider_master table' , 
    `mode_name` VARCHAR(50) NULL DEFAULT NULL , 
    `mode_code` VARCHAR(10) NULL DEFAULT NULL , 
    `mode_status` CHAR(5) NOT NULL DEFAULT 'Y' , 
    `priority` INT(10) NULL DEFAULT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `added_by` VARCHAR(45) NULL DEFAULT NULL,
    `updated_by` VARCHAR(45) NULL DEFAULT NULL, 
    PRIMARY KEY (`ma_fmt_provider_payment_mode_id`), 
    INDEX `idx_ma_fmt_provider_payment_mode_id` (`ma_fmt_provider_payment_mode_id`))
    COMMENT = 'Foreign Money Transfer Partner provider Payment Mode Setting ';