CREATE TABLE `ma_fmt_provider_master` ( 
    `ma_fmt_provider_master_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_fmt_country_master_id` INT NOT NULL COMMENT 'Refer ma_fmt_country_master table' , 
    `provider_display_name` VARCHAR(100) NULL DEFAULT NULL , 
    `provider_code` VARCHAR(5) NULL DEFAULT NULL , 
    `provider_status` CHAR(5) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `provider_credential` TEXT NULL DEFAULT NULL , 
    `priority` INT(10) NOT NULL ,
    `provider_logo_app_url` VARCHAR(100) NULL DEFAULT NULL , 
    `provider_logo_web_url` VARCHAR(100) NULL DEFAULT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `added_by` VARCHAR(45) NULL DEFAULT NULL,
    `updated_by` VARCHAR(45) NULL DEFAULT NULL, 
    PRIMARY KEY (`ma_fmt_provider_master_id`), 
    INDEX `idx_priority` (`priority`), 
    INDEX `idx_provider_status` (`provider_status`), 
    INDEX `idx_ma_fmt_country_master_id` (`ma_fmt_country_master_id`)) 
    COMMENT = 'Foreign Money Transfer Partner provider master ';