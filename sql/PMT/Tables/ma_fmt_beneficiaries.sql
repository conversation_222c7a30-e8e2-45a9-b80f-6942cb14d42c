CREATE TABLE `ma_fmt_beneficiaries` (
  `ma_fmt_beneficiaries_id` int NOT NULL AUTO_INCREMENT,
  `ma_fmt_customer_detail_id` int NOT NULL COMMENT 'refer ma_fmt_customer_detail table',
  `ma_fmt_country_master_id` int NOT NULL COMMENT 'refer ma_fmt_country_master table',
  `ma_user_id` int DEFAULT NULL,
  `userid` int DEFAULT NULL,
  `mobile_number` varchar(50) DEFAULT NULL,
  `beneficiary_name` varchar(50) DEFAULT NULL,
  `beneficiary_mobile` varchar(10) DEFAULT NULL,
  `beneficiary_status` char(5) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'Y',
  `account_number` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `ifsc_code` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `provider_branch_id` int DEFAULT NULL,
  `addedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_fmt_beneficiaries_id`),
    INDEX `idx_ma_fmt_customer_detail_id`(`ma_fmt_customer_detail_id`),
    INDEX `idx_ma_fmt_country_master_id`(`ma_fmt_country_master_id`),
    INDEX `idx_mobile_number`(`mobile_number`)
    INDEX `idx_account_number`(`account_number`)
    INDEX `idx_bene_name`(`beneficiary_name`)
    INDEX `idx_bene_mobile`(`beneficiary_mobile`)
    INDEX `idx_bene_status`(`beneficiary_status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Foreign Money Transfer Partner provider Remitter Beneficiaries Mapping '