CREATE TABLE `ma_fmt_transfers` ( 
    `ma_fmt_transfers_id` INT(10) NULL AUTO_INCREMENT , 
    `ma_transaction_master_id` INT(10) NULL COMMENT 'refer ma_transaction_master table' , 
    `ma_fmt_provider_master_id` INT(10) NULL COMMENT 'refer ma_fmt_provider_master table' , 
    `ma_fmt_customer_detail_id` INT(10) NULL COMMENT 'refer ma_fmt_customer_detail table' , 
    `ma_fmt_beneficiaries_id` INT(10) NULL COMMENT 'refer ma_fmt_beneficiaries table' , 
    `ma_fmt_provider_payment_mode_id` INT(10) NULL COMMENT 'refer ma_fmt_provider_payment_modes table' ,
    `ma_user_id` INT(10) NULL ,
    `userid` INT(10) NULL , 
    `mobile_number` VARCHAR(50) NULL , 
    `request_number` VARCHAR(50) NULL , 
    `brn` VARCHAR(50) NULL , 
    `rrn` VARCHAR(50) NULL , 
    `provider_request` TEXT NULL , 
    `provider_response` TEXT NULL , 
    `provider_response_message` VARCHAR(50) NULL , 
    `transfer_amount` DECIMAL(12,4) NULL , 
    `provider_service_charges` DECIMAL(12,4) NULL , 
    `provider_gross_amount` DECIMAL(12,4) NULL , 
    `transfer_status` CHAR(5) NULL DEFAULT 'I' COMMENT 'I - initiate,H - Hold,A - Approved,PH - Payment Hold,UP - Unpaid,PT - Post,PD - Paid,C - Cancelled,UCD -Unclaimed,R - Refund,S - Success,F - Fail,REV - Reverse,P - Pending' , 
    `refund_flag` CHAR(5) NULL DEFAULT 'N' COMMENT 'Y - Yes, N - No' , 
    `transfer_remarks` VARCHAR(20) NULL , 
    `exchange_inr_rate` DECIMAL(12,4) NULL ,
    `remittance_reason`VARCHAR(100) NULL,
    `sales_id` INT NULL , 
    `addedon` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP , 
    PRIMARY KEY (`ma_fmt_transfers_id`), 
    INDEX `idx_transfer_status` (`transfer_status`), 
    INDEX `idx_ma_transaction_master_id` (`ma_transaction_master_id`), 
    INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`), 
    INDEX `idx_ma_fmt_customer_detail_id` (`ma_fmt_customer_detail_id`), 
    INDEX `idx_ma_fmt_beneficiaries_id` (`ma_fmt_beneficiaries_id`), 
    INDEX `idx_ma_fmt_provider_payment_mode_id` (`ma_fmt_provider_payment_mode_id`), 
    INDEX `idx_mobile_number` (`mobile_number`), 
    INDEX `idx_ma_user_id` (`ma_user_id`)) COMMENT = 'Foreign Money Transfer Transaction';