CREATE TABLE `ma_fmt_slabwise_distribution` (
  `ma_fmt_slabwise_distribution_id` int NOT NULL AUTO_INCREMENT,
  `state_master_id` int NOT NULL DEFAULT '0',
  `ma_user_id` int NOT NULL DEFAULT '0',
  `ma_fmt_provider_master_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Refer ma_fmt_provider_master table primary ID',
  `ma_fmt_provider_payment_mode_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'Refer ma_fmt_provider_payment_mode table primary ID',
  `transaction_mode` varchar(45) DEFAULT NULL COMMENT 'TRANSFER,BENE_VERIFY,KYC_VERIFY',
  `customer_charges` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `customer_charges_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `provider_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `provider_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `provider_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
  `rt_share` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `rt_applied_type` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => fixed , 2 => percent',
  `rt_operative_amount` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
  `dt_share` decimal(12,4) DEFAULT '0.0000',
  `dt_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed, 2 => percent',
  `dt_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
  `sd_share` decimal(12,4) DEFAULT '0.0000',
  `sd_applied_type` enum('1','2') DEFAULT '2' COMMENT '1 => fixed , 2 => percent',
  `sd_operative_amount` enum('1','2') DEFAULT '1' COMMENT '1 => txn_amount, 2 => customer_charges',
  `customer_type` enum('KYC','NONKYC') DEFAULT 'NONKYC' COMMENT 'KYC => KYC Customer, NONKYC => Non Kyc Customer',
  `min_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `max_amount` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `record_status` enum('Y','N') NOT NULL DEFAULT 'Y',
  `ma_dt_sdt_id` int NOT NULL DEFAULT '0',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `settings_flag` enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=> GENERAL, 2=> GLOBAL',
  `added_by` varchar(45) DEFAULT NULL,
  `updated_by` varchar(45) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`ma_fmt_slabwise_distribution_id`),
  INDEX `idx_ma_user_id` (`ma_user_id`),
  INDEX `idx_customer_type` (`customer_type`),
  INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`)
) COMMENT = 'Foreign Money Transfer Slabwise Distribution';