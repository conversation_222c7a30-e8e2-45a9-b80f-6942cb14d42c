CREATE TABLE `ma_global_setting_fmt`(
    `ma_global_setting_fmt_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `setting_name` VARCHAR(100) CHARACTER SET latin1 NOT NULL,
    `setting_code` VARCHAR(100) CHARACTER SET latin1 NOT NULL,
    `setting_value` VARCHAR(100) CHARACTER SET latin1 NOT NULL,
    `setting_description` VARCHAR(500) CHARACTER SET latin1 NOT NULL,
    `setting_input_type` ENUM('1', '2', '3') CHARACTER SET latin1 NOT NULL COMMENT '1 - SELECT, 2 - TEXT, 3 - TEXTAREA',
    `setting_input_options` VARCHAR(500) CHARACTER SET latin1 NOT NULL,
    `validation_rules` JSON DEFAULT NULL,
    `addedon` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedon` TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(`ma_global_setting_fmt_id`)
) COMMENT = 'Foreign Money Transfer Setting';