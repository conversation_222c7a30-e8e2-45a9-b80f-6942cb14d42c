CREATE TABLE `ma_fmt_customer_detail_provider_mapping` ( 
    `ma_fmt_customer_detail_provider_mapping_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_fmt_customer_detail_id` INT NOT NULL , 
    `ma_fmt_provider_master_id` INT NOT NULL COMMENT 'Refer ma_fmt_provider_master table' , 
    `ma_user_id` INT NOT NULL,
    `userid` INT NOT NULL,
    `customer_onboarding_status` CHAR(5) NOT NULL DEFAULT 'Y' COMMENT 'Y-Active,N-Inactive' , 
    `customer_kyc_status` CHAR(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive' , 
    `customer_provider_status` CHAR(5) NOT NULL DEFAULT 'N' COMMENT 'Y-Active,N-Inactive' , 
    `customer_provider_reference_id` VARCHAR(20) NOT NULL ,
    `customer_data_json` TEXT NULL DEFAULT NULL, 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`ma_fmt_customer_detail_provider_mapping_id`), 
    INDEX `idx_ma_fmt_customer_detail_id` (`ma_fmt_customer_detail_id`), 
    INDEX `idx_ma_fmt_provider_master_id` (`ma_fmt_provider_master_id`))
    COMMENT = 'Foreign Money Transfer Partner provider Remitter provider Mapping ';