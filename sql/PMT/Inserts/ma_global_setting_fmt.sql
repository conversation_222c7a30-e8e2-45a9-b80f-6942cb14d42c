INSERT INTO `ma_global_setting_fmt` (`ma_global_setting_fmt_id`, `setting_name`, `setting_code`, `setting_value`, `setting_description`, `setting_input_type`, `setting_input_options`, `validation_rules`, `addedon`, `updatedon`) VALUES (NULL, 'FMT BANK MODE', 'fmt_bank_mode', 'M', 'This value change the fmt mode multibank list of single bank mode', '1', 'MULTI::M|SINGLE::S', '{\"regex\": null, \"length\": null, \"required\": true}', now(), now());

INSERT INTO `ma_global_setting_fmt` (`ma_global_setting_fmt_id`, `setting_name`, `setting_code`, `setting_value`, `setting_description`, `setting_input_type`, `setting_input_options`, `validation_rules`, `addedon`, `updatedon`) VALUES (NULL, 'FMT BANK MINIMUM AMOUNT LIMIT', 'fmt_bank_single_mode_min_amount_limit', '100', 'This mode allows minimum value to return single bank. If Bank limit less than limit then bank will not return', '2', '', '{\"min\": 100, \"regex\": \"[0-9]+\", \"length\": 8, \"required\": true}', now(), now());

INSERT INTO `ma_global_setting_fmt` (`ma_global_setting_fmt_id`, `setting_name`, `setting_code`, `setting_value`, `setting_description`, `setting_input_type`, `setting_input_options`, `validation_rules`, `addedon`, `updatedon`) VALUES (NULL, 'FMT BANK MINIMUM TRANSFER LIMIT', 'fmt_bank_single_mode_min_transfer_limit', '10', 'This mode allows minimum number transfers to return single bank. If Bank limit less than limit then bank will not return', '2', '', '{\"min\": 100, \"regex\": \"[0-9]+\", \"length\": 8, \"required\": true}', now(), now());