INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('50', 'Foreign Money Transaction', 'FMT', 'FMT TRANSACTION', 'YES', 'TRANSACTION_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('51', 'FMT Incentive', 'FMT_INCENTIVE', 'FMT INCENTIVE', 'YES', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('52', 'FMT Customer Charges', 'FMT_CUSTOMER_CHARGES', 'FMT CUSTOMER CHARGES', 'NO', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('53', 'FMT Bene Validation', 'FMT_BENEFICIARY_VALIDATION', 'FMT BENEFICIARY VALIDATION', 'NO', 'TRANSACTION_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('54', 'FMT Bene Validation Incentive', 'FMT_BENE_VALIDATION_INCENTIVE', 'FMT BENE VALIDATION INCENTIVE', 'YES', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('55', 'FMT KYC Charges', 'FMT_KYC_CHARGES', 'FMT KYC CHARGES', 'YES', 'TRANSACTION_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('56', 'FMT KYC Incentive', 'FMT_KYC_INCENTIVE', 'FMT KYC INCENTIVE', 'YES', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('57', 'FMT GST Charges', 'FMT_GST_CHARGES', 'FMT GST CHARGES', 'YES', 'LEDGER_HISTORY', '1', '1', now(), now());

INSERT INTO `ma_transaction_type_master` (`transaction_type`, `transaction_type_description`, `transaction_type_code`, `display_name`, `incentive_status`, `transaction_history_type`, `added_by`, `updated_by`, `addedon`, `updatedon`) VALUES ('58', 'FMT BANK Charges', 'FMT_BANK_CHARGES', 'FMT BANK CHARGES', 'YES', 'LEDGER_HISTORY', '1', '1', now(), now());
