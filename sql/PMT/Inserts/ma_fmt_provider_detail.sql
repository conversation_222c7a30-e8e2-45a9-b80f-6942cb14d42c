/* REMITTER */
INSERT INTO `ma_fmt_provider_detail` (`ma_fmt_provider_detail_id`, `ma_fmt_provider_master_id`, `fields_required_json`, `entity_type`, `entity_status`, `is_otp_required`, `otp_type`, `addedon`, `updatedon`, `added_by`, `updated_by`) VALUES (NULL, '1', '{\r\n \"title\": \"Remitter Register\",\r\n \"formid\": \"1\",\r\n \"child_id\": \"0\",\r\n \"postKey\": \"registerRegister\",\r\n \"fields\": [\r\n {\r\n \"field\": \"Name\",\r\n \"label\": \"Remitter Name\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 50,\r\n \"min\": 10,\r\n \"regex\": \"^[A-Za-z]$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"beneficiary_name\"\r\n },\r\n {\r\n \"field\": \"Mobile\",\r\n \"label\": \"Mobile\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"mobile_number\"\r\n },\r\n {\r\n \"field\": \"Email\",\r\n \"label\": \"Email\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 50,\r\n \"min\": 10,\r\n \"regex\": \"^[A-Za-z]$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"Email\"\r\n },\r\n {\r\n \"field\": \"Employer\",\r\n \"label\": \"Employer\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 50,\r\n \"min\": 10,\r\n \"regex\": \"^[A-Za-z]$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"Employer\"\r\n },\r\n {\r\n \"field\": \"Dob\",\r\n \"label\": \"Dob\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"dob\"\r\n },\r\n {\r\n \"field\": \"Address\",\r\n \"label\": \"Address\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"Address\"\r\n },\r\n {\r\n \"field\": \"City\",\r\n \"label\": \"City\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"City\"\r\n },\r\n {\r\n \"field\": \"Gender\",\r\n \"label\": \"Gender\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"Gender\",\r\n \"ddvalue\": []\r\n },\r\n {\r\n \"field\": \"IDType\",\r\n \"label\": \"IDType\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"IDType\",\r\n \"ddvalue\": []\r\n },\r\n {\r\n \"field\": \"Address\",\r\n \"label\": \"Address\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"text\",\r\n \"postKey\": \"Address\"\r\n },\r\n {\r\n \"field\": \"Nationality\",\r\n \"label\": \"Nationality\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"Nationality\",\r\n \"ddvalue\": []\r\n },\r\n {\r\n \"field\": \"IncomeSource\",\r\n \"label\": \"IncomeSource\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"IncomeSource\",\r\n \"ddvalue\": []\r\n }\r\n ]\r\n}', 'REMITTER_REGISTRATION', 'Y', 'Y', 'PROVIDER', now(), now(), NULL, NULL);

INSERT INTO `ma_fmt_provider_detail` (`ma_fmt_provider_detail_id`, `ma_fmt_provider_master_id`, `fields_required_json`, `entity_type`, `entity_status`, `is_otp_required`, `otp_type`, `addedon`, `updatedon`, `added_by`, `updated_by`) VALUES (NULL, '1', '{\r\n \"title\": \"Remitter Onboarding\",\r\n \"formid\": \"1\",\r\n \"child_id\": \"0\",\r\n \"postKey\": \"registerOnboarding\",\r\n \"fields\": [\r\n {\r\n \"field\": \"CustomerType\",\r\n \"label\": \"Customer Type\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"CustomerType\",\r\n \"ddvalue\": []\r\n },\r\n {\r\n \"field\": \"SourceIncomeType\",\r\n \"label\": \"Source Income\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"SourceIncomeType\",\r\n \"ddvalue\": []\r\n },\r\n {\r\n \"field\": \"AnnualIncome\",\r\n \"label\": \"Annual Income\",\r\n \"isEditable\": true,\r\n \"isVisible\": true,\r\n \"validation\": {\r\n \"max\": 10,\r\n \"min\": 10,\r\n \"regex\": \"^[0-9]{10}$\",\r\n \"required\": true\r\n },\r\n \"inputtype\": \"DD\",\r\n \"postKey\": \"AnnualIncome\",\r\n \"ddvalue\": []\r\n }\r\n ]\r\n}', 'REMITTER_ONBOARDING', 'Y', 'N', '', now(), now(), NULL, NULL);

INSERT INTO `ma_fmt_provider_detail` (`ma_fmt_provider_detail_id`, `ma_fmt_provider_master_id`, `fields_required_json`, `entity_type`, `entity_status`, `is_otp_required`, `otp_type`, `addedon`, `updatedon`, `added_by`, `updated_by`) VALUES 
(NULL, '1', '', 'IS_KYC_MANDATORY', 'Y', 'N', '', now(), now(), NULL, NULL),
(NULL, '1', '', 'IS_ONBOARDING_MANDATORY', 'Y', 'N', '', now(), now(), NULL, NULL),
(NULL, '1', '', 'REMITTER_PRE_VALIDATE', 'Y', 'N', '', now(), now(), NULL, NULL),
(NULL, '1', '', 'IS_STATIC_DATA_AVAILABLE', 'Y', 'N', '', now(), now(), NULL, NULL),
(NULL, '1', '', 'REMITTER_PRE_KYC_VERIFICATION', 'Y', 'N', '', now(), now(), NULL, NULL);


INSERT INTO ma_fmt_provider_detail
(ma_fmt_provider_detail_id, ma_fmt_provider_master_id, fields_required_json, entity_type, entity_status, is_otp_required, otp_type, addedon, updatedon, added_by, updated_by)
VALUES(NULL, '1', '[{"field":"courier","label":"Courier","isEditable":true,"value":"","isVisible":true,"validation":{"max":50,"min":5,"regex":"^[A-Za-z ,.''-]+$","required":true},
"inputype":"text","postKey":"courier"},{"field":"awb","label":"awb","isEditable":true,"value":"","isVisible":true,"validation":{"max":50,"min":5,"regex":"^[a-zA-Z0-9_]+$","required":true},"inputype":"text","postKey":"awb"},
{"field":"Date of dispatch","label":"Date of dispatch","isEditable":true,"value":"","isVisible":true,"validation":{"regex":"^([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}|[0-9]{2})$","required":true},"inputype":"date","postKey":"date_of_dispatch"},
{"field":"Additional Information","label":"Additional Information","isEditable":true,"isVisible":true,"validation":{"max":100,"min":1,"regex":"^[a-zA-Z0-9_]+$","required":true},"inputype":"text","postKey":"additional_information"}]', 'MERCHANT_ONBOARDING',
 'Y', 'N', 'PROVIDER', now(), now(), NULL, NULL);
