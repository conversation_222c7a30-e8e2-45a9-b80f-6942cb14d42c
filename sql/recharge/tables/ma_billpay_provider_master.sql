CREATE TABLE `ma_billpay_provider_master` (
   `ma_billpay_provider_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `operator_code` varchar(20) NOT NULL,
   `operator_name` varchar(32) NOT NULL,
   `operator_category` enum('1','2','3') NOT NULL COMMENT '1-Prepaid,2-Postpaid,3-DTH',
   `ma_billpay_gateway_id` int(10) unsigned NOT NULL,
   `ma_billpay_appid` int(10) unsigned NOT NULL,
   `provider_status` enum('Y','N') NOT NULL DEFAULT 'N',
   `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (`ma_billpay_provider_id`),
   <PERSON><PERSON>Y `ma_billpay_gateway_id` (`ma_billpay_gateway_id`),
   KEY `ma_billpay_appid` (`ma_billpay_appid`)
 ) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4