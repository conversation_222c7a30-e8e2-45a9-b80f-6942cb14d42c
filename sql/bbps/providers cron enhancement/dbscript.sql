--Cron Enhancement Script

CREATE TABLE `ma_bbps_utility_providers_cron_log` (
  `ma_bbps_utility_providers_cron_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `utility_name` varchar(50) DEFAULT NULL,
  `utility_id` int(11) DEFAULT NULL,
  `page` int(11) DEFAULT '1',
  `providers_data` longtext,
  `is_synced` enum('Y','N') DEFAULT 'N',
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_bbps_utility_providers_cron_log_id`),
  KEY `idx_bbps_utility_providers_cron_log` (`utility_id`)
);

ALTER TABLE ma_bbps_providers_validations DROP FOREIGN KEY fk_ma_bbps_utility_id;

Alter table ma_bbps_providers_validations add providers_data longtext default NULL after action_type;

CREATE INDEX `idx_bbps_providers_name` ON `ma_bbps_providers_validations` (`provider_name`);