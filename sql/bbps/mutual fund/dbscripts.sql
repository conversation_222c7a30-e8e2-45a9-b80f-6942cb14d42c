Alter table ma_bbps_providers_validations add action_type varchar(30) default NULL after is_visible;

CREATE TABLE `ma_bbps_register_bill_details` (
  `ma_bbps_register_bill_details_id` int(11) NOT NULL AUTO_INCREMENT,
  `utility_id` int(11) DEFAULT NULL,
  `biller_master_id` int(11) DEFAULT NULL,
  `provider_id` int(11) DEFAULT NULL,
  `account_id` varchar(50) DEFAULT NULL,
  `status` enum('PAID','UNPAID') DEFAULT 'UNPAID',
  `amount` decimal(12,4) DEFAULT NULL,
  `remark` varchar(200) DEFAULT NULL,
  `bill_due_date` datetime DEFAULT NULL,
  `paid_on` datetime DEFAULT NULL,
  `bill_request` text DEFAULT NULL,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_bbps_register_bill_details_id`),
  KEY `idx_bbps_register_bill_details` (`utility_id`,`biller_master_id`,`account_id`)
);

CREATE TABLE `ma_bbps_new_bill_log` (
  `ma_bbps_new_bill_log_id` int(11) NOT NULL AUTO_INCREMENT,
  `utility_id` int(11) DEFAULT NULL,
  `biller_master_id` int(11) DEFAULT NULL,
  `account_id` varchar(50) DEFAULT NULL,
  `bill_request` text DEFAULT NULL,
  `remark` text DEFAULT NULL,
  `addedon` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedon` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ma_bbps_new_bill_log_id`),
  KEY `idx_bbps_new_bill_log` (`utility_id`,`biller_master_id`,`account_id`)
);