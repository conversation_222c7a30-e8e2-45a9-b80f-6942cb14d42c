ALTER TABLE ma_user_master
ADD security_pin varchar(255) COLLATE 'latin1_swedish_ci' NULL DEFAULT '' AFTER password,
ADD security_pin_attempts smallint(1) NULL DEFAULT '0' AFTER security_pin;

ALTER TABLE `ma_otp_master`
CHANGE `otp_type` `otp_type` enum('CO','BBPS','BE','BT','MN','KYC','REF','CW','BA','SP','DR') COLLATE 'latin1_swedish_ci' NULL COMMENT 'CO - customerOnboard,BBPS - BBPS Txn, BE -beneficiary,BT - bankTransfer,MN-migrateNumber,KYC-Customer kyc,REF-Refund,CW-CashWithdrawal,BA- Bank addition for neft, SP- Security Pin,DR- Device registration' AFTER `updatedon`;