-- POB Integration Alter Scripts
-- Add POB-related columns to existing tables for collect money module
-- Created for collect money module POB functionality

-- Add POB status columns to ma_user_master table (if not exists)
-- This helps in quick lookup of merchant POB status
ALTER TABLE `ma_user_master` 
ADD COLUMN `pob_verification_status` enum('NOT_REQUIRED','PENDING','APPROVED','REJECTED','UNDER_REVIEW') DEFAULT 'NOT_REQUIRED' COMMENT 'POB verification status' AFTER `kyc_status`,
ADD COLUMN `pob_verified_on` timestamp NULL DEFAULT NULL COMMENT 'POB verification completion date' AFTER `pob_verification_status`,
ADD COLUMN `qr_services_enabled` enum('Y','N') DEFAULT 'Y' COMMENT 'QR services enabled status' AFTER `pob_verified_on`,
ADD COLUMN `qr_services_disabled_on` timestamp NULL DEFAULT NULL COMMENT 'QR services disabled date' AFTER `qr_services_enabled`,
ADD COLUMN `pob_grace_period_expires` timestamp NULL DEFAULT NULL COMMENT 'POB grace period expiry date' AFTER `qr_services_disabled_on`;

-- Add indexes for POB columns in ma_user_master
ALTER TABLE `ma_user_master`
ADD INDEX `idx_pob_verification_status` (`pob_verification_status`),
ADD INDEX `idx_qr_services_enabled` (`qr_services_enabled`),
ADD INDEX `idx_pob_grace_period_expires` (`pob_grace_period_expires`);

-- Add POB reference to ma_qr_master table (if not exists)
-- This links QR codes to POB verification status
ALTER TABLE `ma_qr_master`
ADD COLUMN `pob_verification_required` enum('Y','N') DEFAULT 'Y' COMMENT 'Whether POB verification is required for this QR' AFTER `enable_ma_flag`,
ADD COLUMN `pob_verified` enum('Y','N') DEFAULT 'N' COMMENT 'Whether merchant has completed POB verification' AFTER `pob_verification_required`,
ADD COLUMN `qr_disabled_due_to_pob` enum('Y','N') DEFAULT 'N' COMMENT 'Whether QR is disabled due to POB non-compliance' AFTER `pob_verified`,
ADD COLUMN `pob_reminder_sent_count` int(11) DEFAULT 0 COMMENT 'Number of POB reminders sent for this QR' AFTER `qr_disabled_due_to_pob`,
ADD COLUMN `last_pob_reminder_sent` timestamp NULL DEFAULT NULL COMMENT 'Last POB reminder sent date' AFTER `pob_reminder_sent_count`;

-- Add indexes for POB columns in ma_qr_master
ALTER TABLE `ma_qr_master`
ADD INDEX `idx_pob_verification_required` (`pob_verification_required`),
ADD INDEX `idx_pob_verified` (`pob_verified`),
ADD INDEX `idx_qr_disabled_due_to_pob` (`qr_disabled_due_to_pob`);

-- Add POB tracking to ma_transaction_master table (if not exists)
-- This helps track transactions made with/without POB verification
ALTER TABLE `ma_transaction_master`
ADD COLUMN `pob_verified_at_transaction` enum('Y','N') DEFAULT 'N' COMMENT 'Whether merchant had POB verification at time of transaction' AFTER `transaction_status`,
ADD COLUMN `qr_service_type_used` enum('STATIC','DYNAMIC','NONE') DEFAULT 'NONE' COMMENT 'Type of QR service used for transaction' AFTER `pob_verified_at_transaction`;

-- Add indexes for POB columns in ma_transaction_master
ALTER TABLE `ma_transaction_master`
ADD INDEX `idx_pob_verified_at_transaction` (`pob_verified_at_transaction`),
ADD INDEX `idx_qr_service_type_used` (`qr_service_type_used`);

-- Create trigger to update POB status in ma_user_master when verification is approved
DELIMITER $$
CREATE TRIGGER `tr_update_user_pob_status_after_approval`
AFTER UPDATE ON `ma_pob_merchant_verification`
FOR EACH ROW
BEGIN
    -- Update ma_user_master when POB is approved
    IF NEW.pob_approval_status = 'APPROVED' AND OLD.pob_approval_status != 'APPROVED' THEN
        UPDATE `ma_user_master` 
        SET 
            `pob_verification_status` = 'APPROVED',
            `pob_verified_on` = NEW.approved_on,
            `qr_services_enabled` = 'Y',
            `qr_services_disabled_on` = NULL,
            `pob_grace_period_expires` = NULL
        WHERE `profileid` = NEW.ma_user_id;
        
        -- Update QR master records
        UPDATE `ma_qr_master` 
        SET 
            `pob_verified` = 'Y',
            `qr_disabled_due_to_pob` = 'N'
        WHERE `ma_user_id` = NEW.ma_user_id;
    END IF;
    
    -- Update ma_user_master when POB is rejected
    IF NEW.pob_approval_status = 'REJECTED' AND OLD.pob_approval_status != 'REJECTED' THEN
        UPDATE `ma_user_master` 
        SET 
            `pob_verification_status` = 'REJECTED',
            `qr_services_enabled` = 'N',
            `qr_services_disabled_on` = NEW.rejected_on
        WHERE `profileid` = NEW.ma_user_id;
        
        -- Update QR master records
        UPDATE `ma_qr_master` 
        SET 
            `pob_verified` = 'N',
            `qr_disabled_due_to_pob` = 'Y'
        WHERE `ma_user_id` = NEW.ma_user_id;
    END IF;
END$$
DELIMITER ;

-- Create trigger to log POB verification history
DELIMITER $$
CREATE TRIGGER `tr_log_pob_verification_history`
AFTER UPDATE ON `ma_pob_merchant_verification`
FOR EACH ROW
BEGIN
    -- Log status changes in history table
    IF OLD.pob_approval_status != NEW.pob_approval_status OR OLD.activation_status != NEW.activation_status THEN
        INSERT INTO `ma_pob_verification_history` (
            `ma_pob_merchant_verification_id`,
            `ma_user_id`,
            `userid`,
            `previous_status`,
            `new_status`,
            `previous_activation_status`,
            `new_activation_status`,
            `action_type`,
            `remarks`,
            `changed_by_type`
        ) VALUES (
            NEW.ma_pob_merchant_verification_id,
            NEW.ma_user_id,
            NEW.last_status_change_by,
            OLD.pob_approval_status,
            NEW.pob_approval_status,
            OLD.activation_status,
            NEW.activation_status,
            CASE 
                WHEN NEW.pob_approval_status = 'APPROVED' THEN 'APPROVAL'
                WHEN NEW.pob_approval_status = 'REJECTED' THEN 'REJECTION'
                WHEN NEW.activation_status = 'ACTIVE' THEN 'QR_ACTIVATION'
                WHEN NEW.activation_status = 'INACTIVE' THEN 'QR_DEACTIVATION'
                ELSE 'STATUS_CHANGE'
            END,
            NEW.verification_remarks,
            'ADMIN'
        );
    END IF;
END$$
DELIMITER ;
