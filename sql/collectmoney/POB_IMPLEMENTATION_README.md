# POB (Proof of Business) Implementation for Collect Money Module

## Overview
This implementation provides a comprehensive POB (Proof of Business) verification system for the collect money module, specifically designed to control QR services (both static and dynamic) based on merchant business document verification status.

## Database Schema

### Core Tables

#### 1. `ma_pob_merchant_verification`
**Purpose**: Main table for managing merchant POB verification status and business documents.

**Key Fields**:
- `ma_user_id`: Reference to merchant
- `business_document_type`: Type of business document (GST, Trade License, etc.)
- `document_file_path`: S3 path of uploaded document
- `pob_approval_status`: PENDING/APPROVED/REJECTED/UNDER_REVIEW
- `activation_status`: INACTIVE/ACTIVE/SUSPENDED
- `qr_services_enabled`: Whether QR services are enabled
- `static_qr_enabled`: Static QR service status
- `dynamic_qr_enabled`: Dynamic QR service status

#### 2. `ma_pob_verification_history`
**Purpose**: Audit trail for all POB verification status changes.

**Key Fields**:
- `action_type`: Type of action (DOCUMENT_UPLOAD, APPROVAL, REJECTION, etc.)
- `previous_status` / `new_status`: Status change tracking
- `changed_by_type`: MERCHANT/ADMIN/SYSTEM
- `ip_address`: Security tracking

#### 3. `ma_pob_document_master`
**Purpose**: Configuration for different business document types.

**Key Fields**:
- `document_type`: Document identifier (GST_CERTIFICATE, TRADE_LICENSE, etc.)
- `is_mandatory`: Whether document is required
- `allowed_mime_types`: Supported file formats
- `validation_rules`: JSON validation configuration
- `auto_approval_enabled`: Whether auto-approval is allowed

#### 4. `ma_pob_qr_service_config`
**Purpose**: QR service configuration and restrictions.

**Key Fields**:
- `pob_required_for_static_qr`: Whether POB is required for static QR
- `pob_required_for_dynamic_qr`: Whether POB is required for dynamic QR
- `grace_period_days`: Grace period before disabling services
- `max_transaction_amount_without_pob`: Transaction limits without POB

#### 5. `ma_pob_merchant_qr_status`
**Purpose**: Track individual merchant QR service status.

**Key Fields**:
- `static_qr_status`: ENABLED/DISABLED/SUSPENDED
- `dynamic_qr_status`: ENABLED/DISABLED/SUSPENDED
- `grace_period_expires_on`: Grace period expiry
- `qr_generation_blocked`: Whether new QR generation is blocked

## Business Logic Implementation

### POB Verification Flow
1. **Document Upload**: Merchant uploads business documents
2. **Validation**: System validates document type, format, and size
3. **Review**: Admin reviews and approves/rejects documents
4. **QR Service Control**: QR services enabled/disabled based on POB status

### QR Service Control Logic
```sql
-- Check if QR services should be enabled
SELECT 
    CASE 
        WHEN pob_approval_status = 'APPROVED' THEN 'ENABLED'
        WHEN pob_approval_status = 'PENDING' AND grace_period_expires_on > NOW() THEN 'ENABLED'
        ELSE 'DISABLED'
    END as qr_service_status
FROM ma_pob_merchant_verification 
WHERE ma_user_id = ?
```

### Document Types Supported
- **GST_CERTIFICATE**: GST Registration Certificate (Mandatory)
- **TRADE_LICENSE**: Municipal Trade License
- **SHOP_ESTABLISHMENT**: Shop & Establishment License
- **UDYAM_CERTIFICATE**: Udyam Registration for MSMEs
- **MSME_CERTIFICATE**: MSME Registration Certificate
- **PARTNERSHIP_DEED**: Partnership Deed for firms
- **MOA_AOA**: Memorandum and Articles of Association
- **OTHER**: Other valid business documents

## System Codes Configuration

### Key Configuration Codes
- `1200001`: POB Required for Static QR (Y/N)
- `1200002`: POB Required for Dynamic QR (Y/N)
- `1200003`: POB Grace Period Days (default: 30)
- `1200004`: Auto Disable QR Services (Y/N)
- `1200005`: Notification Days Before Disable (7,3,1)

## Integration Points

### Existing Table Modifications
- **ma_user_master**: Added POB status columns
- **ma_qr_master**: Added POB verification flags
- **ma_transaction_master**: Added POB tracking for transactions

### API Integration Points
1. **Document Upload API**: Handle business document uploads
2. **QR Generation API**: Check POB status before generating QR
3. **Transaction API**: Validate POB status during transactions
4. **Admin API**: Approve/reject POB verifications

## Security Features

### Data Protection
- Document encryption in S3
- Audit trail for all changes
- IP address tracking
- User agent logging

### Validation
- Document format validation
- File size limits
- Document number pattern validation
- Duplicate document detection

## Notification System

### Merchant Notifications
- POB reminder notifications
- Grace period expiry warnings
- Approval/rejection notifications
- QR service status changes

### Admin Notifications
- New POB submissions
- Pending verifications
- System alerts

## Reporting and Analytics

### Available Reports
- POB verification status summary
- QR service usage by POB status
- Document type distribution
- Approval/rejection trends

## Installation Instructions

### 1. Create Tables
```bash
# Execute table creation scripts
mysql -u username -p database_name < sql/collectmoney/Tables/ma_pob_merchant_verification.sql
mysql -u username -p database_name < sql/collectmoney/Tables/ma_pob_verification_history.sql
mysql -u username -p database_name < sql/collectmoney/Tables/ma_pob_document_master.sql
mysql -u username -p database_name < sql/collectmoney/Tables/ma_pob_qr_service_config.sql
```

### 2. Insert System Codes
```bash
mysql -u username -p database_name < sql/collectmoney/Inserts/pob_system_codes.sql
```

### 3. Apply Alter Scripts
```bash
mysql -u username -p database_name < sql/collectmoney/Alters/ALTER_POB_INTEGRATION.sql
```

## Configuration

### Environment Variables
- `POB_S3_BUCKET`: S3 bucket for document storage
- `POB_ENCRYPTION_KEY`: Document encryption key
- `POB_MAX_FILE_SIZE`: Maximum file size (default: 5MB)
- `POB_GRACE_PERIOD_DAYS`: Grace period (default: 30 days)

### System Code Updates
Update system codes in `ma_system_codes` table to modify POB behavior:
```sql
UPDATE ma_system_codes SET code_value = 'N' WHERE code = 1200001; -- Disable POB for static QR
UPDATE ma_system_codes SET code_value = '60' WHERE code = 1200003; -- Extend grace period to 60 days
```

## Monitoring and Maintenance

### Regular Tasks
- Monitor POB verification queue
- Clean up expired documents
- Generate compliance reports
- Update document validation rules

### Performance Optimization
- Index optimization for large datasets
- Archive old verification history
- Optimize document storage costs

## Troubleshooting

### Common Issues
1. **QR Services Not Enabling**: Check POB approval status and grace period
2. **Document Upload Failures**: Verify file size and format restrictions
3. **Notification Issues**: Check email/SMS configuration
4. **Performance Issues**: Review index usage and query optimization

### Debug Queries
```sql
-- Check merchant POB status
SELECT * FROM ma_pob_merchant_verification WHERE ma_user_id = ?;

-- Check QR service status
SELECT * FROM ma_pob_merchant_qr_status WHERE ma_user_id = ?;

-- View verification history
SELECT * FROM ma_pob_verification_history WHERE ma_user_id = ? ORDER BY addedon DESC;
```
