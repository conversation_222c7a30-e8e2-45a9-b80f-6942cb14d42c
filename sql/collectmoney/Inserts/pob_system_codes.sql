-- POB System Codes Insert
-- System configuration codes for POB functionality in collect money module
-- Created for collect money module POB functionality

-- Insert POB related system codes
INSERT INTO `ma_system_codes` (`code`, `code_desc`, `code_value`, `code_status`, `addedon`, `updatedon`) VALUES
-- POB Configuration Codes
(1200001, 'POB Required for Static QR', 'Y', 'A', NOW(), NOW()),
(1200002, 'POB Required for Dynamic QR', 'Y', 'A', NOW(), NOW()),
(1200003, 'POB Grace Period Days', '30', 'A', NOW(), NOW()),
(1200004, 'POB Auto Disable QR Services', 'Y', 'A', NOW(), NOW()),
(1200005, 'POB Notification Days Before Disable', '7,3,1', 'A', NOW(), NOW()),
(1200006, 'POB Max File Size MB', '5', 'A', NOW(), NOW()),
(1200007, 'POB Allowed File Types', 'jpg,jpeg,png,pdf', 'A', NOW(), NOW()),
(1200008, 'POB Processing Time Days', '3', 'A', NOW(), NOW()),
(1200009, 'POB Auto Approval Enabled', 'N', 'A', NOW(), NOW()),
(1200010, 'POB Show Reminder in App', 'Y', 'A', NOW(), NOW()),

-- POB Transaction Limits Without Verification
(1200011, 'POB Max Transaction Amount Without Verification', '0', 'A', NOW(), NOW()),
(1200012, 'POB Max Daily Amount Without Verification', '0', 'A', NOW(), NOW()),
(1200013, 'POB Max Monthly Amount Without Verification', '0', 'A', NOW(), NOW()),

-- POB Document Validation
(1200014, 'POB GST Number Validation Pattern', '^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$', 'A', NOW(), NOW()),
(1200015, 'POB Udyam Number Validation Pattern', '^UDYAM-[A-Z]{2}-[0-9]{2}-[0-9]{7}$', 'A', NOW(), NOW()),
(1200016, 'POB Trade License Validation Pattern', '^[A-Z0-9]{6,20}$', 'A', NOW(), NOW()),

-- POB Notification Settings
(1200017, 'POB Reminder Email Template', 'POB_REMINDER_EMAIL', 'A', NOW(), NOW()),
(1200018, 'POB Reminder SMS Template', 'POB_REMINDER_SMS', 'A', NOW(), NOW()),
(1200019, 'POB Approval Email Template', 'POB_APPROVAL_EMAIL', 'A', NOW(), NOW()),
(1200020, 'POB Rejection Email Template', 'POB_REJECTION_EMAIL', 'A', NOW(), NOW()),

-- POB QR Service Control
(1200021, 'POB Allow New QR Generation Without Verification', 'N', 'A', NOW(), NOW()),
(1200022, 'POB QR Service Disable Warning Days', '7', 'A', NOW(), NOW()),
(1200023, 'POB Mandatory Document Types', 'GST_CERTIFICATE', 'A', NOW(), NOW()),
(1200024, 'POB Optional Document Types', 'TRADE_LICENSE,SHOP_ESTABLISHMENT,UDYAM_CERTIFICATE', 'A', NOW(), NOW()),

-- POB Admin Settings
(1200025, 'POB Admin Approval Required', 'Y', 'A', NOW(), NOW()),
(1200026, 'POB Document Retention Days', '2555', 'A', NOW(), NOW()), -- 7 years
(1200027, 'POB Verification History Retention Days', '2555', 'A', NOW(), NOW()), -- 7 years
(1200028, 'POB Enable Document OCR Validation', 'N', 'A', NOW(), NOW()),
(1200029, 'POB Enable Duplicate Document Check', 'Y', 'A', NOW(), NOW()),
(1200030, 'POB Maximum Resubmission Attempts', '3', 'A', NOW(), NOW()),

-- POB Integration Settings
(1200031, 'POB S3 Bucket Name', 'pob-documents', 'A', NOW(), NOW()),
(1200032, 'POB S3 Folder Path', 'merchant-documents/pob/', 'A', NOW(), NOW()),
(1200033, 'POB Document Encryption Enabled', 'Y', 'A', NOW(), NOW()),
(1200034, 'POB API Rate Limit Per Hour', '100', 'A', NOW(), NOW()),
(1200035, 'POB Enable Webhook Notifications', 'Y', 'A', NOW(), NOW()),

-- POB Business Rules
(1200036, 'POB Minimum Business Age Months', '6', 'A', NOW(), NOW()),
(1200037, 'POB Required for Amount Above', '50000', 'A', NOW(), NOW()),
(1200038, 'POB Grace Period for Existing Merchants Days', '60', 'A', NOW(), NOW()),
(1200039, 'POB Enable Risk Based Verification', 'Y', 'A', NOW(), NOW()),
(1200040, 'POB High Risk Document Types Require Manual Review', 'OTHER', 'A', NOW(), NOW()),

-- POB Reporting and Analytics
(1200041, 'POB Enable Analytics Tracking', 'Y', 'A', NOW(), NOW()),
(1200042, 'POB Daily Report Email Recipients', '<EMAIL>', 'A', NOW(), NOW()),
(1200043, 'POB Weekly Report Generation Day', 'MONDAY', 'A', NOW(), NOW()),
(1200044, 'POB Monthly Report Generation Date', '1', 'A', NOW(), NOW()),
(1200045, 'POB Enable Real Time Alerts', 'Y', 'A', NOW(), NOW()),

-- POB Error Messages and Response Codes
(1200046, 'POB Document Upload Failed Message', 'Document upload failed. Please try again.', 'A', NOW(), NOW()),
(1200047, 'POB Invalid Document Type Message', 'Invalid document type. Please upload a valid business document.', 'A', NOW(), NOW()),
(1200048, 'POB File Size Exceeded Message', 'File size exceeds maximum limit. Please upload a smaller file.', 'A', NOW(), NOW()),
(1200049, 'POB QR Services Disabled Message', 'QR services are disabled. Please complete business verification.', 'A', NOW(), NOW()),
(1200050, 'POB Verification Pending Message', 'Your business verification is under review. You will be notified once approved.', 'A', NOW(), NOW());

-- Insert POB related error codes in ma_error_codes table (if exists)
-- INSERT INTO `ma_error_codes` (`error_code`, `error_message`, `error_type`, `is_active`, `addedon`) VALUES
-- (12001, 'POB verification required to access QR services', 'POB_ERROR', 'Y', NOW()),
-- (12002, 'Invalid business document type uploaded', 'POB_ERROR', 'Y', NOW()),
-- (12003, 'Business document verification is pending', 'POB_ERROR', 'Y', NOW()),
-- (12004, 'Business document verification rejected', 'POB_ERROR', 'Y', NOW()),
-- (12005, 'QR services are temporarily disabled', 'POB_ERROR', 'Y', NOW()),
-- (12006, 'Maximum file size exceeded for document upload', 'POB_ERROR', 'Y', NOW()),
-- (12007, 'Unsupported file format for business document', 'POB_ERROR', 'Y', NOW()),
-- (12008, 'Business document number validation failed', 'POB_ERROR', 'Y', NOW()),
-- (12009, 'Duplicate business document already exists', 'POB_ERROR', 'Y', NOW()),
-- (12010, 'Maximum resubmission attempts exceeded', 'POB_ERROR', 'Y', NOW());
