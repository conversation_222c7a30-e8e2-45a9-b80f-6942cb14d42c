-- POB Verification History Table
-- This table maintains audit trail and history of POB verification status changes
-- Created for collect money module POB functionality

CREATE TABLE `ma_pob_verification_history` (
  `ma_pob_verification_history_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_pob_merchant_verification_id` int(11) NOT NULL COMMENT 'Reference to ma_pob_merchant_verification table',
  `ma_user_id` int(11) NOT NULL COMMENT 'Reference to ma_user_master table',
  `userid` int(11) NOT NULL COMMENT 'User ID who made the change',
  `previous_status` enum('PENDING','APPROVED','REJECTED','UNDER_REVIEW') DEFAULT NULL COMMENT 'Previous POB approval status',
  `new_status` enum('PENDING','APPROVED','REJECTED','UNDER_REVIEW') NOT NULL COMMENT 'New POB approval status',
  `previous_activation_status` enum('INACTIVE','ACTIVE','SUSPENDED') DEFAULT NULL COMMENT 'Previous activation status',
  `new_activation_status` enum('INACTIVE','ACTIVE','SUSPENDED') NOT NULL COMMENT 'New activation status',
  `action_type` enum('DOCUMENT_UPLOAD','STATUS_CHANGE','APPROVAL','REJECTION','QR_ACTIVATION','QR_DEACTIVATION','ADMIN_UPDATE') NOT NULL COMMENT 'Type of action performed',
  `remarks` text DEFAULT NULL COMMENT 'Remarks or comments for the action',
  `changed_by_type` enum('MERCHANT','ADMIN','SYSTEM') NOT NULL DEFAULT 'MERCHANT' COMMENT 'Who made the change',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address from where change was made',
  `user_agent` text DEFAULT NULL COMMENT 'User agent string',
  `additional_data` json DEFAULT NULL COMMENT 'Additional data in JSON format',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='POB verification history and audit trail';

-- Add indexes for better performance
ALTER TABLE `ma_pob_verification_history`
ADD INDEX `idx_ma_pob_merchant_verification_id` (`ma_pob_merchant_verification_id`),
ADD INDEX `idx_ma_user_id` (`ma_user_id`),
ADD INDEX `idx_userid` (`userid`),
ADD INDEX `idx_new_status` (`new_status`),
ADD INDEX `idx_action_type` (`action_type`),
ADD INDEX `idx_changed_by_type` (`changed_by_type`),
ADD INDEX `idx_addedon` (`addedon`),
ADD INDEX `idx_user_action` (`ma_user_id`, `action_type`, `addedon`);

-- Add foreign key constraints (optional, based on existing schema design)
-- ALTER TABLE `ma_pob_verification_history`
-- ADD CONSTRAINT `fk_pob_history_verification_id` FOREIGN KEY (`ma_pob_merchant_verification_id`) REFERENCES `ma_pob_merchant_verification` (`ma_pob_merchant_verification_id`) ON DELETE CASCADE ON UPDATE CASCADE,
-- ADD CONSTRAINT `fk_pob_history_ma_user_id` FOREIGN KEY (`ma_user_id`) REFERENCES `ma_user_master` (`profileid`) ON DELETE CASCADE ON UPDATE CASCADE;
