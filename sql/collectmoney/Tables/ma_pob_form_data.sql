-- POB Form Data Table
-- This table stores form data collected from merchants for POB verification
-- Captures data from "Do you want UPI QR services" form and business details page

CREATE TABLE `ma_pob_form_data` (
  `ma_pob_form_data_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_user_id` int(11) NOT NULL COMMENT 'Reference to ma_user_master table',
  `userid` int(11) NOT NULL COMMENT 'User ID who filled the form',
  
  -- QR Services Request Fields
  `wants_upi_qr_services` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Does merchant want UPI QR services',
  `static_qr_requested` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Static QR service requested',
  `dynamic_qr_requested` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Dynamic QR service requested',
  
  -- Business Document Selection
  `selected_business_document` enum('GST_CERTIFICATE','TRADE_LICENSE','SHOP_ESTABLISHMENT','UDYAM_CERTIFICATE','MSME_CERTIFICATE','PARTNERSHIP_DEED','MOA_AOA','OTHER') NOT NULL COMMENT 'Selected business document type',
  `document_file_path` varchar(500) DEFAULT NULL COMMENT 'S3 file path of uploaded document',
  
  -- Business Details Form Fields
  `business_name` varchar(255) NOT NULL COMMENT 'Business name as per document',
  `business_type` enum('PROPRIETORSHIP','PARTNERSHIP','PRIVATE_LIMITED','PUBLIC_LIMITED','LLP','OPC','TRUST','SOCIETY','OTHER') DEFAULT NULL COMMENT 'Type of business entity',
  
  -- Business Address Fields
  `business_address` varchar(255) NOT NULL COMMENT 'Business address',
  `business_city` varchar(100) NOT NULL COMMENT 'Business city',
  `business_state` varchar(100) NOT NULL COMMENT 'Business state',
  `business_pincode` varchar(10) NOT NULL COMMENT 'Business pincode',
  `business_country` varchar(100) NOT NULL DEFAULT 'India' COMMENT 'Business country',
  
  -- Additional Business Information
  `annual_turnover_range` enum('BELOW_40L','40L_TO_2CR','2CR_TO_20CR','20CR_TO_250CR','ABOVE_250CR') DEFAULT NULL COMMENT 'Annual turnover range',
  
  -- Validation and Processing
  `pob_approval_status` enum('PENDING','APPROVED','REJECTED','UNDER_REVIEW') NOT NULL DEFAULT 'PENDING' COMMENT 'POB approval status',
  `activation_status` enum('INACTIVE','ACTIVE','SUSPENDED') NOT NULL DEFAULT 'INACTIVE' COMMENT 'QR services activation status',
    
  -- Timestamps
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='POB form data collection for QR services and business details';