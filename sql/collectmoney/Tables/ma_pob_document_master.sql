-- POB Document Master Table
-- This table manages the configuration of different business document types
-- Created for collect money module POB functionality

CREATE TABLE `ma_pob_document_master` (
  `ma_pob_document_master_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `document_type` varchar(50) NOT NULL UNIQUE COMMENT 'Document type identifier',
  `document_name` varchar(100) NOT NULL COMMENT 'Display name of the document',
  `document_description` text DEFAULT NULL COMMENT 'Description of the document',
  `is_mandatory` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether this document is mandatory',
  `allowed_mime_types` varchar(255) NOT NULL DEFAULT 'image/jpeg,image/png,application/pdf' COMMENT 'Allowed MIME types for upload',
  `max_file_size` int(11) NOT NULL DEFAULT 5242880 COMMENT 'Maximum file size in bytes (default 5MB)',
  `validation_rules` json DEFAULT NULL COMMENT 'Validation rules in JSON format',
  `document_priority` int(11) NOT NULL DEFAULT 1 COMMENT 'Priority order for display',
  `is_active` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Whether this document type is active',
  `requires_number_validation` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether document number validation is required',
  `number_validation_pattern` varchar(255) DEFAULT NULL COMMENT 'Regex pattern for document number validation',
  `help_text` text DEFAULT NULL COMMENT 'Help text to display to merchants',
  `sample_document_url` varchar(500) DEFAULT NULL COMMENT 'URL to sample document for reference',
  `processing_time_days` int(11) DEFAULT 3 COMMENT 'Expected processing time in days',
  `auto_approval_enabled` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether auto-approval is enabled for this document type',
  `created_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who created this configuration',
  `updated_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who last updated this configuration',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Master configuration for POB document types';

-- Add indexes for better performance
ALTER TABLE `ma_pob_document_master`
ADD INDEX `idx_document_type` (`document_type`),
ADD INDEX `idx_is_active` (`is_active`),
ADD INDEX `idx_is_mandatory` (`is_mandatory`),
ADD INDEX `idx_document_priority` (`document_priority`),
ADD INDEX `idx_auto_approval_enabled` (`auto_approval_enabled`),
ADD INDEX `idx_addedon` (`addedon`);

-- Insert default document types
INSERT INTO `ma_pob_document_master` 
(`document_type`, `document_name`, `document_description`, `is_mandatory`, `allowed_mime_types`, `max_file_size`, `document_priority`, `requires_number_validation`, `number_validation_pattern`, `help_text`, `processing_time_days`) 
VALUES
('GST_CERTIFICATE', 'GST Registration Certificate', 'Goods and Services Tax Registration Certificate issued by GST department', 'Y', 'image/jpeg,image/png,application/pdf', 5242880, 1, 'Y', '^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$', 'Upload your GST Registration Certificate. GST number should be clearly visible.', 3),
('TRADE_LICENSE', 'Trade License', 'Trade License issued by local municipal authority', 'N', 'image/jpeg,image/png,application/pdf', 5242880, 2, 'Y', '^[A-Z0-9]{6,20}$', 'Upload your Trade License issued by municipal corporation or local authority.', 3),
('SHOP_ESTABLISHMENT', 'Shop & Establishment License', 'Shop and Establishment License issued by state labor department', 'N', 'image/jpeg,image/png,application/pdf', 5242880, 3, 'Y', '^[A-Z0-9]{6,25}$', 'Upload your Shop & Establishment License. License number should be clearly visible.', 3),
('UDYAM_CERTIFICATE', 'Udyam Registration Certificate', 'Udyam Registration Certificate for MSMEs', 'N', 'image/jpeg,image/png,application/pdf', 5242880, 4, 'Y', '^UDYAM-[A-Z]{2}-[0-9]{2}-[0-9]{7}$', 'Upload your Udyam Registration Certificate. Udyam number should be clearly visible.', 2),
('MSME_CERTIFICATE', 'MSME Registration Certificate', 'Micro, Small and Medium Enterprises Registration Certificate', 'N', 'image/jpeg,image/png,application/pdf', 5242880, 5, 'Y', '^[A-Z0-9]{6,20}$', 'Upload your MSME Registration Certificate.', 3),
('PARTNERSHIP_DEED', 'Partnership Deed', 'Partnership Deed for partnership firms', 'N', 'image/jpeg,image/png,application/pdf', 10485760, 6, 'N', NULL, 'Upload your Partnership Deed document.', 5),
('MOA_AOA', 'MOA/AOA', 'Memorandum of Association and Articles of Association for companies', 'N', 'image/jpeg,image/png,application/pdf', 10485760, 7, 'N', NULL, 'Upload your Memorandum of Association (MOA) and Articles of Association (AOA).', 5),
('OTHER', 'Other Business Document', 'Any other valid business registration document', 'N', 'image/jpeg,image/png,application/pdf', 5242880, 8, 'N', NULL, 'Upload any other valid business registration document with clear business name and registration details.', 7);
