-- POB QR Service Configuration Table
-- This table manages QR service configurations and restrictions based on POB status
-- Created for collect money module POB functionality

CREATE TABLE `ma_pob_qr_service_config` (
  `ma_pob_qr_service_config_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `config_name` varchar(100) NOT NULL UNIQUE COMMENT 'Configuration name identifier',
  `config_description` text DEFAULT NULL COMMENT 'Description of the configuration',
  `pob_required_for_static_qr` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Whether POB is required for static QR services',
  `pob_required_for_dynamic_qr` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Whether POB is required for dynamic QR services',
  `grace_period_days` int(11) NOT NULL DEFAULT 0 COMMENT 'Grace period in days before QR services are disabled',
  `max_transaction_amount_without_pob` decimal(12,2) DEFAULT 0.00 COMMENT 'Maximum transaction amount allowed without POB',
  `max_daily_amount_without_pob` decimal(12,2) DEFAULT 0.00 COMMENT 'Maximum daily transaction amount without POB',
  `max_monthly_amount_without_pob` decimal(12,2) DEFAULT 0.00 COMMENT 'Maximum monthly transaction amount without POB',
  `notification_days_before_disable` varchar(50) DEFAULT '7,3,1' COMMENT 'Days before disabling when to send notifications (comma separated)',
  `auto_disable_qr_services` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Whether to auto-disable QR services after grace period',
  `allow_new_qr_generation` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether to allow new QR generation without POB',
  `show_pob_reminder` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Whether to show POB reminder in merchant app',
  `pob_reminder_message` text DEFAULT NULL COMMENT 'Message to show in POB reminder',
  `is_active` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT 'Whether this configuration is active',
  `effective_from` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When this configuration becomes effective',
  `effective_till` timestamp NULL DEFAULT NULL COMMENT 'When this configuration expires',
  `created_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who created this configuration',
  `updated_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who last updated this configuration',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='QR service configuration based on POB requirements';

-- Add indexes for better performance
ALTER TABLE `ma_pob_qr_service_config`
ADD INDEX `idx_config_name` (`config_name`),
ADD INDEX `idx_is_active` (`is_active`),
ADD INDEX `idx_effective_from` (`effective_from`),
ADD INDEX `idx_effective_till` (`effective_till`),
ADD INDEX `idx_pob_required_static` (`pob_required_for_static_qr`),
ADD INDEX `idx_pob_required_dynamic` (`pob_required_for_dynamic_qr`),
ADD INDEX `idx_auto_disable` (`auto_disable_qr_services`);

-- Insert default configuration
INSERT INTO `ma_pob_qr_service_config` 
(`config_name`, `config_description`, `pob_required_for_static_qr`, `pob_required_for_dynamic_qr`, `grace_period_days`, `max_transaction_amount_without_pob`, `max_daily_amount_without_pob`, `max_monthly_amount_without_pob`, `notification_days_before_disable`, `auto_disable_qr_services`, `allow_new_qr_generation`, `show_pob_reminder`, `pob_reminder_message`) 
VALUES
('DEFAULT_POB_CONFIG', 'Default POB configuration for QR services', 'Y', 'Y', 30, 0.00, 0.00, 0.00, '7,3,1', 'Y', 'N', 'Y', 'Complete your business verification to continue using QR services. Upload your business documents to avoid service interruption.');

-- Create table for merchant-specific QR service status
CREATE TABLE `ma_pob_merchant_qr_status` (
  `ma_pob_merchant_qr_status_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_user_id` int(11) NOT NULL COMMENT 'Reference to ma_user_master table',
  `static_qr_status` enum('ENABLED','DISABLED','SUSPENDED') NOT NULL DEFAULT 'DISABLED' COMMENT 'Static QR service status',
  `dynamic_qr_status` enum('ENABLED','DISABLED','SUSPENDED') NOT NULL DEFAULT 'DISABLED' COMMENT 'Dynamic QR service status',
  `qr_services_disabled_on` timestamp NULL DEFAULT NULL COMMENT 'When QR services were disabled',
  `qr_services_disabled_reason` text DEFAULT NULL COMMENT 'Reason for disabling QR services',
  `last_pob_reminder_sent` timestamp NULL DEFAULT NULL COMMENT 'When last POB reminder was sent',
  `pob_reminder_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of POB reminders sent',
  `grace_period_expires_on` timestamp NULL DEFAULT NULL COMMENT 'When grace period expires',
  `auto_disable_scheduled` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether auto-disable is scheduled',
  `last_qr_generation_attempt` timestamp NULL DEFAULT NULL COMMENT 'Last attempt to generate QR',
  `qr_generation_blocked` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether QR generation is blocked',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Merchant-specific QR service status tracking';

-- Add indexes for merchant QR status table
ALTER TABLE `ma_pob_merchant_qr_status`
ADD UNIQUE INDEX `idx_ma_user_id` (`ma_user_id`),
ADD INDEX `idx_static_qr_status` (`static_qr_status`),
ADD INDEX `idx_dynamic_qr_status` (`dynamic_qr_status`),
ADD INDEX `idx_auto_disable_scheduled` (`auto_disable_scheduled`),
ADD INDEX `idx_grace_period_expires` (`grace_period_expires_on`),
ADD INDEX `idx_qr_generation_blocked` (`qr_generation_blocked`);
