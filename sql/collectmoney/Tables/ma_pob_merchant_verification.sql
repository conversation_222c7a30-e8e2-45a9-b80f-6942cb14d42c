-- PO<PERSON> (Proof of Business) Merchant Verification Table
-- This table manages business document verification for merchants to enable QR services
-- Created for collect money module POB functionality

CREATE TABLE `ma_pob_merchant_verification` (
  `ma_pob_merchant_verification_id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `ma_user_id` int(11) NOT NULL COMMENT 'Reference to ma_user_master table',
  `userid` int(11) NOT NULL COMMENT 'User ID who initiated the verification',
  `business_document_type` enum('GST_CERTIFICATE','TRADE_LICENSE','SHOP_ESTABLISHMENT','UDYAM_CERTIFICATE','MSME_CERTIFICATE','PARTNERSHIP_DEED','MOA_AOA','OTHER') NOT NULL COMMENT 'Type of business document uploaded',
  `document_number` varchar(100) DEFAULT NULL COMMENT 'Document number/registration number',
  `document_file_path` varchar(500) DEFAULT NULL COMMENT 'S3 file path of uploaded document',
  `document_file_name` varchar(255) DEFAULT NULL COMMENT 'Original file name',
  `document_file_size` int(11) DEFAULT NULL COMMENT 'File size in bytes',
  `document_mime_type` varchar(50) DEFAULT NULL COMMENT 'MIME type of uploaded file',
  `business_name` varchar(255) DEFAULT NULL COMMENT 'Business name as per document',
  `business_address` text DEFAULT NULL COMMENT 'Business address as per document',
  `business_registration_date` date DEFAULT NULL COMMENT 'Business registration date',
  `pob_approval_status` enum('PENDING','APPROVED','REJECTED','UNDER_REVIEW') NOT NULL DEFAULT 'PENDING' COMMENT 'POB approval status',
  `activation_status` enum('INACTIVE','ACTIVE','SUSPENDED') NOT NULL DEFAULT 'INACTIVE' COMMENT 'QR services activation status',
  `qr_services_enabled` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Whether QR services are enabled - Y=Yes, N=No',
  `static_qr_enabled` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Static QR service status',
  `dynamic_qr_enabled` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT 'Dynamic QR service status',
  `verification_remarks` text DEFAULT NULL COMMENT 'Admin remarks during verification',
  `rejection_reason` text DEFAULT NULL COMMENT 'Reason for rejection if status is REJECTED',
  `verified_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who verified the document',
  `verified_on` timestamp NULL DEFAULT NULL COMMENT 'Verification completion timestamp',
  `approved_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who approved the POB',
  `approved_on` timestamp NULL DEFAULT NULL COMMENT 'Approval timestamp',
  `rejected_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who rejected the POB',
  `rejected_on` timestamp NULL DEFAULT NULL COMMENT 'Rejection timestamp',
  `qr_activation_requested_on` timestamp NULL DEFAULT NULL COMMENT 'When merchant requested QR activation',
  `qr_activated_on` timestamp NULL DEFAULT NULL COMMENT 'When QR services were activated',
  `qr_activated_by` int(11) DEFAULT NULL COMMENT 'Admin user ID who activated QR services',
  `last_status_change_by` int(11) DEFAULT NULL COMMENT 'User ID who made last status change',
  `last_status_change_on` timestamp NULL DEFAULT NULL COMMENT 'Last status change timestamp',
  `addedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updatedon` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='POB merchant verification for QR services activation';

-- Add indexes for better performance
ALTER TABLE `ma_pob_merchant_verification`
ADD INDEX `idx_ma_user_id` (`ma_user_id`),
ADD INDEX `idx_userid` (`userid`),
ADD INDEX `idx_pob_approval_status` (`pob_approval_status`),
ADD INDEX `idx_activation_status` (`activation_status`),
ADD INDEX `idx_qr_services_enabled` (`qr_services_enabled`),
ADD INDEX `idx_business_document_type` (`business_document_type`),
ADD INDEX `idx_addedon` (`addedon`),
ADD INDEX `idx_updatedon` (`updatedon`),
ADD INDEX `idx_verified_on` (`verified_on`),
ADD INDEX `idx_approved_on` (`approved_on`),
ADD INDEX `idx_ma_user_status` (`ma_user_id`, `pob_approval_status`, `activation_status`);

-- Add foreign key constraints (optional, based on existing schema design)
-- ALTER TABLE `ma_pob_merchant_verification`
-- ADD CONSTRAINT `fk_pob_ma_user_id` FOREIGN KEY (`ma_user_id`) REFERENCES `ma_user_master` (`profileid`) ON DELETE CASCADE ON UPDATE CASCADE;
