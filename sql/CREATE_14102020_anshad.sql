DROP TABLE IF EXISTS `ma_bene_migration`;

CREATE TABLE `ma_bene_migration` (
  `ma_bene_migration_id` int(11) NOT NULL AUTO_INCREMENT,
  `remitter_name` varchar(257) DEFAULT NULL,
  `remitter_mobile_number` varchar(30) DEFAULT NULL,
  `bene_name` varchar(257) DEFAULT NULL,
  `bene_mobile_number` varchar(20) DEFAULT NULL,
  `relationship` varchar(10) DEFAULT 'Other',
  `accountnumber` varchar(50) DEFAULT NULL,
  `ifsc_code` varchar(50) DEFAULT NULL,
  `ma_user_id` varchar(50) DEFAULT NULL,
  `userid` varchar(50) DEFAULT NULL,
  `uic` varchar(257) DEFAULT '0',
  `ma_customer_details_id` int(11) DEFAULT 0,
  `ma_beneficiaries_id` int(11) DEFAULT 0,
  `remitter_id` int(11) DEFAULT 0,
  `receiver_id` int(11) DEFAULT 0,
  `remitter_status` enum('Y','N','E','P') DEFAULT NULL COMMENT 'Y-registered,N-not registered,P-Already registred',
  `bene_status` enum('Y','N','E','P') DEFAULT NULL COMMENT 'Y-registered,N-not registered,P-Already registred',
  `migrated` enum('Y','N','E','P') DEFAULT NULL COMMENT 'Y-Done,N-Not Done, E-Error,P-Pending',
  `addedon` timestamp NULL DEFAULT current_timestamp(),
  `updatedon` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `bank_reason` text DEFAULT NULL,
  PRIMARY KEY (`ma_bene_migration_id`),
  UNIQUE KEY `MIGRATION_UNIQUE_INDEX` (`remitter_mobile_number`,`accountnumber`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;