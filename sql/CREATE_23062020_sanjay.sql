CREATE TABLE ma_queue_manager (
  queue_id int(11) NOT NULL AUTO_INCREMENT,
  queue_name varchar(255) NOT NULL,
  queue_payload json NOT NULL,
  queue_status enum('I','A','R','S','F','E') NOT NULL COMMENT 'I-Inserted,A-assigned,R-Received,S-Success,F-Failed,E-Error',
  queue_insert_response json DEFAULT NULL,
  queue_assign_response json DEFAULT NULL,
  queue_process_response json DEFAULT NULL,
  addedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedon timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (queue_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;