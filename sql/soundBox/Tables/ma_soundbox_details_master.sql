CREATE TABLE `ma_soundbox_details_master` ( 
    `ma_soundbox_details_master_id` INT NOT NULL AUTO_INCREMENT , 
    `ma_user_id` INT(10) NOT NULL , 
    `vpa_id` VARCHAR(100) NULL , 
    `device_id` VARCHAR(100) NULL , 
    `device_status` CHAR(10) NOT NULL DEFAULT 'N' COMMENT 'Y-Active ,N-Inactive' , 
    `device_language` CHAR(10) NULL COMMENT 'Device language' , 
    `api_request` TEXT NULL , 
    `api_response` TEXT NULL , 
    `addedon` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    `updatedon` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
    PRIMARY KEY (`ma_soundbox_details_master_id`),
    INDEX `ma_user_id_idx` (`ma_user_id`), 
    INDEX `device_status_idx` (`device_status`), 
    INDEX `device_id_idx` (`device_id`)) COMMENT  'soundbox device merchant mapping';