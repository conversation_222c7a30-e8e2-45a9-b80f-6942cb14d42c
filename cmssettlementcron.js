'use strict'

const { serverlessErrorHandler } = require('./src/util/errorHandler')
var cmsSettlementController = require('./src/controller/cms/cmsSettlementController.js')
const log = require('./src/util/log')

module.exports.cmssettlementcron = async (event, context, callback) => {
  console.log('---RUNNING CMS SETTLEMENT CRON---')
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  var payload = {}
  try {
    var response = ''
    response = await cmsSettlementController.cmsSettlementCron()
    console.log(response)
    response = await serverlessErrorHandler(event, response)

    console.log('---CMS SETTLEMENT CRON FINAL RESPONSE---', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('---CMS SETTLEMENT CRON CATCH ERROR---', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
