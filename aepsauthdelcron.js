'use strict'

const aepsMerchantAuthController = require('./src/controller/aepsMerchantAuth/aepsMerchantAuthController')
const log = require('./src/util/log')

module.exports.aepsauthdelcron = async (event, context, callback) => {
  console.log('---RUNNING AEPS AUTH Merchant Deletion CRON---')
  // const originalLogger = log.logger
  // log.logger = (logData) => {
  //   originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  // }
  log.setAwsRequestId(context.awsRequestId)

  try {
    var delResponse = ''
    delResponse = await aepsMerchantAuthController.aepsDeleteMerchantAuthResultsData()
    console.log(delResponse)
    console.log('---AEPS DELETE Merchant DATA CRON FINAL RESPONSE---', delResponse)
    return delResponse
  } catch (error) {
    console.log('---AEPS DELETE Merchant DATA CRON CRON CATCH ERROR---', error)
    let errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
