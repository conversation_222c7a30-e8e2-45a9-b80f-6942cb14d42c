'use strict'

const cmsController = require('./src/controller/cms/cmsController.js')
const log = require('./src/util/log')

module.exports.bajajrevalidationcron = async (event, context, callback) => {
  console.log('---RUNNING Bajaj Revalidation Cron---')
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  try {
    var response = ''
    response = await cmsController.bajajRevalidationCron()
    console.log(response)
    console.log('---Bajaj Revalidation Cron FINAL RESPONSE---', response)
    return response
  } catch (error) {
    console.log('---Bajaj Revalidation Cron CATCH ERROR---', error)
    let errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
