'use strict'

const { serverlessErrorHandler } = require('./src/util/errorHandler')
var aepsTransactionController = require('./src/controller/transaction/aepsTransactionController')
const log = require('./src/util/log')

module.exports.aepsrequerycron = async (event, context, callback) => {
  console.log('---RUNNING AEPS REQUERY CRON---')
  // const originalLogger = log.logger
  // log.logger = (logData) => {
  //   originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  // }
  log.setAwsRequestId(context.awsRequestId)
  // if (process.env.IS_OFFLINE === 'true') {
  //   event.data = event.body
  //   console.log('After Parse event.data', event.data)
  // }
  var payload = {}
  try {
    var response = ''
    response = await aepsTransactionController.aepsRequeryCron()
    response = await serverlessErrorHandler(event, response)

    console.log('---AEPS REQUERY CRON FINAL RESPONSE---', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('---AEPS REQUERY CRON CATCH ERROR---', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
