'use strict'

const { graphql } = require('graphql')
const jwt = require('jsonwebtoken')
const util = require('./src/util/util')
const schema = require('./src/schema/externalApiHandlers/index')
const common = require('./src/util/common')
const middleware = require('./src/util/middleware')
const { serverlessErrorHandler } = require('./src/util/errorHandler')
const { processRequest } = require('graphql-upload-minimal')
const log = require('./src/util/log')

module.exports.apirouterfileupload = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  try {
    event.data = await processRequest(event, null, { environment: 'lambda' })
    console.log('After Parse event.data', event.data)
  } catch (error) {
    console.log('graphql-upload-minimal Error', error)
  }

  const downtimeresp = await common.downTime()
  if (downtimeresp[0] != 'undefined' && downtimeresp[0] != null) {
    if (downtimeresp[0].notice_flag == 'Y') {
      if (util.encryptionFlag) {
        return await common.encrypt(JSON.stringify({ status: 301, message: downtimeresp[0].message }))
      } else {
        return JSON.stringify({ status: 301, message: downtimeresp[0].message })
      }
    }
  }
  var payload = {}
  var token = ''
  try {
    // util.isProduction() = true
    if (util.isProduction()) {
      console.log('Production123')
      token = event.headers.Authorization
      payload = jwt.verify(token, util.jwtKey)
      console.log('payload data++++++++', payload)
    }
    if (!util.isProduction() || (util.isProduction() && payload.publickey)) {
      console.log('before decrypt data', event.data.query)
      if (util.isProduction()) {
        const tokenresp = await common.tokenValidation(token, payload.publickey)
        if (tokenresp.status === 400) {
          return JSON.stringify({ status: 401, message: tokenresp.message })
        }
      }

      const isSecuredResponse = await middleware.secureServerless(event)
      if (isSecuredResponse.status === 500) {
        return JSON.stringify(isSecuredResponse)
      }

      let response = await graphql(schema, event.data.query, {}, {}, event.data.variables || {})

      response = await serverlessErrorHandler(event.data, response)

      console.log('res1', response)
      if (process.env.IS_OFFLINE === 'true') {
        callback(null, { statusCode: 200, body: JSON.stringify(response) })
      }
      return response
      // console.log('=========Graphql Res==========',res)
    } else {
      console.log('jwt failed')
    }
  } catch (error) {
    console.log('JWT FAILED IN VERIFICATION', error)
    // return { status: 400, message: 'jwt failed'}
    var errResp = {}
    if (error instanceof jwt.JsonWebTokenError) {
      errResp = { status: 401, message: error.message }
    } else {
      errResp = { status: 400, message: 'Request Failed' }
    }
    console.log('res1', errResp)
    return errResp
  }
}
