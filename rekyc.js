'use strict'

const { graphql } = require('graphql')
const jwt = require('jsonwebtoken')
const util = require('./src/util/util')
const schema = require('./src/schema/ma_re_kyc/index')
const common = require('./src/util/common')
const middleware = require('./src/util/middleware')

const { serverlessErrorHandler, notifyCatchErrorEmail } = require('./src/util/errorHandler')
const log = require('./src/util/log')

module.exports.rekyc = async (event, context, callback) => {
  console.log('event++++++++++++++++', event)
  const originalLogger = log.logger
  log.logger = (logData) => {
    originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  }

  if (process.env.IS_OFFLINE === 'true') {
    event.data = JSON.parse(event.body)
    console.log('After Parse event.data', event.data)

    // Testing purpose
    const isAuthorize = middleware.validateTokenSchema(event, {
      publickey: 7556
    })

    console.log('isAuthorize))==>', isAuthorize)
  }
  const downtimeresp = await common.downTime()
  if (downtimeresp[0] != 'undefined' && downtimeresp[0] != null) {
    if (downtimeresp[0].notice_flag == 'Y') {
      if (util.encryptionFlag) {
        return await common.encrypt(JSON.stringify({ status: 301, message: downtimeresp[0].message }))
      } else {
        return JSON.stringify({ status: 301, message: downtimeresp[0].message })
      }
    }
  }
  // console.log('downtime resp3', downtimeresp[0])
  // console.log('downtime resp1', downtimeresp[0].message)
  // console.log('downtime resp2', downtimeresp[0].notice_flag)

  // // middleware.logs(event)
  var payload = {}
  var token = ''
  try {
    // util.isProduction() = true
    // jwt verify
    if (util.isProduction() || util.isStaging()) {
      console.log('Production123')
      token = event.headers.Authorization
      payload = jwt.verify(token, util.jwtKey)
      console.log('payload data++++++++', payload)
    }
    // jwt token db check
    if ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey)) {
      console.log('before decrypt data', event.data.query)
      if (util.isProduction() || util.isStaging()) {
        const tokenresp = await common.tokenValidation(token, payload.publickey)
        console.log('tokenresp>>', tokenresp)
        if (tokenresp.status === 400) {
          if (util.encryptionFlag) {
            return await common.encrypt(JSON.stringify({ status: 401, message: tokenresp.message }))
          } else {
            return JSON.stringify({ status: 401, message: tokenresp.message })
          }
        }
      }
    }
    console.log('before decrypt data', event.data.query)
    if (util.encryptionFlag) {
      event.data.query = await common.decrypt(event.data.query, util.encryptpassword)
      console.log('after decrypt data', event.data.query)
    }

    // request size
    const isSecuredResponse = await middleware.secureServerless(event, context, payload)
    if (isSecuredResponse.status === 500) {
      if (util.encryptionFlag) {
        return await common.encrypt(JSON.stringify(isSecuredResponse))
      } else {
        return JSON.stringify(isSecuredResponse)
      }
    }

    // jwt detail  verify with grapql deails ma_user-id & profileid
    let isAuthorize = {}
    if ((util.isProduction() && payload.publickey) || (util.isStaging() && payload.publickey)) {
      isAuthorize = middleware.validateTokenSchema(event, payload)
      console.log('isAuthorize))==>', isAuthorize)
      if (isAuthorize.status != 200) {
        if (util.encryptionFlag) {
          return await common.encrypt(JSON.stringify(isAuthorize))
        } else {
          return JSON.stringify(isAuthorize)
        }
      }
    }
    console.log('check pos handler ==============')
    let response = await graphql(schema, event.data.query, (err, result) => {
      if (err) {
        console.log('Error in graphql', err)
        callback(err)
      }
      console.log('handler result', result)
      // callback(null, { statusCode: 200, body: JSON.stringify(err) })
    })

    response = await serverlessErrorHandler(event, response)

    if (util.encryptionFlag) {
      console.log('res1', await common.encrypt(JSON.stringify(response), util.encryptpassword))
      return await common.encrypt(JSON.stringify(response), util.encryptpassword)
    } else {
      console.log('res1', response)
      if (process.env.IS_OFFLINE === 'true') {
        callback(null, { statusCode: 200, body: JSON.stringify(response) })
      }
      return response
    }
  } catch (error) {
    console.log('JWT FAILED IN VERIFICATION', error)
    // return { status: 400, message: 'jwt failed'} // TokenExpiredError
    if (!(error instanceof jwt.JsonWebTokenError) && !(error instanceof jwt.TokenExpiredError)) {
      notifyCatchErrorEmail({
        function: 'qractivationHandler',
        data: event.data,
        error: error
      })
    }
    var errResp = {}
    if (error instanceof jwt.JsonWebTokenError) {
      errResp = { status: 401, message: error.message }
    } else {
      errResp = { status: 400, message: 'Request Failed' }
    }
    if (util.encryptionFlag) {
      console.log('err1', await common.encrypt(JSON.stringify(errResp), util.encryptpassword))
      return await common.encrypt(JSON.stringify(errResp), util.encryptpassword)
    } else {
      console.log('res1', errResp)
      return errResp
    }
  }
}
